package com.hollysys.inspection.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.entity.InspThirdApp;
import com.hollysys.inspection.entity.InspThirdInterface;
import com.hollysys.inspection.entity.InspThirdPermission;
import com.hollysys.inspection.mapper.InspThirdAppMapper;
import com.hollysys.inspection.model.BindInterfacesReqModel;
import com.hollysys.inspection.model.InspThirdAppModel;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;

class InspThirdAppServiceTest {

    @Spy
    @InjectMocks
    private InspThirdAppService inspThirdAppService;

    @Mock
    private InspThirdAppMapper inspThirdAppMapper;

    @Mock
    private InspThirdInterfaceService thirdInterfaceService;

    @Mock
    private InspThirdPermissionService thirdPermissionService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testGetAppDetail() {
        Mockito.when(inspThirdAppMapper.selectById(Mockito.any())).thenReturn(null);
        try {
            inspThirdAppService.getAppDetail("23");
        } catch (Exception e) {
            Assertions.assertTrue(e instanceof InspectionException);
        }
        Mockito.when(inspThirdAppMapper.selectById(Mockito.any())).thenReturn(new InspThirdAppModel());
        inspThirdAppService.getAppDetail("23");
    }

    @Test
    void testNewApp() {
        InspThirdAppModel inspThirdAppModel = new InspThirdAppModel();
        inspThirdAppModel.setName("123");
        Mockito.when(inspThirdAppMapper.selectList(Mockito.any())).thenReturn(null);
        Mockito.when(inspThirdAppMapper.insert(Mockito.any())).thenReturn(1);
        inspThirdAppService.newApp(inspThirdAppModel);


        inspThirdAppModel.setName("");
        try {
            inspThirdAppService.newApp(inspThirdAppModel);
        } catch (Exception e) {
            Assertions.assertTrue(e instanceof InspectionException);
        }
        inspThirdAppModel.setName("123");
        Mockito.when(inspThirdAppMapper.selectList(Mockito.any())).thenReturn(Collections.singletonList(new InspThirdAppModel()));
        try {
            inspThirdAppService.newApp(inspThirdAppModel);
        } catch (Exception e) {
            Assertions.assertTrue(e instanceof InspectionException);
        }
    }

    @Test
    void testUpdateApp() {
        InspThirdAppModel inspThirdAppModel = new InspThirdAppModel();
        try {
            inspThirdAppService.updateApp(inspThirdAppModel);
        } catch (Exception e) {
            Assertions.assertTrue(e instanceof InspectionException);
        }
        inspThirdAppModel.setId("123");
        Mockito.when(inspThirdAppMapper.selectById(Mockito.any())).thenReturn(null);
        try {
            inspThirdAppService.updateApp(inspThirdAppModel);
        } catch (Exception e) {
            Assertions.assertTrue(e instanceof InspectionException);
        }
        inspThirdAppModel.setName("123");
        InspThirdAppModel inspThirdAppModel1 = new InspThirdAppModel();
        inspThirdAppModel1.setName("23");
        Mockito.when(inspThirdAppMapper.selectById(Mockito.any())).thenReturn(inspThirdAppModel1);
        Mockito.when(inspThirdAppMapper.selectList(Mockito.any())).thenReturn(Collections.singletonList(new InspThirdAppModel()));
        try {
            inspThirdAppService.updateApp(inspThirdAppModel);
        } catch (Exception e) {
            Assertions.assertTrue(e instanceof InspectionException);
        }
        Mockito.when(inspThirdAppMapper.selectList(Mockito.any())).thenReturn(null);
        Mockito.when(inspThirdAppMapper.updateById(Mockito.any())).thenReturn(1);
        inspThirdAppModel.setAvailable(true);
        inspThirdAppService.updateApp(inspThirdAppModel);
    }

    @Test
    void testRemoveApps() {
        Mockito.when(inspThirdAppMapper.deleteBatchIds(Collections.singletonList("123"))).thenReturn(1);
        Mockito.doNothing().when(thirdPermissionService).removeByAppIds(Collections.singletonList("123"));
        inspThirdAppService.removeApps(Collections.singletonList("123"));

    }

    @Test
    void testListAllInterface() {
        Mockito.when(thirdInterfaceService.list()).thenReturn(new ArrayList<>());
        inspThirdAppService.listAllInterface();
        InspThirdInterface inspThirdInterface = new InspThirdInterface();
        inspThirdInterface.setClassification("123");
        Mockito.when(thirdInterfaceService.list()).thenReturn(Collections.singletonList(inspThirdInterface));
        inspThirdAppService.listAllInterface();
    }

    @Test
    void testBindInterfaces() {
        BindInterfacesReqModel bindInterfacesReqModel = new BindInterfacesReqModel();
        try {
            inspThirdAppService.bindInterfaces(bindInterfacesReqModel);
        } catch (Exception e) {
            Assertions.assertTrue(e instanceof InspectionException);
        }
        bindInterfacesReqModel.setAppId("123");
        Mockito.when(inspThirdAppMapper.selectById(Mockito.any())).thenReturn(null);
        try {
            inspThirdAppService.bindInterfaces(bindInterfacesReqModel);
        } catch (Exception e) {
            Assertions.assertTrue(e instanceof InspectionException);
        }
        Mockito.when(inspThirdAppMapper.selectById(Mockito.any())).thenReturn(new InspThirdAppModel());
        Mockito.doNothing().when(thirdPermissionService).removeByAppIds(Mockito.any());
        inspThirdAppService.bindInterfaces(bindInterfacesReqModel);
        bindInterfacesReqModel.setInterfaceIds(Collections.singletonList("123"));
        Mockito.when(thirdInterfaceService.listByIds(Mockito.any())).thenReturn(null);
        inspThirdAppService.bindInterfaces(bindInterfacesReqModel);
        Mockito.doNothing().when(thirdPermissionService).refreshPermissionCache(Mockito.any());
        Mockito.when(thirdInterfaceService.listByIds(Mockito.any())).thenReturn(Collections.singletonList(new InspThirdInterface()));
        Mockito.when(thirdPermissionService.saveBatch(Mockito.any())).thenReturn(true);
        inspThirdAppService.bindInterfaces(bindInterfacesReqModel);
    }

    @Test
    void testGetByAppCode() {
        Mockito.when(inspThirdAppMapper.selectList(Mockito.any())).thenReturn(null);
        inspThirdAppService.getByAppCode("123");
        Mockito.when(inspThirdAppMapper.selectList(Mockito.any())).thenReturn(Collections.singletonList(new InspThirdAppModel()));
        inspThirdAppService.getByAppCode("123");
    }

    @Test
    void testListPage() {
        Page<InspThirdApp> page = new Page<>();
        InspThirdAppModel inspThirdAppModel = new InspThirdAppModel();
        inspThirdAppModel.setUpdateTime(LocalDateTime.now());
        inspThirdAppModel.setCreateTime(LocalDateTime.now());
        page.setRecords(Collections.singletonList(inspThirdAppModel));
        Mockito.when(inspThirdAppMapper.selectPage(Mockito.any(), Mockito.any())).thenReturn(page);
        inspThirdAppService.listPage(page, "1", "123");
        page.setRecords(null);
        inspThirdAppService.listPage(page, "1", "123");
    }

    @Test
    void testListBindInterface() {

        Mockito.when(thirdPermissionService.listByAppId(Mockito.any())).thenReturn(Collections.singletonList(new InspThirdPermission()));
        inspThirdAppService.listBindInterface("123");
    }
}