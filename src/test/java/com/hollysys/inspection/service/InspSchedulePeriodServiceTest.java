package com.hollysys.inspection.service;

import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.entity.InspSchedulePeriod;
import com.hollysys.inspection.mapper.InspSchedulePeriodMapper;
import com.hollysys.inspection.model.period.InspScheduleCopyModel;
import com.hollysys.inspection.model.period.InspSchedulePeriodModel;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import java.util.Collections;
import java.util.List;

class InspSchedulePeriodServiceTest {

    @InjectMocks
    @Spy
    private InspSchedulePeriodService inspSchedulePeriodService;

    @Mock
    private InspSchedulePeriodMapper inspSchedulePeriodMapper;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }


    @Test
    void copyToOther() {
        Mockito.when(inspSchedulePeriodMapper.selectList(Mockito.any())).thenReturn(Collections.singletonList(new InspSchedulePeriod()));
        Mockito.doThrow(new InspectionException("")).when(inspSchedulePeriodMapper).insert(Mockito.any());
        try {
            inspSchedulePeriodService.copyToOther(new InspScheduleCopyModel());
        } catch (Exception e) {

        }


    }

    @Test
    void listByTaskId() {
        Mockito.when(inspSchedulePeriodMapper.selectList(Mockito.any())).thenReturn(null);
        InspSchedulePeriodModel inspSchedulePeriodModel = inspSchedulePeriodService.listByTaskId("");
        Assertions.assertNull(inspSchedulePeriodModel);
        InspSchedulePeriod inspSchedulePeriod = new InspSchedulePeriod();
        inspSchedulePeriod.setWeek(0);
        Mockito.when(inspSchedulePeriodMapper.selectList(Mockito.any())).thenReturn(Collections.singletonList(inspSchedulePeriod));
        inspSchedulePeriodModel = inspSchedulePeriodService.listByTaskId("");
        Assertions.assertNotNull(inspSchedulePeriodModel);
    }

    @Test
    void listByTaskIdAndWeekDay() {
        InspSchedulePeriod inspSchedulePeriod = new InspSchedulePeriod();
        inspSchedulePeriod.setWeek(0);
        Mockito.when(inspSchedulePeriodMapper.selectList(Mockito.any())).thenReturn(Collections.singletonList(inspSchedulePeriod));
        InspSchedulePeriod inspSchedulePeriod1 = inspSchedulePeriodService.listByTaskIdAndWeekDay(0, "2");
        Assertions.assertNotNull(inspSchedulePeriod1);
        Mockito.when(inspSchedulePeriodMapper.selectList(Mockito.any())).thenReturn(null);
        InspSchedulePeriod inspSchedulePeriod12 = inspSchedulePeriodService.listByTaskIdAndWeekDay(0, "2");
        Assertions.assertNull(inspSchedulePeriod12);
    }

    @Test
    void listByWeekDay() {
        Mockito.when(inspSchedulePeriodMapper.selectList(Mockito.any())).thenReturn(null);
        List<InspSchedulePeriod> schedulePeriods = inspSchedulePeriodService.listByWeekDay(1);
        Assertions.assertNull(schedulePeriods);

    }

    @Test
    void removeByTaskIds() {
        Mockito.when(inspSchedulePeriodMapper.delete(Mockito.any())).thenReturn(1);
        inspSchedulePeriodService.removeByTaskIds(Collections.singletonList("123"));
    }
}