package com.hollysys.inspection.service;

import com.hollysys.inspection.mapper.InspAlarmRecordMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import java.util.Collections;

class InspAlarmRecordServiceTest {
    @Spy
    @InjectMocks
    private InspAlarmRecordService inspAlarmRecordService;

    @Mock
    private InspAlarmRecordMapper inspAlarmRecordMapper;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void removeByChannelIds() {
        Mockito.when(inspAlarmRecordMapper.delete(Mockito.any())).thenReturn(1);
        inspAlarmRecordService.removeByChannelIds(Collections.singletonList("1"));
    }
}