package com.hollysys.inspection.service;

import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.constants.algorithm.param.DataType;
import com.hollysys.inspection.model.algorithm.param.AlgorithmParamConst;
import com.hollysys.inspection.model.algorithm.param.SelectorOption;
import com.hollysys.inspection.model.algorithm.param.data.Range;
import com.hollysys.inspection.utils.AssertUtil;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

class DataTypeTest {

    @Test
    void testRange() {
        DataType dataType = DataType.RANGE;
        AlgorithmParamConst algorithmParamConst = new AlgorithmParamConst();
        algorithmParamConst.setRequired(true);
        algorithmParamConst.setMax(10f);
        algorithmParamConst.setMin(5f);

        AssertUtil.throwsException(() -> dataType.checkValue("", algorithmParamConst), InspectionException.class);
        AssertUtil.throwsException(() -> dataType.checkValue(null, algorithmParamConst), InspectionException.class);

        Range range = new Range();
        range.setStart(5.0f);
        range.setEnd(90.0f);
        AssertUtil.throwsException(() -> dataType.checkValue(range, algorithmParamConst), InspectionException.class);

        range.setStart(-5.0f);
        range.setEnd(9.0f);
        AssertUtil.throwsException(() -> dataType.checkValue(range, algorithmParamConst), InspectionException.class);

        range.setStart(5.0f);
        range.setEnd(9.0f);
        dataType.checkValue(range, algorithmParamConst);

        range.setStart(5.0f);
        range.setEnd(10.0f);
        dataType.checkValue(range, algorithmParamConst);

        algorithmParamConst.setRequired(false);
        dataType.checkValue("", algorithmParamConst);
        dataType.checkValue(null, algorithmParamConst);
    }

    @Test
    void testRgb() {
        DataType dataType = DataType.RGB;
        AlgorithmParamConst algorithmParamConst = new AlgorithmParamConst();
        algorithmParamConst.setRequired(true);

        AssertUtil.throwsException(() -> dataType.checkValue("", algorithmParamConst), InspectionException.class);
        AssertUtil.throwsException(() -> dataType.checkValue(null, algorithmParamConst), InspectionException.class);
        AssertUtil.throwsException(() -> dataType.checkValue(Arrays.asList(0, 1, 1000), algorithmParamConst), InspectionException.class);
        AssertUtil.throwsException(() -> dataType.checkValue(Arrays.asList(0, 1), algorithmParamConst), InspectionException.class);
        AssertUtil.throwsException(() -> dataType.checkValue(Arrays.asList(0, 1, 2, 5), algorithmParamConst), InspectionException.class);
        AssertUtil.throwsException(() -> dataType.checkValue(Arrays.asList(0, null, 2), algorithmParamConst), InspectionException.class);

        dataType.checkValue(Arrays.asList(0, 1, 255), algorithmParamConst);

        algorithmParamConst.setRequired(false);
        dataType.checkValue("", algorithmParamConst);
        dataType.checkValue(null, algorithmParamConst);
    }

    @Test
    void testSelector() {
        DataType dataType = DataType.SELECTOR;

        // 多选场景
        AlgorithmParamConst algorithmParamConst = new AlgorithmParamConst();
        algorithmParamConst.setRequired(true);
        algorithmParamConst.setMaxLength(2);

        SelectorOption selectorOption1 = new SelectorOption();
        selectorOption1.setKey("key1");
        selectorOption1.setLabel("label1");

        SelectorOption selectorOption2 = new SelectorOption();
        selectorOption2.setKey("key2");
        selectorOption2.setLabel("label2");

        SelectorOption selectorOption3 = new SelectorOption();
        selectorOption3.setKey("key3");
        selectorOption3.setLabel("label3");
        List<SelectorOption> options = Arrays.asList(selectorOption1, selectorOption2, selectorOption3);
        algorithmParamConst.setOptions(options);

        AssertUtil.throwsException(() -> dataType.checkValue("", algorithmParamConst), InspectionException.class);
        AssertUtil.throwsException(() -> dataType.checkValue(null, algorithmParamConst), InspectionException.class);
        AssertUtil.throwsException(() -> dataType.checkValue(Arrays.asList(0, 1, 1000), algorithmParamConst), InspectionException.class);
        AssertUtil.throwsException(() -> dataType.checkValue(Arrays.asList(0, 1), algorithmParamConst), InspectionException.class);
        AssertUtil.throwsException(() -> dataType.checkValue(Arrays.asList(0, 1, 2, 5), algorithmParamConst), InspectionException.class);
        AssertUtil.throwsException(() -> dataType.checkValue(Arrays.asList(0, null, 2), algorithmParamConst), InspectionException.class);
        AssertUtil.throwsException(() -> dataType.checkValue(Arrays.asList("key1", "key2", "key3"), algorithmParamConst), InspectionException.class);
        AssertUtil.throwsException(() -> dataType.checkValue(Arrays.asList("key2", "key2", "key3"), algorithmParamConst), InspectionException.class);
        AssertUtil.throwsException(() -> dataType.checkValue(Arrays.asList("key2", "key2"), algorithmParamConst), InspectionException.class);
        AssertUtil.throwsException(() -> dataType.checkValue(Arrays.asList("key2", "key4"), algorithmParamConst), InspectionException.class);
        AssertUtil.throwsException(() -> dataType.checkValue(Arrays.asList("key9", "key4"), algorithmParamConst), InspectionException.class);

        dataType.checkValue(Arrays.asList("key2", "key3"), algorithmParamConst);
        dataType.checkValue(Arrays.asList("key2", "key1"), algorithmParamConst);

        // 单选场景
        algorithmParamConst.setMaxLength(null);
        AssertUtil.throwsException(() -> dataType.checkValue("", algorithmParamConst), InspectionException.class);
        AssertUtil.throwsException(() -> dataType.checkValue(null, algorithmParamConst), InspectionException.class);
        AssertUtil.throwsException(() -> dataType.checkValue(Arrays.asList("key2", "key2"), algorithmParamConst), InspectionException.class);
        AssertUtil.throwsException(() -> dataType.checkValue(Collections.singletonList("key4"), algorithmParamConst), InspectionException.class);
        dataType.checkValue(Collections.singletonList("key1"), algorithmParamConst);
        dataType.checkValue(Collections.singletonList("key2"), algorithmParamConst);
        dataType.checkValue(Collections.singletonList("key3"), algorithmParamConst);

        // 非必填
        algorithmParamConst.setRequired(false);
        dataType.checkValue("", algorithmParamConst);
        dataType.checkValue(null, algorithmParamConst);
    }

    @Test
    void testSelectorAlgorithmParamConst() {
        DataType dataType = DataType.SELECTOR;
        AlgorithmParamConst algorithmParamConst = new AlgorithmParamConst();
        AssertUtil.isFalse(dataType.checkConstraints(null));
        AssertUtil.isFalse(dataType.checkConstraints(algorithmParamConst));

        algorithmParamConst.setMaxLength(-1);
        AssertUtil.isFalse(dataType.checkConstraints(algorithmParamConst));

        algorithmParamConst.setOptions(new ArrayList<>());
        AssertUtil.isFalse(dataType.checkConstraints(algorithmParamConst));

        algorithmParamConst.setOptions(Collections.singletonList(null));
        AssertUtil.isFalse(dataType.checkConstraints(algorithmParamConst));

        algorithmParamConst.setOptions(Collections.singletonList(null));
        AssertUtil.isFalse(dataType.checkConstraints(algorithmParamConst));

        algorithmParamConst.setMaxLength(2);
        SelectorOption selectorOption1 = new SelectorOption();
        selectorOption1.setKey("key1");
        selectorOption1.setLabel("label1");

        SelectorOption selectorOption2 = new SelectorOption();


        SelectorOption selectorOption3 = new SelectorOption();
        selectorOption3.setKey("key3");
        selectorOption3.setLabel("label3");
        List<SelectorOption> options = Arrays.asList(selectorOption1, selectorOption2, selectorOption3);
        algorithmParamConst.setOptions(options);

        AssertUtil.isFalse(dataType.checkConstraints(algorithmParamConst));

        selectorOption2.setKey("key1");
        AssertUtil.isFalse(dataType.checkConstraints(algorithmParamConst));

        selectorOption2.setLabel("label2");
        AssertUtil.isFalse(dataType.checkConstraints(algorithmParamConst));

        selectorOption2.setKey("key2");
        selectorOption2.setLabel("label2");
        AssertUtil.isTrue(dataType.checkConstraints(algorithmParamConst));
    }

    @Test
    void testIntegerAlgorithmParamConst() {
        DataType dataType = DataType.INTEGER;
        testIntegerAlgorithmParamConst(dataType);
    }

    @Test
    void testFloatAlgorithmParamConst() {
        DataType dataType = DataType.FLOAT;

        AssertUtil.isFalse(dataType.checkConstraints(null));
        AlgorithmParamConst algorithmParamConst = new AlgorithmParamConst();
        AssertUtil.isFalse(dataType.checkConstraints(algorithmParamConst));

        algorithmParamConst.setPrecision(0f);
        AssertUtil.isFalse(dataType.checkConstraints(algorithmParamConst));

        algorithmParamConst.setPrecision(1.2f);
        AssertUtil.isFalse(dataType.checkConstraints(algorithmParamConst));

        algorithmParamConst.setMax(-1f);
        AssertUtil.isFalse(dataType.checkConstraints(algorithmParamConst));

        algorithmParamConst.setMax(1f);
        AssertUtil.isFalse(dataType.checkConstraints(algorithmParamConst));

        algorithmParamConst.setMin(1f);
        algorithmParamConst.setMax(null);
        AssertUtil.isFalse(dataType.checkConstraints(algorithmParamConst));

        algorithmParamConst.setMin(1f);
        algorithmParamConst.setMax(-10f);
        AssertUtil.isFalse(dataType.checkConstraints(algorithmParamConst));

        algorithmParamConst.setMin(1f);
        algorithmParamConst.setMax(10f);
        AssertUtil.isTrue(dataType.checkConstraints(algorithmParamConst));

        algorithmParamConst.setPrecision(1f);
        AssertUtil.isTrue(dataType.checkConstraints(algorithmParamConst));
    }

    @Test
    void testRangeAlgorithmParamConst() {
        DataType dataType = DataType.RANGE;
        testIntegerAlgorithmParamConst(dataType);
    }

    void testIntegerAlgorithmParamConst(DataType dataType) {
        AssertUtil.isFalse(dataType.checkConstraints(null));
        AlgorithmParamConst algorithmParamConst = new AlgorithmParamConst();
        AssertUtil.isFalse(dataType.checkConstraints(algorithmParamConst));

        algorithmParamConst.setPrecision(0f);
        AssertUtil.isFalse(dataType.checkConstraints(algorithmParamConst));

        algorithmParamConst.setPrecision(1.2f);
        AssertUtil.isFalse(dataType.checkConstraints(algorithmParamConst));

        algorithmParamConst.setMax(-1f);
        AssertUtil.isFalse(dataType.checkConstraints(algorithmParamConst));

        algorithmParamConst.setMax(1f);
        AssertUtil.isFalse(dataType.checkConstraints(algorithmParamConst));

        algorithmParamConst.setMin(1f);
        algorithmParamConst.setMax(null);
        AssertUtil.isFalse(dataType.checkConstraints(algorithmParamConst));

        algorithmParamConst.setMin(1f);
        algorithmParamConst.setMax(-10f);
        AssertUtil.isFalse(dataType.checkConstraints(algorithmParamConst));

        algorithmParamConst.setMin(1f);
        algorithmParamConst.setMax(10f);
        AssertUtil.isFalse(dataType.checkConstraints(algorithmParamConst));

        algorithmParamConst.setPrecision(1f);
        AssertUtil.isTrue(dataType.checkConstraints(algorithmParamConst));
    }

    @Test
    void testStringAlgorithmParamConst() {
        DataType dataType = DataType.STRING;

        AssertUtil.isFalse(dataType.checkConstraints(null));
        AlgorithmParamConst algorithmParamConst = new AlgorithmParamConst();
        AssertUtil.isFalse(dataType.checkConstraints(algorithmParamConst));

        algorithmParamConst.setMinLength(-1);
        AssertUtil.isFalse(dataType.checkConstraints(algorithmParamConst));

        algorithmParamConst.setMinLength(10);
        AssertUtil.isFalse(dataType.checkConstraints(algorithmParamConst));

        algorithmParamConst.setMaxLength(1);
        AssertUtil.isFalse(dataType.checkConstraints(algorithmParamConst));

        algorithmParamConst.setMaxLength(10);
        AssertUtil.isTrue(dataType.checkConstraints(algorithmParamConst));

        algorithmParamConst.setMaxLength(100);
        AssertUtil.isTrue(dataType.checkConstraints(algorithmParamConst));
    }
}
