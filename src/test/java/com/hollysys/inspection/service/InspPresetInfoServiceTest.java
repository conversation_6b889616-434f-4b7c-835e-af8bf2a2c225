package com.hollysys.inspection.service;

import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.entity.*;
import com.hollysys.inspection.mapper.InspPresetInfoMapper;
import com.hollysys.inspection.model.ModelInfoModel;
import com.hollysys.inspection.model.algorithm.configure.SaveScheduleConfigReqModel;
import com.hollysys.inspection.model.algorithm.correct.PicCorrectParamModel;
import com.hollysys.inspection.model.algorithm.param.data.Point;
import com.hollysys.inspection.model.algorithm.param.data.Square;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import java.util.ArrayList;
import java.util.Collections;


class InspPresetInfoServiceTest {
    @InjectMocks
    @Spy
    private InspPresetInfoService inspPresetInfoService;

    @Mock
    private InspPresetInfoMapper inspPresetInfoMapper;

    @Mock
    private ProjectTreeNodeService treeNodeService;

    @Mock
    private InspChannelInfoService inspChannelInfoService;

    @Mock
    private InspSceneDefinitionService sceneDefinitionService;

    @Mock
    private InspAlgorithmService algorithmService;

    @Mock
    private InspAlgorithmInstanceService algorithmInstanceService;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getById() {
        Mockito.when(inspPresetInfoMapper.selectById(Mockito.any())).thenReturn(null);
        try {
            inspPresetInfoService.getById("123");
        } catch (Exception e) {
            Assertions.assertTrue(e instanceof InspectionException);
        }
        Mockito.when(inspPresetInfoMapper.selectById(Mockito.any())).thenReturn(new InspPresetInfo());
        inspPresetInfoService.getById("123");
    }

    @Test
    void getDetailById() {
        Mockito.when(inspPresetInfoMapper.selectById(Mockito.any())).thenReturn(new InspPresetInfo());
        Mockito.when(treeNodeService.getById(Mockito.any())).thenReturn(new InspProjectTreeNode());
        Mockito.when(inspChannelInfoService.getById(Mockito.any())).thenReturn(new InspChannelInfo());
        inspPresetInfoService.getDetailById("123");
    }

    @Test
    void getForConfig() {
        InspPresetInfo presetInfo = new InspPresetInfo();
        PicCorrectParamModel picCorrectParamModel = new PicCorrectParamModel();
        Square square = new Square();
        square.add(new Point(1, 1));
        square.add(new Point(2, 1));
        square.add(new Point(2, 2));
        square.add(new Point(1, 2));
        picCorrectParamModel.setToSquare(square);
        presetInfo.setCorrectParam(picCorrectParamModel);
        Mockito.when(inspPresetInfoMapper.selectById(Mockito.any())).thenReturn(presetInfo);
        Mockito.when(treeNodeService.getById(Mockito.any())).thenReturn(new InspProjectTreeNode());
        Mockito.when(inspChannelInfoService.getById(Mockito.any())).thenReturn(new InspChannelInfo());
        Mockito.when(sceneDefinitionService.listByPresetId(Mockito.any())).thenReturn(Collections.singletonList(new InspSceneDefinition()));
        InspAlgorithmInstance inspAlgorithmInstance = new InspAlgorithmInstance();
        inspAlgorithmInstance.setHasSaveConfig(true);
        Mockito.when(algorithmInstanceService.list(Mockito.any())).thenReturn(Collections.singletonList(inspAlgorithmInstance));
        Mockito.when(algorithmService.list(Mockito.any())).thenReturn(Collections.singletonList(new InspAlgorithmInstance()));
        inspPresetInfoService.getForConfig("1");
    }

    @Test
    void getPresetInfoList() {
        InspProjectTreeNode inspProjectTreeNode = new InspProjectTreeNode();
        Mockito.when(treeNodeService.list(Mockito.any())).thenReturn(Collections.singletonList(inspProjectTreeNode)).thenReturn(Collections.singletonList(inspProjectTreeNode));
        InspPresetInfo presetInfo = new InspPresetInfo();
        presetInfo.setIsSchedule(true);
        Mockito.when(inspPresetInfoMapper.selectById(Mockito.any())).thenReturn(presetInfo);
        inspPresetInfoService.getPresetInfoList();
    }

    @Test
    void saveScheduleConfig() {
        SaveScheduleConfigReqModel saveScheduleConfigReqModel = new SaveScheduleConfigReqModel();
        try {
            inspPresetInfoService.saveScheduleConfig(saveScheduleConfigReqModel);
        } catch (Exception e) {
            Assertions.assertTrue(e instanceof InspectionException);
        }
        try {
            inspPresetInfoService.saveScheduleConfig(saveScheduleConfigReqModel);
        } catch (Exception e) {
            Assertions.assertTrue(e instanceof InspectionException);
        }
        saveScheduleConfigReqModel.setPostExecuteDelay(-1);
        saveScheduleConfigReqModel.setValidTimes(2);
        saveScheduleConfigReqModel.setPreExecuteDelay(-1);
        try {
            inspPresetInfoService.saveScheduleConfig(saveScheduleConfigReqModel);
        } catch (Exception e) {
            Assertions.assertTrue(e instanceof InspectionException);
        }
        saveScheduleConfigReqModel.setPostExecuteDelay(1);
        saveScheduleConfigReqModel.setValidTimes(-1);
        saveScheduleConfigReqModel.setPreExecuteDelay(1);
        try {
            inspPresetInfoService.saveScheduleConfig(saveScheduleConfigReqModel);
        } catch (Exception e) {
            Assertions.assertTrue(e instanceof InspectionException);
        }
        saveScheduleConfigReqModel.setValidTimes(1);
        Mockito.when(inspPresetInfoMapper.selectById(Mockito.any())).thenReturn(new InspPresetInfo());
        Mockito.when(inspPresetInfoMapper.updateById(Mockito.any())).thenReturn(1);
        inspPresetInfoService.saveScheduleConfig(saveScheduleConfigReqModel);
    }

    @Test
    void listByProjectId() {
        Mockito.when(inspPresetInfoMapper.selectList(Mockito.any())).thenReturn(new ArrayList<>());
    }

    @Test
    void updatePTZStatus() {
        inspPresetInfoService.updatePTZStatus(new ArrayList<>());
        ModelInfoModel model = new ModelInfoModel();
        inspPresetInfoService.updatePTZStatus(Collections.singletonList(model));
        model.setId("123");
        Mockito.when(inspPresetInfoMapper.updateById(Mockito.any())).thenReturn(1);
        inspPresetInfoService.updatePTZStatus(Collections.singletonList(model));
    }
}
