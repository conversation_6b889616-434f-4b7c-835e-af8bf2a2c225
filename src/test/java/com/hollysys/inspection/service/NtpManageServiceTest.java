package com.hollysys.inspection.service;

import cn.hutool.core.util.RuntimeUtil;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.entity.InspNtpManageInfo;
import com.hollysys.inspection.mapper.InspNtpManageInfoMapper;
import com.hollysys.inspection.model.ntp.GetNtpConfigInfoRespModel;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import java.lang.reflect.Field;
import java.util.Collections;

class NtpManageServiceTest {

    @Spy
    @InjectMocks
    private NtpManageService ntpManageService;

    @Mock
    private InspNtpManageInfoMapper ntpManageMapper;


    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getSysDateNow() throws NoSuchFieldException, IllegalAccessException {
        Class<? extends NtpManageService> aClass = ntpManageService.getClass();
        Field dateFormatTemp = aClass.getDeclaredField("dateFormatTemp");
        dateFormatTemp.setAccessible(true);
        dateFormatTemp.set(ntpManageService, "yyyy-MM-dd HH:mm:ss");
        String sysDateNow = ntpManageService.getSysDateNow();
        Assertions.assertNotNull(sysDateNow);
    }

    @Test
    void setSystemDate() {
        try {
            ntpManageService.setSystemDate("");
        } catch (Exception e) {
            Assertions.assertTrue(e instanceof InspectionException);
        }
        try {
            ntpManageService.setSystemDate("123");
        } catch (Exception e) {
            Assertions.assertTrue(e instanceof InspectionException);
        }

        try (MockedStatic<RuntimeUtil> runtimeUtilMockedStatic = Mockito.mockStatic(RuntimeUtil.class)) {
            runtimeUtilMockedStatic.when(() -> RuntimeUtil.execForLines(Mockito.any(String.class), Mockito.any(String.class), Mockito.any(String.class))).thenReturn(Collections.singletonList("123"));
            Object o = ntpManageService.setSystemDate("2003-12-23 00:22:23");
            Assertions.assertNotNull(o);
        } catch (Exception e) {
            Assertions.assertTrue(e instanceof InspectionException);
        }

        try (MockedStatic<RuntimeUtil> runtimeUtilMockedStatic = Mockito.mockStatic(RuntimeUtil.class)) {
            runtimeUtilMockedStatic.when(() -> RuntimeUtil.execForLines(Mockito.any(String.class), Mockito.any(String.class), Mockito.any(String.class))).thenThrow(new InspectionException("")).thenReturn(Collections.singletonList("123"));
            Object o = ntpManageService.setSystemDate("2003-12-23 00:22:23");
            Assertions.assertNotNull(o);
        } catch (Exception e) {
            Assertions.assertTrue(e instanceof InspectionException);
        }

    }

    @Test
    void scheduleSyncDateFromNtp() throws Exception {


        ntpManageService.scheduleSyncDateFromNtp();
        Class<? extends NtpManageService> aClass = ntpManageService.getClass();
        Field ntpSyncEnable = aClass.getDeclaredField("ntpSyncEnable");
        ntpSyncEnable.setAccessible(true);
        ntpSyncEnable.set(ntpManageService, true);
        Mockito.when(ntpManageMapper.selectList(Mockito.any())).thenReturn(null);
        ntpManageService.scheduleSyncDateFromNtp();
        InspNtpManageInfo inspNtpManageInfo = new InspNtpManageInfo();
        inspNtpManageInfo.setInterval(1);
        inspNtpManageInfo.setAvailable(true);
        Mockito.when(ntpManageMapper.selectList(Mockito.any())).thenReturn(Collections.singletonList(inspNtpManageInfo));
        ntpManageService.scheduleSyncDateFromNtp();
    }

    @Test
    void getNtpConfigInfo() throws Exception {
        Class<? extends NtpManageService> aClass = ntpManageService.getClass();
        Field dateFormatTemp = aClass.getDeclaredField("dateFormatTemp");
        dateFormatTemp.setAccessible(true);
        dateFormatTemp.set(ntpManageService, "123");
        Mockito.when(ntpManageMapper.selectList(Mockito.any())).thenReturn(null);
        GetNtpConfigInfoRespModel ntpConfigInfo = ntpManageService.getNtpConfigInfo();
        Assertions.assertNotNull(ntpConfigInfo);
        Mockito.when(ntpManageMapper.selectList(Mockito.any())).thenReturn(Collections.singletonList(new InspNtpManageInfo()));
        ntpConfigInfo = ntpManageService.getNtpConfigInfo();
        Assertions.assertNotNull(ntpConfigInfo);
    }

    @Test
    void saveNtpDateFromNtp() {
        InspNtpManageInfo inspNtpManageInfo = new InspNtpManageInfo();
        try {
            ntpManageService.saveNtpDateFromNtp(inspNtpManageInfo);
        } catch (Exception e) {
            Assertions.assertTrue(e instanceof InspectionException);
        }

        try {
            inspNtpManageInfo.setNtpServerHost("80");
            ntpManageService.saveNtpDateFromNtp(inspNtpManageInfo);
        } catch (Exception e) {
            Assertions.assertTrue(e instanceof InspectionException);
        }

        try {
            inspNtpManageInfo.setNtpServerPort(80);
            ntpManageService.saveNtpDateFromNtp(inspNtpManageInfo);
        } catch (Exception e) {
            Assertions.assertTrue(e instanceof InspectionException);
        }
        inspNtpManageInfo.setInterval(1);
        inspNtpManageInfo.setAvailable(true);
        Mockito.when(ntpManageMapper.delete(Mockito.any())).thenReturn(1);
        Mockito.when(ntpManageMapper.selectList(Mockito.any())).thenReturn(Collections.singletonList(new InspNtpManageInfo()));
        ntpManageService.saveNtpDateFromNtp(inspNtpManageInfo);


    }
}
