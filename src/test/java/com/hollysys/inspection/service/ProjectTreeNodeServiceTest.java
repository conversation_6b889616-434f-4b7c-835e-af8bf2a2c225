package com.hollysys.inspection.service;

import cn.hutool.http.HttpRequest;
import com.hollysys.inspection.constants.project.OnlineState;
import com.hollysys.inspection.entity.InspChannelInfo;
import com.hollysys.inspection.entity.InspProject;
import com.hollysys.inspection.entity.InspProjectTreeNode;
import com.hollysys.inspection.mapper.ProjectTreeNodeMapper;
import com.hollysys.inspection.model.tree.InspProjectTreeNodeModel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import java.util.ArrayList;
import java.util.Collections;

class ProjectTreeNodeServiceTest {

    @Spy
    @InjectMocks
    ProjectTreeNodeService projectTreeNodeService;
    @Mock
    ProjectTreeNodeMapper projectTreeNodeMapper;
    @Mock
    InspProjectService projectService;
    @Mock
    InspChannelInfoService inspChannelInfoService;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getTree() {
        InspProjectTreeNode treeNode = new InspProjectTreeNode();
        treeNode.setParentId("123");
        treeNode.setLabel("123");
        treeNode.setId("123");
        treeNode.setUrl("123");
        treeNode.setDes("123");
        treeNode.setSortNo(1L);
        Mockito.when(projectTreeNodeMapper.selectList(Mockito.any())).thenReturn(Collections.singletonList(treeNode));
        InspProject project = new InspProject();
        project.setOnlineState(OnlineState.OFFLINE.name());
        Mockito.when(projectService.getById(Mockito.any())).thenReturn(project);
        Mockito.when(projectTreeNodeMapper.selectList(Mockito.any())).thenReturn(new ArrayList<>());

    }

    @Test
    void getById() {
    }


    @Test
    void listByProjectId() {
        Mockito.when(projectTreeNodeMapper.selectList(Mockito.any())).thenReturn(new ArrayList<>());
    }

    @Test
    void getControlUrl() {
        InspProjectTreeNodeModel inspProjectTreeNodeModel = new InspProjectTreeNodeModel();
        inspProjectTreeNodeModel.setUrl("123");
        Mockito.when(projectTreeNodeMapper.selectOne(Mockito.any())).thenReturn(inspProjectTreeNodeModel);
        //projectTreeNodeService.getControlUrl();
        Mockito.when(projectTreeNodeMapper.selectOne(Mockito.any())).thenReturn(null);
        //projectTreeNodeService.getControlUrl();
    }

    @Test
    void freshChannelNodeState() {
        InspProjectTreeNodeModel inspProjectTreeNodeModel = new InspProjectTreeNodeModel();
        inspProjectTreeNodeModel.setId("123");
        Mockito.when(projectTreeNodeMapper.selectList(Mockito.any())).thenReturn(Collections.singletonList(inspProjectTreeNodeModel));
        InspChannelInfo inspChannelInfo = new InspChannelInfo();
        inspChannelInfo.setAddress("127.0.0.1");
        Mockito.when(inspChannelInfoService.list(Mockito.any())).thenReturn(Collections.singletonList(inspChannelInfo));
        Mockito.when(projectTreeNodeMapper.updateById(Mockito.any())).thenReturn(1);
        projectTreeNodeService.freshChannelNodeState();
        inspChannelInfo.setAddress("rtsp://");
        projectTreeNodeService.freshChannelNodeState();
        inspChannelInfo.setUsername("123");
        inspChannelInfo.setPassword("123");
        inspChannelInfo.setAddress("127.0.0.1");
        Mockito.when(inspChannelInfoService.list(Mockito.any())).thenReturn(Collections.singletonList(inspChannelInfo));
        try (MockedStatic<HttpRequest> httpRequestMockedStatic = Mockito.mockStatic(HttpRequest.class);) {
            httpRequestMockedStatic.when(() -> {
                HttpRequest.head("123").timeout(3000).keepAlive(true).execute();
            }).thenCallRealMethod();
            projectTreeNodeService.freshChannelNodeState();
        } catch (Exception e) {

        }
    }
}
