package com.hollysys.inspection.service;

import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.entity.InspThirdInterface;
import com.hollysys.inspection.entity.InspThirdPermission;
import com.hollysys.inspection.mapper.InspThirdPermissionMapper;
import com.hollysys.inspection.model.InspThirdAppModel;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import java.util.Collections;
import java.util.List;

class InspThirdPermissionServiceTest {
    @Spy
    @InjectMocks
    InspThirdPermissionService inspThirdPermissionService;

    @Mock
    private InspThirdAppService thirdAppService;
    @Mock
    InspThirdPermissionMapper inspThirdPermissionMapper;

    @Mock
    private InspThirdInterfaceService thirdInterfaceService;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void removeByAppIds() {
        Mockito.when(inspThirdPermissionMapper.delete(Mockito.any())).thenReturn(1);
        inspThirdPermissionService.removeByAppIds(Collections.singletonList("123"));

    }

    @Test
    void listByAppId() {
        Mockito.when(inspThirdPermissionMapper.selectList(Mockito.any())).thenReturn(null);
        List<InspThirdPermission> inspThirdPermissions = inspThirdPermissionService.listByAppId("123");
        Assertions.assertNull(inspThirdPermissions);
    }

    @Test
    void refreshPermissionCache() {
        inspThirdPermissionService.refreshPermissionCache("123");
    }

    @Test
    void checkPermission() {
        try {
            inspThirdPermissionService.checkPermission("", "0", "");
        } catch (Exception e) {
            Assertions.assertTrue(e instanceof InspectionException);
        }
        try {
            inspThirdPermissionService.checkPermission("11", "0", "");
        } catch (Exception e) {
            Assertions.assertTrue(e instanceof InspectionException);
        }

        try {
            Mockito.when(thirdAppService.getByAppCode(Mockito.any())).thenReturn(null);
            inspThirdPermissionService.checkPermission("1:1", "0", "");
        } catch (Exception e) {
            Assertions.assertTrue(e instanceof InspectionException);
        }

        try {
            Mockito.when(thirdAppService.getByAppCode(Mockito.any())).thenReturn(new InspThirdAppModel());
            inspThirdPermissionService.checkPermission("1:1", "0", "");
        } catch (Exception e) {
            Assertions.assertTrue(e instanceof InspectionException);
        }
        try {
            InspThirdAppModel inspThirdAppModel = new InspThirdAppModel();
            inspThirdAppModel.setAvailable(true);
            Mockito.when(thirdAppService.getByAppCode(Mockito.any())).thenReturn(inspThirdAppModel);
            inspThirdPermissionService.checkPermission("1:1", "0", "");
        } catch (Exception e) {
            Assertions.assertTrue(e instanceof InspectionException);
        }

        try {
            InspThirdAppModel inspThirdAppModel = new InspThirdAppModel();
            inspThirdAppModel.setAvailable(true);
            inspThirdAppModel.setLicenseCode("1");
            inspThirdAppModel.setId("123");
            Mockito.when(thirdAppService.getByAppCode(Mockito.any())).thenReturn(inspThirdAppModel);
            Mockito.when(thirdInterfaceService.listByIds(Mockito.any())).thenReturn(Collections.singletonList(new InspThirdInterface()));
            Mockito.when(inspThirdPermissionMapper.selectList(Mockito.any())).thenReturn(Collections.singletonList(new InspThirdPermission()));
            inspThirdPermissionService.checkPermission("1:1", "0", "");
        } catch (Exception e) {
            Assertions.assertTrue(e instanceof InspectionException);
        }
    }
}