package com.hollysys.inspection.service;

import com.hollysys.inspection.entity.InspAlgorithmParamInstance;
import com.hollysys.inspection.mapper.InspAlgorithmParamInstanceMapper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import java.util.Collections;
import java.util.List;

class InspAlgorithmParamInstanceServiceTest {
    @Spy
    @InjectMocks
    private InspAlgorithmParamInstanceService inspAlgorithmParamInstanceService;

    @Mock
    private InspAlgorithmParamInstanceMapper inspAlgorithmParamInstanceMapper;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void listByInstanceIdAndSceneId() {
        Mockito.when(inspAlgorithmParamInstanceMapper.selectList(Mockito.any())).thenReturn(null);
        List<InspAlgorithmParamInstance> inspAlgorithmParamInstances = inspAlgorithmParamInstanceService.listByInstanceIdAndSceneId("123", "123");
        Assertions.assertNull(inspAlgorithmParamInstances);
    }

    @Test
    void listByAlgorithmInstanceIds() {
        Mockito.when(inspAlgorithmParamInstanceMapper.selectList(Mockito.any())).thenReturn(null);
        List<InspAlgorithmParamInstance> inspAlgorithmParamInstances = inspAlgorithmParamInstanceService.listByAlgorithmInstanceIds(Collections.singletonList("123"));
        Assertions.assertNull(inspAlgorithmParamInstances);
    }
}
