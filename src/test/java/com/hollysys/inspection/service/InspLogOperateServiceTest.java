package com.hollysys.inspection.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hollysys.inspection.constants.operatelog.BusinessClassify;
import com.hollysys.inspection.constants.operatelog.OperateType;
import com.hollysys.inspection.entity.*;
import com.hollysys.inspection.mapper.InspLogOperateMapper;
import com.hollysys.inspection.model.operatelog.OperateLogPageReqModel;
import com.hollysys.inspection.model.operatelog.OperateLogPageRespModel;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import java.util.ArrayList;
import java.util.Collections;

class InspLogOperateServiceTest {

    @Spy
    @InjectMocks
    private InspLogOperateService inspLogOperateService;

    @Mock
    private InspLogOperateMapper inspLogOperateMapper;

    @Mock
    private InspSystemUserService inspSystemUserService;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void log() {
        Mockito.when(inspLogOperateMapper.insert(Mockito.any())).thenReturn(1);
        inspLogOperateService.log("", OperateType.UPDATE, BusinessClassify.PROJECT, new InspLogOperate());
    }

    @Test
    void buildMessageAndOperateIds() {
        InspProject inspProject = new InspProject();
        inspProject.setId("123");
        InspLogOperate inspLogOperate = inspLogOperateService.buildMessageAndOperateIds("", OperateType.UPDATE, BusinessClassify.PROJECT, inspProject);
        Assertions.assertNotNull(inspLogOperate);
        inspLogOperate = inspLogOperateService.buildMessageAndOperateIds("", OperateType.UPDATE, BusinessClassify.PROJECT, inspProject);
        Assertions.assertNotNull(inspLogOperate);

        InspProjectTreeNode inspProjectTreeNode = new InspProjectTreeNode();
        inspProjectTreeNode.setId("123");
        inspLogOperate = inspLogOperateService.buildMessageAndOperateIds("", OperateType.UPDATE, BusinessClassify.PROJECT_NODE, inspProjectTreeNode);
        Assertions.assertNotNull(inspLogOperate);
        inspProjectTreeNode.setLabel("123");
        inspLogOperate = inspLogOperateService.buildMessageAndOperateIds("", OperateType.UPDATE, BusinessClassify.PROJECT_NODE, inspProjectTreeNode);
        Assertions.assertNotNull(inspLogOperate);

        InspChannelInfo inspChannelInfo = new InspChannelInfo();
        inspChannelInfo.setId("123");
        inspLogOperate = inspLogOperateService.buildMessageAndOperateIds("", OperateType.UPDATE, BusinessClassify.CHANNEL, inspChannelInfo);
        Assertions.assertNotNull(inspLogOperate);
        inspChannelInfo.setAddress("123");
        inspLogOperate = inspLogOperateService.buildMessageAndOperateIds("", OperateType.UPDATE, BusinessClassify.CHANNEL, inspChannelInfo);
        Assertions.assertNotNull(inspLogOperate);
        inspLogOperate = inspLogOperateService.buildMessageAndOperateIds("", OperateType.UPDATE, BusinessClassify.NTP, inspChannelInfo);
        Assertions.assertNotNull(inspLogOperate);
        inspLogOperate = inspLogOperateService.buildMessageAndOperateIds("", OperateType.UPDATE, BusinessClassify.ALGORITHM, inspChannelInfo);
        Assertions.assertNotNull(inspLogOperate);

    }

    @Test
    void listPage() {
        Mockito.when(inspLogOperateMapper.selectPage(Mockito.any(), Mockito.any())).thenReturn(null);
        OperateLogPageReqModel reqModel = new OperateLogPageReqModel();
        reqModel.setSearchText("123");
        reqModel.setStartTime("2023-08-29 00:00:00");
        reqModel.setEndTime("2023-08-29 00:00:00");
        IPage<InspLogOperate> pageResult = new Page<>();
        pageResult.setRecords(new ArrayList<>());
        Mockito.when(inspLogOperateMapper.selectPage(Mockito.any(), Mockito.any())).thenReturn(pageResult);
        Object o = inspLogOperateService.listPage(null, reqModel);
        Assertions.assertNotNull(o);


        OperateLogPageRespModel operateLogPageRespModel = new OperateLogPageRespModel();
        operateLogPageRespModel.setOperateType(OperateType.UPDATE.name());
        operateLogPageRespModel.setBusinessType(BusinessClassify.CHANNEL.name());
        pageResult = new Page<>();
        pageResult.setRecords(Collections.singletonList(operateLogPageRespModel));
        Mockito.when(inspLogOperateMapper.selectPage(Mockito.any(), Mockito.any())).thenReturn(pageResult);
//        o = inspLogOperateService.listPage(null, reqModel);
//        Assertions.assertNotNull(o);


        Mockito.when(inspSystemUserService.getById(Mockito.any())).thenReturn(new InspSystemUser());
        o = inspLogOperateService.listPage(null, reqModel);
        Assertions.assertNotNull(o);


    }
}
