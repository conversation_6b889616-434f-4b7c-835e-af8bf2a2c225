package com.hollysys.inspection.service;

import cn.dev33.satoken.stp.StpUtil;
import com.hollysys.inspection.config.redis.RedisHelper;
import com.hollysys.inspection.model.portal.GetCaptchaRespModel;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import java.lang.reflect.Field;

class PortalServiceTest {

    @Spy
    @InjectMocks
    private PortalService portalService;

    @Mock
    private RedisHelper redisUtil;

    @Mock
    private InspSystemUserService userService;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void logout() {
        try (
                MockedStatic<StpUtil> stpUtilMockedStatic = Mockito.mockStatic(StpUtil.class)
        ) {
//            stpUtilMockedStatic.when(StpUtil::logout).thenCallRealMethod();
            boolean logout = portalService.logout();
            Assertions.assertTrue(logout);
        }

    }

    @Test
    void getCaptcha() throws NoSuchFieldException, IllegalAccessException {
        Class<? extends PortalService> aClass = portalService.getClass();
        Field captchaWidth = aClass.getDeclaredField("captchaWidth");
        captchaWidth.setAccessible(true);
        captchaWidth.set(portalService, 123);
        Field captchaHeight = aClass.getDeclaredField("captchaHeight");
        captchaHeight.setAccessible(true);
        captchaHeight.set(portalService, 123);
        Field captchaLen = aClass.getDeclaredField("captchaLen");
        captchaLen.setAccessible(true);
        captchaLen.set(portalService, 123);
//        Mockito.doNothing().when(redisUtil).setEx(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        GetCaptchaRespModel captcha = portalService.getCaptcha();
        Assertions.assertNotNull(captcha);
    }
}
