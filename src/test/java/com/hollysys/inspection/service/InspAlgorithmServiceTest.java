package com.hollysys.inspection.service;

import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.entity.InspAlgorithm;
import com.hollysys.inspection.entity.InspAlgorithmInstance;
import com.hollysys.inspection.entity.InspProjectTreeNode;
import com.hollysys.inspection.mapper.InspAlgorithmMapper;
import com.hollysys.inspection.model.algorithm.SaveAlgorithmReqModel;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.mock.web.MockMultipartFile;

import java.util.Collections;

class InspAlgorithmServiceTest {
    @Spy
    @InjectMocks
    private InspAlgorithmService inspAlgorithmService;

    @Mock
    private InspAlgorithmMapper inspAlgorithmMapper;

    @Mock
    private InspAlgorithmInstanceService algorithmInstanceService;

    @Mock
    private ProjectTreeNodeService projectTreeNodeService;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getById() {
        Mockito.when(inspAlgorithmMapper.selectById("123")).thenReturn(null);
        try {
            inspAlgorithmService.getById("123");
        } catch (Exception e) {
            Assertions.assertTrue(e instanceof InspectionException);
        }

        Mockito.when(inspAlgorithmMapper.selectById("123")).thenReturn(new InspAlgorithmInstance());
        InspAlgorithm byId = inspAlgorithmService.getById("123");
        Assertions.assertNotNull(byId);
    }

    @Test
    void updateAlgorithm() {
        SaveAlgorithmReqModel saveAlgorithmReqModel = new SaveAlgorithmReqModel();
        try {
            inspAlgorithmService.updateAlgorithm(saveAlgorithmReqModel);
        } catch (Exception e) {
            Assertions.assertTrue(e instanceof InspectionException);
        }
        try {
            saveAlgorithmReqModel.setAlgorithmId("123");
            inspAlgorithmService.updateAlgorithm(saveAlgorithmReqModel);
        } catch (Exception e) {
            Assertions.assertTrue(e instanceof InspectionException);
        }
        InspAlgorithm inspAlgorithm = new InspAlgorithmInstance();
        inspAlgorithm.setName("123");
        inspAlgorithm.setIsPreset(true);
        saveAlgorithmReqModel.setAlgorithmName("234");
        Mockito.when(inspAlgorithmMapper.selectById(Mockito.any())).thenReturn(inspAlgorithm);
        try {
            inspAlgorithmService.updateAlgorithm(saveAlgorithmReqModel);
        } catch (Exception e) {
            Assertions.assertTrue(e instanceof InspectionException);
        }
        inspAlgorithm.setIsPreset(false);
        Mockito.when(inspAlgorithmMapper.selectById(Mockito.any())).thenReturn(inspAlgorithm);
        Mockito.when(inspAlgorithmMapper.updateById(Mockito.any())).thenReturn(1);
        InspAlgorithm inspAlgorithm1 = inspAlgorithmService.updateAlgorithm(saveAlgorithmReqModel);
        Assertions.assertNotNull(inspAlgorithm1);


        Mockito.when(algorithmInstanceService.list(Mockito.any())).thenReturn(Collections.singletonList(new InspAlgorithmInstance()));
        Mockito.when(projectTreeNodeService.listByIds(Mockito.any())).thenReturn(Collections.singletonList(new InspProjectTreeNode()));
        saveAlgorithmReqModel.setAlgorithmFile(new MockMultipartFile("./1.txt", new byte[]{}));
        try {
            inspAlgorithmService.updateAlgorithm(saveAlgorithmReqModel);
        } catch (Exception e) {
            Assertions.assertTrue(e instanceof InspectionException);
        }
        saveAlgorithmReqModel.setAlgorithmFile(null);
        InspAlgorithm inspAlgorithm2 = inspAlgorithmService.updateAlgorithm(saveAlgorithmReqModel);
        Assertions.assertNotNull(inspAlgorithm2);
    }

    @Test
    void newAlgorithm() {
    }

    @Test
    void listAlgorithm() {
    }

    @Test
    void removeAlgorithm() {
    }
}