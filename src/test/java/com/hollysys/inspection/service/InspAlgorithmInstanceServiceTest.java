package com.hollysys.inspection.service;

import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.entity.InspAlgorithmInstance;
import com.hollysys.inspection.mapper.InspAlgorithmInstanceMapper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import java.util.List;

class InspAlgorithmInstanceServiceTest {

    @Spy
    @InjectMocks
    private InspAlgorithmInstanceService algorithmInstanceService;

    @Mock
    private InspAlgorithmInstanceMapper inspAlgorithmInstanceMapper;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getById() {
        Mockito.when(inspAlgorithmInstanceMapper.selectById("123")).thenReturn(null);
        try {
            algorithmInstanceService.getById("123");
        } catch (Exception e) {
            Assertions.assertTrue(e instanceof InspectionException);
        }
        Mockito.when(inspAlgorithmInstanceMapper.selectById("123")).thenReturn(new InspAlgorithmInstance());
        InspAlgorithmInstance byId = algorithmInstanceService.getById("123");
        Assertions.assertNotNull(byId);


    }

    @Test
    void listByPresetId() {
        Mockito.when(inspAlgorithmInstanceMapper.selectList(Mockito.any())).thenReturn(null);
        List<InspAlgorithmInstance> algorithmInstances = algorithmInstanceService.listByPresetId("123");
        Assertions.assertNull(algorithmInstances);
    }
}
