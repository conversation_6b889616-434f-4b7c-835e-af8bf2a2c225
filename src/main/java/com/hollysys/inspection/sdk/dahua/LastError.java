package com.hollysys.inspection.sdk.dahua;

/**
 * 错误类型代号，对应CLIENT_GetLastError接口的返回值, 十进制
 *
 * <AUTHOR>
 */
public class LastError {
    public static final int NET_NOERROR = 0;               // 没有错误
    public static final int NET_ERROR = -1;               // 未知错误
    public static final int NET_SYSTEM_ERROR = (0x80000000 | 1);   // Windows系统出错
    public static final int NET_NETWORK_ERROR = (0x80000000 | 2);   // 网络错误，可能是因为网络超时
    public static final int NET_DEV_VER_NOMATCH = (0x80000000 | 3);   // 设备协议不匹配
    public static final int NET_INVALID_HANDLE = (0x80000000 | 4);   // 句柄无效
    public static final int NET_OPEN_CHANNEL_ERROR = (0x80000000 | 5);   // 打开通道失败
    public static final int NET_CLOSE_CHANNEL_ERROR = (0x80000000 | 6);   // 关闭通道失败
    public static final int NET_ILLEGAL_PARAM = (0x80000000 | 7);   // 用户参数不合法
    public static final int NET_SDK_INIT_ERROR = (0x80000000 | 8);   // SDK初始化出错
    public static final int NET_SDK_UNINIT_ERROR = (0x80000000 | 9);   // SDK清理出错
    public static final int NET_RENDER_OPEN_ERROR = (0x80000000 | 10);  // 申请render资源出错
    public static final int NET_DEC_OPEN_ERROR = (0x80000000 | 11);  // 打开解码库出错
    public static final int NET_DEC_CLOSE_ERROR = (0x80000000 | 12);  // 关闭解码库出错
    public static final int NET_MULTIPLAY_NOCHANNEL = (0x80000000 | 13);  // 多画面预览中检测到通道数为0
    public static final int NET_TALK_INIT_ERROR = (0x80000000 | 14);  // 录音库初始化失败
    public static final int NET_TALK_NOT_INIT = (0x80000000 | 15);  // 录音库未经初始化
    public static final int NET_TALK_SENDDATA_ERROR = (0x80000000 | 16);  // 发送音频数据出错
    public static final int NET_REAL_ALREADY_SAVING = (0x80000000 | 17);  // 实时数据已经处于保存状态
    public static final int NET_NOT_SAVING = (0x80000000 | 18);  // 未保存实时数据
    public static final int NET_OPEN_FILE_ERROR = (0x80000000 | 19);  // 打开文件出错
    public static final int NET_PTZ_SET_TIMER_ERROR = (0x80000000 | 20);  // 启动云台控制定时器失败
    public static final int NET_RETURN_DATA_ERROR = (0x80000000 | 21);  // 对返回数据的校验出错
    public static final int NET_INSUFFICIENT_BUFFER = (0x80000000 | 22);  // 没有足够的缓存
    public static final int NET_NOT_SUPPORTED = (0x80000000 | 23);  // 当前SDK未支持该功能
    public static final int NET_NO_RECORD_FOUND = (0x80000000 | 24);  // 查询不到录像
    public static final int NET_NOT_AUTHORIZED = (0x80000000 | 25);  // 无操作权限
    public static final int NET_NOT_NOW = (0x80000000 | 26);  // 暂时无法执行
    public static final int NET_NO_TALK_CHANNEL = (0x80000000 | 27);  // 未发现对讲通道
    public static final int NET_NO_AUDIO = (0x80000000 | 28);  // 未发现音频
    public static final int NET_NO_INIT = (0x80000000 | 29);  // 网络SDK未经初始化
    public static final int NET_DOWNLOAD_END = (0x80000000 | 30);  // 下载已结束
    public static final int NET_EMPTY_LIST = (0x80000000 | 31);  // 查询结果为空
    public static final int NET_ERROR_GETCFG_SYSATTR = (0x80000000 | 32);  // 获取系统属性配置失败
    public static final int NET_ERROR_GETCFG_SERIAL = (0x80000000 | 33);  // 获取序列号失败
    public static final int NET_ERROR_GETCFG_GENERAL = (0x80000000 | 34);  // 获取常规属性失败
    public static final int NET_ERROR_GETCFG_DSPCAP = (0x80000000 | 35);  // 获取DSP能力描述失败
    public static final int NET_ERROR_GETCFG_NETCFG = (0x80000000 | 36);  // 获取网络配置失败
    public static final int NET_ERROR_GETCFG_CHANNAME = (0x80000000 | 37);  // 获取通道名称失败
    public static final int NET_ERROR_GETCFG_VIDEO = (0x80000000 | 38);  // 获取视频属性失败
    public static final int NET_ERROR_GETCFG_RECORD = (0x80000000 | 39);  // 获取录象配置失败
    public static final int NET_ERROR_GETCFG_PRONAME = (0x80000000 | 40);  // 获取解码器协议名称失败
    public static final int NET_ERROR_GETCFG_FUNCNAME = (0x80000000 | 41);  // 获取232串口功能名称失败
    public static final int NET_ERROR_GETCFG_485DECODER = (0x80000000 | 42);  // 获取解码器属性失败
    public static final int NET_ERROR_GETCFG_232COM = (0x80000000 | 43);  // 获取232串口配置失败
    public static final int NET_ERROR_GETCFG_ALARMIN = (0x80000000 | 44);  // 获取外部报警输入配置失败
    public static final int NET_ERROR_GETCFG_ALARMDET = (0x80000000 | 45);  // 获取动态检测报警失败
    public static final int NET_ERROR_GETCFG_SYSTIME = (0x80000000 | 46);  // 获取设备时间失败
    public static final int NET_ERROR_GETCFG_PREVIEW = (0x80000000 | 47);  // 获取预览参数失败
    public static final int NET_ERROR_GETCFG_AUTOMT = (0x80000000 | 48);  // 获取自动维护配置失败
    public static final int NET_ERROR_GETCFG_VIDEOMTRX = (0x80000000 | 49);  // 获取视频矩阵配置失败
    public static final int NET_ERROR_GETCFG_COVER = (0x80000000 | 50);  // 获取区域遮挡配置失败
    public static final int NET_ERROR_GETCFG_WATERMAKE = (0x80000000 | 51);  // 获取图象水印配置失败
    public static final int NET_ERROR_GETCFG_MULTICAST = (0x80000000 | 52);  // 获取配置失败位置：组播端口按通道配置
    public static final int NET_ERROR_SETCFG_GENERAL = (0x80000000 | 55);  // 修改常规属性失败
    public static final int NET_ERROR_SETCFG_NETCFG = (0x80000000 | 56);  // 修改网络配置失败
    public static final int NET_ERROR_SETCFG_CHANNAME = (0x80000000 | 57);  // 修改通道名称失败
    public static final int NET_ERROR_SETCFG_VIDEO = (0x80000000 | 58);  // 修改视频属性失败
    public static final int NET_ERROR_SETCFG_RECORD = (0x80000000 | 59);  // 修改录象配置失败
    public static final int NET_ERROR_SETCFG_485DECODER = (0x80000000 | 60);  // 修改解码器属性失败
    public static final int NET_ERROR_SETCFG_232COM = (0x80000000 | 61);  // 修改232串口配置失败
    public static final int NET_ERROR_SETCFG_ALARMIN = (0x80000000 | 62);  // 修改外部输入报警配置失败
    public static final int NET_ERROR_SETCFG_ALARMDET = (0x80000000 | 63);  // 修改动态检测报警配置失败
    public static final int NET_ERROR_SETCFG_SYSTIME = (0x80000000 | 64);  // 修改设备时间失败
    public static final int NET_ERROR_SETCFG_PREVIEW = (0x80000000 | 65);  // 修改预览参数失败
    public static final int NET_ERROR_SETCFG_AUTOMT = (0x80000000 | 66);  // 修改自动维护配置失败
    public static final int NET_ERROR_SETCFG_VIDEOMTRX = (0x80000000 | 67);  // 修改视频矩阵配置失败
    public static final int NET_ERROR_SETCFG_COVER = (0x80000000 | 68);  // 修改区域遮挡配置失败
    public static final int NET_ERROR_SETCFG_WATERMAKE = (0x80000000 | 69);  // 修改图象水印配置失败
    public static final int NET_ERROR_SETCFG_WLAN = (0x80000000 | 70);  // 修改无线网络信息失败
    public static final int NET_ERROR_SETCFG_WLANDEV = (0x80000000 | 71);  // 选择无线网络设备失败
    public static final int NET_ERROR_SETCFG_REGISTER = (0x80000000 | 72);  // 修改主动注册参数配置失败
    public static final int NET_ERROR_SETCFG_CAMERA = (0x80000000 | 73);  // 修改摄像头属性配置失败
    public static final int NET_ERROR_SETCFG_INFRARED = (0x80000000 | 74);  // 修改红外报警配置失败
    public static final int NET_ERROR_SETCFG_SOUNDALARM = (0x80000000 | 75);  // 修改音频报警配置失败
    public static final int NET_ERROR_SETCFG_STORAGE = (0x80000000 | 76);  // 修改存储位置配置失败
    public static final int NET_AUDIOENCODE_NOTINIT = (0x80000000 | 77);  // 音频编码接口没有成功初始化
    public static final int NET_DATA_TOOLONGH = (0x80000000 | 78);  // 数据过长
    public static final int NET_UNSUPPORTED = (0x80000000 | 79);  // 设备不支持该操作
    public static final int NET_DEVICE_BUSY = (0x80000000 | 80);  // 设备资源不足
    public static final int NET_SERVER_STARTED = (0x80000000 | 81);  // 服务器已经启动
    public static final int NET_SERVER_STOPPED = (0x80000000 | 82);  // 服务器尚未成功启动
    public static final int NET_LISTER_INCORRECT_SERIAL = (0x80000000 | 83);  // 输入序列号有误
    public static final int NET_QUERY_DISKINFO_FAILED = (0x80000000 | 84);  // 获取硬盘信息失败
    public static final int NET_ERROR_GETCFG_SESSION = (0x80000000 | 85);  // 获取连接Session信息
    public static final int NET_USER_FLASEPWD_TRYTIME = (0x80000000 | 86);  // 输入密码错误超过限制次数
    public static final int NET_LOGIN_ERROR_PASSWORD = (0x80000000 | 100); // 密码不正确
    public static final int NET_LOGIN_ERROR_USER = (0x80000000 | 101); // 帐户不存在
    public static final int NET_LOGIN_ERROR_TIMEOUT = (0x80000000 | 102); // 等待登录返回超时
    public static final int NET_LOGIN_ERROR_RELOGGIN = (0x80000000 | 103); // 帐号已登录
    public static final int NET_LOGIN_ERROR_LOCKED = (0x80000000 | 104); // 帐号已被锁定
    public static final int NET_LOGIN_ERROR_BLACKLIST = (0x80000000 | 105); // 帐号已被列为禁止名单
    public static final int NET_LOGIN_ERROR_BUSY = (0x80000000 | 106); // 资源不足，系统忙
    public static final int NET_LOGIN_ERROR_CONNECT = (0x80000000 | 107); // 登录设备超时，请检查网络并重试
    public static final int NET_LOGIN_ERROR_NETWORK = (0x80000000 | 108); // 网络连接失败
    public static final int NET_LOGIN_ERROR_SUBCONNECT = (0x80000000 | 109); // 登录设备成功，但无法创建视频通道，请检查网络状况
    public static final int NET_LOGIN_ERROR_MAXCONNECT = (0x80000000 | 110); // 超过最大连接数
    public static final int NET_LOGIN_ERROR_PROTOCOL3_ONLY = (0x80000000 | 111); // 只支持3代协议
    public static final int NET_LOGIN_ERROR_UKEY_LOST = (0x80000000 | 112); // 未插入U盾或U盾信息错误
    public static final int NET_LOGIN_ERROR_NO_AUTHORIZED = (0x80000000 | 113); // 客户端IP地址没有登录权限
    public static final int NET_LOGIN_ERROR_USER_OR_PASSOWRD = (0x80000000 | 117); // 账号或密码错误
    public static final int NET_LOGIN_ERROR_DEVICE_NOT_INIT = (0x80000000 | 118);    // 设备尚未初始化，不能登录，请先初始化设备
    public static final int NET_RENDER_SOUND_ON_ERROR = (0x80000000 | 120); // Render库打开音频出错
    public static final int NET_RENDER_SOUND_OFF_ERROR = (0x80000000 | 121); // Render库关闭音频出错
    public static final int NET_RENDER_SET_VOLUME_ERROR = (0x80000000 | 122); // Render库控制音量出错
    public static final int NET_RENDER_ADJUST_ERROR = (0x80000000 | 123); // Render库设置画面参数出错
    public static final int NET_RENDER_PAUSE_ERROR = (0x80000000 | 124); // Render库暂停播放出错
    public static final int NET_RENDER_SNAP_ERROR = (0x80000000 | 125); // Render库抓图出错
    public static final int NET_RENDER_STEP_ERROR = (0x80000000 | 126); // Render库步进出错
    public static final int NET_RENDER_FRAMERATE_ERROR = (0x80000000 | 127); // Render库设置帧率出错
    public static final int NET_RENDER_DISPLAYREGION_ERROR = (0x80000000 | 128); // Render库设置显示区域出错
    public static final int NET_RENDER_GETOSDTIME_ERROR = (0x80000000 | 129); // Render库获取当前播放时间出错
    public static final int NET_GROUP_EXIST = (0x80000000 | 140); // 组名已存在
    public static final int NET_GROUP_NOEXIST = (0x80000000 | 141); // 组名不存在
    public static final int NET_GROUP_RIGHTOVER = (0x80000000 | 142); // 组的权限超出权限列表范围
    public static final int NET_GROUP_HAVEUSER = (0x80000000 | 143); // 组下有用户，不能删除
    public static final int NET_GROUP_RIGHTUSE = (0x80000000 | 144); // 组的某个权限被用户使用，不能删除
    public static final int NET_GROUP_SAMENAME = (0x80000000 | 145); // 新组名同已有组名重复
    public static final int NET_USER_EXIST = (0x80000000 | 146); // 用户已存在
    public static final int NET_USER_NOEXIST = (0x80000000 | 147); // 用户不存在
    public static final int NET_USER_RIGHTOVER = (0x80000000 | 148); // 用户权限超出组权限
    public static final int NET_USER_PWD = (0x80000000 | 149); // 保留帐号，不容许修改密码
    public static final int NET_USER_FLASEPWD = (0x80000000 | 150); // 密码不正确
    public static final int NET_USER_NOMATCHING = (0x80000000 | 151); // 密码不匹配
    public static final int NET_USER_INUSE = (0x80000000 | 152); // 账号正在使用中
    public static final int NET_ERROR_GETCFG_ETHERNET = (0x80000000 | 300); // 获取网卡配置失败
    public static final int NET_ERROR_GETCFG_WLAN = (0x80000000 | 301); // 获取无线网络信息失败
    public static final int NET_ERROR_GETCFG_WLANDEV = (0x80000000 | 302); // 获取无线网络设备失败
    public static final int NET_ERROR_GETCFG_REGISTER = (0x80000000 | 303); // 获取主动注册参数失败
    public static final int NET_ERROR_GETCFG_CAMERA = (0x80000000 | 304); // 获取摄像头属性失败
    public static final int NET_ERROR_GETCFG_INFRARED = (0x80000000 | 305); // 获取红外报警配置失败
    public static final int NET_ERROR_GETCFG_SOUNDALARM = (0x80000000 | 306); // 获取音频报警配置失败
    public static final int NET_ERROR_GETCFG_STORAGE = (0x80000000 | 307); // 获取存储位置配置失败
    public static final int NET_ERROR_GETCFG_MAIL = (0x80000000 | 308); // 获取邮件配置失败
    public static final int NET_CONFIG_DEVBUSY = (0x80000000 | 309); // 暂时无法设置
    public static final int NET_CONFIG_DATAILLEGAL = (0x80000000 | 310); // 配置数据不合法
    public static final int NET_ERROR_GETCFG_DST = (0x80000000 | 311); // 获取夏令时配置失败
    public static final int NET_ERROR_SETCFG_DST = (0x80000000 | 312); // 设置夏令时配置失败
    public static final int NET_ERROR_GETCFG_VIDEO_OSD = (0x80000000 | 313); // 获取视频OSD叠加配置失败
    public static final int NET_ERROR_SETCFG_VIDEO_OSD = (0x80000000 | 314); // 设置视频OSD叠加配置失败
    public static final int NET_ERROR_GETCFG_GPRSCDMA = (0x80000000 | 315); // 获取CDMA\GPRS网络配置失败
    public static final int NET_ERROR_SETCFG_GPRSCDMA = (0x80000000 | 316); // 设置CDMA\GPRS网络配置失败
    public static final int NET_ERROR_GETCFG_IPFILTER = (0x80000000 | 317); // 获取IP过滤配置失败
    public static final int NET_ERROR_SETCFG_IPFILTER = (0x80000000 | 318); // 设置IP过滤配置失败
    public static final int NET_ERROR_GETCFG_TALKENCODE = (0x80000000 | 319); // 获取语音对讲编码配置失败
    public static final int NET_ERROR_SETCFG_TALKENCODE = (0x80000000 | 320); // 设置语音对讲编码配置失败
    public static final int NET_ERROR_GETCFG_RECORDLEN = (0x80000000 | 321); // 获取录像打包长度配置失败
    public static final int NET_ERROR_SETCFG_RECORDLEN = (0x80000000 | 322); // 设置录像打包长度配置失败
    public static final int NET_DONT_SUPPORT_SUBAREA = (0x80000000 | 323); // 不支持网络硬盘分区
    public static final int NET_ERROR_GET_AUTOREGSERVER = (0x80000000 | 324); // 获取设备上主动注册服务器信息失败
    public static final int NET_ERROR_CONTROL_AUTOREGISTER = (0x80000000 | 325); // 主动注册重定向注册错误
    public static final int NET_ERROR_DISCONNECT_AUTOREGISTER = (0x80000000 | 326); // 断开主动注册服务器错误
    public static final int NET_ERROR_GETCFG_MMS = (0x80000000 | 327); // 获取mms配置失败
    public static final int NET_ERROR_SETCFG_MMS = (0x80000000 | 328); // 设置mms配置失败
    public static final int NET_ERROR_GETCFG_SMSACTIVATION = (0x80000000 | 329); // 获取短信激活无线连接配置失败
    public static final int NET_ERROR_SETCFG_SMSACTIVATION = (0x80000000 | 330); // 设置短信激活无线连接配置失败
    public static final int NET_ERROR_GETCFG_DIALINACTIVATION = (0x80000000 | 331); // 获取拨号激活无线连接配置失败
    public static final int NET_ERROR_SETCFG_DIALINACTIVATION = (0x80000000 | 332); // 设置拨号激活无线连接配置失败
    public static final int NET_ERROR_GETCFG_VIDEOOUT = (0x80000000 | 333); // 查询视频输出参数配置失败
    public static final int NET_ERROR_SETCFG_VIDEOOUT = (0x80000000 | 334); // 设置视频输出参数配置失败
    public static final int NET_ERROR_GETCFG_OSDENABLE = (0x80000000 | 335); // 获取osd叠加使能配置失败
    public static final int NET_ERROR_SETCFG_OSDENABLE = (0x80000000 | 336); // 设置osd叠加使能配置失败
    public static final int NET_ERROR_SETCFG_ENCODERINFO = (0x80000000 | 337); // 设置数字通道前端编码接入配置失败
    public static final int NET_ERROR_GETCFG_TVADJUST = (0x80000000 | 338); // 获取TV调节配置失败
    public static final int NET_ERROR_SETCFG_TVADJUST = (0x80000000 | 339); // 设置TV调节配置失败
    public static final int NET_ERROR_CONNECT_FAILED = (0x80000000 | 340); // 请求建立连接失败
    public static final int NET_ERROR_SETCFG_BURNFILE = (0x80000000 | 341); // 请求刻录文件上传失败
    public static final int NET_ERROR_SNIFFER_GETCFG = (0x80000000 | 342); // 获取抓包配置信息失败
    public static final int NET_ERROR_SNIFFER_SETCFG = (0x80000000 | 343); // 设置抓包配置信息失败
    public static final int NET_ERROR_DOWNLOADRATE_GETCFG = (0x80000000 | 344); // 查询下载限制信息失败
    public static final int NET_ERROR_DOWNLOADRATE_SETCFG = (0x80000000 | 345); // 设置下载限制信息失败
    public static final int NET_ERROR_SEARCH_TRANSCOM = (0x80000000 | 346); // 查询串口参数失败
    public static final int NET_ERROR_GETCFG_POINT = (0x80000000 | 347); // 获取预制点信息错误
    public static final int NET_ERROR_SETCFG_POINT = (0x80000000 | 348); // 设置预制点信息错误
    public static final int NET_SDK_LOGOUT_ERROR = (0x80000000 | 349); // SDK没有正常登出设备
    public static final int NET_ERROR_GET_VEHICLE_CFG = (0x80000000 | 350); // 获取车载配置失败
    public static final int NET_ERROR_SET_VEHICLE_CFG = (0x80000000 | 351); // 设置车载配置失败
    public static final int NET_ERROR_GET_ATM_OVERLAY_CFG = (0x80000000 | 352); // 获取atm叠加配置失败
    public static final int NET_ERROR_SET_ATM_OVERLAY_CFG = (0x80000000 | 353); // 设置atm叠加配置失败
    public static final int NET_ERROR_GET_ATM_OVERLAY_ABILITY = (0x80000000 | 354); // 获取atm叠加能力失败
    public static final int NET_ERROR_GET_DECODER_TOUR_CFG = (0x80000000 | 355); // 获取解码器解码轮巡配置失败
    public static final int NET_ERROR_SET_DECODER_TOUR_CFG = (0x80000000 | 356); // 设置解码器解码轮巡配置失败
    public static final int NET_ERROR_CTRL_DECODER_TOUR = (0x80000000 | 357); // 控制解码器解码轮巡失败
    public static final int NET_GROUP_OVERSUPPORTNUM = (0x80000000 | 358); // 超出设备支持最大用户组数目
    public static final int NET_USER_OVERSUPPORTNUM = (0x80000000 | 359); // 超出设备支持最大用户数目
    public static final int NET_ERROR_GET_SIP_CFG = (0x80000000 | 368); // 获取SIP配置失败
    public static final int NET_ERROR_SET_SIP_CFG = (0x80000000 | 369); // 设置SIP配置失败
    public static final int NET_ERROR_GET_SIP_ABILITY = (0x80000000 | 370); // 获取SIP能力失败
    public static final int NET_ERROR_GET_WIFI_AP_CFG = (0x80000000 | 371); // 获取WIFI ap配置失败
    public static final int NET_ERROR_SET_WIFI_AP_CFG = (0x80000000 | 372); // 设置WIFI ap配置失败
    public static final int NET_ERROR_GET_DECODE_POLICY = (0x80000000 | 373); // 获取解码策略配置失败
    public static final int NET_ERROR_SET_DECODE_POLICY = (0x80000000 | 374); // 设置解码策略配置失败
    public static final int NET_ERROR_TALK_REJECT = (0x80000000 | 375); // 拒绝对讲
    public static final int NET_ERROR_TALK_OPENED = (0x80000000 | 376); // 对讲被其他客户端打开
    public static final int NET_ERROR_TALK_RESOURCE_CONFLICIT = (0x80000000 | 377); // 资源冲突
    public static final int NET_ERROR_TALK_UNSUPPORTED_ENCODE = (0x80000000 | 378); // 不支持的语音编码格式
    public static final int NET_ERROR_TALK_RIGHTLESS = (0x80000000 | 379); // 无权限
    public static final int NET_ERROR_TALK_FAILED = (0x80000000 | 380); // 请求对讲失败
    public static final int NET_ERROR_GET_MACHINE_CFG = (0x80000000 | 381); // 获取机器相关配置失败
    public static final int NET_ERROR_SET_MACHINE_CFG = (0x80000000 | 382); // 设置机器相关配置失败
    public static final int NET_ERROR_GET_DATA_FAILED = (0x80000000 | 383); // 设备无法获取当前请求数据
    public static final int NET_ERROR_MAC_VALIDATE_FAILED = (0x80000000 | 384); // MAC地址验证失败
    public static final int NET_ERROR_GET_INSTANCE = (0x80000000 | 385); // 获取服务器实例失败
    public static final int NET_ERROR_JSON_REQUEST = (0x80000000 | 386); // 生成的json字符串错误
    public static final int NET_ERROR_JSON_RESPONSE = (0x80000000 | 387); // 响应的json字符串错误
    public static final int NET_ERROR_VERSION_HIGHER = (0x80000000 | 388); // 协议版本低于当前使用的版本
    public static final int NET_SPARE_NO_CAPACITY = (0x80000000 | 389); // 设备操作失败, 容量不足
    public static final int NET_ERROR_SOURCE_IN_USE = (0x80000000 | 390); // 显示源被其他输出占用
    public static final int NET_ERROR_REAVE = (0x80000000 | 391); // 高级用户抢占低级用户资源
    public static final int NET_ERROR_NETFORBID = (0x80000000 | 392); // 禁止入网
    public static final int NET_ERROR_GETCFG_MACFILTER = (0x80000000 | 393); // 获取MAC过滤配置失败
    public static final int NET_ERROR_SETCFG_MACFILTER = (0x80000000 | 394); // 设置MAC过滤配置失败
    public static final int NET_ERROR_GETCFG_IPMACFILTER = (0x80000000 | 395); // 获取IP/MAC过滤配置失败
    public static final int NET_ERROR_SETCFG_IPMACFILTER = (0x80000000 | 396); // 设置IP/MAC过滤配置失败
    public static final int NET_ERROR_OPERATION_OVERTIME = (0x80000000 | 397); // 当前操作超时
    public static final int NET_ERROR_SENIOR_VALIDATE_FAILED = (0x80000000 | 398); // 高级校验失败
    public static final int NET_ERROR_DEVICE_ID_NOT_EXIST = (0x80000000 | 399); // 设备ID不存在
    public static final int NET_ERROR_UNSUPPORTED = (0x80000000 | 400); // 不支持当前操作
    public static final int NET_ERROR_PROXY_DLLLOAD = (0x80000000 | 401); // 代理库加载失败
    public static final int NET_ERROR_PROXY_ILLEGAL_PARAM = (0x80000000 | 402); // 代理用户参数不合法
    public static final int NET_ERROR_PROXY_INVALID_HANDLE = (0x80000000 | 403); // 代理句柄无效
    public static final int NET_ERROR_PROXY_LOGIN_DEVICE_ERROR = (0x80000000 | 404); // 代理登入前端设备失败
    public static final int NET_ERROR_PROXY_START_SERVER_ERROR = (0x80000000 | 405); // 启动代理服务失败
    public static final int NET_ERROR_SPEAK_FAILED = (0x80000000 | 406); // 请求喊话失败
    public static final int NET_ERROR_NOT_SUPPORT_F6 = (0x80000000 | 407); // 设备不支持此F6接口调用
    public static final int NET_ERROR_CD_UNREADY = (0x80000000 | 408); // 光盘未就绪
    public static final int NET_ERROR_DIR_NOT_EXIST = (0x80000000 | 409); // 目录不存在
    public static final int NET_ERROR_UNSUPPORTED_SPLIT_MODE = (0x80000000 | 410); // 设备不支持的分割模式
    public static final int NET_ERROR_OPEN_WND_PARAM = (0x80000000 | 411); // 开窗参数不合法
    public static final int NET_ERROR_LIMITED_WND_COUNT = (0x80000000 | 412); // 开窗数量超过限制
    public static final int NET_ERROR_UNMATCHED_REQUEST = (0x80000000 | 413); // 请求命令与当前模式不匹配
    public static final int NET_RENDER_ENABLELARGEPICADJUSTMENT_ERROR = (0x80000000 | 414); // Render库启用高清图像内部调整策略出错
    public static final int NET_ERROR_UPGRADE_FAILED = (0x80000000 | 415); // 设备升级失败
    public static final int NET_ERROR_NO_TARGET_DEVICE = (0x80000000 | 416); // 找不到目标设备
    public static final int NET_ERROR_NO_VERIFY_DEVICE = (0x80000000 | 417); // 找不到验证设备
    public static final int NET_ERROR_CASCADE_RIGHTLESS = (0x80000000 | 418); // 无级联权限
    public static final int NET_ERROR_LOW_PRIORITY = (0x80000000 | 419); // 低优先级
    public static final int NET_ERROR_REMOTE_REQUEST_TIMEOUT = (0x80000000 | 420); // 远程设备请求超时
    public static final int NET_ERROR_LIMITED_INPUT_SOURCE = (0x80000000 | 421); // 输入源超出最大路数限制
    public static final int NET_ERROR_SET_LOG_PRINT_INFO = (0x80000000 | 422); // 设置日志打印失败
    public static final int NET_ERROR_PARAM_DWSIZE_ERROR = (0x80000000 | 423); // 入参的dwsize字段出错
    public static final int NET_ERROR_LIMITED_MONITORWALL_COUNT = (0x80000000 | 424); // 电视墙数量超过上限
    public static final int NET_ERROR_PART_PROCESS_FAILED = (0x80000000 | 425); // 部分过程执行失败
    public static final int NET_ERROR_TARGET_NOT_SUPPORT = (0x80000000 | 426); // 该功能不支持转发
    public static final int NET_ERROR_VISITE_FILE = (0x80000000 | 510); // 访问文件失败
    public static final int NET_ERROR_DEVICE_STATUS_BUSY = (0x80000000 | 511); // 设备忙
    public static final int NET_USER_PWD_NOT_AUTHORIZED = (0x80000000 | 512); // 修改密码无权限
    public static final int NET_USER_PWD_NOT_STRONG = (0x80000000 | 513); // 密码强度不够
    public static final int NET_ERROR_NO_SUCH_CONFIG = (0x80000000 | 514); // 没有对应的配置
    public static final int NET_ERROR_AUDIO_RECORD_FAILED = (0x80000000 | 515); // 录音失败
    public static final int NET_ERROR_SEND_DATA_FAILED = (0x80000000 | 516); // 数据发送失败
    public static final int NET_ERROR_OBSOLESCENT_INTERFACE = (0x80000000 | 517); // 废弃接口
    public static final int NET_ERROR_INSUFFICIENT_INTERAL_BUF = (0x80000000 | 518); // 内部缓冲不足
    public static final int NET_ERROR_NEED_ENCRYPTION_PASSWORD = (0x80000000 | 519); // 修改设备ip时,需要校验密码
    public static final int NET_ERROR_NOSUPPORT_RECORD = (0x80000000 | 520); // 设备不支持此记录集
    public static final int NET_ERROR_ANALYSE_TASK_FULL = (0x80000000 | 523); // 智能分析任务已经满
    public static final int NET_ERROR_SERIALIZE_ERROR = (0x80000000 | 1010);// 数据序列化错误
    public static final int NET_ERROR_DESERIALIZE_ERROR = (0x80000000 | 1011);// 数据反序列化错误
    public static final int NET_ERROR_LOWRATEWPAN_ID_EXISTED = (0x80000000 | 1012);// 该无线ID已存在
    public static final int NET_ERROR_LOWRATEWPAN_ID_LIMIT = (0x80000000 | 1013);// 无线ID数量已超限
    public static final int NET_ERROR_LOWRATEWPAN_ID_ABNORMAL = (0x80000000 | 1014);// 无线异常添加
    public static final int NET_ERROR_ENCRYPT = (0x80000000 | 1015);// 加密数据失败
    public static final int NET_ERROR_PWD_ILLEGAL = (0x80000000 | 1016);// 新密码不合规范
    public static final int NET_ERROR_DEVICE_ALREADY_INIT = (0x80000000 | 1017);// 设备已经初始化
    public static final int NET_ERROR_SECURITY_CODE = (0x80000000 | 1018);// 安全码错误
    public static final int NET_ERROR_SECURITY_CODE_TIMEOUT = (0x80000000 | 1019);// 安全码超出有效期
    public static final int NET_ERROR_GET_PWD_SPECI = (0x80000000 | 1020);// 获取密码规范失败
    public static final int NET_ERROR_NO_AUTHORITY_OF_OPERATION = (0x80000000 | 1021);// 无权限进行该操作
    public static final int NET_ERROR_DECRYPT = (0x80000000 | 1022);// 解密数据失败
    public static final int NET_ERROR_2D_CODE = (0x80000000 | 1023);// 2D code校验失败
    public static final int NET_ERROR_INVALID_REQUEST = (0x80000000 | 1024);// 非法的RPC请求
    public static final int NET_ERROR_PWD_RESET_DISABLE = (0x80000000 | 1025);// 密码重置功能已关闭
    public static final int NET_ERROR_PLAY_PRIVATE_DATA = (0x80000000 | 1026);// 显示私有数据，比如规则框等失败
    public static final int NET_ERROR_ROBOT_OPERATE_FAILED = (0x80000000 | 1027);// 机器人操作失败
    public static final int NET_ERROR_PHOTOSIZE_EXCEEDSLIMIT = (0x80000000 | 1028);// 图片大小超限
    public static final int NET_ERROR_USERID_INVALID = (0x80000000 | 1029);// 用户ID不存在
    public static final int NET_ERROR_EXTRACTFEATURE_FAILED = (0x80000000 | 1030);// 照片特征值提取失败
    public static final int NET_ERROR_PHOTO_EXIST = (0x80000000 | 1031);// 照片已存在
    public static final int NET_ERROR_PHOTO_OVERFLOW = (0x80000000 | 1032);// 照片数量超过上限
    public static final int NET_ERROR_CHANNEL_ALREADY_OPENED = (0x80000000 | 1033);// 通道已经打开
    public static final int NET_ERROR_CREATE_SOCKET = (0x80000000 | 1034);// 创建套接字失败
    public static final int NET_ERROR_CHANNEL_NUM = (0x80000000 | 1035);// 通道号错误
    public static final int NET_ERROR_PHOTO_FORMAT = (0x80000000 | 1036);// 图片格式错误
    public static final int NET_ERROR_DIGITAL_CERTIFICATE_INTERNAL_ERROR = (0x80000000 | 1037);      // 内部错误(比如：相关硬件问题，获取公钥失败，内部接口调用失败，写文件失败等等)
    public static final int NET_ERROR_DIGITAL_CERTIFICATE_GET_ID_FAILED = (0x80000000 | 1038);      // 获取设备ID失败
    public static final int NET_ERROR_DIGITAL_CERTIFICATE_IMPORT_ILLEGAL = (0x80000000 | 1039);      // 证书文件非法(格式不支持或者不是证书文件)
    public static final int NET_ERROR_DIGITAL_CERTIFICATE_SN_ERROR = (0x80000000 | 1040);      // 证书sn重复或错误或不规范
    public static final int NET_ERROR_DIGITAL_CERTIFICATE_COMMON_NAME_ILLEGAL = (0x80000000 | 1041);// 证书commonName非法(本地设备证书与系统中的不匹配devid_cryptoID,或者对端的不符合规则(devid_crytoID))
    public static final int NET_ERROR_DIGITAL_CERTIFICATE_NO_ROOT_CERT = (0x80000000 | 1042);      // 根证书未导入或不存在
    public static final int NET_ERROR_DIGITAL_CERTIFICATE_CERT_REVOKED = (0x80000000 | 1043);      // 证书被吊销
    public static final int NET_ERROR_DIGITAL_CERTIFICATE_CERT_INVALID = (0x80000000 | 1044);      // 证书不可用或未生效或已过期
    public static final int NET_ERROR_DIGITAL_CERTIFICATE_CERT_ERROR_SIGN = (0x80000000 | 1045);      // 证书签名不匹配
    public static final int NET_ERROR_DIGITAL_CERTIFICATE_COUNTS_UPPER_LIMIT = (0x80000000 | 1046); // 超出证书导入上限
    public static final int NET_ERROR_DIGITAL_CERTIFICATE_CERT_NO_EXIST = (0x80000000 | 1047);      // 证书文件不存在(导出证书或者获取对应证书的公钥)#define NET_ERROR_DEFULAT_SEARCH_PORT								_EC(1048)		  // 默认搜索端口无法使用（5050,37810）
    public static final int NET_ERROR_FACE_RECOGNITION_SERVER_MULTI_APPEND_STOUP = (0x80000000 | 1049);            // 批量添加人脸停止
    public static final int NET_ERROR_FACE_RECOGNITION_SERVER_MULTI_APPEND_ERROR = (0x80000000 | 1050);            // 批量添加人脸失败
    public static final int NET_ERROR_FACE_RECOGNITION_SERVER_GROUP_ID_EXCEED = (0x80000000 | 1051);            // 组ID超过最大值
    public static final int NET_ERROR_FACE_RECOGNITION_SERVER_GROUP_ID_NOT_IN_REGISTER_GROUP = (0x80000000 | 1052);            // 组ID不存在或为空
    public static final int NET_ERROR_FACE_RECOGNITION_SERVER_PICTURE_NOT_FOUND = (0x80000000 | 1053);            // 无图片数据
    public static final int NET_ERROR_FACE_RECOGNITION_SERVER_GENERATE_GROUP_ID_FAILED = (0x80000000 | 1054);            // 生成组ID超出范围
    public static final int NET_ERROR_FACE_RECOGNITION_SERVER_SET_CONFIG_FAILED = (0x80000000 | 1055);            // 设置配置失败
    public static final int NET_ERROR_FACE_RECOGNITION_SERVER_FILE_OPEN_FAILED = (0x80000000 | 1056);            // 图片文件打开失败
    public static final int NET_ERROR_FACE_RECOGNITION_SERVER_FILE_READ_FAILED = (0x80000000 | 1057);            // 图片文件读取失败
    public static final int NET_ERROR_FACE_RECOGNITION_SERVER_FILE_WRITE_FAILED = (0x80000000 | 1058);            // 图片文件写入失败
    public static final int NET_ERROR_FACE_RECOGNITION_SERVER_PICTURE_DPI_ERROR = (0x80000000 | 1059);            // 图片分辨率异常
    public static final int NET_ERROR_FACE_RECOGNITION_SERVER_PICTURE_PX_ERROR = (0x80000000 | 1060);            // 图片像素异常
    public static final int NET_ERROR_FACE_RECOGNITION_SERVER_PICTURE_SIZE_ERROR = (0x80000000 | 1061);            // 图片大小不对
    public static final int NET_ERROR_FACE_RECOGNITION_SERVER_DATA_BASE_ERROR = (0x80000000 | 1062);            // 数据库操作失败
    public static final int NET_ERROR_FACE_RECOGNITION_SERVER_FACE_MAX_NUM = (0x80000000 | 1063);            // 人员数量超过限制
    public static final int NET_ERROR_FACE_RECOGNITION_SERVER_BIRTH_DAY_FORMAT_ERROR = (0x80000000 | 1064);            // 生日日期格式错误
    public static final int NET_ERROR_FACE_RECOGNITION_SERVER_UID_ERROR = (0x80000000 | 1065);            // 人员UID不存在或为空
    public static final int NET_ERROR_FACE_RECOGNITION_SERVER_TOKEN_ERROR = (0x80000000 | 1066);            // 令牌不存在或为空
    public static final int NET_ERROR_FACE_RECOGNITION_SERVER_BEGIN_NUM_OVER_RUN = (0x80000000 | 1067);            // 查询起始数大于总数
    public static final int NET_ERROR_FACE_RECOGNITION_SERVER_ABSTRACT_NUM_ZERO = (0x80000000 | 1068);            // 需手动建模人数为0
    public static final int NET_ERROR_FACE_RECOGNITION_SERVER_ABSTRACT_INIT_ERROR = (0x80000000 | 1069);            // 建模分析器启动失败
    public static final int NET_ERROR_FACE_RECOGNITION_SERVER_AUTO_ABSTRACT_STATE = (0x80000000 | 1070);            // 设备正在自动建模
    public static final int NET_ERROR_FACE_RECOGNITION_SERVER_ABSTRACT_STATE = (0x80000000 | 1071);            // 设备正在手动建模
    public static final int NET_ERROR_FACE_RECOGNITION_SERVER_IM_EX_STATE = (0x80000000 | 1072);            // 设备正在导入导出
    public static final int NET_ERROR_FACE_RECOGNITION_SERVER_PIC_WRITE_FAILED = (0x80000000 | 1073);            // 图片写入失败
    public static final int NET_ERROR_FACE_RECOGNITION_SERVER_GROUP_SPACE_EXCEED = (0x80000000 | 1074);            // 超出人脸库空间大小限制
    public static final int NET_ERROR_FACE_RECOGNITION_SERVER_GROUP_PIC_COUNT_EXCEED = (0x80000000 | 1075);            // 超出人脸库图片数量限制
    public static final int NET_ERROR_FACE_RECOGNITION_SERVER_GROUP_NOT_FOUND = (0x80000000 | 1076);            // 人脸库不存在
    public static final int NET_ERROR_FACE_RECOGNITION_SERVER_FIND_RECORDS_ERROR = (0x80000000 | 1077);            // 查询原人脸库数据结果无效
    public static final int NET_ERROR_FACE_RECOGNITION_SERVER_DELETE_PERSON_ERROR = (0x80000000 | 1078);            // 删除原人脸库数据失败
    public static final int NET_ERROR_DEVICE_PARSE_PROTOCOL = (0x80000000 | 1079);          // 设备解析协议错误
    public static final int NET_ERROR_DEVICE_INVALID_REQUEST = (0x80000000 | 1080);          // 设备返回无效请求
    public static final int NET_ERROR_DEVICE_INTERNAL_ERROR = (0x80000000 | 1081);          // 设备内部错误
    public static final int NET_ERROR_DEVICE_REQUEST_TIMEOUT = (0x80000000 | 1082);          // 设备内部请求超时
    public static final int NET_ERROR_DEVICE_KEEPALIVE_FAIL = (0x80000000 | 1083);          // 设备保活失败
    public static final int NET_ERROR_DEVICE_NETWORK_ERROR = (0x80000000 | 1084);          // 设备网络错误
    public static final int NET_ERROR_DEVICE_UNKNOWN_ERROR = (0x80000000 | 1085);          // 设备内部未知错误
    public static final int NET_ERROR_DEVICE_COM_INTERFACE_NOTFOUND = (0x80000000 | 1086);          // 设备组件接口没有找到
    public static final int NET_ERROR_DEVICE_COM_IMPLEMENT_NOTFOUND = (0x80000000 | 1087);          // 设备组件实现没有找到
    public static final int NET_ERROR_DEVICE_COM_NOTFOUND = (0x80000000 | 1088);          // 设备接入组件没有找到
    public static final int NET_ERROR_DEVICE_COM_INSTANCE_NOTEXIST = (0x80000000 | 1089);          // 设备接入组件实例不存在
    public static final int NET_ERROR_DEVICE_CREATE_COM_FAIL = (0x80000000 | 1090);          // 设备组件工厂创建组件失败
    public static final int NET_ERROR_DEVICE_GET_COM_FAIL = (0x80000000 | 1091);          // 设备组件工厂获取组件实例失败
    public static final int NET_ERROR_DEVICE_BAD_REQUEST = (0x80000000 | 1092);          // 设备业务请求不被接受
    public static final int NET_ERROR_DEVICE_REQUEST_IN_PROGRESS = (0x80000000 | 1093);          // 设备已经在处理请求，不接受重复请求
    public static final int NET_ERROR_DEVICE_LIMITED_RESOURCE = (0x80000000 | 1094);          // 设备资源不足
    public static final int NET_ERROR_DEVICE_BUSINESS_TIMEOUT = (0x80000000 | 1095);          // 设备业务超时
    public static final int NET_ERROR_DEVICE_TOO_MANY_REQUESTS = (0x80000000 | 1096);          // 设备接收过多请求
    public static final int NET_ERROR_DEVICE_NOT_ALREADY = (0x80000000 | 1097);          // 设备未准备就绪，不接受业务请求
    public static final int NET_ERROR_DEVICE_SEARCHRECORD_TIMEOUT = (0x80000000 | 1098);          // 设备录像查询超时
    public static final int NET_ERROR_DEVICE_SEARCHTIME_INVALID = (0x80000000 | 1099);          // 设备录像查询时间无效
    public static final int NET_ERROR_DEVICE_SSID_INVALID = (0x80000000 | 1100);          // 设备校验SSID无效
    public static final int NET_ERROR_DEVICE_CHANNEL_STREAMTYPE_ERROR = (0x80000000 | 1101);    // 设备校验通道号或码流类型无效
    public static final int NET_ERROR_DEVICE_STREAM_PACKINGFORMAT_UNSUPPORT = (0x80000000 | 1102);    // 设备不支持该码流打包格式
    public static final int NET_ERROR_DEVICE_AUDIO_ENCODINGFORMAT_UNSUPPORT = (0x80000000 | 1103);    // 设备不支持该语音编码格式
    public static final int NET_ERROR_SECURITY_ERROR_SUPPORT_GUI = (0x80000000 | 1104);    // 校验请求安全码失败,可使用本地GUI方式重置密码
    public static final int NET_ERROR_SECURITY_ERROR_SUPPORT_MULT = (0x80000000 | 1105);    // 校验请求安全码失败,可使用DH渠道APP、configtool工具重置密码
    public static final int NET_ERROR_SECURITY_ERROR_SUPPORT_UNIQUE = (0x80000000 | 1106);    // 校验请求安全码失败,可登陆Web页面重置密码
    public static final int NET_ERROR_STREAMCONVERTOR_DEFECT = (0x80000000 | 1107);    // 转码库缺失


    public static final int NET_ERROR_SECURITY_GENERATE_SAFE_CODE = (0x80000000 | 1108);    // 调用DH加密库产生安全码失败
    public static final int NET_ERROR_SECURITY_GET_CONTACT = (0x80000000 | 1109);    // 获取联系方式失败
    public static final int NET_ERROR_SECURITY_GET_QRCODE = (0x80000000 | 1110);    // 获取重置密码的二维码信息失败
    public static final int NET_ERROR_SECURITY_CANNOT_RESET = (0x80000000 | 1111);    // 设备未初始化,无法重置
    public static final int NET_ERROR_SECURITY_NOT_SUPPORT_CONTACT_MODE = (0x80000000 | 1112);    // 不支持设置该种联系方式,如只支持设置手机号，却请求设置邮箱
    public static final int NET_ERROR_SECURITY_RESPONSE_TIMEOUT = (0x80000000 | 1113);    // 对端响应超时
    public static final int NET_ERROR_SECURITY_AUTHCODE_FORBIDDEN = (0x80000000 | 1114);    // 尝试校验AuthCode次数过多，禁止校验
    public static final int NET_ERROR_TRANCODE_LOGIN_REMOTE_DEV = (0x80000000 | 1115);        // (虚拟转码)登陆远程设备失败
    public static final int NET_ERROR_TRANCODE_NOFREE_CHANNEL = (0x80000000 | 1116);        // (虚拟转码)没有可用的通道资源
    public static final int NET_ERROR_VK_INFO_DECRYPT_FAILED = (0x80000000 | 1117);        // VK信息解密失败
    public static final int NET_ERROR_VK_INFO_DESERIALIZE_FAILED = (0x80000000 | 1118);        // VK信息解析失败
    public static final int NET_ERROR_GDPR_ABILITY_NOT_ENABLE = (0x80000000 | 1119);        // SDK GDPR功能未使能

    /*门禁快速导入及复核错误码 start*/
    public static final int NET_ERROR_FAST_CHECK_NO_AUTH = (0x80000000 | 1120);    // 门禁快速复核:无权限
    public static final int NET_ERROR_FAST_CHECK_NO_FILE = (0x80000000 | 1121);    // 门禁快速复核:文件不存在
    public static final int NET_ERROR_FAST_CHECK_FILE_FAIL = (0x80000000 | 1122);    // 门禁快速复核:文件准备失败
    public static final int NET_ERROR_FAST_CHECK_BUSY = (0x80000000 | 1123);    // 门禁快速复核:系统忙
    public static final int NET_ERROR_FAST_CHECK_NO_PASSWORD = (0x80000000 | 1124);    // 门禁快速复核:未定义密码, 不允许导出
    public static final int NET_ERROR_IMPORT_ACCESS_SEND_FAILD = (0x80000000 | 1125);    // 门禁快速导入:发送门禁数据失败
    public static final int NET_ERROR_IMPORT_ACCESS_BUSY = (0x80000000 | 1126);    // 门禁快速导入:系统忙, 已经有导入任务
    public static final int NET_ERROR_IMPORT_ACCESS_DATAERROR = (0x80000000 | 1127);    // 门禁快速导入:数据包检验失败
    public static final int NET_ERROR_IMPORT_ACCESS_DATAINVALID = (0x80000000 | 1128);    // 门禁快速导入:数据包非法
    public static final int NET_ERROR_IMPORT_ACCESS_SYNC_FALID = (0x80000000 | 1129);    // 门禁快速导入:同步失败,数据库无法生成
    public static final int NET_ERROR_IMPORT_ACCESS_DBFULL = (0x80000000 | 1130);    // 门禁快速导入:数据库已满, 无法导入
    public static final int NET_ERROR_IMPORT_ACCESS_SDFULL = (0x80000000 | 1131);    // 门禁快速导入:存储空间已满, 无法导入
    public static final int NET_ERROR_IMPORT_ACCESS_CIPHER_ERROR = (0x80000000 | 1132);    // 门禁快速导入:导入压缩包密码不对
    /*门禁快速导入及复核错误码 end*/

    public static final int NET_ERROR_INVALID_PARAM = (0x80000000 | 1133);    // 参数无效
    public static final int NET_ERROR_INVALID_PASSWORD = (0x80000000 | 1134);    // 密码无效
    public static final int NET_ERROR_INVALID_FINGERPRINT = (0x80000000 | 1135);    // 信息无效
    public static final int NET_ERROR_INVALID_FACE = (0x80000000 | 1136);    // 人脸无效
    public static final int NET_ERROR_INVALID_CARD = (0x80000000 | 1137);    // 卡无效
    public static final int NET_ERROR_INVALID_USER = (0x80000000 | 1138);    // 用户无效
    public static final int NET_ERROR_GET_SUBSERVICE = (0x80000000 | 1139);    // 能力集子服务获取失败
    public static final int NET_ERROR_GET_METHOD = (0x80000000 | 1140);   // 获取组件的方法集失败
    public static final int NET_ERROR_GET_SUBCAPS = (0x80000000 | 1141);    // 获取资源实体能力集失败
    public static final int NET_ERROR_UPTO_INSERT_LIMIT = (0x80000000 | 1142);   // 已达插入上限
    public static final int NET_ERROR_UPTO_MAX_INSERT_RATE = (0x80000000 | 1143);   // 已达最大插入速度
    public static final int NET_ERROR_ERASE_FINGERPRINT = (0x80000000 | 1144);   // 清除信息数据失败
    public static final int NET_ERROR_ERASE_FACE = (0x80000000 | 1145);   // 清除人脸数据失败
    public static final int NET_ERROR_ERASE_CARD = (0x80000000 | 1146);   // 清除卡数据失败
    public static final int NET_ERROR_NO_RECORD = (0x80000000 | 1147);   // 没有记录
    public static final int NET_ERROR_NOMORE_RECORDS = (0x80000000 | 1148);   // 查找到最后，没有更多记录
    public static final int NET_ERROR_RECORD_ALREADY_EXISTS = (0x80000000 | 1149);   // 下发卡或信息时，数据重复
    public static final int NET_ERROR_EXCEED_MAX_FINGERPRINT_PERUSER = (0x80000000 | 1150);   // 超过个人最大信息记录数
    public static final int NET_ERROR_EXCEED_MAX_CARD_PERUSER = (0x80000000 | 1151);   // 超过个人最大卡片记录数
    public static final int NET_ERROR_EXCEED_ADMINISTRATOR_LIMIT = (0x80000000 | 1152);   // 超过门禁管理员个数限制

    public static final int NET_LOGIN_ERROR_DEVICE_NOT_SUPPORT_HIGHLEVEL_SECURITY_LOGIN = (0x80000000 | 1153);   // 设备不支持高安全等级登录
    public static final int NET_LOGIN_ERROR_DEVICE_ONLY_SUPPORT_HIGHLEVEL_SECURITY_LOGIN = (0x80000000 | 1154);   // 设备只支持高安全等级登录

    public static final int NET_ERROR_VIDEO_CHANNEL_OFFLINE = (0x80000000 | 1155);   // 表示此视频通道处于离线，拉流失败
    public static final int NET_ERROR_USERID_FORMAT_INCORRECT = (0x80000000 | 1156);    // 用户编号不规范
    public static final int NET_ERROR_CANNOT_FIND_CHANNEL_RELATE_TO_SN = (0x80000000 | 1157);    // 找不到该SN对应的通道
    public static final int NET_ERROR_TASK_QUEUE_OF_CHANNEL_IS_FULL = (0x80000000 | 1158);    // 该通道的任务队列满
    public static final int NET_ERROR_APPLY_USER_INFO_BLOCK_FAIL = (0x80000000 | 1159);    // 申请不到新的用户信息(权限)块
    public static final int NET_ERROR_EXCEED_MAX_PASSWD_PERUSER = (0x80000000 | 1160);    // 用户密码数量超过限制
    public static final int NET_ERROR_PARSE_PROTOCOL = (0x80000000 | 1161);    // 设备内部异常引起协议解析错误
    public static final int NET_ERROR_CARD_NUM_EXIST = (0x80000000 | 1162);    // 卡号已存在
    public static final int NET_ERROR_FINGERPRINT_EXIST = (0x80000000 | 1163);    // 信息已存在

    public static final int NET_ERROR_OPEN_PLAYGROUP_FAIL = (0x80000000 | 1164);    // 打开播放组失败
    public static final int NET_ERROR_ALREADY_IN_PLAYGROUP = (0x80000000 | 1165);    // 已位于播放组中
    public static final int NET_ERROR_QUERY_PLAYGROUP_TIME_FAIL = (0x80000000 | 1166);    // 查询播放组时间失败
    public static final int NET_ERROR_SET_PLAYGROUP_BASECHANNEL_FAIL = (0x80000000 | 1167);    // 设置播放组基准通道失败
    public static final int NET_ERROR_SET_PLAYGROUP_DIRECTION_FAIL = (0x80000000 | 1168);    // 设置播放组方向失败
    public static final int NET_ERROR_SET_PLAYGROUP_SPEED_FAIL = (0x80000000 | 1169);    // 设置播放组速度失败
    public static final int NET_ERROR_ADD_PLAYGROUP_FAIL = (0x80000000 | 1170);    // 加入播放组失败

    public static final int NET_ERROR_EXPORT_AOL_LOGFILE_NO_AUTH = (0x80000000 | 1171);    // 导出AOL日志:无权限
    public static final int NET_ERROR_EXPORT_AOL_LOGFILE_NO_FILE = (0x80000000 | 1172);    // 导出AOL日志:文件不存在
    public static final int NET_ERROR_EXPORT_AOL_LOGFILE_FILE_FAIL = (0x80000000 | 1173);    // 导出AOL日志:文件准备失败
    public static final int NET_ERROR_EXPORT_AOL_LOGFILE_BUSY = (0x80000000 | 1174);    // 导出AOL日志:系统忙

    // 设备上APP安装相关错误码
    public static final int NET_ERROR_EMPTY_LICENSE = (0x80000000 | 1175);   // License为空
    public static final int NET_ERROR_UNSUPPORTED_MODE = (0x80000000 | 1176);   // 不支持该模式
    public static final int NET_ERROR_URL_APP_NOT_MATCH = (0x80000000 | 1177);   // URL与APP不匹配
    public static final int NET_ERROR_READ_INFO_FAILED = (0x80000000 | 1178);   // 读取信息失败
    public static final int NET_ERROR_WRITE_FAILED = (0x80000000 | 1179);   // 写入失败
    public static final int NET_ERROR_NO_SUCH_APP = (0x80000000 | 1180);   // 未找到APP
    public static final int NET_ERROR_VERIFIF_FAILED = (0x80000000 | 1181);   // 校验失败
    public static final int NET_ERROR_LICENSE_OUT_DATE = (0x80000000 | 1182);   // License已过期

    public static final int NET_ERROR_UPGRADE_PROGRAM_TOO_OLD = (0x80000000 | 1183);    // 升级程序版本过低
    public static final int NET_ERROR_SECURE_TRANSMIT_BEEN_CUT = (0x80000000 | 1184);    // 加密传输被裁剪
    public static final int NET_ERROR_DEVICE_NOT_SUPPORT_SECURE_TRANSMIT = (0x80000000 | 1185);    // 设备不支持安全传输

    public static final int NET_ERROR_EXTRA_STREAM_LOGIN_FAIL_CAUSE_BY_MAIN_STREAM = (0x80000000 | 1186);    // 主码流成功的情况下，辅码流登录失败
    public static final int NET_ERROR_EXTRA_STREAM_CLOSED_BY_REMOTE_DEVICE = (0x80000000 | 1187);    // 辅码流被前端关闭

    /*人脸库导入导出错误码 start*/
    public static final int NET_ERROR_IMPORT_FACEDB_SEND_FAILD = (0x80000000 | 1188);    // 人脸库导入:发送人脸库数据失败
    public static final int NET_ERROR_IMPORT_FACEDB_BUSY = (0x80000000 | 1189);    // 人脸库导入:系统忙, 已经有导入任务
    public static final int NET_ERROR_IMPORT_FACEDB_DATAERROR = (0x80000000 | 1190);    // 人脸库导入:数据包检验失败
    public static final int NET_ERROR_IMPORT_FACEDB_DATAINVALID = (0x80000000 | 1191);    // 人脸库导入:数据包非法
    public static final int NET_ERROR_IMPORT_FACEDB_UPGRADE_FAILD = (0x80000000 | 1192);   // 人脸库导入:上传失败
    public static final int NET_ERROR_IMPORT_FACEDB_NO_AUTHORITY = (0x80000000 | 1193);   // 人脸库导入:用户无权限
    public static final int NET_ERROR_IMPORT_FACEDB_ABNORMAL_FILE = (0x80000000 | 1194);   // 人脸库导入:文件格式异常
    public static final int NET_ERROR_IMPORT_FACEDB_SYNC_FALID = (0x80000000 | 1195);    // 人脸库导入:同步失败,数据库无法生成
    public static final int NET_ERROR_IMPORT_FACEDB_DBFULL = (0x80000000 | 1196);    // 人脸库导入:数据库已满, 无法导入
    public static final int NET_ERROR_IMPORT_FACEDB_SDFULL = (0x80000000 | 1197);    // 人脸库导入:存储空间已满, 无法导入
    public static final int NET_ERROR_IMPORT_FACEDB_CIPHER_ERROR = (0x80000000 | 1198);    // 人脸库导入:导入压缩包密码不对

    public static final int NET_ERROR_EXPORT_FACEDB_NO_AUTH = (0x80000000 | 1199);    // 人脸库导出:无权限
    public static final int NET_ERROR_EXPORT_FACEDB_NO_FILE = (0x80000000 | 1200);    // 人脸库导出:文件不存在
    public static final int NET_ERROR_EXPORT_FACEDB_FILE_FAIL = (0x80000000 | 1201);    // 人脸库导出:文件准备失败
    public static final int NET_ERROR_EXPORT_FACEDB_BUSY = (0x80000000 | 1202);    // 人脸库导出:系统忙
    public static final int NET_ERROR_EXPORT_FACEDB_NO_PASSWORD = (0x80000000 | 1203);    // 人脸库导出:未定义密码, 不允许导出
    /*人脸库导入导出错误码 end*/

    /* 人脸图片操作错误码 范围 _EC(1300) ~ _EC(1400) */
    public static final int NET_ERROR_FACEMANAGER_NO_FACE_DETECTED = (0x80000000 | 1300);  // 图片中检测到0个人脸目标
    public static final int NET_ERROR_FACEMANAGER_MULTI_FACE_DETECTED = (0x80000000 | 1301);  // 图片中检测到多个人脸，无法返回特征
    public static final int NET_ERROR_FACEMANAGER_PICTURE_DECODING_ERROR = (0x80000000 | 1302);  // 图片解码错误
    public static final int NET_ERROR_FACEMANAGER_LOW_PICTURE_QUALITY = (0x80000000 | 1303);  // 图片质量太低
    public static final int NET_ERROR_FACEMANAGER_NOT_RECOMMENDED = (0x80000000 | 1304);  // 结果不推荐使用，比如：对外国人，特征提取成功，但算法支持不好，容易造成误识别
    public static final int NET_ERROR_FACEMANAGER_FACE_FEATURE_ALREADY_EXIST = (0x80000000 | 1305); // 人脸特征已存在
    public static final int NET_ERROR_FACEMANAGER_FACE_ANGLE_OVER_THRESHOLDS = (0x80000000 | 1307); // 人脸角度超过配置阈值
    public static final int NET_ERROR_FACEMANAGER_FACE_RADIO_EXCEEDS_RANGE = (0x80000000 | 1308); // 人脸占比超出范围，算法建议占比:不要超过2/3;不要小于1/3
    public static final int NET_ERROR_FACEMANAGER_FACE_OVER_EXPOSED = (0x80000000 | 1309);  // 人脸过爆
    public static final int NET_ERROR_FACEMANAGER_FACE_UNDER_EXPOSED = (0x80000000 | 1310);  // 人脸欠爆
    public static final int NET_ERROR_FACEMANAGER_BRIGHTNESS_IMBALANCE = (0x80000000 | 1311);  // 人脸亮度不均衡 ,用于判断阴阳脸
    public static final int NET_ERROR_FACEMANAGER_FACE_LOWER_CONFIDENCE = (0x80000000 | 1312);  // 人脸的置信度低
    public static final int NET_ERROR_FACEMANAGER_FACE_LOW_ALIGN = (0x80000000 | 1313);  // 人脸对齐分数低
    public static final int NET_ERROR_FACEMANAGER_FRAGMENTARY_FACE_DETECTED = (0x80000000 | 1314);  // 人脸存在遮挡、残缺不全
    public static final int NET_ERROR_FACEMANAGER_PUPIL_DISTANCE_NOT_ENOUGH = (0x80000000 | 1315);  // 人脸瞳距小于阈值
    public static final int NET_ERROR_FACEMANAGER_FACE_DATA_DOWNLOAD_FAILED = (0x80000000 | 1316);  // 人脸数据下载失败

}
