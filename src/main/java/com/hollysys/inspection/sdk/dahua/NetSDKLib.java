package com.hollysys.inspection.sdk.dahua;

import com.sun.jna.*;
import com.sun.jna.ptr.IntByReference;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.List;

public interface NetSDKLib extends Library {
    public static final int MAX_CHANNELNAME_LEN = 64; // 最大通道名称长度
    public static final int MAX_VIDEOSTREAM_NUM = 4; // 最大码流个数
    public static final int MAX_VIDEO_COVER_NUM = 16; // 最大遮挡区域个数
    public static final int MAX_PATH = 260;
    class LLong extends IntegerType {
        private static final long serialVersionUID = 1L;

        /**
         * Size of a native long, in bytes.
         */
        public static int size;

        static {
            size = Native.LONG_SIZE;
            if (getOsPrefix().equalsIgnoreCase("linux-amd64")
                    || getOsPrefix().equalsIgnoreCase("win32-amd64")
                    || getOsPrefix().equalsIgnoreCase("mac-64")) {
                size = 8;
            } else if (getOsPrefix().equalsIgnoreCase("linux-i386")
                    || getOsPrefix().equalsIgnoreCase("win32-x86")) {
                size = 4;
            }
        }

        /**
         * Create a zero-valued LLong.
         */
        public LLong() {
            this(0);
        }
        /**
         * Create a LLong with the given value.
         */
        public LLong(long value) {
            super(size, value);
        }
    }

    public static String getOsPrefix() {
        String arch = System.getProperty("os.arch").toLowerCase();
        final String name = System.getProperty("os.name");
        String osPrefix;
        switch (Platform.getOSType()) {
            case Platform.WINDOWS: {
                if ("i386".equals(arch))
                    arch = "x86";
                osPrefix = "win32-" + arch;
            }
            break;
            case Platform.LINUX: {
                if ("x86".equals(arch)) {
                    arch = "i386";
                } else if ("x86_64".equals(arch)) {
                    arch = "amd64";
                }
                osPrefix = "linux-" + arch;
            }
            break;
            case Platform.MAC: {
                //mac系统的os.arch都是ppc(老版本的mac是powerpc，已经基本不用)看不出系统位数，使用下面的参数表示
                arch = System.getProperty("sun.arch.data.model");
                osPrefix = "mac-" + arch;
            }
            break;
            default: {
                osPrefix = name.toLowerCase();
                if ("x86".equals(arch)) {
                    arch = "i386";
                }
                if ("x86_64".equals(arch)) {
                    arch = "amd64";
                }
                int space = osPrefix.indexOf(" ");
                if (space != -1) {
                    osPrefix = osPrefix.substring(0, space);
                }
                osPrefix += "-" + arch;
            }
            break;

        }

        return osPrefix;
    }

    public static class SdkStructure extends Structure {
        @Override
        protected List<String> getFieldOrder() {
            List<String> fieldOrderList = new ArrayList<String>();
            for (Class<?> cls = getClass();
                 !cls.equals(SdkStructure.class);
                 cls = cls.getSuperclass()) {
                Field[] fields = cls.getDeclaredFields();
                int modifiers;
                for (Field field : fields) {
                    modifiers = field.getModifiers();
                    if (Modifier.isStatic(modifiers) || !Modifier.isPublic(modifiers)) {
                        continue;
                    }
                    fieldOrderList.add(field.getName());
                }
            }
            return fieldOrderList;
        }

        @Override
        public int fieldOffset(String name) {
            return super.fieldOffset(name);
        }
    }

    // 图像通道属性信息
    public static class CFG_ENCODE_INFO extends SdkStructure
    {
        public int              nChannelID;                           //通道号(0开始),获取时，该字段有效；设置时，该字段无效
        public byte[]           szChnName = new byte[MAX_CHANNELNAME_LEN]; //无效字段
        public CFG_VIDEOENC_OPT[] stuMainStream = (CFG_VIDEOENC_OPT[])new CFG_VIDEOENC_OPT().toArray(MAX_VIDEOSTREAM_NUM); // 主码流，0－普通录像，1-动检录像，2－报警录像
        public int              nValidCountMainStream;                // 主码流数组中有效的个数
        public CFG_VIDEOENC_OPT[] stuExtraStream = (CFG_VIDEOENC_OPT[])new CFG_VIDEOENC_OPT().toArray(MAX_VIDEOSTREAM_NUM); // 辅码流，0－辅码流1，1－辅码流2，2－辅码流3
        public int              nValidCountExtraStream;               // 辅码流数组中有效的个数
        public CFG_VIDEOENC_OPT[] stuSnapFormat = (CFG_VIDEOENC_OPT[])new CFG_VIDEOENC_OPT().toArray(MAX_VIDEOSTREAM_NUM); // 抓图，0－普通抓图，1－动检抓图，2－报警抓图
        public int              nValidCountSnapFormat;                // 抓图数组中有效的个数
        public int              dwCoverAbilityMask;                   //无效字段
        public int              dwCoverEnableMask;                    //无效字段
        public CFG_VIDEO_COVER  stuVideoCover;                        //无效字段
        public CFG_OSD_INFO     stuChnTitle;                          //无效字段
        public CFG_OSD_INFO     stuTimeTitle;                         //无效字段
        public CFG_COLOR_INFO   stuVideoColor;                        //无效字段
        public int              emAudioFormat;                        //无效字段, 取值为CFG_AUDIO_FORMAT中的值
        public int              nProtocolVer;                         //协议版本号,只读,获取时，该字段有效；设置时，该字段无效
        public int              bIsUseExtraStreamEx;                  //是否使用辅码流扩展
        public CFG_VIDEOENC_OPT[] stuExtraStreamEx = new CFG_VIDEOENC_OPT[12]; //辅码流扩展，0－辅码流1，1－辅码流2，2－辅码流3，3－辅码流4，4－辅码流5，5－辅码流6，6－辅码流7，7－辅码流8，8－辅码流9，9－辅码流10，10－辅码流11，11－辅码流12,参见结构体定义 {@link com.netsdk.lib.NetSDKLib.CFG_VIDEOENC_OPT}
        public int              nValidCountExtraStreamEx;             //辅码流扩展数组中有效的个数
        public byte[]           szReserved = new byte[120];           //保留字节
    }

    // 遮挡信息
    public static class CFG_COVER_INFO extends SdkStructure
    {
        // 能力
        public byte             abBlockType;                          // 类型为bool, 取值0或1
        public byte             abEncodeBlend;                        // 类型为bool, 取值0或1
        public byte             abPreviewBlend;                       // 类型为bool, 取值0或1
        // 信息
        public CFG_RECT         stuRect = new CFG_RECT();             //覆盖的区域坐标
        public CFG_RGBA         stuColor = new CFG_RGBA();            //覆盖的颜色
        public int              nBlockType;                           //覆盖方式；0－黑块，1－马赛克
        public int              nEncodeBlend;                         //编码级遮挡；1－生效，0－不生效
        public int              nPreviewBlend;                        //预览遮挡；1－生效，0－不生效
    }

    // RGBA信息
    public static class CFG_RGBA extends SdkStructure
    {
        public int              nRed;
        public int              nGreen;
        public int              nBlue;
        public int              nAlpha;
    }

    // 区域信息
    public static class CFG_RECT extends SdkStructure
    {
        public int              nLeft;
        public int              nTop;
        public int              nRight;
        public int              nBottom;
    }

    // 多区域遮挡配置
    public static class CFG_VIDEO_COVER extends SdkStructure
    {
        public int              nTotalBlocks;                         //支持的遮挡块数
        public int              nCurBlocks;                           //已设置的块数
        public CFG_COVER_INFO[] stuCoverBlock = (CFG_COVER_INFO[])new CFG_COVER_INFO().toArray(MAX_VIDEO_COVER_NUM); // 覆盖的区域
    }

    // OSD信息
    public static class CFG_OSD_INFO extends SdkStructure
    {
        // 能力
        public byte             abShowEnable;                         // 类型为bool, 取值0或1
        // 信息
        public CFG_RGBA         stuFrontColor = new CFG_RGBA();       //前景颜色
        public CFG_RGBA         stuBackColor = new CFG_RGBA();        //背景颜色
        public CFG_RECT         stuRect = new CFG_RECT();             //矩形区域
        public int              bShowEnable;                          //显示使能, 类型为BOOL, 取值0或者1
    }

    // 画面颜色属性
    public static class CFG_COLOR_INFO extends SdkStructure
    {
        public int              nBrightness;                          //亮度(0-100)
        public int              nContrast;                            //对比度(0-100)
        public int              nSaturation;                          //饱和度(0-100)
        public int              nHue;                                 //色度(0-100)
        public int              nGain;                                //增益(0-100)
        public int              bGainEn;                              //增益使能, 类型为BOOL, 取值0或者1
    }
    // 视频编码参数
    public static class CFG_VIDEOENC_OPT extends SdkStructure
    {
        // 能力
        public byte             abVideoEnable;                        // 类型为bool, 取值0或1
        public byte             abAudioEnable;                        // 类型为bool, 取值0或1
        public byte             abSnapEnable;                         // 类型为bool, 取值0或1
        public byte             abAudioAdd;                           //音频叠加能力, 类型为bool, 取值0或1
        public byte             abAudioFormat;                        // 类型为bool, 取值0或1
        // 信息
        public int              bVideoEnable;                         //视频使能, 类型为BOOL, 取值0或者1
        public CFG_VIDEO_FORMAT stuVideoFormat;                       //视频格式
        public int              bAudioEnable;                         //音频使能, 类型为BOOL, 取值0或者1
        public int              bSnapEnable;                          //定时抓图使能, 类型为BOOL, 取值0或者1
        public int              bAudioAddEnable;                      //音频叠加使能, 类型为BOOL, 取值0或者1
        public CFG_AUDIO_ENCODE_FORMAT stuAudioFormat;                //音频格式
    }

    // 音频格式
    public static class CFG_AUDIO_ENCODE_FORMAT extends SdkStructure
    {
        // 能力
        public byte             abCompression;                        // 类型为bool, 取值0或1
        public byte             abDepth;                              // 类型为bool, 取值0或1
        public byte             abFrequency;                          // 类型为bool, 取值0或1
        public byte             abMode;                               // 类型为bool, 取值0或1
        public byte             abFrameType;                          // 类型为bool, 取值0或1
        public byte             abPacketPeriod;                       // 类型为bool, 取值0或1
        public byte             abChannels;                           // 类型为bool, 取值0或1
        public byte             abMix;                                // 类型为bool, 取值0或1
        // 信息
        public int              emCompression;                        //音频压缩模式，取值为CFG_AUDIO_FORMAT中的值
        public int              nDepth;                               //音频采样深度
        public int              nFrequency;                           //音频采样频率
        public int              nMode;                                //音频编码模式
        public int              nFrameType;                           //音频打包模式,0-DHAV,1-PS
        public int              nPacketPeriod;                        //音频打包周期,ms
        public int              nChannelsNum;                         // 视频通道的伴音通道号列表个数
        public int              arrChannels[] = new int[8];           // 视频通道的伴音通道号列表
        public int              bMix;                                 // 是否同源
    }

    // 视频格式
    public static class CFG_VIDEO_FORMAT extends SdkStructure
    {
        // 能力
        public byte             abCompression;                        // 类型为bool, 取值0或1
        public byte             abWidth;                              // 类型为bool, 取值0或1
        public byte             abHeight;                             // 类型为bool, 取值0或1
        public byte             abBitRateControl;                     // 类型为bool, 取值0或1
        public byte             abBitRate;                            // 类型为bool, 取值0或1
        public byte             abFrameRate;                          // 类型为bool, 取值0或1
        public byte             abIFrameInterval;                     // 类型为bool, 取值0或1
        public byte             abImageQuality;                       // 类型为bool, 取值0或1
        public byte             abFrameType;                          // 类型为bool, 取值0或1
        public byte             abProfile;                            // 类型为bool, 取值0或1
        // 信息
        public int              emCompression;                        //视频压缩格式, 取值为CFG_VIDEO_COMPRESSION中的值
        public int              nWidth;                               //视频宽度
        public int              nHeight;                              //视频高度
        public int              emBitRateControl;                     //码流控制模式, 取值为CFG_BITRATE_CONTROL中的值
        public int              nBitRate;                             //视频码流(kbps)
        public float            nFrameRate;                           //视频帧率
        public int              nIFrameInterval;                      //I帧间隔(1-100)，比如50表示每49个B帧或P帧，设置一个I帧。
        public int              emImageQuality;                       //图像质量, 取值为CFG_IMAGE_QUALITY中的值
        public int              nFrameType;                           //打包模式，0－DHAV，1－"PS"
        public int              emProfile;                            //H.264编码级别, 取值为CFG_H264_PROFILE_RANK中的值
        public int              nMaxBitrate;                          // 最大码流单位是kbps（博世专用）
    }


    // SDK全局日志打印信息
    public static class LOG_SET_PRINT_INFO extends SdkStructure
    {
        public int              dwSize;
        public int              bSetFilePath;                         //是否重设日志路径, BOOL类型，取值0或1
        public byte[]           szLogFilePath = new byte[260]; //日志路径(默认"./sdk_log/sdk_log.log")
        public int              bSetFileSize;                         //是否重设日志文件大小, BOOL类型，取值0或1
        public int              nFileSize;                            //每个日志文件的大小(默认大小10240),单位:比特, 类型为unsigned int
        public int              bSetFileNum;                          //是否重设日志文件个数, BOOL类型，取值0或1
        public int              nFileNum;                             //绕接日志文件个数(默认大小10), 类型为unsigned int
        public int              bSetPrintStrategy;                    //是否重设日志打印输出策略, BOOL类型，取值0或1
        public int              nPrintStrategy;                       //日志输出策略,0:输出到文件(默认); 1:输出到窗口, 类型为unsigned int
        public byte[]           byReserved = new byte[4];             // 字节对齐
        public Pointer cbSDKLogCallBack;                     // 日志回调，需要将sdk日志回调出来时设置，默认为NULL
        public Pointer          dwUser;                               // 用户数据

        public LOG_SET_PRINT_INFO()
        {
            this.dwSize = this.size();
        }
    }
    // 设备信息扩展///////////////////////////////////////////////////
    public static class NET_DEVICEINFO_Ex extends SdkStructure {
        public byte[]           sSerialNumber = new byte[48]; // 序列号
        public int              byAlarmInPortNum;                     // DVR报警输入个数
        public int              byAlarmOutPortNum;                    // DVR报警输出个数
        public int              byDiskNum;                            // DVR硬盘个数
        public int              byDVRType;                            // DVR类型,见枚举NET_DEVICE_TYPE
        public int              byChanNum;                            // DVR通道个数
        public byte             byLimitLoginTime;                     // 在线超时时间,为0表示不限制登陆,非0表示限制的分钟数
        public byte             byLeftLogTimes;                       // 当登陆失败原因为密码错误时,通过此参数通知用户,剩余登陆次数,为0时表示此参数无效
        public byte[]           bReserved = new byte[2];              // 保留字节,字节对齐
        public int              byLockLeftTime;                       // 当登陆失败,用户解锁剩余时间（秒数）, -1表示设备未设置该参数
        public byte[]           Reserved = new byte[24];              // 保留
    }
    // 私有云台控制扩展接口,支持三维快速定位
    public boolean CLIENT_DHPTZControlEx(LLong lLoginID,int nChannelID,int dwPTZCommand,int lParam1,int lParam2,int lParam3,int dwStop);

    // 云台控制扩展接口,支持三维快速定位,鱼眼
    // dwStop类型为BOOL, 取值0或者1
    // dwPTZCommand取值为NET_EXTPTZ_ControlType中的值或者是NET_PTZ_ControlType中的值
    // NET_IN_PTZBASE_MOVEABSOLUTELY_INFO
    // 精准绝对移动控制命令, param4对应结构 NET_IN_PTZBASE_MOVEABSOLUTELY_INFO（通过 CFG_CAP_CMD_PTZ 命令获取云台能力集( CFG_PTZ_PROTOCOL_CAPS_INFO )，若bSupportReal为TRUE则设备支持该操作）
    public boolean CLIENT_DHPTZControlEx2(LLong lLoginID,int nChannelID,int dwPTZCommand,int lParam1,int lParam2,int lParam3,int dwStop,Pointer param4);


    // 获取配置
    // error 为设备返回的错误码： 0-成功 1-失败 2-数据不合法 3-暂时无法设置 4-没有权限
    public boolean CLIENT_GetNewDevConfig(LLong lLoginID,String szCommand,int nChannelID,byte[] szOutBuffer,int dwOutBufferSize,IntByReference error,int waiitime,Pointer pReserved);
    /************************************************************************
     ** 结构体
     ***********************************************************************/
    // 设置登入时的相关参数
    public static class NET_PARAM extends SdkStructure
    {
        public int              nWaittime;                            // 等待超时时间(毫秒为单位)，为0默认5000ms
        public int              nConnectTime;                         // 连接超时时间(毫秒为单位)，为0默认1500ms
        public int              nConnectTryNum;                       // 连接尝试次数，为0默认1次
        public int              nSubConnectSpaceTime;                 // 子连接之间的等待时间(毫秒为单位)，为0默认10ms
        public int              nGetDevInfoTime;                      // 获取设备信息超时时间，为0默认1000ms
        public int              nConnectBufSize;                      // 每个连接接收数据缓冲大小(字节为单位)，为0默认250*1024
        public int              nGetConnInfoTime;                     // 获取子连接信息超时时间(毫秒为单位)，为0默认1000ms
        public int              nSearchRecordTime;                    // 按时间查询录像文件的超时时间(毫秒为单位),为0默认为3000ms
        public int              nsubDisconnetTime;                    // 检测子链接断线等待时间(毫秒为单位)，为0默认为60000ms
        public byte             byNetType;                            // 网络类型, 0-LAN, 1-WAN
        public byte             byPlaybackBufSize;                    // 回放数据接收缓冲大小（M为单位），为0默认为4M
        public byte             bDetectDisconnTime;                   // 心跳检测断线时间(单位为秒),为0默认为60s,最小时间为2s
        public byte             bKeepLifeInterval;                    // 心跳包发送间隔(单位为秒),为0默认为10s,最小间隔为2s
        public int              nPicBufSize;                          // 实时图片接收缓冲大小（字节为单位），为0默认为2*1024*1024
        public byte[]           bReserved = new byte[4];              // 保留字段字段
    }


    // 通用云台控制命令
    public static class NET_PTZ_ControlType extends SdkStructure
    {
        public static final int   NET_PTZ_UP_CONTROL = 0;               //上
        public static final int   NET_PTZ_DOWN_CONTROL = NET_PTZ_UP_CONTROL+1; //下
        public static final int   NET_PTZ_LEFT_CONTROL = NET_PTZ_DOWN_CONTROL+1; //左
        public static final int   NET_PTZ_RIGHT_CONTROL = NET_PTZ_LEFT_CONTROL+1; //右
        public static final int   NET_PTZ_ZOOM_ADD_CONTROL = NET_PTZ_RIGHT_CONTROL+1; //变倍+
        public static final int   NET_PTZ_ZOOM_DEC_CONTROL = NET_PTZ_ZOOM_ADD_CONTROL+1; //变倍-
        public static final int   NET_PTZ_FOCUS_ADD_CONTROL = NET_PTZ_ZOOM_DEC_CONTROL+1; //调焦+
        public static final int   NET_PTZ_FOCUS_DEC_CONTROL = NET_PTZ_FOCUS_ADD_CONTROL+1; //调焦-
        public static final int   NET_PTZ_APERTURE_ADD_CONTROL = NET_PTZ_FOCUS_DEC_CONTROL+1; //光圈+
        public static final int   NET_PTZ_APERTURE_DEC_CONTROL = NET_PTZ_APERTURE_ADD_CONTROL+1; //光圈-
        public static final int   NET_PTZ_POINT_MOVE_CONTROL = NET_PTZ_APERTURE_DEC_CONTROL+1; //转至预置点
        public static final int   NET_PTZ_POINT_SET_CONTROL = NET_PTZ_POINT_MOVE_CONTROL+1; //设置
        public static final int   NET_PTZ_POINT_DEL_CONTROL = NET_PTZ_POINT_SET_CONTROL+1; //删除
        public static final int   NET_PTZ_POINT_LOOP_CONTROL = NET_PTZ_POINT_DEL_CONTROL+1; //点间巡航
        public static final int   NET_PTZ_LAMP_CONTROL = NET_PTZ_POINT_LOOP_CONTROL+1; //灯光雨刷
    }

    public static class NET_PTZSPACE_UNNORMALIZED extends SdkStructure
    {
        public int              nPosX;                                // x坐标
        public int              nPosY;                                // y坐标
        public int              nZoom;                                // 放大倍率
        public byte[]           byReserved = new byte[52];            // 预留字节
    }

    public static class NET_VIDEOCHANNELS_OUTPUT extends SdkStructure
    {
        public int              nVGACount;                            // VGA输出个数
        public int[]            nVGA = new int[128];                  // VGA输出
        public int              nTVCount;                             // TV输出个数
        public int[]            nTV = new int[128];                   // TV输出
        public byte[]           reserved = new byte[512];
    }

    public static class NET_VIDEOCHANNELS_INPUT extends SdkStructure
    {
        public int              nThermographyCount;                   // 热成像通道个数
        public int[]            nThermography = new int[64];          // 热成像通道的通道号
        public int              nMultiPreviewCount;                   // 多画面预览通道个数
        public int[]            nMultiPreview = new int[4];           // 多画面预览通道号
        public int              nPIPCount;                            // 画中画通道个数
        public int[]            nPIP = new int[4];                    // 画中画通道号
        public int              nCompressPlayCount;                   // 二次压缩回放通道个数
        public int[]            nCompressPlay = new int[4];           // 二次压缩回放通道号
        public int              nSDCount;                             // 球机通道个数
        public int[]            nSD = new int[64];                    // 球机通道号
        public int              nPTZCount;                            // 支持云台程序的通道数量
        public short[]          nPTZ = new short[64];                 // 支持云台程序的通道号
        public int              nFuseRadarCount;                      // 支持融合雷达流，可见光叠加雷达流的通道数量
        public int[]            nFuseRadar = new int[64];             // 支持融合雷达流，可见光叠加雷达流的通道号
        public int              nPureRadarCount;                      // 支持纯雷达流，无可见光的通道数量
        public int[]            nPureRadar = new int[64];             // 支持纯雷达流，无可见光的通道号
        public byte[]           reserved = new byte[4096];
    }
    // 云台控制扩展命令
    public static class NET_EXTPTZ_ControlType extends SdkStructure
    {
        public static final int   NET_EXTPTZ_LEFTTOP = 0x20;            //左上
        public static final int   NET_EXTPTZ_RIGHTTOP = NET_EXTPTZ_LEFTTOP+1; //右上
        public static final int   NET_EXTPTZ_LEFTDOWN = NET_EXTPTZ_RIGHTTOP+1; //左下
        public static final int   NET_EXTPTZ_RIGHTDOWN = NET_EXTPTZ_LEFTDOWN+1; //右下
        public static final int   NET_EXTPTZ_ADDTOLOOP = NET_EXTPTZ_RIGHTDOWN+1; //加入预置点到巡航巡航线路预置点值
        public static final int   NET_EXTPTZ_DELFROMLOOP = NET_EXTPTZ_ADDTOLOOP+1; //删除巡航中预置点巡航线路预置点值
        public static final int   NET_EXTPTZ_CLOSELOOP = NET_EXTPTZ_DELFROMLOOP+1; //清除巡航巡航线路
        public static final int   NET_EXTPTZ_STARTPANCRUISE = NET_EXTPTZ_CLOSELOOP+1; //开始水平旋转
        public static final int   NET_EXTPTZ_STOPPANCRUISE = NET_EXTPTZ_STARTPANCRUISE+1; //停止水平旋转
        public static final int   NET_EXTPTZ_SETLEFTBORDER = NET_EXTPTZ_STOPPANCRUISE+1; //设置左边界
        public static final int   NET_EXTPTZ_SETRIGHTBORDER = NET_EXTPTZ_SETLEFTBORDER+1; //设置右边界
        public static final int   NET_EXTPTZ_STARTLINESCAN = NET_EXTPTZ_SETRIGHTBORDER+1; //开始线扫
        public static final int   NET_EXTPTZ_CLOSELINESCAN = NET_EXTPTZ_STARTLINESCAN+1; //停止线扫
        public static final int   NET_EXTPTZ_SETMODESTART = NET_EXTPTZ_CLOSELINESCAN+1; //设置模式开始模式线路
        public static final int   NET_EXTPTZ_SETMODESTOP = NET_EXTPTZ_SETMODESTART+1; //设置模式结束模式线路
        public static final int   NET_EXTPTZ_RUNMODE = NET_EXTPTZ_SETMODESTOP+1; //运行模式模式线路
        public static final int   NET_EXTPTZ_STOPMODE = NET_EXTPTZ_RUNMODE+1; //停止模式模式线路
        public static final int   NET_EXTPTZ_DELETEMODE = NET_EXTPTZ_STOPMODE+1; //清除模式模式线路
        public static final int   NET_EXTPTZ_REVERSECOMM = NET_EXTPTZ_DELETEMODE+1; //翻转命令
        public static final int   NET_EXTPTZ_FASTGOTO = NET_EXTPTZ_REVERSECOMM+1; //快速定位水平坐标(8192)垂直坐标(8192)变倍(4)
        public static final int   NET_EXTPTZ_AUXIOPEN = NET_EXTPTZ_FASTGOTO+1; //辅助开关开辅助点
        public static final int   NET_EXTPTZ_AUXICLOSE = NET_EXTPTZ_AUXIOPEN+1; //辅助开关关辅助点
        public static final int   NET_EXTPTZ_OPENMENU = 0x36;           //打开球机菜单
        public static final int   NET_EXTPTZ_CLOSEMENU = NET_EXTPTZ_OPENMENU+1; //关闭菜单
        public static final int   NET_EXTPTZ_MENUOK = NET_EXTPTZ_CLOSEMENU+1; //菜单确定
        public static final int   NET_EXTPTZ_MENUCANCEL = NET_EXTPTZ_MENUOK+1; //菜单取消
        public static final int   NET_EXTPTZ_MENUUP = NET_EXTPTZ_MENUCANCEL+1; //菜单上
        public static final int   NET_EXTPTZ_MENUDOWN = NET_EXTPTZ_MENUUP+1; //菜单下
        public static final int   NET_EXTPTZ_MENULEFT = NET_EXTPTZ_MENUDOWN+1; //菜单左
        public static final int   NET_EXTPTZ_MENURIGHT = NET_EXTPTZ_MENULEFT+1; //菜单右
        public static final int   NET_EXTPTZ_ALARMHANDLE = 0x40;        //报警联动云台parm1：报警输入通道；parm2：报警联动类型1-预置点2-线扫3-巡航；parm3：联动值，如预置点号
        public static final int   NET_EXTPTZ_MATRIXSWITCH = 0x41;       //矩阵切换parm1：预览器号(视频输出号)；parm2：视频输入号；parm3：矩阵号
        public static final int   NET_EXTPTZ_LIGHTCONTROL = NET_EXTPTZ_MATRIXSWITCH+1; //灯光控制器
        public static final int   NET_EXTPTZ_EXACTGOTO = NET_EXTPTZ_LIGHTCONTROL+1; //三维精确定位parm1：水平角度(0~3600)；parm2：垂直坐标(0~900)；parm3：变倍(1~128)
        public static final int   NET_EXTPTZ_RESETZERO = NET_EXTPTZ_EXACTGOTO+1; //三维定位重设零位
        public static final int   NET_EXTPTZ_MOVE_ABSOLUTELY = NET_EXTPTZ_RESETZERO+1; //绝对移动控制命令，param4对应结构PTZ_CONTROL_ABSOLUTELY
        public static final int   NET_EXTPTZ_MOVE_CONTINUOUSLY = NET_EXTPTZ_MOVE_ABSOLUTELY+1; //持续移动控制命令，param4对应结构PTZ_CONTROL_CONTINUOUSLY
        public static final int   NET_EXTPTZ_GOTOPRESET = NET_EXTPTZ_MOVE_CONTINUOUSLY+1; //云台控制命令，以一定速度转到预置位点，parm4对应结构PTZ_CONTROL_GOTOPRESET
        public static final int   NET_EXTPTZ_SET_VIEW_RANGE = 0x49;     //设置可视域(param4对应结构PTZ_VIEW_RANGE_INFO)
        public static final int   NET_EXTPTZ_FOCUS_ABSOLUTELY = 0x4A;   //绝对聚焦(param4对应结构PTZ_FOCUS_ABSOLUTELY)
        public static final int   NET_EXTPTZ_HORSECTORSCAN = 0x4B;      //水平扇扫(param4对应PTZ_CONTROL_SECTORSCAN,param1、param2、param3无效)
        public static final int   NET_EXTPTZ_VERSECTORSCAN = 0x4C;      //垂直扇扫(param4对应PTZ_CONTROL_SECTORSCAN,param1、param2、param3无效)
        public static final int   NET_EXTPTZ_SET_ABS_ZOOMFOCUS = 0x4D;  //设定绝对焦距、聚焦值,param1为焦距,范围:0,255],param2为聚焦,范围:[0,255],param3、param4无效
        public static final int   NET_EXTPTZ_SET_FISHEYE_EPTZ = 0x4E;   //控制鱼眼电子云台，param4对应结构PTZ_CONTROL_SET_FISHEYE_EPTZ
        public static final int   NET_EXTPTZ_UP_TELE = 0x70;            //上 + TELE param1=速度(1-8)，下同
        public static final int   NET_EXTPTZ_DOWN_TELE = NET_EXTPTZ_UP_TELE+1; //下 + TELE
        public static final int   NET_EXTPTZ_LEFT_TELE = NET_EXTPTZ_DOWN_TELE+1; //左 + TELE
        public static final int   NET_EXTPTZ_RIGHT_TELE = NET_EXTPTZ_LEFT_TELE+1; //右 + TELE
        public static final int   NET_EXTPTZ_LEFTUP_TELE = NET_EXTPTZ_RIGHT_TELE+1; //左上 + TELE
        public static final int   NET_EXTPTZ_LEFTDOWN_TELE = NET_EXTPTZ_LEFTUP_TELE+1; //左下 + TELE
        public static final int   NET_EXTPTZ_TIGHTUP_TELE = NET_EXTPTZ_LEFTDOWN_TELE+1; //右上 + TELE
        public static final int   NET_EXTPTZ_RIGHTDOWN_TELE = NET_EXTPTZ_TIGHTUP_TELE+1; //右下 + TELE
        public static final int   NET_EXTPTZ_UP_WIDE = NET_EXTPTZ_RIGHTDOWN_TELE+1; // 上 + WIDEparam1=速度(1-8)，下同
        public static final int   NET_EXTPTZ_DOWN_WIDE = NET_EXTPTZ_UP_WIDE+1; //下 + WIDE
        public static final int   NET_EXTPTZ_LEFT_WIDE = NET_EXTPTZ_DOWN_WIDE+1; //左 + WIDE
        public static final int   NET_EXTPTZ_RIGHT_WIDE = NET_EXTPTZ_LEFT_WIDE+1; //右 + WIDE
        public static final int   NET_EXTPTZ_LEFTUP_WIDE = NET_EXTPTZ_RIGHT_WIDE+1; //左上 + WIDENET_IN_PTZBASE_SET_FOCUS_MAP_VALUE_INFO
        public static final int   NET_EXTPTZ_LEFTDOWN_WIDE = NET_EXTPTZ_LEFTUP_WIDE+1; //左下 + WIDE
        public static final int   NET_EXTPTZ_TIGHTUP_WIDE = NET_EXTPTZ_LEFTDOWN_WIDE+1; //右上 + WIDE
        public static final int   NET_EXTPTZ_RIGHTDOWN_WIDE = NET_EXTPTZ_TIGHTUP_WIDE+1; //右下 + WIDE
        public static final int   NET_EXTPTZ_GOTOPRESETSNAP = 0x80;     // 转至预置点并抓图
        public static final int   NET_EXTPTZ_DIRECTIONCALIBRATION = 0x82; // 校准云台方向（双方向校准）
        public static final int   NET_EXTPTZ_SINGLEDIRECTIONCALIBRATION = 0x83; // 校准云台方向（单防线校准）,param4对应结构 NET_IN_CALIBRATE_SINGLEDIRECTION
        public static final int   NET_EXTPTZ_MOVE_RELATIVELY = 0x84;    // 云台相对定位,param4对应结构 NET_IN_MOVERELATIVELY_INFO
        public static final int   NET_EXTPTZ_SET_DIRECTION = 0x85;      // 设置云台方向, param4对应结构 NET_IN_SET_DIRECTION_INFO
        public static final int   NET_EXTPTZ_BASE_MOVE_ABSOLUTELY = 0x86; // 精准绝对移动控制命令, param4对应结构 NET_IN_PTZBASE_MOVEABSOLUTELY_INFO（通过 CFG_CAP_CMD_PTZ 命令获取云台能力集( CFG_PTZ_PROTOCOL_CAPS_INFO )，若bSupportReal为TRUE则设备支持该操作）
        public static final int   NET_EXTPTZ_BASE_MOVE_CONTINUOUSLY = NET_EXTPTZ_BASE_MOVE_ABSOLUTELY+1; // 云台连续移动控制命令, param4 对应结构 NET_IN_PTZBASE_MOVE_CONTINUOUSLY_INFO.  通过 CFG_CAP_CMD_PTZ 命令获取云台能力集
        // 若 CFG_PTZ_PROTOCOL_CAPS_INFO 中 stuMoveContinuously 字段的 stuType.bSupportExtra 为 TRUE, 表示设备支持该操作
        public static final int   NET_EXTPTZ_BASE_SET_FOCUS_MAP_VALUE = NET_EXTPTZ_BASE_MOVE_CONTINUOUSLY+1; // 设置当前位置聚焦值, param4对应结构体 NET_IN_PTZBASE_SET_FOCUS_MAP_VALUE_INFO
        public static final int   NET_EXTPTZ_BASE_MOVE_ABSOLUTELY_ONLYPT = NET_EXTPTZ_BASE_SET_FOCUS_MAP_VALUE+1; // 绝对定位独立控制PT并能以度/秒为单位的速度控制, param4对应结构 NET_IN_PTZBASE_MOVEABSOLUTELY_ONLYPT_INFO
        public static final int   NET_EXTPTZ_BASE_MOVE_ABSOLUTELY_ONLYZOOM = NET_EXTPTZ_BASE_MOVE_ABSOLUTELY_ONLYPT+1; // 绝对定位独立控制zoom，并能控制变倍速度, param4对应结构 NET_IN_PTZBASE_MOVEABSOLUTELY_ONLYZOOM_INFO
        public static final int   NET_EXTPTZ_STOP_MOVE = NET_EXTPTZ_BASE_MOVE_ABSOLUTELY_ONLYZOOM+1; // 云台移动停止,同时也停止巡航模式,param4对应结构体 NET_IN_STOP_MOVE_INFO
        public static final int   NET_EXTPTZ_START = NET_EXTPTZ_STOP_MOVE+1; // 开始云台控制,param4对应结构体 NET_IN_PTZ_START_INFO
        public static final int   NET_EXTPTZ_STOP = NET_EXTPTZ_START+1; // 结束云台控制,param4对应结构体 NET_IN_PTZ_STOP_INFO
        public static final int   NET_EXTPTZ_START_PATTERN_RECORD = NET_EXTPTZ_STOP+1; // 开始模式记录,param4对应结构体 NET_IN_START_PATTERN_RECORD_INFO
        public static final int   NET_EXTPTZ_STOP_PATTERN_RECORD = NET_EXTPTZ_START_PATTERN_RECORD+1; // 停止模式记录,param4对应结构体 NET_IN_STOP_PATTERN_RECORD_INFO
        public static final int   NET_EXTPTZ_START_PATTERN_REPLAY = NET_EXTPTZ_STOP_PATTERN_RECORD+1; // 开始模式回放,param4对应结构体 NET_IN_START_PATTERN_REPLAY_INFO
        public static final int   NET_EXTPTZ_STOP_PATTERN_REPLAY = NET_EXTPTZ_START_PATTERN_REPLAY+1; // 停止模式回放,param4对应结构体 NET_IN_STOP_PATTERN_REPLAY_INFO
        public static final int   NET_EXTPTZ_MOVE_DIRECTLY = NET_EXTPTZ_STOP_PATTERN_REPLAY+1; // 云台三维定位, param4对应结构体 NET_IN_MOVE_DIRECTLY_INFO
        public static final int   NET_EXTPTZ_TOTAL = NET_EXTPTZ_MOVE_DIRECTLY+1; //最大命令值
    }
    // 设置连接设备超时时间和尝试次数
    public void CLIENT_SetConnectTime(int nWaitTime,int nTryTimes);
    // 设置登陆网络环境
    public void CLIENT_SetNetworkParam(NET_PARAM pNetParam);

    // CLIENT_LoginWithHighLevelSecurity 输入参数
    public static class NET_IN_LOGIN_WITH_HIGHLEVEL_SECURITY extends SdkStructure
    {
        public int              dwSize;                               // 结构体大小
        public byte[]           szIP = new byte[64];                  // IP
        public int              nPort;                                // 端口
        public byte[]           szUserName = new byte[64];            // 用户名
        public byte[]           szPassword = new byte[64];            // 密码
        public int              emSpecCap;                            // 登录模式
        public byte[]           byReserved = new byte[4];             // 字节对齐
        public Pointer          pCapParam;                            // 见 CLIENT_LoginEx 接口 pCapParam 与 nSpecCap 关系
        public int              emTLSCap;                             //登录的TLS模式，参考EM_LOGIN_TLS_TYPE，目前仅支持EM_LOGIN_SPEC_CAP_TCP，EM_LOGIN_SPEC_CAP_SERVER_CONN 模式下的 tls登陆

        public NET_IN_LOGIN_WITH_HIGHLEVEL_SECURITY()
        {
            this.dwSize = this.size();
        }// 此结构体大小
    }
    // CLIENT_LoginWithHighLevelSecurity 输出参数
    public static class NET_OUT_LOGIN_WITH_HIGHLEVEL_SECURITY extends SdkStructure
    {
        public int              dwSize;                               // 结构体大小
        public NET_DEVICEINFO_Ex stuDeviceInfo;                       // 设备信息
        public int              nError;                               // 错误码，见 CLIENT_Login 接口错误码
        public byte[]           byReserved = new byte[132];           // 预留字段

        public NET_OUT_LOGIN_WITH_HIGHLEVEL_SECURITY()
        {
            this.dwSize = this.size();
        }// 此结构体大小
    }

    // CLIENT_SnapPictureToFile 接口输入参数
    public static class NET_IN_SNAP_PIC_TO_FILE_PARAM extends SdkStructure {
        public int              dwSize;                               // 结构体大小
        public SNAP_PARAMS      stuParam;                             // 抓图参数, 其中mode字段仅一次性抓图, 不支持定时或持续抓图; 除了车载DVR, 其他设备仅支持每秒一张的抓图频率
        public byte[]           szFilePath = new byte[MAX_PATH];      // 写入文件的地址

        public NET_IN_SNAP_PIC_TO_FILE_PARAM() {
            this.dwSize = this.size();
            this.stuParam = new SNAP_PARAMS();
        }
    }

    //  CLIENT_SnapPictureToFile 接口输出参数
    public static class NET_OUT_SNAP_PIC_TO_FILE_PARAM extends SdkStructure {
        public int              dwSize;
        public Pointer          szPicBuf;                             // 图片内容,用户分配内存
        public int              dwPicBufLen;                          // 图片内容内存大小, 单位:字节
        public int              dwPicBufRetLen;                       // 返回的图片大小, 单位:字节

        public NET_OUT_SNAP_PIC_TO_FILE_PARAM() {
            this.dwSize = this.size();
        }

        public NET_OUT_SNAP_PIC_TO_FILE_PARAM(int nMaxBuf) {
            this.dwSize = this.size();
            this.dwPicBufLen = nMaxBuf;
            Memory mem = new Memory(nMaxBuf);
            mem.clear();
            this.szPicBuf = mem;
        }
    }

    // 抓图同步接口,将图片数据直接返回给用户
    // public boolean CLIENT_SnapPictureToFile(LLong lLoginID, NET_IN_SNAP_PIC_TO_FILE_PARAM pInParam, NET_OUT_SNAP_PIC_TO_FILE_PARAM pOutParam, int nWaitTime);
    public boolean CLIENT_SnapPictureToFile(LLong lLoginID,Pointer pInParam,Pointer pOutParam,int nWaitTime);

    // 查询设备状态(pBuf内存由用户申请释放)
    // pBuf指向char *,输出参数
    // pRetLen指向int *;输出参数，实际返回的数据长度，单位字节
    public boolean CLIENT_QueryDevState(LLong lLoginID,int nType,Pointer pBuf,int nBufLen,IntByReference pRetLen,int waittime);

    // 抓图回调函数原形(pBuf内存由SDK内部申请释放)
    // EncodeType 编码类型，10：表示jpeg图片      0：mpeg4    CmdSerial : 操作流水号，同步抓图的情况下用不上
    public interface fSnapRev extends Callback {
        public void invoke(LLong lLoginID,Pointer pBuf,int RevLen,int EncodeType,int CmdSerial,Pointer dwUser);
    }

    // 设置抓图回调函数, fSnapRev回调
    public void CLIENT_SetSnapRevCallBack(Callback OnSnapRevMessage,Pointer dwUser);

    // 抓图参数结构体
    public static class SNAP_PARAMS extends SdkStructure
    {
        public int              Channel;                              // 抓图的通道
        public int              Quality;                              // 画质；1~6
        public int              ImageSize;                            // 画面大小；0：QCIF,1：CIF,2：D1
        public int              mode;                                 // 抓图模式；-1:表示停止抓图, 0：表示请求一帧, 1：表示定时发送请求, 2：表示连续请求
        public int              InterSnap;                            // 时间单位秒；若mode=1表示定时发送请求时
        // 只有部分特殊设备(如：车载设备)支持通过该字段实现定时抓图时间间隔的配置
        // 建议通过 CFG_CMD_ENCODE 配置的stuSnapFormat[nSnapMode].stuVideoFormat.nFrameRate字段实现相关功能
        public int              CmdSerial;                            // 请求序列号，有效值范围 0~65535，超过范围会被截断为 unsigned short
        public int[]            Reserved = new int[4];
    }

    // 解析查询到的配置信息
    public boolean CLIENT_ParseData(String szCommand,byte[] szInBuffer,Pointer lpOutBuffer,int dwOutBufferSize,Pointer pReserved);
    public boolean CLIENT_SnapPictureEx(LLong lLoginID, SNAP_PARAMS stParam, IntByReference reserved);
    // 高安全级别登陆
    public LLong CLIENT_LoginWithHighLevelSecurity(NET_IN_LOGIN_WITH_HIGHLEVEL_SECURITY pstInParam,NET_OUT_LOGIN_WITH_HIGHLEVEL_SECURITY pstOutParam);
    // 关闭日志功能
    public boolean CLIENT_LogClose();
    //  JNA直接调用方法定义，SDK退出清理
    public void CLIENT_Cleanup();

    //  JNA直接调用方法定义，向设备注销
    public boolean CLIENT_Logout(LLong lLoginID);

    // 网络连接恢复回调函数原形
    public interface fHaveReConnect extends Callback {
        public void invoke(LLong lLoginID,String pchDVRIP,int nDVRPort,Pointer dwUser);
    }
    /************************************************************************
     ** 接口
     ***********************************************************************/
    //  JNA直接调用方法定义，cbDisConnect 实际情况并不回调Java代码，仅为定义可以使用如下方式进行定义。 fDisConnect 回调
    public boolean CLIENT_Init(Callback cbDisConnect,Pointer dwUser);
    /***********************************************************************
     ** 回调
     ***********************************************************************/
    //JNA Callback方法定义,断线回调
    public interface fDisConnect extends Callback {
        public void invoke(LLong lLoginID,String pchDVRIP,int nDVRPort,Pointer dwUser);
    }
    // 打开日志功能
    // pstLogPrintInfo指向LOG_SET_PRINT_INFO的指针
    public boolean CLIENT_LogOpen(LOG_SET_PRINT_INFO pstLogPrintInfo);
    //  JNA直接调用方法定义，设置断线重连成功回调函数，设置后SDK内部断线自动重连, fHaveReConnect 回调
    public void CLIENT_SetAutoReconnect(Callback cbAutoConnect,Pointer dwUser);

    public int CLIENT_GetLastError();
}
