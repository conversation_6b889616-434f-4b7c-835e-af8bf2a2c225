package com.hollysys.inspection.sdk.hik;

import com.sun.jna.*;
import com.sun.jna.ptr.IntByReference;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.List;

public interface HCNetSDK extends Library {
    int SERIALNO_LEN = 48;   // 序列号长度
    int NET_SDK_INIT_CFG_SDK_PATH = 2; // 设置HCNetSDK库所在目录
    int NET_SDK_INIT_CFG_LIBEAY_PATH = 3; // 设置OpenSSL的libeay32.dll/libcrypto.so/libcrypto.dylib所在路径
    int NET_SDK_INIT_CFG_SSLEAY_PATH = 4;  // 设置OpenSSL的ssleay32.dll/libssl.so/libssl.dylib所在路径
    int NET_SDK_MAX_FILE_PATH = 256;// 路径长度
    int NET_DVR_DEV_ADDRESS_MAX_LEN = 129;
    int NET_DVR_LOGIN_USERNAME_MAX_LEN = 64;
    int NET_DVR_LOGIN_PASSWD_MAX_LEN = 64;
    int NET_DVR_GET_IPPARACFG = 1048;    // 获取IP接入配置信息

    /**********************
     * 云台控制命令 begin
     *************************/
    int SET_PRESET = 8;    /* 设置预置点 */
    int CLE_PRESET = 9;    /* 清除预置点 */
    int GOTO_PRESET = 39;    /* 快球转到预置点 */
    int NET_DVR_SET_PRESET_NAME = 3382;
    int MAX_DAYS = 7;
    int MAX_TIMESEGMENT_V30 = 8;
    int NET_DVR_GET_RECORDCFG_V40 = 1008;

    int NAME_LEN = 32;    // 用户名长度
    int PASSWD_LEN = 16;    // 密码长度
    int MAX_IP_DEVICE = 32;    // 允许接入的最大IP设备数
    int MAX_ANALOG_CHANNUM = 32;    // 最大32个模拟通道
    int MAX_IP_CHANNEL = 32;   // 允许加入的最多IP通道数
    /*************************************************
     * 回放时播放控制命令宏定义
     * NET_DVR_PlayBackControl
     * NET_DVR_PlayControlLocDisplay
     * NET_DVR_DecPlayBackCtrl的宏定义
     * 具体支持查看函数说明和代码
     **************************************************/
    public static final int NET_DVR_PLAYSTART = 1;//开始播放
    public static final int NET_DVR_PLAYSTOP = 2;//停止播放
    public static final int NET_DVR_PLAYPAUSE = 3;//暂停播放
    public static final int NET_DVR_PLAYRESTART = 4;//恢复播放
    public static final int NET_DVR_PLAYFAST = 5;//快放
    public static final int NET_DVR_PLAYSLOW = 6;//慢放
    public static final int NET_DVR_PLAYNORMAL = 7;//正常速度
    public static final int NET_DVR_PLAYFRAME = 8;//单帧放
    public static final int NET_DVR_PLAYSTARTAUDIO = 9;//打开声音
    public static final int NET_DVR_PLAYSTOPAUDIO = 10;//关闭声音
    public static final int NET_DVR_PLAYAUDIOVOLUME = 11;//调节音量
    public static final int NET_DVR_PLAYSETPOS = 12;//改变文件回放的进度
    public static final int NET_DVR_PLAYGETPOS = 13;//获取文件回放的进度
    public static final int NET_DVR_PLAYGETTIME = 14;//获取当前已经播放的时间(按文件回放的时候有效)
    public static final int NET_DVR_PLAYGETFRAME = 15;//获取当前已经播放的帧数(按文件回放的时候有效)
    public static final int NET_DVR_GETTOTALFRAMES = 16;//获取当前播放文件总的帧数(按文件回放的时候有效)
    public static final int NET_DVR_GETTOTALTIME = 17;//获取当前播放文件总的时间(按文件回放的时候有效)
    public static final int NET_DVR_THROWBFRAME = 20;//丢B帧
    public static final int NET_DVR_SETSPEED = 24;//设置码流速度
    public static final int NET_DVR_KEEPALIVE = 25;//保持与设备的心跳(如果回调阻塞，建议2秒发送一次)
    public static final int NET_DVR_SET_TRANS_TYPE = 32; //设置转码格式

    class HIKSDKStructure extends Structure {
        protected List getFieldOrder() {
            List fieldOrderList = new ArrayList();
            for (Class cls = getClass();
                 !cls.equals(HIKSDKStructure.class);
                 cls = cls.getSuperclass()) {
                Field[] fields = cls.getDeclaredFields();
                int modifiers;
                for (Field field : fields) {
                    modifiers = field.getModifiers();
                    if (Modifier.isStatic(modifiers) || !Modifier.isPublic(modifiers)) {
                        continue;
                    }
                    fieldOrderList.add(field.getName());
                }
            }
            return fieldOrderList;
        }
    }

    class NET_DVR_IPPARACFG extends HIKSDKStructure {/* IP接入配置结构 */
        public int dwSize;                                        /* 结构大小 */
        public NET_DVR_IPDEVINFO[] struIPDevInfo = new NET_DVR_IPDEVINFO[MAX_IP_DEVICE];    /* IP设备 */
        public byte[] byAnalogChanEnable = new byte[MAX_ANALOG_CHANNUM];        /* 模拟通道是否启用，从低到高表示1-32通道，0表示无效 1有效 */
        public NET_DVR_IPCHANINFO[] struIPChanInfo = new NET_DVR_IPCHANINFO[MAX_IP_CHANNEL];    /* IP通道 */
    }

    // IPC接入参数配置
    class NET_DVR_IPDEVINFO extends HIKSDKStructure {/* IP设备结构 */
        public int dwEnable;                    /* 该IP设备是否启用 */
        public byte[] sUserName = new byte[NAME_LEN];        /* 用户名 */
        public byte[] sPassword = new byte[PASSWD_LEN];        /* 密码 */
        public NET_DVR_IPADDR struIP = new NET_DVR_IPADDR();            /* IP地址 */
        public short wDVRPort;                    /* 端口号 */
        public byte[] byres = new byte[34];                /* 保留 */
    }

    class NET_DVR_IPCHANINFO extends HIKSDKStructure {/* IP通道匹配参数 */
        public byte byEnable;                    /* 该通道是否启用 */
        public byte byIPID;                    /* IP设备ID 取值1- MAX_IP_DEVICE */
        public byte byChannel;                    /* 通道号 */
        public byte[] byres = new byte[33];                    /* 保留 */
    }

    class NET_DVR_IPADDR extends HIKSDKStructure {
        public byte[] sIpV4 = new byte[16];
        public byte[] byRes = new byte[128];

        public String toString() {
            return "NET_DVR_IPADDR.sIpV4: " + new String(sIpV4) + "\n" + "NET_DVR_IPADDR.byRes: " + new String(byRes) + "\n";
        }
    }

    class NET_DVR_RECORD_V40 extends HIKSDKStructure {
        public int dwSize;
        public int dwRecord;/*是否录像 0-否 1-是*/
        public NET_DVR_RECORDDAY_V40[] struRecAllDay = new NET_DVR_RECORDDAY_V40[MAX_DAYS];
        public RECORDSCHEDARRAY[] struRecordSched = new RECORDSCHEDARRAY[MAX_DAYS];
        public int dwRecordTime;/* 录象延时长度 0-5秒， 1-10秒， 2-30秒， 3-1分钟， 4-2分钟， 5-5分钟， 6-10分钟*/
        public int dwPreRecordTime;/* 预录时间 0-不预录 1-5秒 2-10秒 3-15秒 4-20秒 5-25秒 6-30秒 7-0xffffffff(尽可能预录) */
        public int dwRecorderDuration;/* 录像保存的最长时间 */
        public byte byRedundancyRec;/*是否冗余录像,重要数据双备份：0/1*/
        public byte byAudioRec;/*录像时复合流编码时是否记录音频数据：国外有此法规*/
        public byte byStreamType;// 0-主码流，1-子码流，2-主子码流同时 3-三码流
        public byte byPassbackRecord;// 0:不回传录像 1：回传录像
        public short wLockDuration;// 录像锁定时长，单位小时 0表示不锁定，0xffff表示永久锁定，录像段的时长大于锁定的持续时长的录像，将不会锁定
        public byte byRecordBackup;// 0:录像不存档 1：录像存档
        public byte bySVCLevel;// SVC抽帧类型：0-不抽，1-抽二分之一 2-抽四分之三
        public byte byRecordManage;// 录像调度，0-启用， 1-不启用; 启用时进行定时录像；不启用时不进行定时录像，但是录像计划仍在使用，比如移动侦测，回传都还在按这条录像计划进行
        public byte byExtraSaveAudio;// 音频单独存储
        /*开启智能录像功能后，算法库是自动启用智能录像算法，其功能为若录像中无目标出现，会降低码率、帧率，而目标出现时又恢复全码率及帧率，从而达到减少资源消耗的目的*/
        public byte byIntelligentRecord;// 是否开启智能录像功能 0-否 1-是
        public byte[] byRes = new byte[125];
    }

    class NET_DVR_SCHEDTIME extends HIKSDKStructure {
        public byte byStartHour;    // 开始时间
        public byte byStartMin;
        public byte byStopHour;            // 结束时间
        public byte byStopMin;

    }

    // 时间段录像参数
    class NET_DVR_RECORDSCHED_V40 extends HIKSDKStructure {
        public NET_DVR_SCHEDTIME struRecordTime; // 录像时间段
        public byte byRecordType;
        /*录像类型，0:定时录像，1:移动侦测，2:报警录像，3:动测|报警，4:动测&报警 5:命令触发,
        6-智能报警录像，10-PIR报警，11-无线报警，12-呼救报警，13-全部事件,14-智能交通事件,
        15-越界侦测,16-区域入侵,17-声音异常,18-场景变更侦测,
        19-智能侦测(越界侦测|区域入侵|人脸侦测|声音异常|场景变更侦测),20－人脸侦测,21-POS录像,
        22-进入区域侦测, 23-离开区域侦测,24-徘徊侦测,25-人员聚集侦测,26-快速运动侦测,27-停车侦测,
        28-物品遗留侦测,29-物品拿取侦测,30-火点检测，31-防破坏检测,32-打架斗殴事件(司法),33-起身事件(司法), 34-瞌睡事件(司法)
        35-船只检测, 36-测温预警，37-测温报警，38-温差报警，39-离线测温报警,40-防区报警，41-紧急求助,42-业务咨询,43-起身检测,44-折线攀高,45-如厕超时，46-人脸抓拍，47-非法摆摊,48-目标抓拍,
        49-剧烈运动，50离岗检测，51-起立，52人数变化 53-废气排放显示 54-灰度报警*/
        public byte[] byRes = new byte[31];
    }

    class RECORDSCHEDARRAY extends HIKSDKStructure {
        public NET_DVR_RECORDSCHED_V40[] wRightPlan = new NET_DVR_RECORDSCHED_V40[MAX_TIMESEGMENT_V30];
    }

    class NET_DVR_RECORDDAY_V40 extends HIKSDKStructure {
        public byte byAllDayRecord; /* 是否全天录像 0-否 1-是*/
        /*录像类型，0:定时录像，1:移动侦测，2:报警录像，3:动测|报警，4:动测&报警 5:命令触发,
        6-智能报警录像，10-PIR报警，11-无线报警，12-呼救报警，13-全部事件,14-智能交通事件,
        15-越界侦测,16-区域入侵,17-声音异常,18-场景变更侦测,
        19-智能侦测(越界侦测|区域入侵|人脸侦测|声音异常|场景变更侦测),20－人脸侦测,21-POS录像,
        22-进入区域侦测, 23-离开区域侦测,24-徘徊侦测,25-人员聚集侦测,26-快速运动侦测,27-停车侦测,
        28-物品遗留侦测,29-物品拿取侦测,30-火点检测，31-防破坏检测,32-打架斗殴事件(司法),33-起身事件(司法), 34-瞌睡事件(司法)
        35-船只检测, 36-测温预警，37-测温报警，38-温差报警，39-离线测温报警,40-防区报警，41-紧急求助,42-业务咨询,43-起身检测,44-折线攀高,45-如厕超时,46-人脸抓拍,47-非法摆摊,48-目标抓拍,
        49-剧烈运动，50离岗检测，51-起立，52人数变化  53-废气排放显示  54-灰度报警*/
        public byte byRecordType;
        public byte[] byRes = new byte[62];
    }

    boolean NET_DVR_Init();

    boolean NET_DVR_Logout(int lUserID);

    boolean NET_DVR_Cleanup();

    // 图片质量
    class NET_DVR_JPEGPARA extends HIKSDKStructure {
        /*注意：当图像压缩分辨率为VGA时，支持0=CIF, 1=QCIF, 2=D1抓图，
	当分辨率为3=UXGA(1600x1200), 4=SVGA(800x600), 5=HD720p(1280x720),6=VGA,7=XVGA, 8=HD900p
	仅支持当前分辨率的抓图*/
        public short wPicSize;                /* 0=CIF, 1=QCIF, 2=D1 3=UXGA(1600x1200), 4=SVGA(800x600), 5=HD720p(1280x720),6=VGA*/
        public short wPicQuality;            /* 图片质量系数 0-最好 1-较好 2-一般 */
    }

    // 区域框参数
    class NET_VCA_RECT extends HIKSDKStructure {
        public float fX;
        public float fY;
        public float fWidth;
        public float fHeight;
    }

    /**
     * 热成像相关
     */
    // 设备抓图附加全屏测温数据结构体
    class NET_DVR_JPEGPICTURE_WITH_APPENDDATA extends HIKSDKStructure {
        public int dwSize;
        public int dwChannel;// 通道号
        public int dwJpegPicLen;// Jpeg图片长度
        public Pointer pJpegPicBuff;// Jpeg图片指针
        public int dwJpegPicWidth;  // 图像宽度
        public int dwJpegPicHeight;  // 图像高度
        public int dwP2PDataLen;// 全屏测温数据长度
        public Pointer pP2PDataBuff; // 全屏测温数据指针
        public int dwVisiblePicLen; // 可见光图片长度
        public Pointer pVisiblePicBuff; // 可见光图片指针
        public NET_VCA_RECT struThermalValidRect; // 热成像有效区域
        public NET_VCA_RECT struVisibleValidRect;// 可见光有效区域
    }

    class NET_DVR_LOCAL_SDK_PATH extends HIKSDKStructure {
        public byte[] sPath = new byte[NET_SDK_MAX_FILE_PATH];// 组件库地址
        public byte[] byRes = new byte[128];
    }

    // NET_DVR_Login_V30()参数结构
    class NET_DVR_DEVICEINFO_V40 extends HIKSDKStructure {
        public NET_DVR_DEVICEINFO_V30 struDeviceV30 = new NET_DVR_DEVICEINFO_V30();
        public byte bySupportLock;
        public byte byRetryLoginTime;
        public byte byPasswordLevel;
        public byte byRes1;
        public int dwSurplusLockTime;
        public byte byCharEncodeType;// 字符编码类型：0- 无字符编码信息(老设备)，1- GB2312(简体中文)，2- GBK，3- BIG5(繁体中文)，4- Shift_JIS(日文)，5- EUC-KR(韩文)，6- UTF-8，7- ISO8859-1，8- ISO8859-2，9- ISO8859-3，…，依次类推，21- ISO8859-15(西欧)
        public byte bySupportDev5; // 支持v50版本的设备参数获取，设备名称和设备类型名称长度扩展为64字节
        public byte bySupport;  // 能力集扩展，位与结果：0- 不支持，1- 支持
        public byte byLoginMode; // 登录模式 0-Private登录 1-ISAPI登录
        public int dwOEMCode;
        public int iResidualValidity;   // 该用户密码剩余有效天数，单位：天，返回负值，表示密码已经超期使用，例如“-3表示密码已经超期使用3天”
        public byte byResidualValidity; // iResidualValidity字段是否有效，0-无效，1-有效
        public byte bySingleStartDTalkChan;    // 独立音轨接入的设备，起始接入通道号，0-为保留字节，无实际含义，音轨通道号不能从0开始
        public byte bySingleDTalkChanNums;    // 独立音轨接入的设备的通道总数，0-表示不支持
        public byte byPassWordResetLevel; // 0-无效，1-管理员创建一个非管理员用户为其设置密码，该非管理员用户正确登录设备后要提示“请修改初始登录密码”，未修改的情况下，用户每次登入都会进行提醒；2-当非管理员用户的密码被管理员修改，该非管理员用户再次正确登录设备后，需要提示“请重新设置登录密码”，未修改的情况下，用户每次登入都会进行提醒。
        public byte bySupportStreamEncrypt;  // 能力集扩展，位与结果：0- 不支持，1- 支持 bySupportStreamEncrypt & 0x1:表示是否支持RTP/TLS取流 bySupportStreamEncrypt & 0x2:  表示是否支持SRTP/UDP取流 bySupportStreamEncrypt & 0x4:  表示是否支持SRTP/MULTICAST取流
        public byte byMarketType;// 0-无效（未知类型）,1-经销型，2-行业型
        public byte[] byRes2 = new byte[238];
    }

    class NET_DVR_DEVICEINFO_V30 extends HIKSDKStructure {
        public byte[] sSerialNumber = new byte[SERIALNO_LEN];  // 序列号
        public byte byAlarmInPortNum;    // 报警输入个数
        public byte byAlarmOutPortNum;   // 报警输出个数
        public byte byDiskNum;           // 硬盘个数
        public byte byDVRType;         // 设备类型, 1:DVR 2:ATM DVR 3:DVS ......
        public byte byChanNum;         // 模拟通道个数
        public byte byStartChan;      // 起始通道号,例如DVS-1,DVR - 1
        public byte byAudioChanNum;    // 语音通道数
        public byte byIPChanNum;     // 最大数字通道个数，低位
        public byte byZeroChanNum;    // 零通道编码个数 //2010-01-16
        public byte byMainProto;      // 主码流传输协议类型 0-private, 1-rtsp,2-同时支持private和rtsp
        public byte bySubProto;        // 子码流传输协议类型0-private, 1-rtsp,2-同时支持private和rtsp
        public byte bySupport;        // 能力，位与结果为0表示不支持，1表示支持，
        public byte bySupport1;        // 能力集扩充，位与结果为0表示不支持，1表示支持
        public byte bySupport2; /*能力*/
        public short wDevType;              // 设备型号
        public byte bySupport3; // 能力集扩展
        /**
         * 是否支持多码流，按位表示，位与结果：0-不支持，1-支持
         * byMultiStreamProto & 0x1, 表示是否支持码流3
         * byMultiStreamProto & 0x2, 表示是否支持码流4
         * byMultiStreamProto & 0x40,表示是否支持主码流
         * byMultiStreamProto & 0x80,表示是否支持子码流
         */
        public byte byMultiStreamProto;// 是否支持多码流,按位表示,0-不支持,1-支持,bit1-码流3,bit2-码流4,bit7-主码流，bit-8子码流
        public byte byStartDChan;        // 起始数字通道号,0表示无效
        public byte byStartDTalkChan;    // 起始数字对讲通道号，区别于模拟对讲通道号，0表示无效
        public byte byHighDChanNum;        // 数字通道个数，高位
        public byte bySupport4;        // 能力集扩展
        public byte byLanguageType;// 支持语种能力,按位表示,每一位0-不支持,1-支持
        //  byLanguageType 等于0 表示 老设备
        //  byLanguageType & 0x1表示支持中文
        //  byLanguageType & 0x2表示支持英文
        public byte byVoiceInChanNum;   // 音频输入通道数
        public byte byStartVoiceInChanNo; // 音频输入起始通道号 0表示无效
        public byte bySupport5;
        public byte bySupport6;   // 能力
        public byte byMirrorChanNum;    // 镜像通道个数，<录播主机中用于表示导播通道>
        public short wStartMirrorChanNo;  // 起始镜像通道号
        public byte bySupport7;   // 能力
        public byte byRes2;        // 保留
    }

    boolean NET_DVR_GetDVRConfig(int lUserID, int dwCommand, int lChannel, Pointer lpOutBuffer, int dwOutBufferSize, IntByReference lpBytesReturned);


    boolean NET_DVR_SetDVRConfig(int lUserID, int dwCommand, int lChannel, Pointer lpInBuffer, int dwInBufferSize);

    class NET_DVR_PRESET_NAME extends HIKSDKStructure {
        public int dwSize;
        public short wPresetNum;
        public byte[] byRes1 = new byte[2];
        public byte[] byName = new byte[32];
        public short wPanPos;
        public short wTiltPos;
        public short wZoomPos;
        public byte[] byRes = new byte[58];

        public NET_DVR_PRESET_NAME() {
            dwSize = this.size();
        }
    }


    // NET_DVR_Login_V40()参数
    class NET_DVR_USER_LOGIN_INFO extends HIKSDKStructure {
        public byte[] sDeviceAddress = new byte[NET_DVR_DEV_ADDRESS_MAX_LEN];
        public byte byUseTransport;
        public short wPort;
        public byte[] sUserName = new byte[NET_DVR_LOGIN_USERNAME_MAX_LEN];
        public byte[] sPassword = new byte[NET_DVR_LOGIN_PASSWD_MAX_LEN];
        public FLoginResultCallBack cbLoginResult;
        public Pointer pUser;
        public boolean bUseAsynLogin;
        public byte byProxyType; // 0:不使用代理，1：使用标准代理，2：使用EHome代理
        public byte byUseUTCTime;    // 0-不进行转换，默认,1-接口上输入输出全部使用UTC时间,SDK完成UTC时间与设备时区的转换,2-接口上输入输出全部使用平台本地时间，SDK完成平台本地时间与设备时区的转换
        public byte byLoginMode; // 0-Private 1-ISAPI 2-自适应
        public byte byHttps;    // 0-不适用tls，1-使用tls 2-自适应
        public int iProxyID;    // 代理服务器序号，添加代理服务器信息时，相对应的服务器数组下表值
        public byte byVerifyMode;  // 认证方式，0-不认证，1-双向认证，2-单向认证；认证仅在使用TLS的时候生效;
        public byte[] byRes2 = new byte[119];
    }

    //录像文件查找条件结构V50
    public static class NET_DVR_FILECOND_V50 extends HIKSDKStructure {
        public NET_DVR_STREAM_INFO struStreamID; //流ID或通道号
        public NET_DVR_TIME_SEARCH_COND struStartTime = new NET_DVR_TIME_SEARCH_COND(); //开始时间
        public NET_DVR_TIME_SEARCH_COND struStopTime = new NET_DVR_TIME_SEARCH_COND(); //结束时间
        public byte byFindType; //0-查询普通卷，1-查询存档卷 2-查询N+1录像文件
        public byte byDrawFrame; //是否抽帧 0-不抽帧 1-抽帧
        public byte byQuickSearch; //0-普通查询，1-快速（日历）查询
        public byte byStreamType; //0-主码流，1-子码流，2-3码流，0xff-全部
        public int dwFileType; // 文件类型
        public int dwVolumeNum; //存档卷号，byFindType为1时有效
        public byte byIsLocked; //是否锁定 0-正常文件,1-锁定文件, 0xff表示所有文件
        public byte byNeedCard; //是否需要查询卡，0-不需要 1-需要
        public byte byOnlyAudioFile;        //音频文件 0-视频文件 1-音频文件
        public byte bySpecialFindInfoType; //0-无效， 1-带ATM查询条件
        public byte[] szCardNum = new byte[32];  //卡号，byNeedCard为1时有效
        public byte[] szWorkingDeviceGUID = new byte[16]; //工作机GUID，通过获取N+1得到，byFindType为2时有效
        public NET_DVR_SPECIAL_FINDINFO_UNION uSpecialFindInfo = new NET_DVR_SPECIAL_FINDINFO_UNION(); //专有查询条件联合体
        public int dwTimeout; //查找超时时间（指定NET_DVR_FindNextFile_V30/NET_DVR_FindNextFile_V40/NET_DVR_FindNextFile_V50接口的超时时间返回）；单位：毫秒，不填写（默认为0时），接口行为跟以前一样 ;有效值：0, [5000 – 15000]
        public byte[] byRes = new byte[252];
    }

    public static class NET_DVR_STREAM_INFO extends HIKSDKStructure {
        public int dwSize;
        public byte[] byID = new byte[32];
        public int dwChannel;
        public byte[] byRes = new byte[32];
    }

    public static class NET_DVR_TIME_SEARCH_COND extends HIKSDKStructure {
        public short wYear; //年
        public byte byMonth; //月
        public byte byDay; //日
        public byte byHour; //时
        public byte byMinute; //分
        public byte bySecond; //秒
        public byte byLocalOrUTC; //0-时差无效，设备本地时间，即设备OSD时间  1-时差有效
        public short wMillisecond;       //毫秒，精度不够，默认为0
        public byte cTimeDifferenceH;   //与UTC的时差（小时），-12 ... +14，+表示东区，byLocalOrUTC为1时有效
        public byte cTimeDifferenceM;   //与UTC的时差（分钟），-30, 0, 30, 45，+表示东区，byLocalOrUTC为1时有效
    }

    public static class NET_DVR_SPECIAL_FINDINFO_UNION extends Union {
        public byte[] byLenth = new byte[8];
        public NET_DVR_ATMFINDINFO struATMFindInfo = new NET_DVR_ATMFINDINFO();           //ATM查询
    }

    public static class NET_DVR_ATMFINDINFO extends HIKSDKStructure {
        public byte byTransactionType;       //交易类型 0-全部，1-查询， 2-取款， 3-存款， 4-修改密码，5-转账， 6-无卡查询 7-无卡存款， 8-吞钞 9-吞卡 10-自定义
        public byte[] byRes = new byte[3];    //保留
        public int dwTransationAmount;     //交易金额 ;
    }

    public static class NET_DVR_FINDDATA_V50 extends HIKSDKStructure {
        public byte[] sFileName = new byte[100];
        public NET_DVR_TIME_SEARCH struStartTime = new NET_DVR_TIME_SEARCH();
        public NET_DVR_TIME_SEARCH struStopTime = new NET_DVR_TIME_SEARCH();
        public NET_DVR_ADDRESS struAddr = new NET_DVR_ADDRESS(); //片段所在的地址信息，集群回放时用到
        public int dwFileSize; //文件大小
        public byte byLocked; //文件是否被锁定，1－文件已锁定；0－文件未锁定
        public byte byFileType; //文件类型，与V40相同
        public byte byQuickSearch; //0- 普通查询结果，1- 快速（日历）查询结果
        public byte byStreamType; //码流类型：0- 主码流，1- 子码流，2- 码流三
        public int dwFileIndex; //文件索引号
        public byte[] sCardNum = new byte[32]; //卡号
        public int dwTotalLenH; // 对于大文件搜索，时间段内数据总长度，高32字节
        public int dwTotalLenL; // 对于大文件搜索，时间段内数据总长度，低32字节
        public byte byBigFileType;  // 0为普通片段搜索，1为大文件搜索
        public byte[] byRes = new byte[247];
    }

    public static class NET_DVR_TIME_SEARCH extends HIKSDKStructure {
        public short wYear;        //年，设备OSD时间
        public byte byMonth;        //月，设备OSD时间
        public byte byDay;        //日，设备OSD时间
        public byte byHour;        //时，设备OSD时间
        public byte byMinute;    //分，设备OSD时间
        public byte bySecond;    //秒，设备OSD时间
        public byte cTimeDifferenceH;        //与国际标准时间的时差（小时），-12 ... +14
        public byte cTimeDifferenceM;        //与国际标准时间的时差（分钟），-30, 0, 30, 45
        public byte byLocalOrUTC;      //0-时差无效，设备本地时间，即设备OSD时间  1-时差有效
        public short wMillisecond;      //毫秒，精度不够，默认为0
    }

    public static class NET_DVR_ADDRESS extends HIKSDKStructure {
        public NET_DVR_IPADDR struIP = new NET_DVR_IPADDR(); //IP地址
        public short wPort;    //端口号
        public byte[] byRes = new byte[2];
    }

    public static class NET_DVR_TIME extends HIKSDKStructure {//校时结构参数
        public int dwYear;        //年
        public int dwMonth;        //月
        public int dwDay;        //日
        public int dwHour;        //时
        public int dwMinute;        //分
        public int dwSecond;        //秒

        public String toString() {
            return "NET_DVR_TIME.dwYear: " + dwYear + "\n" + "NET_DVR_TIME.dwMonth: \n" + dwMonth + "\n" + "NET_DVR_TIME.dwDay: \n" + dwDay + "\n" + "NET_DVR_TIME.dwHour: \n" + dwHour + "\n" + "NET_DVR_TIME.dwMinute: \n" + dwMinute + "\n" + "NET_DVR_TIME.dwSecond: \n" + dwSecond;
        }

        //用于列表中显示
        public String toStringTime() {
            return String.format("%02d/%02d/%02d%02d:%02d:%02d", dwYear, dwMonth, dwDay, dwHour, dwMinute, dwSecond);
        }

        //存储文件名使用
        public String toStringTitle() {
            return String.format("Time%02d%02d%02d%02d%02d%02d", dwYear, dwMonth, dwDay, dwHour, dwMinute, dwSecond);
        }
    }

    public static class NET_DVR_FILECOND extends HIKSDKStructure //录象文件查找条件结构
    {
        public int lChannel;//通道号
        public int dwFileType;//录象文件类型0xff－全部，0－定时录像,1-移动侦测 ，2－报警触发，3-报警|移动侦测 4-报警&移动侦测 5-命令触发 6-手动录像
        public int dwIsLocked;//是否锁定 0-正常文件,1-锁定文件, 0xff表示所有文件
        public int dwUseCardNo;//是否使用卡号
        public byte[] sCardNumber = new byte[32];//卡号
        public NET_DVR_TIME struStartTime;//开始时间
        public NET_DVR_TIME struStopTime;//结束时间
    }

    interface FLoginResultCallBack extends Callback {
        int invoke(int lUserID, int dwResult, NET_DVR_DEVICEINFO_V30 lpDeviceinfo, Pointer pUser);
    }

    int NET_DVR_Login_V40(NET_DVR_USER_LOGIN_INFO pLoginInfo, NET_DVR_DEVICEINFO_V40 lpDeviceInfo);


    boolean NET_DVR_PTZControlWithSpeed_Other(int lUserID, int lChannel, int dwPTZCommand, int dwStop, int dwSpeed);

    boolean NET_DVR_PTZPreset_Other(int lUserID, int lChannel, int dwPTZPresetCmd, int dwPresetIndex);

    boolean NET_DVR_CaptureJPEGPicture_NEW(int lUserID, int lChannel, NET_DVR_JPEGPARA lpJpegPara, Pointer sJpegPicBuffer, int dwPicSize, IntByReference lpSizeReturned);

    int NET_DVR_GetLastError();

    int NET_DVR_SDKChannelToISAPI(int lUserID, int lInChannel, boolean bSDKToISAPI);

    class BYTE_ARRAY extends HIKSDKStructure {
        public byte[] byValue;

        public BYTE_ARRAY(int iLen) {
            byValue = new byte[iLen];
        }
    }

    boolean NET_DVR_SetSDKInitCfg(int enumType, Pointer lpInBuff);

    int NET_DVR_FindNextFile_V50(int lFindHandle, NET_DVR_FINDDATA_V50 lpFindData);

    boolean NET_DVR_FindClose_V30(int lFindHandle);

    int NET_DVR_GetFileByName(int lUserID, String sDVRFileName, byte[] sSavedFileName);

    boolean NET_DVR_PlayBackControl_V40(int lPlayHandle, int dwControlCode, Pointer lpInBuffer, int dwInLen, Pointer lpOutBuffer, IntByReference lpOutLen);

    boolean NET_DVR_StopGetFile(int lFileHandle);

    int NET_DVR_GetDownloadPos(int lFileHandle);

    int NET_DVR_FindFile_V30(int lUserID, NET_DVR_FILECOND pFindCond);
}
