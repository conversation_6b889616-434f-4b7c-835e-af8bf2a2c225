package com.hollysys.inspection.sdk.ys;

import com.sun.jna.*;
import com.sun.jna.ptr.IntByReference;
import com.sun.jna.win32.StdCallLibrary.StdCallCallback;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Classname NetDEVSDKLib
 * @Description TODO
 * @Date 2022/4/13 23:54
 */
public interface NetDEVSDKLib extends Library {

    public static final int	NETDEV_GET_STREAMCFG = 120;
    public static final int	NETDEV_GET_STREAMCFG_EX = 122;

    class SdkStructure extends Structure {
        @Override
        protected List<String> getFieldOrder() {
            List<String> fieldOrderList = new ArrayList<>();
            for (Class<?> cls = getClass();
                 !cls.equals(SdkStructure.class);
                 cls = cls.getSuperclass()) {
                Field[] fields = cls.getDeclaredFields();
                int modifiers;
                for (Field field : fields) {
                    modifiers = field.getModifiers();
                    if (Modifier.isStatic(modifiers) || !Modifier.isPublic(modifiers)) {
                        continue;
                    }
                    fieldOrderList.add(field.getName());
                }
            }
            return fieldOrderList;
        }

        @Override
        public int fieldOffset(String name) {
            return super.fieldOffset(name);
        }
    }

    public static final int NETDEV_LEN_2 = 2;
    public static final int NETDEV_LEN_4 = 4;
    public static final int NETDEV_IPSAN_MAX_NUM = 4;
    public static final int NETDEV_LEN_6 = 6;
    public static final int NETDEV_LEN_8 = 8;
    public static final int NETDEV_MAX_LINK_ACTION_NUM = 9;
    public static final int NETDEV_LEN_16 = 16;
    public static final int NETDEV_LEN_32 = 32;
    public static final int NETDEV_LEN_40 = 40;
    public static final int NETDEV_LEN_64 = 64;
    public static final int NETDEV_LEN_128 = 128;
    public static final int NETDEV_LEN_132 = 132;
    public static final int NETDEV_LEN_256 = 256;
    public static final int NETDEV_LEN_260 = 260;
    public static final int NETDEV_LEN_480 = 480;
    public static final int NETDEV_LEN_512 = 512;
    public static final int NETDEV_LEN_1024 = 1024;
    public static final int NETDEV_IPADDR_STR_MAX_LEN = 64;
    public static final int NETDEV_IPV4_LEN_MAX = 16;

    /**
     * @struct NETDEV_VIDEO_CHL_DETAIL_INFO_EX_S
     * @brief 视频通道详细信息 结构体定义  Structure definition
     * @attention 无 None
     */
    public class NETDEV_VIDEO_CHL_DETAIL_INFO_EX_S extends SdkStructure {
        public int dwChannelID;                        /* 通道ID  Channel ID */
        public int bPtzSupported;                      /* 是否支持云台 Whether ptz is supported */
        public int enStatus;       /* 通道状态  Channel status NETDEV_CHANNEL_STATUS_E */
        public int dwStreamNum;                        /* 流个数  Number of streams 当enStatus为 NETDEV_CHL_STATUS_UNBIND 时，此值无效*/
        public int enChannelType;                      /* 通道类型，(note: 该字段仅对混合NVR有效)，参考 NETDEV_CHANNEL_TYPE_E*/
        public int enVideoFormat;                      /* 视频输入制式，参考NETDEV_CHANNEL_TYPE_E，当ChannelType为NETDEV_CHL_TYPE_DIGITAL时，此值无效 (note: 该字段仅对混合NVR有效) */
        public int enAddressType;                      /* IP地址类型 ,参考 NETDEV_HOSTTYPE_E*/
        public byte[] szIPAddr = new byte[NETDEV_IPADDR_STR_MAX_LEN];/* IP地址 IP address*/
        public int dwPort;                             /* 端口号 */
        public byte[] szChnName = new byte[NETDEV_LEN_64];
        ;           /* 通道名称 Channel Name */
        public int allowDistribution;                   /* 是否允许流分发*/
        public int dwDeviceType;                       /* 通道接入的设备类型，参见 枚举 NETDEV_CHANNEL_CAMERA_TYPE_E. Channel device Type see#NETDEV_CHANNEL_CAMERA_TYPE_E */
        public byte[] szManufacturer = new byte[NETDEV_LEN_32];      /* 厂商，范围[0,31] */
        public byte[] szDeviceModel = new byte[NETDEV_LEN_32];       /* 设备型号，范围[0,31]  */
        public int udwAccessProtocol;                  /* 接入协议类型 参见 NETDEV_ACCESS_PROTOCOL_E */
        public Pointer pstExtendedInformation;            /* 附加属性, 需要自行申请内存 参见  NETDEV_VIDEO_CHL_DETAIL_EXTEND_INFO_S*/
        public byte[] byRes = new byte[16];                          /* 保留字段  Reserved field*/
    }

    /**
     * @brief 云台命令 枚举 定义 PTZ commands Enumeration definition
     * @attention 无 None
     */
    public static class NETDEV_PTZ_E extends Structure {
        public static final int	NETDEV_PTZ_IRISCLOSE_STOP       =0x0101;       /* 光圈关停止  Iris close stop */
        public static final int	NETDEV_PTZ_IRISCLOSE            =0x0102;       /* 光圈关  Iris close */
        public static final int	NETDEV_PTZ_IRISOPEN_STOP        =0x0103;       /* 光圈开停止  Iris open stop */
        public static final int	NETDEV_PTZ_IRISOPEN             =0x0104;       /* 光圈开  Iris open */

        public static final int	NETDEV_PTZ_FOCUSNEAR_STOP       =0x0201;       /* 近聚集停止  Focus near stop */
        public static final int	NETDEV_PTZ_FOCUSNEAR            =0x0202;       /* 近聚集  Focus near */
        public static final int	NETDEV_PTZ_FOCUSFAR_STOP        =0x0203;       /* 远聚集停止  Focus far stop */
        public static final int	NETDEV_PTZ_FOCUSFAR             =0x0204;       /* 远聚集  Focus far */

        public static final int	NETDEV_PTZ_ZOOMTELE_STOP        = 0x0301;       /* 放大停止  Zoom in stop */
        public static final int	NETDEV_PTZ_ZOOMTELE             = 0x0302;       /* 放大  Zoom in */
        public static final int	NETDEV_PTZ_ZOOMWIDE_STOP        = 0x0303;       /* 缩小停止  Zoom out stop */
        public static final int	NETDEV_PTZ_ZOOMWIDE             = 0x0304;       /* 缩小  Zoom out */
        public static final int	NETDEV_PTZ_TILTUP               = 0x0402;       /* 向上  Tilt up */
        public static final int	NETDEV_PTZ_TILTDOWN             = 0x0404;       /* 向下  Tilt down */
        public static final int	NETDEV_PTZ_PANRIGHT             = 0x0502;       /* 向右  Pan right */
        public static final int	NETDEV_PTZ_PANLEFT              = 0x0504;       /* 向左  Pan left */
        public static final int	NETDEV_PTZ_LEFTUP               = 0x0702;       /* 左上  Move up left */
        public static final int	NETDEV_PTZ_LEFTDOWN             = 0x0704;       /* 左下  Move down left */
        public static final int	NETDEV_PTZ_RIGHTUP              = 0x0802;       /* 右上  Move up right */
        public static final int	NETDEV_PTZ_RIGHTDOWN            = 0x0804;       /* 右下  Move down right */

        public static final int	NETDEV_PTZ_ALLSTOP              = 0x0901;       /* 全停命令字  All-stop command word */
        public static final int	NETDEV_PTZ_FOCUS_AND_IRIS_STOP  = 0x0907;       /* 聚焦.光圈停止  Focus & Iris-stop command word */
        public static final int	NETDEV_PTZ_MOVE_STOP            = 0x0908;       /* 移动停止命令字  move stop command word */
        public static final int	NETDEV_PTZ_ZOOM_STOP            = 0x0909;       /* 变倍停止命令字  zoom stop command word */
        //NETDEV_PTZ_TRACKCRUISE支持IPC
        public static final int	NETDEV_PTZ_TRACKCRUISE          = 0x1001;       /* 开始轨迹巡航  Start route patrol*/
        //NETDEV_PTZ_TRACKCRUISESTOP支持IPC
        public static final int	NETDEV_PTZ_TRACKCRUISESTOP      = 0x1002;       /* 停止轨迹巡航  Stop route patrol*/
        public static final int	NETDEV_PTZ_TRACKCRUISEREC       = 0x1003;       /* 开始录制轨迹  Start recording route */
        public static final int	NETDEV_PTZ_TRACKCRUISERECSTOP   = 0x1004;       /* 停止录制轨迹  Stop recording route */
        public static final int	NETDEV_PTZ_TRACKCRUISEADD       = 0x1005;       /* 添加巡航轨迹  Add patrol route */
        public static final int	NETDEV_PTZ_TRACKCRUISEDEL       = 0x1006;       /* 删除轨迹巡航  Delete patrol route */

        public static final int	NETDEV_PTZ_AREAZOOMIN           = 0x1101;       /* 拉框放大  Zoom in area */
        public static final int	NETDEV_PTZ_AREAZOOMOUT          = 0x1102;       /* 拉框缩小  Zoom out area */
        public static final int	NETDEV_PTZ_AREAZOOM3D           = 0x1103;       /* 3D定位  3D positioning */

        public static final int	NETDEV_PTZ_BRUSHON              = 0x0A01;       /* 雨刷开  Wiper on */
        public static final int	NETDEV_PTZ_BRUSHOFF             = 0x0A02;       /* 雨刷关  Wiper off */

        public static final int	NETDEV_PTZ_LIGHTON              = 0x0B01;       /* 灯开  Lamp on */
        public static final int	NETDEV_PTZ_LIGHTOFF             = 0x0B02;       /* 灯关  Lamp off */

        public static final int	NETDEV_PTZ_HEATON               = 0x0C01;       /* 加热开  Heater on */
        public static final int	NETDEV_PTZ_HEATOFF              = 0x0C02;       /* 加热关  Heater off */

        public static final int	NETDEV_PTZ_SNOWREMOINGON        = 0x01301;       /* 除雪开  Snowremoval on */
        public static final int	NETDEV_PTZ_SNOWREMOINGOFF       = 0x01302;       /* 除雪关  Snowremoval off  */

        public static final int	NETDEV_PTZ_INFRAREDON           = 0x0D01;       /* 红外开  IR on */
        public static final int	NETDEV_PTZ_INFRAREDOFF          = 0x0D02;       /* 红外关  IR off */

        public static final int	NETDEV_PTZ_SELF_CHECKING        = 0x1801;       /* 自检:当参数dwSpeed为1时,执行云台自检;当为0时,执行恢复球机云台默认物理参数,此时会重启 */

        public static final int	NETDEV_PTZ_INVALID				= 0XFF;

    }

    /**
     * 云台控制操作(不启动实况预览)  PTZ control operation (preview not required)
     * @param  lpUserID             用户登录句柄 User login ID
     * @param  dwChannelID          通道号 Channel ID
     * @param  dwPTZCommand         云台控制命令,参见#NETDEV_PTZ_E
        PTZ control commands, see #NETDEV_PTZ_E
     * @param  dwSpeed              云台控制的速度,用户按不同解码器的速度控制值设置.取值范围[1,9] Speed of PTZ control, which is configured according to the speed control value of different decoders. Value ranges from 1 to 9.
     * @return TRUE表示成功,其他表示失败 TRUE means success, and any other value means failure.
     * @note 支持win64 支持NVR 支持IPC
     */
    public boolean NETDEV_PTZControl_Other(Pointer lpUserID, int dwChannelID, int dwPTZCommand, int dwSpeed);

    /**
     * @brief 云台预置位操作命令 枚举 定义 PTZ preset operation commands Enumeration Definition
     * @attention 无 None
     */
    public static class NETDEV_PTZ_PRESETCMD_E extends SdkStructure {
        public static final int NETDEV_PTZ_SET_PRESET = 0;             /* 设置预置位  Set preset */
        public static final int NETDEV_PTZ_CLE_PRESET = 1;             /* 清除预置位  Clear preset */
        public static final int NETDEV_PTZ_GOTO_PRESET = 2;             /* 转到预置位  Go to preset */
    }

    /**
     * @struct NETDEV_DEVICE_LOGIN_INFO_S
     * @brief 设备登录信息
     * @attention
     */
    public class NETDEV_DEVICE_LOGIN_INFO_S extends SdkStructure {
        public byte[] szIPAddr = new byte[NETDEV_LEN_260];    /* IP地址/域名 */
        public int dwPort;                                /* 端口号 */
        public byte[] szUserName = new byte[NETDEV_LEN_132];  /* 用户名 */
        public byte[] szPassword = new byte[NETDEV_LEN_128];    /* 密码 */
        public int dwLoginProto;                            /* 登录协议 0:onvif 1:私有*/
        public int dwDeviceType;                            /* 设备类型 */
        public byte[] byRes = new byte[256];
    }

    /**
     * @struct NETDEV_SELOG_INFO_S
     * @brief 安全登录信息
     * @attention
     */
    public class NETDEV_SELOG_INFO_S extends SdkStructure {
        public int dwSELogCount;            /* 安全登录次数 */
        public int dwSELogTime;            /* 安全登录时间 */
        public byte[] byRes = new byte[64];
    }

    /**
     * SDK 初始化  SDK initialization
     *
     * @return TRUE表示成功, 其他表示失败 TRUE means success, and any other value means failure.
     * @note 线程不安全 Thread not safe
     */
    public boolean NETDEV_Init();

    /**
     * 设置日志路径业务 Set log path
     *
     * @param  pszLogPath  日志路径(不包含文件名)  Log path (file name not included)
     * @return TRUE表示成功, 其他表示失败  TRUE means success, and any other value means failure.
     * @note 支持win64 支持NVR 支持IPC
     * @note 支持win32 支持VMS
     */
    public boolean NETDEV_SetLogPath(String pszLogPath);


    /**
     * @struct NETDEV_PICTURE_FORMAT_E
     * @brief 图片格式 枚举 定义 Picture type Enumeration definition
     * @attention 无 None
     */
    public static class NETDEV_PICTURE_FORMAT_E extends SdkStructure {
        public static final int NETDEV_PICTURE_BMP = 0;    /* 图片格式为bmp格式  Picture format is bmp */
        public static final int NETDEV_PICTURE_JPG = 1;    /* 图片格式为jpg格式  Picture format is jpg */
        public static final int NETDEV_PICTURE_INVALID = 0xFF;
    }

    /**
     * 非预览下抓拍  Snapshot without preview
     *
     * @param  lpUserID             用户登录句柄 User login ID
     * @param  dwChannelID          通道号 Channel ID
     * @param  dwStreamType;        码流类型,参见枚举#NETDEV_LIVE_STREAM_INDEX_E  Stream type, see enumeration #NETDEV_LIVE_STREAM_INDEX_E
     * @param  pszFileName          保存图像的文件路径（包括文件名） File path to save images (including file name)
     * @param  dwCaptureMode        保存图像格式,参见#NETDEV_PICTURE_FORMAT_E   Image saving format, see #NETDEV_PICTURE_FORMAT_E
     * @return TRUE表示成功, 其他表示失败 TRUE means success, and any other value means failure.
     * @note 仅支持JPG格式.
     * Only JPG format is supported.
     * @note 支持win64 支持NVR 支持IPC
     */
    public boolean NETDEV_CaptureNoPreview(Pointer lpUserID, int dwChannelID, int dwStreamType, String pszFileName, int dwCaptureMode);


    /**
     * 云台预置位操作(不需要启动预览)  PTZ preset operation (preview not required)
     *
     * @param  lpUserID             用户登录句柄 User login ID
     * @param  dwChannelID          通道号 Channel ID
     * @param  dwPTZPresetCmd       操作云台预置位命令,参考枚举NETDEV_PTZ_PRESETCMD_E PTZ preset operation commands, see NETDEV_PTZ_PRESETCMD_E
     * @param  pszPresetName        预置位的名称 Preset name
     * @param  dwPresetID           预置位的序号（从1开始）,最多支持255个预置位. Preset number (starting from 1). Up to 255 presets are supported.
     * @return TRUE表示成功, 其他表示失败 TRUE means success, and any other value means failure.
     * @note 支持Win32 支持VMS
     * @note 支持win64 支持NVR 支持IPC
     */
    public boolean NETDEV_PTZPreset_Other(Pointer lpUserID, int dwChannelID, int dwPTZPresetCmd, String pszPresetName, int dwPresetID);


    /**
     * 设备登录
     *
     * @param  pstDevLoginInfo  设备登录信息
     * @param  pstSELogInfo     安全登录信息，包含登录失败次数及下次登录时间
     * @return 返回值为用户ID。
     * @note 安全登录信息此字段仅适用于使用LAPI协议登录的设备
     * @note 支持Win32 支持VMS
     * @note 支持win64 支持NVR 支持IPC
     */
    public Pointer NETDEV_Login_V30(NETDEV_DEVICE_LOGIN_INFO_S pstDevLoginInfo, NETDEV_SELOG_INFO_S pstSELogInfo);

    /**
     * 查询视频通道信息列表  Query channel info list
     *
     * @param lpUserID           用户登录句柄 User login ID
     * @param pdwChlCount        通道数 Number of channels
     * @param  pstVideoChlList    通道能力集列表 List of channel info list
     * @return TRUE表示成功, 其他表示失败 TRUE means success, and any other value means failure.
     */
    public boolean NETDEV_QueryVideoChlDetailListEx(Pointer lpUserID, IntByReference pdwChlCount, NETDEV_VIDEO_CHL_DETAIL_INFO_EX_S[] pstVideoChlList);

    /**
     * 用户注销  User logout
     * @param lpUserID lpUserID    用户登录句柄 User login ID
     * @return TRUE表示成功,其他表示失败 TRUE means success, and any other value means failure.
     * @note 支持Win32 支持VMS
     * @note 支持win64 支持NVR 支持IPC
     */
    public boolean NETDEV_Logout(Pointer lpUserID);

    /**
     * SDK 清理  SDK cleaning
     * @return TRUE表示成功,其他表示失败 TRUE means success, and any other value means failure.
     * @note
     */
    public boolean NETDEV_Cleanup();

    /**
     * 获取错误码  Get error codes
     *
     * @return 错误码 Error codes
     * @note 支持Win32 支持VMS
     * @note 支持win64 支持NVR 支持IPC
     */
    public int NETDEV_GetLastError();
}
