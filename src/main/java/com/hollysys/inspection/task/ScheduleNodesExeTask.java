package com.hollysys.inspection.task;

import cn.hutool.core.collection.ConcurrentHashSet;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.hollysys.inspection.config.redis.RedisHelper;
import com.hollysys.inspection.constants.CommonCache;
import com.hollysys.inspection.constants.ScheduleTriggerType;
import com.hollysys.inspection.constants.ScheduleVariableType;
import com.hollysys.inspection.constants.project.DeployState;
import com.hollysys.inspection.entity.InspProject;
import com.hollysys.inspection.entity.InspSchedulePeriod;
import com.hollysys.inspection.entity.InspScheduleTask;
import com.hollysys.inspection.service.InspProjectService;
import com.hollysys.inspection.service.InspSchedulePeriodService;
import com.hollysys.inspection.service.InspScheduleTaskService;
import com.hollysys.inspection.utils.ExecuteUtil;
import com.hollysys.inspection.utils.LicenseUtil;
import com.hollysys.inspection.utils.PresetScheduleUtil;
import com.hollysys.inspection.utils.TimeSlotUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
public class ScheduleNodesExeTask implements Runnable {

    public static final String CONDITION_VALUE_KEY_PREFIX = "GLOBAL:SCHEDULE:VARIABLE_VALUE:";

    public static final ConcurrentHashSet<String> CACHE_TASK_ID = new ConcurrentHashSet<>();

    private final InspProjectService projectService = SpringUtil.getBean(InspProjectService.class);

    private final InspScheduleTaskService scheduleTaskService = SpringUtil.getBean(InspScheduleTaskService.class);

    private final InspSchedulePeriodService schedulePeriodService = SpringUtil.getBean(InspSchedulePeriodService.class);

    private final RedisHelper redisUtil = SpringUtil.getBean(RedisHelper.class);

    private final String taskId;

    public ScheduleNodesExeTask(String taskId) {
        this.taskId = taskId;
    }

    @Override
    public void run() {
        if (CommonCache.ZERO_TASK_ID.contains(taskId)) {
            // 正在执行零点任务，直接返回
            return;
        }

        if (CACHE_TASK_ID.contains(taskId)) {
            return;
        }

        // 添加缓存
        CACHE_TASK_ID.add(taskId);
        try {
            doExecute();
        } finally {
            // 清除缓存
            CACHE_TASK_ID.remove(taskId);
        }
    }

    private void doExecute() {
        while (true) {
            // 判断授权
            if (!BooleanUtil.isTrue(LicenseUtil.IS_VERIFY_SUCCESS.get())) {
                ExecuteUtil.printExecuteLog(log, "调度定时任务[{}]执行结束，因为授权校验失败", taskId);
                return;
            }
            InspProject projectInfo = projectService.getFirstOne();
            if (DeployState.UN_DEPLOYED.name().equals(projectInfo.getDeployState())) {
                ExecuteUtil.printExecuteLog(log, "调度定时任务[{}]执行结束，因为工程状态为未部署", taskId);
                return;
            }

            // 查询任务信息
            InspScheduleTask byIdNotNull = scheduleTaskService.getByIdNotNull(taskId);
            if (!BooleanUtil.isTrue(byIdNotNull.getScheduleEnable())) {
                ExecuteUtil.printExecuteLog(log, "调度定时任务[{}]执行结束，因为任务调度状态为关闭", taskId);
                return;
            }

            ExecuteUtil.printExecuteLog(log, "调度任务[{}]执行开始", taskId);
            // 判断任务调度触发类型
            String triggerType = byIdNotNull.getTriggerType();
            if (EnumUtil.equals(ScheduleTriggerType.TIME_TRIGGER, triggerType)) {
                if (!handleTimeTrigger()) {
                    return;
                }
            } else if (EnumUtil.equals(ScheduleTriggerType.CONDITION_TRIGGER, triggerType)) {
                if (!handleConditionTrigger(byIdNotNull)) {
                    return;
                }
            } else {
                log.error("任务调度[{}]失败，触发类型[{}]错误", taskId, triggerType);
                return;
            }

            scheduleTaskService.executeTask(taskId);

            Integer interval = byIdNotNull.getInterval();
            String intervalUnit = byIdNotNull.getIntervalUnit();
            // 转化为毫秒
            long intervalMilliseconds = intervalToMilliseconds(interval, intervalUnit);
            ExecuteUtil.printExecuteLog(log, "调度定时任务[{}]进入下次执行等待时间，间隔时间[{}]，单位[{}]", taskId, interval, intervalUnit);
            PresetScheduleUtil.waitMillisecond(taskId, intervalMilliseconds);
            ExecuteUtil.printExecuteLog(log, "等待时间结束---调度定时任务[{}]等待时间结束，执行等待间隔时间[{}]，单位[{}]", taskId, interval, intervalUnit);
        }
    }

    private long intervalToMilliseconds(Integer interval, String intervalUnit) {
        TimeUnit timeUnit = EnumUtil.fromString(TimeUnit.class, intervalUnit, TimeUnit.MILLISECONDS);
        return timeUnit.toMillis(interval);
    }

    private boolean handleTimeTrigger() {
        // 查询任务绑定的调度周期配置
        InspSchedulePeriod schedulePeriod = schedulePeriodService.listByTaskIdAndWeekDay(TimeSlotUtil.getDateWeek(), taskId);
        if (Objects.isNull(schedulePeriod)) {
            ExecuteUtil.printExecuteLog(log, "调度定时任务[{}]执行结束，因为当前时间调度周期配置为空", taskId);
            return false;
        }
        // 判断当前时间是否在调度周期内
        String timeSlot = schedulePeriod.getTimeSlot();
        DateTime dateNow = DateUtil.date();
        if (!TimeSlotUtil.isContains(timeSlot, dateNow)) {
            ExecuteUtil.printExecuteLog(log, "调度定时任务[{}]执行结束，当前时间不在调度周期内", taskId);
            return false;
        }
        return true;
    }

    private boolean handleConditionTrigger(InspScheduleTask byIdNotNull) {
        Boolean triggerValue = byIdNotNull.getTriggerValue();
        if (Objects.isNull(triggerValue)) {
            ExecuteUtil.printExecuteLog(log, "调度定时任务[{}]执行结束，条件开关判断值为空", taskId);
            return false;
        }
        // TODO
        String currentValue = null;
        String variableType = byIdNotNull.getVariableType();
        if (EnumUtil.equals(ScheduleVariableType.DCS, variableType)) {
            String namespace = byIdNotNull.getNamespace();
            String tag = byIdNotNull.getTag();
            String item = byIdNotNull.getItem();
            if (StrUtil.hasBlank(namespace, tag, item)) {
                ExecuteUtil.printExecuteLog(log, "调度定时任务[{}]执行结束，ICS点项配置有空值", taskId);
                return false;
            }
            // TODO
            // currentValue = icsPlatformApiService.readPointsFromCache(namespace, tag, item);

        } else if (EnumUtil.equals(ScheduleVariableType.INTERNAL, variableType)) {
            String variableName = byIdNotNull.getVariableName();
            if (StrUtil.isBlank(variableName)) {
                ExecuteUtil.printExecuteLog(log, "调度定时任务[{}]执行结束，变量名称配置为空", taskId);
                return false;
            }
            // 读取内部变量
            currentValue = redisUtil.get(CONDITION_VALUE_KEY_PREFIX + variableName);
        } else {
            log.error("任务调度[{}]失败，触发值类型[{}]错误", taskId, variableType);
            return false;
        }
        if (StrUtil.isBlank(currentValue)) {
            ExecuteUtil.printExecuteLog(log, "调度定时任务[{}]执行结束，读取变量的值为空", taskId);
            return false;
        }

        if (!NumberUtil.isNumber(currentValue)) {
            ExecuteUtil.printExecuteLog(log, "调度定时任务[{}]执行结束，读取变量的值[{}]非数字", taskId, currentValue);
            return false;
        }

        int parseInt = NumberUtil.parseInt(currentValue);
        Boolean aBoolean = BooleanUtil.toBooleanObject(String.valueOf(parseInt));
        if (triggerValue.equals(aBoolean)) {
            ExecuteUtil.printExecuteLog(log, "调度定时任务[{}]执行，条件判断成功", taskId);
        } else {
            ExecuteUtil.printExecuteLog(log, "调度定时任务[{}]跳过执行，读取变量值值和目标值不一致", taskId);
            return false;
        }
        return true;
    }
}
