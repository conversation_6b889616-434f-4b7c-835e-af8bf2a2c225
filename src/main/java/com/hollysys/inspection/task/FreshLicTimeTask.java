package com.hollysys.inspection.task;

import cn.hutool.core.io.FileUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.File;

/**
 * Lid刷新任务调度任务
 */
@Slf4j
public class FreshLicTimeTask implements Runnable {

    private final File cipher;

    private boolean isRefreshing = false;

    public synchronized void setRefreshing(boolean refreshing) {
        isRefreshing = refreshing;
    }

    public synchronized boolean getRefreshing() {
        return isRefreshing;
    }


    public FreshLicTimeTask(File cipher) {
        this.cipher = cipher;
    }

    @Override
    public void run() {
        long now = System.currentTimeMillis();
        if (getRefreshing() || !FileUtil.exist(cipher) || now <= cipher.lastModified()) {
            return;
        }

        setRefreshing(true);
        try {
            if (cipher.setLastModified(now) && (log.isDebugEnabled())) {
                log.debug("cipher 刷新成功");
            }
        } catch (Exception exception) {
            log.error("FreshLicTimeTask error... ", exception);
        } finally {
            setRefreshing(false);
        }
    }
}
