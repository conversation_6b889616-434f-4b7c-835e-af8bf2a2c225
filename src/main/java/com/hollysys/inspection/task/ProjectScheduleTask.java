package com.hollysys.inspection.task;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hollysys.inspection.constants.project.DeployState;
import com.hollysys.inspection.entity.InspProject;
import com.hollysys.inspection.entity.InspScheduleTask;
import com.hollysys.inspection.service.InspProjectService;
import com.hollysys.inspection.service.InspScheduleTaskService;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;

@Slf4j
public class ProjectScheduleTask implements Runnable {


    private final InspProjectService projectService = SpringUtil.getBean(InspProjectService.class);

    private final InspScheduleTaskService scheduleTaskService = SpringUtil.getBean(InspScheduleTaskService.class);

    @Override
    public void run() {
        // 获取工程部署状态，如果是停止部署状态，则直接进入下次循环
        InspProject projectInfo = projectService.getFirstOne();
        if (Objects.isNull(projectInfo) || DeployState.UN_DEPLOYED.name().equals(projectInfo.getDeployState())) {
            log.debug("工程状态为未部署，调度任务未启动");
            return;
        }

        // 获取全部开启调度的任务列表
        LambdaQueryWrapper<InspScheduleTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspScheduleTask::getScheduleEnable, Boolean.TRUE);
        List<InspScheduleTask> scheduleTaskList = scheduleTaskService.list(wrapper);
        if (CollectionUtil.isEmpty(scheduleTaskList)) {
            return;
        }
        // 过滤出全部可执行的任务
        for (InspScheduleTask scheduleTask : scheduleTaskList) {
            ScheduleNodesExeTask scheduleNodesExeTask = new ScheduleNodesExeTask(scheduleTask.getId());
            ThreadUtil.execute(scheduleNodesExeTask);
        }
    }
}
