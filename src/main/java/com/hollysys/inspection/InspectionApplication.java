package com.hollysys.inspection;

import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableScheduling
@SpringBootApplication(exclude = {RabbitAutoConfiguration.class})
public class InspectionApplication {

    public static void main(String[] args) {
        new SpringApplicationBuilder(InspectionApplication.class)
                .web(WebApplicationType.SERVLET)
                .run(args);
    }
}
