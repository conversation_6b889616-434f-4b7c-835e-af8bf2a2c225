package com.hollysys.inspection.entity;


import com.hollysys.inspection.entity.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 第三方接口(InspThirdInterface)表实体类
 *
 * <AUTHOR>
 * @since 2023-07-05 14:28:49
 */
@Setter
@Getter
public class InspThirdInterface extends BaseEntity {
    // 接口名称
    private String name;
    // 接口地址
    private String url;
    // 接口请求方式（GET、POST...）
    private String httpMethod;
    // 排序字段
    private Integer sortNo;
    // 是否可用
    private Boolean available;
    // 接口分类
    private String classification;
}

