package com.hollysys.inspection.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.hollysys.inspection.entity.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 第三方应用管理(InspThirdApp)表实体类
 *
 * <AUTHOR>
 * @since 2023-07-05 14:28:35
 */
@Setter
@Getter
public class InspThirdApp extends BaseEntity {
    // 应用名称
    private String name;
    // 应用码（业务主键）
    private String appCode;
    // 授权码
    private String licenseCode;
    // 创建时间
    private LocalDateTime createTime;
    // 更新时间
    @TableField(update = "now()")
    private LocalDateTime updateTime;
    // 是否可用
    private Boolean available;
}

