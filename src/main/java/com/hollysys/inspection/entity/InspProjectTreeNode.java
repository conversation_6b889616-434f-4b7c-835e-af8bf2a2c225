package com.hollysys.inspection.entity;


import com.hollysys.inspection.entity.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * (ProjectTreeNode)表实体类
 *
 * <AUTHOR>
 * @since 2023-02-01 15:31:45
 */
@Getter
@Setter
public class InspProjectTreeNode extends BaseEntity {
    // 节点名称
    private String label;
    // 通道类型
    private String type;
    // 父级id
    private String parentId;
    // 节点描述
    private String des;
    // 节点点击跳转地址
    private String url;
    // 排序字段
    private Long sortNo;
    // 节点状态
    private String status;
}

