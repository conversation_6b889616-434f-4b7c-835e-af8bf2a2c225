package com.hollysys.inspection.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hollysys.inspection.config.mybaits.CorrectParamToStringHandle;
import com.hollysys.inspection.config.mybaits.ObjectToStringHandle;
import com.hollysys.inspection.model.algorithm.correct.CorrectParamSaveModel;
import lombok.Getter;
import lombok.Setter;
import org.apache.ibatis.type.JdbcType;

/**
 * 算法信息表(InspAlgorithmInstance)表实体类
 *
 * <AUTHOR>
 * @since 2023-03-07 17:09:34
 */
@Setter
@Getter
@TableName(autoResultMap = true)
public class InspAlgorithmInstance extends InspAlgorithm {
    // ROI坐标
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = ObjectToStringHandle.class)
    private Object roi;
    // 所属算法id
    private String algorithmId;
    // 所属预置点id
    private String presetId;
    // 是否保存过配置
    private Boolean hasSaveConfig;
    // 批量图片输入,截图间隔(单位秒)
    private Integer cutPicInterval;
    // 批量图片输入,图片张数
    private Integer cutPicSize;
    // 是否进行模板匹配
    private Boolean matchEnable;
    // 是否进行透视变换
    private Boolean correctEnable;
    // 透视变换矫正参数
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = CorrectParamToStringHandle.class)
    private CorrectParamSaveModel correctParam;
}

