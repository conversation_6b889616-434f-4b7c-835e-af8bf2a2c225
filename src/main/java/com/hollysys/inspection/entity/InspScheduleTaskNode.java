package com.hollysys.inspection.entity;


import com.hollysys.inspection.entity.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 通道运行周期调度信息表(InspScheduleTaskNode)表实体类
 *
 * <AUTHOR>
 * @since 2023-06-20 15:09:04
 */
@Setter
@Getter
public class InspScheduleTaskNode extends BaseEntity {
    // 所属任务ID
    private String taskId;
    // 关联预置点ID
    private String presetId;
    // 关联通道ID
    private String channelId;
    // 排序字段
    private Integer sortNo;
    // 运行状态
    private String status;
}

