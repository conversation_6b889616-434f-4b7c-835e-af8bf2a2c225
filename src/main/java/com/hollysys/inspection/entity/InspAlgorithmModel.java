package com.hollysys.inspection.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hollysys.inspection.config.mybaits.ListToStringHandle;
import com.hollysys.inspection.entity.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import org.apache.ibatis.type.JdbcType;

import java.time.LocalDateTime;
import java.util.List;
@TableName(autoResultMap = true)
@Setter
@Getter
public class InspAlgorithmModel extends BaseEntity {
    // 模型名称
    private String name;
    // 模型编码
    private String code;
    // 模型版本
    private String version;
    // 模型文件包名
    private String packageFile;
    // 创建时间
    private LocalDateTime createTime;
    // 更新时间
    private LocalDateTime updateTime;
    // 引用该模型的算法名字列表
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = ListToStringHandle.class)
    private List<String> referencedAlgorithmNames;
}
