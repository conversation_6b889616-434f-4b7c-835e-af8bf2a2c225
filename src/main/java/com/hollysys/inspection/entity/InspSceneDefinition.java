package com.hollysys.inspection.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hollysys.inspection.config.mybaits.SquareToStringHandle;
import com.hollysys.inspection.entity.base.DateBaseEntity;
import com.hollysys.inspection.model.algorithm.param.data.Square;
import com.hollysys.inspection.utils.MinioUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.ibatis.type.JdbcType;

/**
 * (InspSceneDefinition)表实体类
 *
 * <AUTHOR>
 * @since 2023-02-02 16:29:38
 */
@Setter
@Getter
@TableName(autoResultMap = true)
public class InspSceneDefinition extends DateBaseEntity {
    // 预置点id
    private String presetId;
    // 模板图
    private String tmpPic;
    // 模板图基于基准图四点坐标
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = SquareToStringHandle.class)
    private Square tmpSquare;
    // 基准图
    private String benchPic;
    // 场景名
    private String name;
    // 基准图分辨率
    private String benchShape;

    public void picUrlToRelative() {
        // 图片路径转化为前端使用的相对路径
        String tmpPic = getTmpPic();
        setTmpPic(MinioUtil.urlToRelative(tmpPic));
        String benchPic = getBenchPic();
        setBenchPic(MinioUtil.urlToRelative(benchPic));
    }
}

