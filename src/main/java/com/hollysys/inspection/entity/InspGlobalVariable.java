package com.hollysys.inspection.entity;

import com.hollysys.inspection.entity.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 全局变量配置表(InspGlobalVariable)表实体类
 *
 * <AUTHOR>
 * @since 2025-04-15 17:33:48
 */
@Getter
@Setter
public class InspGlobalVariable extends BaseEntity {
    // 变量名
    private String valName;
    // 别名
    private String nickName;
    // 描述
    private String description;
    // 单位
    private String unit;
    // 值关联点项名
    private String pointName;
    // 质量位关联点项名
    private String qualityPointName;
    // 数据类型
    private String dataType;
    // 预置点ID
    private String presetId;
    // 所属算法实例ID
    private String algorithmInstanceId;
    // 绑定的算法出参ID（参数定义表，不是参数实例表）
    private String outputId;
    // 随点项值刷新、超时自动置坏
    private String qualitySetType;
    // 质量位超时时间（quality_set_type为超时自动置坏时有意义）
    private Integer qualityTimeout;
    // 质量位超时时间单位，只允许时、分、秒（quality_set_type为超时自动置坏时有意义）
    private String qualityTimeoutUnit;
    // 变量值读取方式（实时读取、定时读取）
    private String valueReadType;
    // 变量值定时读取间隔/秒（value_read_type为定时读取时有意义）
    private Integer valueReadInterval;
    // 变量的读写类型
    private String valType;
    // 创建时间
    private LocalDateTime createTime;
    // 更新时间
    private LocalDateTime updateTime;
}

