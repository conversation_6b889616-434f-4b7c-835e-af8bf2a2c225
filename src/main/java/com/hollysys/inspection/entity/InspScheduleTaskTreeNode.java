package com.hollysys.inspection.entity;


import com.hollysys.inspection.entity.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * (insp_schedule_task_tree_node)表实体类
 *
 * <AUTHOR>
 * @since 2023-02-01 15:31:45
 */
@Getter
@Setter
public class InspScheduleTaskTreeNode extends BaseEntity {
    // 节点名称
    private String label;
    // 通道类型
    private String type;
    // 父级id
    private String parentId;
    // 创建时间
    private Date createTime;
}

