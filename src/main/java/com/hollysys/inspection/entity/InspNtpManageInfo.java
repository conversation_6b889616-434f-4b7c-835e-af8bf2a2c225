package com.hollysys.inspection.entity;


import com.hollysys.inspection.entity.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * (InspNtpManageInfo)表实体类
 *
 * <AUTHOR>
 * @since 2023-05-16 14:54:37
 */
@Setter
@Getter
public class InspNtpManageInfo extends BaseEntity {
    // 是否可用
    private Boolean available;
    // NTP服务主机名
    private String ntpServerHost;
    // NTP服务端口
    private Integer ntpServerPort;
    // 校准间隔
    private Integer interval;
}

