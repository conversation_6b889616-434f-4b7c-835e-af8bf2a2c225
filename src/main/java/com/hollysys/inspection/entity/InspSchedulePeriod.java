package com.hollysys.inspection.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.hollysys.inspection.entity.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 通道运行周期调度信息表(InspSchedulePeriod)表实体类
 *
 * <AUTHOR>
 * @since 2023-02-02 17:37:41
 */
@Setter
@Getter
@TableName(autoResultMap = true)
public class InspSchedulePeriod extends BaseEntity {
    // 巡检任务id
    private String taskId;
    // 所属星期几
    private Integer week;
    // 时间段集合
    private String timeSlot;
    // 复制来源任务id
    private String fromTaskId;
}

