package com.hollysys.inspection.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hollysys.inspection.config.mybaits.ListToStringHandle;
import com.hollysys.inspection.entity.base.DateBaseEntity;
import com.hollysys.inspection.utils.MinioUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.ibatis.type.JdbcType;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * (InspAlarmRecord)表实体类
 *
 * <AUTHOR>
 * @since 2023-12-14 11:25:06
 */
@Setter
@Getter
@TableName(autoResultMap = true)
public class InspAlarmRecord extends DateBaseEntity {
    // 通道id
    private String channelId;
    // 预置点id
    private String presetId;
    // 算法实例id
    private String algorithmInstanceId;
    // 输出参数定义ID
    private String outputParamId;
    // 算法结果图地址（数组形式，长度最大为2，分别为首次报警图片、最新报警图片）
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = ListToStringHandle.class)
    private List<String> resultImgs;
    // 是否确认
    private Boolean confirm;
    // 报警等级
    private String alarmLevel;
    // 报警等级设定的判断值
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Object levelValue;
    // 报警描述文本
    private String alarmDesc;
    // 恢复时间
    private LocalDateTime restoreTime;
    // 确认时间
    private LocalDateTime confirmTime;
    // 误报
    private Boolean falseAlarm;

    public void addResultImg(String imgUrl) {
        if (Objects.isNull(resultImgs)) {
            resultImgs = new ArrayList<>();
        }
        if (resultImgs.size() > 1) {
            // 删除第二张图片
            String picUrl = resultImgs.get(1);
            MinioUtil.removeObject(picUrl);
            // 然后使用新图片覆盖
            resultImgs.set(1, imgUrl);
        } else {
            resultImgs.add(imgUrl);
        }
    }

    // csv生成文件头部
    public static String[] generateCsvHeader() {
        List<String> headers = new ArrayList<>();
        Field[] declaredFields = InspAlarmRecord.class.getDeclaredFields();
        for (Field field : declaredFields) {
            headers.add(field.getName());
        }
        return headers.toArray(new String[0]);
    }
}

