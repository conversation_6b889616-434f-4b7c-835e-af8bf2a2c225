package com.hollysys.inspection.entity;


import com.hollysys.inspection.entity.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 算法信息表(InspSystemUser)表实体类
 *
 * <AUTHOR>
 * @since 2023-05-17 16:10:22
 */
@Setter
@Getter
public class InspSystemUser extends BaseEntity {
    // 用户名称
    private String name;
    // 账号名
    private String username;
    // 密码
    private String password;
}

