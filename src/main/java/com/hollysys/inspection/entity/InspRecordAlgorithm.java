package com.hollysys.inspection.entity;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hollysys.inspection.config.mybaits.ExecuteRspToStringHandle;
import com.hollysys.inspection.config.mybaits.JSONObjectToStringHandle;
import com.hollysys.inspection.config.mybaits.ListToStringHandle;
import com.hollysys.inspection.entity.base.BaseEntity;
import com.hollysys.inspection.model.algorithm.execute.AlgorithmExecuteRsp;
import com.hollysys.inspection.model.context.ExecuteAlgorithmCtx;
import lombok.Getter;
import lombok.Setter;
import org.apache.ibatis.type.JdbcType;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 算法执行记录
 */
@Setter
@Getter
@TableName(autoResultMap = true)
public class InspRecordAlgorithm extends BaseEntity {
    // 预置点执行记录ID
    private String recordPresetId;
    // 通道id
    private String channelId;
    // 预置点id
    private String presetId;
    // 算法实例id
    private String algorithmInstanceId;
    // 输入图片地址
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = ListToStringHandle.class)
    private List<String> inputImages;
    // 算法执行输入参数
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = JSONObjectToStringHandle.class)
    private JSONObject inputParams;
    // 输出图片地址
    private String outputImage;
    // 算法执行结果
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = ExecuteRspToStringHandle.class)
    private AlgorithmExecuteRsp executeResult;
    // 执行结果状态（成功/失败）
    private String resultStatus;
    // 失败原因
    private String failureReason;
    // 开始时间
    private LocalDateTime startTime;
    // 结束时间
    private LocalDateTime endTime;

    // csv文件头部
    public static String[] generateCsvHeader() {
        List<String> headers = new ArrayList<>();
        Field[] declaredFields = InspRecordAlgorithm.class.getDeclaredFields();
        for (Field field : declaredFields) {
            headers.add(field.getName());
        }
        return headers.toArray(new String[0]);
    }

    public static InspRecordAlgorithm buildByAlgorithmCtx(ExecuteAlgorithmCtx algorithmCtx) {
        InspRecordAlgorithm executeRecord = new InspRecordAlgorithm();
        BeanUtil.copyProperties(algorithmCtx, executeRecord, "inputParams");
        // 参数集合转化为map结构
        List<InspAlgorithmParamInstance> inputParamsReq = algorithmCtx.getInputParams();
        if (CollectionUtil.isNotEmpty(inputParamsReq)) {
            JSONObject params = new JSONObject();
            for (InspAlgorithmParamInstance paramInstance : inputParamsReq) {
                params.set(paramInstance.getKey(), paramInstance.getValue());
            }
            executeRecord.setInputParams(params);
        }

        return executeRecord;
    }
}

