package com.hollysys.inspection.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hollysys.inspection.config.mybaits.ListToStringHandle;
import com.hollysys.inspection.entity.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

/**
 * (InspSceneCustomPic)表实体类
 *
 * <AUTHOR>
 * @since 2023-03-10 13:54:39
 */
@TableName(autoResultMap = true)
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class InspSceneCustomPic extends BaseEntity {
    // 预置点id
    private String presetId;
    // 上传自定义场景路径
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = ListToStringHandle.class)
    private List<String> pics;

    private String type;
}

