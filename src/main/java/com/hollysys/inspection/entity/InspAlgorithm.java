package com.hollysys.inspection.entity;


import com.hollysys.inspection.entity.base.DateBaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 算法信息表(InspAlgorithm)表实体类
 *
 * <AUTHOR>
 * @since 2023-02-03 15:49:14
 */
@Setter
@Getter
public class InspAlgorithm extends DateBaseEntity {
    // 算法编码（业务主键）
    private String code;
    // 算法类型（CV、视觉）
    private String type;
    // 算法名称
    private String name;
    // 算法图标，SVG图片格式
    private String icon;
    // 算法描述
    private String description;
    // 算法是否需要ROI
    private Boolean roiRequired;
    // 算法ROI绘制图像类型
    private String roiDrawType;
    // 算法是否需要批量图片输入
    private Boolean isBatchProcessing;
    // 是否是系统预置算法
    private Boolean isPreset;
    // 算法分类
    private String classification;
    // 算法名称
    private String packageFile;
    // 算法版本
    private String version;
    // 算法所使用的模型code
    private String modelCode;
}

