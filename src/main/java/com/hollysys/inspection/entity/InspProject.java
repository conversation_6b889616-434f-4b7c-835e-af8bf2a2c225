package com.hollysys.inspection.entity;


import com.hollysys.inspection.entity.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 算法信息表(InspProject)表实体类
 *
 * <AUTHOR>
 * @since 2023-05-05 11:15:28
 */
@Setter
@Getter
public class InspProject extends BaseEntity {
    // 部署状态（部署、停止）
    private String deployState;
    // 在线状态（在线、离线）
    private String onlineState;

    private Boolean screenShotState;
}

