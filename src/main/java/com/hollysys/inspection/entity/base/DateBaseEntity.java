package com.hollysys.inspection.entity.base;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class DateBaseEntity extends BaseEntity {
    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    public DateBaseEntity() {
        LocalDateTime now = LocalDateTime.now();
        createTime = now;
        updateTime = now;
    }
}
