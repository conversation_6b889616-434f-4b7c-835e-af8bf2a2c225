package com.hollysys.inspection.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hollysys.inspection.config.mybaits.ListToStringHandle;
import com.hollysys.inspection.entity.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import org.apache.ibatis.type.JdbcType;
import java.time.LocalDateTime;
import java.util.List;

@TableName(autoResultMap = true)
@Setter
@Getter
public class InspThirdAlgorithmSubscription extends BaseEntity {
    // 发送方式
    private String subscriptionType;
    // 算法id
    private String algorithmId;
    // 算法实例id集合
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = ListToStringHandle.class)
    private List<String> algorithmInstanceIds;
    // 预置点
    private String presetId;
    // 订阅回调地址
    private String callbackUrl;
    // 创建时间
    private LocalDateTime createTime;
    // 更新时间
    private LocalDateTime updateTime;
    // 应用id
    private String appId;
    // 接口请求方式
    private String httpMethod;
}
