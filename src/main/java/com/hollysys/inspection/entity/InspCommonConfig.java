package com.hollysys.inspection.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hollysys.inspection.config.mybaits.*;
import com.hollysys.inspection.entity.base.BaseEntity;
import com.hollysys.inspection.model.DataBaseConfig;
import com.hollysys.inspection.model.algorithm.osd.OsdConfig;
import com.hollysys.inspection.model.backup.SystemBackupConfig;
import lombok.Getter;
import lombok.Setter;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

/**
 * (InspCommonConfig)表实体类
 *
 * <AUTHOR>
 * @since 2024-01-17 10:33:18
 */
@Getter
@Setter
@TableName(autoResultMap = true)
public class InspCommonConfig extends BaseEntity {
    // 执行过程中日志是否打印
    private Boolean executeLogEnable;
    // 执行过程中图片是否删除
    private Boolean executeImgDelEnable;
    // DCS报警列表页面，报警状态颜色
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = ObjectToStringHandle.class)
    private Object dcsAlarmStateColor;

    // OSD相关配置
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = OsdConfigToStringHandle.class)
    private OsdConfig osdConfig;

    // 日志级别
    private String loggingLevel;

    // 备份配置
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = SystemBackupConfigToStringHandle.class)
    private SystemBackupConfig systemBackupConfig;

    // 数据底座配置
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = DatabaseConfigToStringHandle.class)
    private DataBaseConfig dataBaseConfig;

    // OSD文字背景颜色绘制开关
    private Boolean fontBgEnable = true;

    // 执行记录展示开关
    private Boolean executeRecordShowable = false;

    // 流媒体地址
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = ListToStringHandle.class)
    private List<String> mtxIps;
}

