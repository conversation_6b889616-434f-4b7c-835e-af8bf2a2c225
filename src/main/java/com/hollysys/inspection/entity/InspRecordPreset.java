package com.hollysys.inspection.entity;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hollysys.inspection.constants.ExecuteStartModel;
import com.hollysys.inspection.entity.base.BaseEntity;
import com.hollysys.inspection.model.context.ExecutePresetCtx;
import lombok.Getter;
import lombok.Setter;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 预置点执行记录
 */
@Setter
@Getter
@TableName(autoResultMap = true)
public class InspRecordPreset extends BaseEntity {
    // 通道id
    private String channelId;
    // 预置点id
    private String presetId;
    // 执行次
    private Integer times;
    // 执行结果状态（成功/失败）
    private String resultStatus;
    // 失败原因
    private String failureReason;
    // 触发模式
    private String model;
    // 开始时间
    private LocalDateTime startTime;
    // 结束时间
    private LocalDateTime endTime;

    // csv文件头部
    public static String[] generateCsvHeader() {
        List<String> headers = new ArrayList<>();
        Field[] declaredFields = InspRecordPreset.class.getDeclaredFields();
        for (Field field : declaredFields) {
            headers.add(field.getName());
        }
        return headers.toArray(new String[0]);
    }

    public static InspRecordPreset buildByPresetCtx(ExecutePresetCtx presetCtx) {
        InspRecordPreset executeRecord = new InspRecordPreset();
        BeanUtil.copyProperties(presetCtx, executeRecord);
        String clientKey = presetCtx.getClientKey();
        if (StrUtil.isBlank(clientKey)) {
            executeRecord.setModel(ExecuteStartModel.SCHEDULE.name());
        } else {
            executeRecord.setModel(ExecuteStartModel.MANUAL.name());
        }
        return executeRecord;
    }
}

