package com.hollysys.inspection.entity;


import com.hollysys.inspection.entity.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 通道类型节点信息表(InspChannelInfo)表实体类
 *
 * <AUTHOR>
 * @since 2023-07-06 16:28:24
 */
@Setter
@Getter
public class InspChannelInfo extends BaseEntity {
    // 通道类型
    private String deviceType;
    // 通道连接IP
    private String address;
    // 通道号
    private Integer channelNum;
    // 通道RTSP地址
    private String rtsp;
    // 用户名
    private String username;
    // 密码
    private String password;
    // 视频接入协议类型
    private String videoProtocolType;
    // 视频传输协议类型TCP、UDP（mediamtx流媒体服务需要）
    private String sourceProtocol;
    // 摄像机接入协议类型
    private String cameraCtrlProtocol;
    // // 此字段为GB28181协议下SIP用户名
    // private String sipName;
    // // 此字段为GB28181协议下视频通道ID
    // private String videoChannelId;
    // 摄像头厂商
    private String manufacturer;
}

