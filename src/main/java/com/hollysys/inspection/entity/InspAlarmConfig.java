package com.hollysys.inspection.entity;

import cn.hutool.core.util.EnumUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hollysys.inspection.config.mybaits.AdvanceAlarmToStringHandle;
import com.hollysys.inspection.constants.AlarmModeType;
import com.hollysys.inspection.constants.algorithm.param.DataType;
import com.hollysys.inspection.entity.base.BaseEntity;
import com.hollysys.inspection.model.alarm.config.AdvanceAlarmRuleInfo;
import com.hollysys.inspection.model.alarm.config.AlarmRuleItemModel;
import lombok.Getter;
import lombok.Setter;
import org.apache.ibatis.type.JdbcType;

import java.util.Objects;

/**
 * (InspAlarmConfig)表实体类
 *
 * <AUTHOR>
 * @since 2025-05-08 14:02:05
 */
@Getter
@Setter
@TableName(autoResultMap = true)
public class InspAlarmConfig extends BaseEntity {
    // 通道id
    private String channelId;
    // 预置点id
    private String presetId;
    // 算法实例id
    private String algorithmInstanceId;
    // 输出参数定义ID
    private String outputParamId;
    // 报警类型，越限报警、偏差报警、深度报警
    private String alarmType;
    // 越限报警规则
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = AdvanceAlarmToStringHandle.class)
    private AdvanceAlarmRuleInfo ruleLimit;
    // 偏差报警规则
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = AdvanceAlarmToStringHandle.class)
    private AdvanceAlarmRuleInfo ruleDeviation;
    // 深度报警规则
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = AdvanceAlarmToStringHandle.class)
    private AdvanceAlarmRuleInfo ruleDeep;
    // 字符串报警规则
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = AdvanceAlarmToStringHandle.class)
    private AdvanceAlarmRuleInfo ruleStr;
    // 布尔报警规则
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = AdvanceAlarmToStringHandle.class)
    private AdvanceAlarmRuleInfo ruleBool;

    public void initRuleInfo(String dataType) {
        if (!EnumUtil.contains(DataType.class, dataType)) {
            return;
        }
        DataType dataTypeEnum = EnumUtil.fromString(DataType.class, dataType);
        if (DataType.INTEGER.equals(dataTypeEnum) || DataType.FLOAT.equals(dataTypeEnum)) {
            if (Objects.isNull(getRuleLimit())) {
                setRuleLimit(AlarmRuleItemModel.newLimitAlarmRule());
            }
            if (Objects.isNull(getRuleDeep())) {
                setRuleDeep(AlarmRuleItemModel.newDeepAlarmRule());
            }
            if (Objects.isNull(getRuleDeviation())) {
                setRuleDeviation(AlarmRuleItemModel.newDeviationAlarmRule());
            }
        } else if (DataType.STRING.equals(dataTypeEnum)) {
            if (Objects.isNull(getRuleStr())) {
                setRuleStr(AlarmRuleItemModel.newStrAlarmRule());
            }
            setAlarmType(AlarmModeType.STR_ALARM.name());
        } else if (DataType.BOOLEAN.equals(dataTypeEnum)) {
            if (Objects.isNull(getRuleBool())) {
                setRuleBool(AlarmRuleItemModel.newBooleanAlarmRule());
            }
            setAlarmType(AlarmModeType.BOOL_ALARM.name());
        }
    }
}

