package com.hollysys.inspection.entity;


import com.hollysys.inspection.entity.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 通道运行周期调度信息表(InspScheduleTask)表实体类
 *
 * <AUTHOR>
 * @since 2023-06-20 11:05:31
 */
@Setter
@Getter
public class InspScheduleTask extends BaseEntity {
    // 任务名称
    private String name;
    // 排序字段
    private Integer sortNo;
    // 调度开关
    private Boolean scheduleEnable;

    // 调度任务触发方式，TIME_TRIGGER：时间触发   CONDITION_TRIGGER：变量值条件触发
    private String triggerType;

    // 点域名称
    private String namespace;

    // 点名
    private String tag;

    // 项名
    private String item;

    // 触发点项值
    private Boolean triggerValue;

    // 条件触发-变量类型：DCS（DCS点项值）、INTERNAL（内部变量值）
    private String variableType;

    // 触发变量名称
    private String variableName;

    // 调度间隔
    private Integer interval;

    // 调度间隔单位
    private String intervalUnit;
}

