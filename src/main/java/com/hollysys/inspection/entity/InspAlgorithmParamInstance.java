package com.hollysys.inspection.entity;


import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hollysys.inspection.config.mybaits.ObjectToStringHandle;
import lombok.Getter;
import lombok.Setter;
import org.apache.ibatis.type.JdbcType;

/**
 * 算法参数定义表(InspAlgorithmParamInstance)表实体类
 *
 * <AUTHOR>
 * @since 2023-03-01 15:47:35
 */
@Setter
@Getter
@TableName(autoResultMap = true)
public class InspAlgorithmParamInstance extends InspAlgorithmParam {
    // 所属算法实例ID
    private String algorithmInstanceId;
    // 所属场景ID
    private String sceneId;
    // 参数值
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = ObjectToStringHandle.class)
    private Object value;

    public static InspAlgorithmParamInstance buildByAlgorithmParam(InspAlgorithmParam algorithmParam) {
        InspAlgorithmParamInstance paramInstance = new InspAlgorithmParamInstance();
        BeanUtil.copyProperties(algorithmParam, paramInstance);
        paramInstance.setId(null);
        return paramInstance;
    }
}

