package com.hollysys.inspection.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.hollysys.inspection.entity.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * (InspPresetInfo)表预置点实体类
 *
 * <AUTHOR>
 * @since 2023-02-13 14:51:10
 */
@Getter
@Setter
@TableName(autoResultMap = true)
public class InspPresetInfo extends BaseEntity {
    // 是否可用
    private Boolean available;
    // 所属通道id
    private String channelId;
    // 前置延时（秒）
    private Integer preExecuteDelay;
    // 后置延时（秒）
    private Integer postExecuteDelay;
    // 验证次数
    private Integer validTimes;
    // 是否开启自动截图
    private Boolean isSchedule;
    // 预置点号
    private Integer presetNum;
}

