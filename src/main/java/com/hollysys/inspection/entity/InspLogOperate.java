package com.hollysys.inspection.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hollysys.inspection.config.mybaits.ListToStringHandle;
import com.hollysys.inspection.entity.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import org.apache.ibatis.type.JdbcType;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * (InspLogOperate)表实体类
 *
 * <AUTHOR>
 * @since 2023-05-19 09:39:35
 */
@Setter
@Getter
@TableName(autoResultMap = true)
public class InspLogOperate extends BaseEntity {
    // 时间
    private LocalDateTime time;
    // 操作人用户名
    private String userName;
    // 操作类型
    private String operateType;
    // 业务划分
    private String businessType;
    // 信息
    private String message;
    // 被操作相关数据的主键
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = ListToStringHandle.class)
    private List<String> operateIds;

    // csv生成文件头部
    public static String[] generateCsvHeader() {
        List<String> headers = new ArrayList<>();
        Field[] declaredFields = InspLogOperate.class.getDeclaredFields();
        for (Field field : declaredFields) {
            headers.add(field.getName());
        }
        return headers.toArray(new String[0]);
    }
}
