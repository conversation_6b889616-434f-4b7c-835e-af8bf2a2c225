package com.hollysys.inspection.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hollysys.inspection.config.mybaits.ConstraintsToStringHandle;
import com.hollysys.inspection.config.mybaits.ObjectToStringHandle;
import com.hollysys.inspection.entity.base.BaseEntity;
import com.hollysys.inspection.model.algorithm.param.AlgorithmParamConst;
import lombok.Getter;
import lombok.Setter;
import org.apache.ibatis.type.JdbcType;

/**
 * 算法参数定义表(InspAlgorithmParam)表实体类
 *
 * <AUTHOR>
 * @since 2023-02-06 09:30:23
 */
@Setter
@Getter
@TableName(autoResultMap = true)
public class InspAlgorithmParam extends BaseEntity {
    // 所属算法ID
    private String algorithmId;
    // 参数key
    private String key;
    // 参数名称
    private String label;
    // 参数值数据类型
    private String dataType;
    // 参数默认值
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = ObjectToStringHandle.class)
    private Object defaultValue;
    // 参数值约束
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = ConstraintsToStringHandle.class)
    private AlgorithmParamConst constraints;
    // 是否将参数值绘制到OSD（图像类参数、出参有效）
    private Boolean drawToOsd;
    // 参数排序字段
    private Long sortNo;
    // 出参或者入参类型
    private String outOrIn;
}

