package com.hollysys.inspection.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpStatus;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.constants.FileDirEnum;
import com.hollysys.inspection.constants.InspConstants;
import com.hollysys.inspection.constants.UploadPicEnum;
import com.hollysys.inspection.entity.*;
import com.hollysys.inspection.entity.base.BaseEntity;
import com.hollysys.inspection.mapper.InspSceneCustomPicMapper;
import com.hollysys.inspection.model.FailedUploadModel;
import com.hollysys.inspection.model.channel.ScaleModel;
import com.hollysys.inspection.service.protocol.impl.CameraCtrlProxyService;
import com.hollysys.inspection.utils.LocalFileServerUtil;
import com.hollysys.inspection.utils.MinioUtil;
import com.hollysys.inspection.utils.PicUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * (InspSceneCustomPic)表服务接口
 *
 * <AUTHOR>
 * @since 2023-03-10 13:54:00
 */
@Service
@Slf4j
public class InspSceneCustomPicService extends ServiceImpl<InspSceneCustomPicMapper, InspSceneCustomPic> {

    @Value("${file-server.root-dir}")
    private String fileRootDir;

    @Resource
    private CameraCtrlProxyService cameraCtrlProxyService;

    @Resource
    private InspPresetInfoService inspPresetInfoService;

    @Resource
    private InspChannelInfoService inspChannelInfoService;

    @Resource
    private InspScheduleTaskNodeService inspScheduleTaskNodeService;

    @Resource
    private ProjectTreeNodeService projectTreeNodeService;

    @Resource
    private InspProjectService inspProjectService;

    @Resource
    private InspScheduleTaskService inspScheduleTaskService;

    @Value("${custom-schedule-pic.enable}")
    private boolean customEnable;

    public Object uploadScenePic(FailedUploadModel failedUploadModel) {
        String presetId = failedUploadModel.getPresetId();
        String proxyPath = failedUploadModel.getProxyPath();
        if (!proxyPath.endsWith(".JPEG")) {
            throw new InspectionException(HttpStatus.HTTP_BAD_REQUEST, "图片不是JPEG格式，请检查");
        }
        proxyPath = MinioUtil.urlToAbsolute(proxyPath);
        String proxyFilePath = LocalFileServerUtil.downloadToTemp(proxyPath).getAbsolutePath();
        File file = new File(proxyFilePath);
        if (!file.exists()) {
            throw new InspectionException(HttpStatus.HTTP_BAD_REQUEST, "图片地址错误，请检查");
        }
        validResolutionRatio(presetId, proxyFilePath);
        LambdaQueryWrapper<InspSceneCustomPic> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InspSceneCustomPic::getPresetId, presetId);
        queryWrapper.eq(InspSceneCustomPic::getType, UploadPicEnum.TEMPLATE_FAILED.name());
        InspSceneCustomPic inspSceneCustomPic = getOne(queryWrapper);
        if (inspSceneCustomPic == null) {
            save(new InspSceneCustomPic(presetId, Collections.singletonList(proxyFilePath), UploadPicEnum.TEMPLATE_FAILED.name()));
        } else {
            List<String> pics = inspSceneCustomPic.getPics();
            if (pics.size() == InspConstants.SCENE_PIC_COUNT) {
                pics.remove(0);
            }

            pics.add(proxyFilePath);
            update(new InspSceneCustomPic(presetId, pics, UploadPicEnum.TEMPLATE_FAILED.name()), queryWrapper);
        }
        return null;
    }

    public Object uploadScenePic(String presetId, MultipartFile[] sceneFiles) {
        if (sceneFiles.length > 10) {
            throw new InspectionException(HttpStatus.HTTP_BAD_REQUEST, "一次最多上传10张图片，每张不大于1M");
        }
        List<String> serverPaths = new ArrayList<>();
        List<String> deleteOutPic = new ArrayList<>();
        boolean saveOrUpdate = false;
        LambdaQueryWrapper<InspSceneCustomPic> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InspSceneCustomPic::getPresetId, presetId);
        queryWrapper.eq(InspSceneCustomPic::getType, UploadPicEnum.MANUAL.name());
        InspSceneCustomPic inspSceneCustomPic = getOne(queryWrapper);
        if (inspSceneCustomPic != null) {
            saveOrUpdate = true;
            serverPaths = inspSceneCustomPic.getPics();
            if (CollectionUtils.isEmpty(serverPaths)) {
                serverPaths = new ArrayList<>();
            }
            int originSize = serverPaths.size();
            int uploadSize = sceneFiles.length;
            int maxStorageSize = InspConstants.SCENE_PIC_COUNT;
            int outPic = originSize + uploadSize - maxStorageSize;
            delFirstPic(serverPaths, deleteOutPic, originSize, outPic);
        }
        List<String> successUploadList = new ArrayList<>();
        try {
            File file = new File(fileRootDir);
            if (!file.exists()) {
                file.mkdirs();
            }
            // 校验
            vaildFile(sceneFiles);
            transferFile(presetId, sceneFiles, serverPaths, successUploadList, file);
            modifyPic(presetId, serverPaths, deleteOutPic, saveOrUpdate, queryWrapper);
        } catch (InspectionException e) {
            if (!CollectionUtils.isEmpty(successUploadList)) {
                MinioUtil.removeObjects(successUploadList);
            }
            throw new InspectionException(e.getMessage());
        } catch (Exception e) {
            if (!CollectionUtils.isEmpty(successUploadList)) {
                MinioUtil.removeObjects(successUploadList);
            }
            log.error("文件上传失败，稍后请重试 message= {}", e.getMessage());
            throw new InspectionException("文件上传失败，稍后请重试");
        }
        return null;
    }

    private static void delFirstPic(List<String> serverPaths, List<String> deleteOutPic, int originSize, int outPic) {
        if (outPic > 0) {
            for (int i = originSize - 1; i >= originSize - outPic; i--) {
                deleteOutPic.add(serverPaths.get(i));
                serverPaths.remove(serverPaths.get(i));
            }
        }
    }

    private static void vaildFile(MultipartFile[] sceneFiles) throws IOException {
        for (MultipartFile multipartFile : sceneFiles) {
            // check file
            String name = multipartFile.getOriginalFilename();
            if (StrUtil.isBlank(name) || !name.contains(".") || (!name.toUpperCase().endsWith(InspConstants.PIC_JPEG)
                    && !name.toUpperCase().endsWith(InspConstants.PIC_PNG)
                    && !name.toUpperCase().endsWith(InspConstants.PIC_JPG))
                    || multipartFile.getBytes().length > InspConstants.PIC_MAX_BYTE) {

                throw new InspectionException(HttpStatus.HTTP_BAD_REQUEST, name + "-文件格式：单张大小不超过3M," +
                        "后缀为 = png、jpg、jpeg,请检查后再试");
            }
        }
    }

    private void transferFile(String presetId, MultipartFile[] sceneFiles, List<String> serverPaths, List<String> successUploadList, File file) throws IOException {
        for (MultipartFile sceneFile : sceneFiles) {
            String name = sceneFile.getOriginalFilename();
            String subName = name.substring(name.lastIndexOf("."));
            String fileName = DateUtil.format(new Date(), "yyyyMMddHHmmssSSS");
            File tempFile = LocalFileServerUtil.getTempFile(fileName + subName);
            sceneFile.transferTo(tempFile);
            validResolutionRatio(presetId, tempFile.getAbsolutePath());
            String httpFileUrl = MinioUtil.uploadFile(FileDirEnum.UPLOAD, tempFile);
            successUploadList.add(httpFileUrl);

            serverPaths.add(0, httpFileUrl);
        }
    }

    private void modifyPic(String presetId, List<String> serverPaths, List<String> deleteOutPic, boolean saveOrUpdate, LambdaQueryWrapper<InspSceneCustomPic> queryWrapper) {
        if (saveOrUpdate) {
            update(new InspSceneCustomPic(presetId, serverPaths, UploadPicEnum.MANUAL.name()), queryWrapper);
        } else {
            save(new InspSceneCustomPic(presetId, serverPaths, UploadPicEnum.MANUAL.name()));
        }
        if (!CollectionUtils.isEmpty(deleteOutPic)) {
            MinioUtil.removeObjects(deleteOutPic);
        }
    }

    private void validResolutionRatio(String presetId, String imgPath) {
        LambdaQueryWrapper<InspProjectTreeNode> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(InspProjectTreeNode::getId, presetId);
        InspProjectTreeNode one = projectTreeNodeService.getOne(lambdaQueryWrapper);
        String parentId = one.getParentId();
        InspChannelInfo byId = inspChannelInfoService.getOneById(parentId);

        ScaleModel scaleByImg = PicUtil.getScaleByLocalImg(imgPath);
        ScaleModel scale = cameraCtrlProxyService.getDisplayResolution(byId);
        if (!Objects.equals(scale.getWidth(), scaleByImg.getWidth()) || !Objects.equals(scale.getHeight(), scaleByImg.getHeight())) {
            throw new InspectionException("图片分辨率格式错误,要求=" + scale);
        }
    }

    public InspSceneCustomPic getCustomPic(Serializable presetId, String type) {
        LambdaQueryWrapper<InspSceneCustomPic> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InspSceneCustomPic::getPresetId, presetId);
        queryWrapper.eq(InspSceneCustomPic::getType, type);
        InspSceneCustomPic one = getOne(queryWrapper);
        if (Objects.isNull(one)) {
            one = new InspSceneCustomPic();
        }
        List<String> pics = one.getPics();
        if (CollectionUtils.isEmpty(pics)) {
            pics = new ArrayList<>();
        } else {
            Collections.reverse(pics);
        }
        InspPresetInfo inspPresetInfo = inspPresetInfoService.getOneById(presetId);
        String channelId = inspPresetInfo.getChannelId();
        InspChannelInfo channelInfo = inspChannelInfoService.getOneById(channelId);
        String path = cameraCtrlProxyService.getPic(channelInfo);
        pics.add(0, path);
        one.setPics(MinioUtil.urlsToRelative(pics));
        return one;
    }

    @Scheduled(cron = "${custom-schedule-pic.cron}")
    public void scheduleChannelPic() {
        if (!customEnable) {
            return;
        }
        InspProject firstOne = inspProjectService.getFirstOne();
        if (Objects.isNull(firstOne)) {
            return;
        }
        Boolean screenShotState = firstOne.getScreenShotState();
        if (!BooleanUtil.isTrue(screenShotState)) {
            log.warn("当前定时截图已关，跳过本地截图");
            return;
        }
        LambdaQueryWrapper<InspPresetInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspPresetInfo::getAvailable, true);
        wrapper.eq(InspPresetInfo::getIsSchedule, true);
        List<InspPresetInfo> presetInfos = inspPresetInfoService.list(wrapper);
        if (CollectionUtils.isEmpty(presetInfos)) {
            return;
        }
        presetInfos.forEach(y -> {
            List<InspScheduleTaskNode> inspScheduleTaskNodes = inspScheduleTaskNodeService.listByPresetId(y.getId());
            if (!CollectionUtils.isEmpty(inspScheduleTaskNodes)) {
                // 如果找到了任务节点，检查是否有已开启的调度任务
                for (InspScheduleTaskNode node : inspScheduleTaskNodes) {
                    InspScheduleTask task = inspScheduleTaskService.getById(node.getTaskId());
                    if (Objects.nonNull(task) && BooleanUtil.isTrue(task.getScheduleEnable())) {
                        // 只要找到一个已开启的调度任务，就跳过当前预置点
                        log.warn("预置点 {} 所属通道关联的调度任务已开启，跳过定时截图。", y.getId());
                        return;
                    }
                }
            }
            // 如果没有找到已开启的调度任务，则执行截图
            log.debug("当前定时截图预置点={}", JSON.toJSONString(y));
            InspChannelInfo inspChannelInfo = inspChannelInfoService.getOneById(y.getChannelId());
            String ip = inspChannelInfo.getAddress();
            String password = inspChannelInfo.getPassword();
            String username = inspChannelInfo.getUsername();
            autoSnapPic(y, inspChannelInfo, ip, password, username);
        });
    }

    private void autoSnapPic(InspPresetInfo y, InspChannelInfo inspChannelInfo, String ip, String password, String username) {
        try {
            cameraCtrlProxyService.gotoPreset(inspChannelInfo, y);
            String picHttpUrl = cameraCtrlProxyService.getPic(inspChannelInfo);
            // 图片复制到PictureCutTypeEnum.AUTO_CUT
            picHttpUrl = MinioUtil.copyObject(picHttpUrl, FileDirEnum.AUTO_CUT);
            LambdaQueryWrapper<InspSceneCustomPic> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(InspSceneCustomPic::getPresetId, y.getId());
            lambdaQueryWrapper.eq(InspSceneCustomPic::getType, UploadPicEnum.SCHEDULE.name());
            InspSceneCustomPic inspSceneCustomPic = getOne(lambdaQueryWrapper);
            if (inspSceneCustomPic == null) {
                save(new InspSceneCustomPic(y.getId(), Collections.singletonList(picHttpUrl), UploadPicEnum.SCHEDULE.name()));
            } else {
                List<String> pics = inspSceneCustomPic.getPics();
                if (pics.size() >= InspConstants.SCENE_PIC_COUNT) {
                    // removeAutoPic(pics, picHttpUrl);
                    // 删除第一张
                    String pic0 = pics.get(0);
                    MinioUtil.removeObject(pic0);
                    pics.remove(0);
                }
                log.debug("增加截图文件={}", picHttpUrl);
                pics.add(picHttpUrl);

                update(new InspSceneCustomPic(y.getId(), pics, UploadPicEnum.SCHEDULE.name()), lambdaQueryWrapper);
            }
        } catch (Exception e) {
            log.error("自动截图失败{},{},{}", ip, username, password, e);
        }
    }

    // public void removeAutoPic(List<String> pics, String picHttpUrl) {
    //     int index = -1;
    //     for (int i = 0; i < pics.size(); i++) {
    //         String pic = pics.get(i);
    //         LambdaQueryWrapper<InspSceneDefinition> lambdaQueryWrapper = new LambdaQueryWrapper<>();
    //         lambdaQueryWrapper.like(InspSceneDefinition::getBenchPic, file.getName());
    //         List<InspSceneDefinition> list = inspSceneDefinitionService.list(lambdaQueryWrapper);
    //         if (list.isEmpty()) {
    //             MinioUtil.removeObject(pic);
    //             log.info("删除老旧截图文件={}", pic);
    //             index = i;
    //             break;
    //         }
    //     }
    //     if (index != -1) {
    //         pics.remove(index);
    //         log.info("增加自动截图文件={}", picHttpUrl);
    //         pics.add(picHttpUrl);
    //     } else {
    //         MinioUtil.removeObject(picHttpUrl);
    //         log.info("删除无效文件-{}", picHttpUrl);
    //     }
    // }

    public void removeByChannelIds(String channelId) {
        List<InspProjectTreeNode> byParentId = projectTreeNodeService.getByParentId(channelId);
        if (!CollectionUtils.isEmpty(byParentId)) {
            LambdaQueryWrapper<InspSceneCustomPic> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(InspSceneCustomPic::getPresetId, byParentId.stream().map(BaseEntity::getId).collect(Collectors.toList()));
            remove(queryWrapper);
        }
    }

    public void removeByPresetIds(String id) {
        LambdaQueryWrapper<InspSceneCustomPic> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InspSceneCustomPic::getPresetId, id);
        remove(queryWrapper);
    }
}

