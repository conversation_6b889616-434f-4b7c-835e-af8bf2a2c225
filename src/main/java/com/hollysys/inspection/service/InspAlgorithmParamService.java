package com.hollysys.inspection.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hollysys.inspection.constants.algorithm.param.DataType;
import com.hollysys.inspection.constants.algorithm.param.OutOrInEnum;
import com.hollysys.inspection.entity.InspAlgorithmParam;
import com.hollysys.inspection.mapper.InspAlgorithmParamMapper;
import com.hollysys.inspection.utils.AssertUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 算法参数定义表(InspAlgorithmParam)表服务接口
 *
 * <AUTHOR>
 * @since 2023-02-06 09:30:23
 */
@Service
public class InspAlgorithmParamService extends ServiceImpl<InspAlgorithmParamMapper, InspAlgorithmParam> {

    public InspAlgorithmParam getOneById(String paramId) {
        InspAlgorithmParam one = lambdaQuery()
                .eq(InspAlgorithmParam::getId, paramId)
                .one();
        AssertUtil.isTrue(Objects.nonNull(one), "参数不存在");
        return one;
    }

    public List<InspAlgorithmParam> listInputByAlgorithmId(String algorithmId) {
        LambdaQueryWrapper<InspAlgorithmParam> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspAlgorithmParam::getAlgorithmId, algorithmId);
        wrapper.eq(InspAlgorithmParam::getOutOrIn, OutOrInEnum.IN.name());
        wrapper.orderByAsc(InspAlgorithmParam::getSortNo);
        return list(wrapper);
    }

    public List<InspAlgorithmParam> listOutputByAlgorithmId(String algorithmId) {
        LambdaQueryWrapper<InspAlgorithmParam> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspAlgorithmParam::getAlgorithmId, algorithmId);
        wrapper.eq(InspAlgorithmParam::getOutOrIn, OutOrInEnum.OUT.name());
        wrapper.orderByAsc(InspAlgorithmParam::getSortNo);
        return list(wrapper);
    }

    public List<InspAlgorithmParam> listOutputByAlgorithmIds(List<String> algorithmIds) {
        LambdaQueryWrapper<InspAlgorithmParam> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(InspAlgorithmParam::getAlgorithmId, algorithmIds);
        wrapper.eq(InspAlgorithmParam::getOutOrIn, OutOrInEnum.OUT.name());
        wrapper.orderByAsc(InspAlgorithmParam::getSortNo);
        return list(wrapper);
    }

    public List<InspAlgorithmParam> listByAlgorithmId(String algorithmId) {
        LambdaQueryWrapper<InspAlgorithmParam> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspAlgorithmParam::getAlgorithmId, algorithmId);
        return list(wrapper);
    }

    public void removeByAlgorithmId(String algorithmId) {
        LambdaQueryWrapper<InspAlgorithmParam> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspAlgorithmParam::getAlgorithmId, algorithmId);
        remove(wrapper);
    }

    @Transactional
    public void insertParams(String algorithmId, List<InspAlgorithmParam> inputs, List<InspAlgorithmParam> outputs) {
        List<InspAlgorithmParam> params = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(inputs)) {
            for (InspAlgorithmParam param : inputs) {
                param.setOutOrIn(OutOrInEnum.IN.name());
            }
            params.addAll(inputs);
        }

        AssertUtil.isTrue(CollectionUtil.isNotEmpty(outputs), "算法出参定义不允许为空");

        for (InspAlgorithmParam output : outputs) {
            output.setOutOrIn(OutOrInEnum.OUT.name());
        }
        params.addAll(outputs);

        if (CollectionUtil.isNotEmpty(params)) {
            // 参数检查
            checkParams(params);
            for (int i = 0; i < params.size(); i++) {
                InspAlgorithmParam param = params.get(i);
                param.setId(null);
                param.setSortNo((long)i);
                param.setAlgorithmId(algorithmId);
            }

            saveBatch(params);
        }
    }

    private void checkParams(List<InspAlgorithmParam> params) {
        if (CollectionUtil.isEmpty(params)) {
            return;
        }

        TreeSet<Object> keySet = new TreeSet<>();
        TreeSet<Object> labelSet = new TreeSet<>();
        for (InspAlgorithmParam param : params) {
            String key = param.getKey();
            AssertUtil.isTrue(StrUtil.isNotBlank(key), "算法参数标识不允许为空");

            String label = param.getLabel();
            AssertUtil.isTrue(StrUtil.isNotBlank(label), "算法参数名称不允许为空");

            // dataType格式校验
            String dataType = param.getDataType();
            AssertUtil.isTrue(EnumUtil.contains(DataType.class, dataType), "参数数据类型错误");

            // 参数出参类型校验
            String outOrIn = param.getOutOrIn();
            if (OutOrInEnum.OUT.name().equals(outOrIn)) {
                AssertUtil.isTrue(DataType.outputDataTypeNames().contains(dataType), "出参参数数据类型错误");
            } else {
                DataType dataTypeEnum = EnumUtil.fromString(DataType.class, dataType);
                // 参数值约束对象属性校验
                dataTypeEnum.checkConstraints(param.getConstraints());
                // 参数默认值类型校验
                Object defaultValue = param.getDefaultValue();
                if (Objects.nonNull(defaultValue)) {
                    AssertUtil.isTrue(dataTypeEnum.checkType(defaultValue), "参数[{}]默认值与数据类型不匹配", label);
                }
            }

            keySet.add(key);
            labelSet.add(label);
        }

        int size = params.size();
        // key重复校验
        AssertUtil.isTrue(size == keySet.size(), "算法中参数标识重复");
        // name重复校验
        AssertUtil.isTrue(size == labelSet.size(), "算法中参数名称重复");
    }

    public Map<String, InspAlgorithmParam> mapOutputByAlgorithmId(String algorithmId) {
        List<InspAlgorithmParam> algorithmParams = listOutputByAlgorithmId(algorithmId);
        return algorithmParams.stream().collect(Collectors.toMap(InspAlgorithmParam::getKey, Function.identity()));
    }
}

