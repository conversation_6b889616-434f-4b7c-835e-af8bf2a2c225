package com.hollysys.inspection.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.io.unit.DataSize;
import cn.hutool.core.io.unit.DataUnit;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hollysys.inspection.config.annotations.TempFileCleanable;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.entity.InspAlgorithmModel;
import com.hollysys.inspection.mapper.InspAlgorithmModelMapper;
import com.hollysys.inspection.model.model.ListModelReqModel;
import com.hollysys.inspection.model.model.SaveModelReqModel;
import com.hollysys.inspection.utils.AssertUtil;
import com.hollysys.inspection.utils.LocalFileServerUtil;
import com.hollysys.inspection.utils.StrEscapeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.Serializable;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class InspModelService extends ServiceImpl<InspAlgorithmModelMapper, InspAlgorithmModel> {
    private static final String ALGORITHM_FILE_TYPE = "zip";

    private static final String MODEL_VERSION = "1.0";

    private static final long MAX_MODEL_FILE_SIZE_GB = 2;

    @Resource
    private InspAlgorithmService inspAlgorithmService;

    @Value("${model.base}")
    private String modelBasePath;

    @Transactional
    @TempFileCleanable
    public Object newModal(SaveModelReqModel reqModel) {
        MultipartFile modelFile = reqModel.getModelFile();

        if (Objects.isNull(modelFile) || modelFile.getSize() <= 0) {
            throw new InspectionException("上传文件不允许为空");
        }
        String originalFilename = modelFile.getOriginalFilename();

        // 文件大小判断
        DataSize dataSize = DataSize.of(MAX_MODEL_FILE_SIZE_GB, DataUnit.GIGABYTES);
        long maxSize = dataSize.toBytes();
        long size = modelFile.getSize();
        if (size > maxSize) {
            throw new InspectionException("上传文件大小最大允许2GB");
        }

        // 文件类型判断
        String suffix = FileUtil.getSuffix(originalFilename);
        if (!ALGORITHM_FILE_TYPE.equals(suffix)) {
            throw new InspectionException("只允许上传zip压缩文件");
        }

        // 压缩包保存到临时目录
        File tempFile = LocalFileServerUtil.saveToTemp(modelFile);

        // 解压模型文件
        File actualContentDir = zipFile(tempFile);

        // 解压缩后查找模型数据 模型名字 code 版本
        InspAlgorithmModel inspAlgorithmModel = findModelSchema(actualContentDir);

        // 校验模型名称和模型code
        checkModelName(reqModel.getName());
        checkModelCode(inspAlgorithmModel.getCode());

        // 模型文件上传至宿主机指定目录
        Path targetPath = Paths.get(modelBasePath);

        // 创建目录
        FileUtil.mkdir(modelBasePath);

        // 将模型文件复制到目标目录
        FileUtil.move(actualContentDir.toPath(), targetPath, true);

        // 保存模型
        inspAlgorithmModel.setName(reqModel.getName());
        inspAlgorithmModel.setPackageFile(actualContentDir.getName());
        inspAlgorithmModel.setCreateTime(LocalDateTime.now());

        save(inspAlgorithmModel);

        log.debug("模型上传成功, 路径：{}", targetPath);
        return inspAlgorithmModel;
    }

    /**
     * 更新模型
     * @param reqModel 更新模型
     * @return 更新后的模型
     */
    @Transactional
    @TempFileCleanable
    public InspAlgorithmModel updateModal(SaveModelReqModel reqModel) {
        // 模型ID判空
        String modelId = reqModel.getModelId();
        if (StrUtil.isBlank(modelId)) {
            throw new InspectionException("模型ID不能为空");
        }

        // 模型名称判空
        String modelName = reqModel.getName();
        if (StrUtil.isBlank(modelName)) {
            throw new InspectionException("模型名称不能为空");
        }

        InspAlgorithmModel model = getOneById(reqModel.getModelId());

        MultipartFile modelFile = reqModel.getModelFile();

        if (Objects.nonNull(modelFile)) {
            // 存入临时文件夹
            File tempFile = LocalFileServerUtil.saveToTemp(modelFile);

            // 解压模型文件
            File actualContentDir = zipFile(tempFile);

            // 解压缩后查找模型数据 模型名字 code 版本
            InspAlgorithmModel inspAlgorithmModel = findModelSchema(actualContentDir);

            // 更新时必须保证模型code与之前的一致
            if (!model.getCode().equals(inspAlgorithmModel.getCode())) {
                throw new InspectionException("模型code不一致, 无法更新");
            }

            // 模型文件上传至宿主机指定目录
            Path targetPath = Paths.get(modelBasePath);
            // 将模型文件复制到目标目录
            FileUtil.move(actualContentDir.toPath(), targetPath, true);

            // 算法文件不为空则修改
            // 新文件名
            model.setPackageFile(actualContentDir.getName());
            // 新版本
            model.setVersion(inspAlgorithmModel.getVersion());
            // 更新时间
            model.setUpdateTime(LocalDateTime.now());

            log.debug("模型更新成功, 路径：{}", targetPath);
        }

        updateById(model);

        return model;
    }

    /**
     * 删除模型
     * @param modelId 模型id
     * @return 结果
     */
    @Transactional
    public boolean removeModel(String modelId) {
        InspAlgorithmModel model = getOneById(modelId);
        // 判断是否被引用
        inspAlgorithmService.checkModelUnUse(model.getCode());

        // 构建模型文件的完整路径
        String packageFile = model.getPackageFile();
        Path modelFilePath = Paths.get(modelBasePath, packageFile);
        File modelFileToDelete = modelFilePath.toFile();

        // 删除模型文件
        if (modelFileToDelete.exists()) {
            boolean deleted = FileUtil.del(modelFileToDelete);
            if (deleted) {
                log.debug("模型文件删除成功: {}", modelFilePath);
            } else {
                log.error("模型文件删除失败: {}", modelFilePath);
            }
        } else {
            log.warn("模型文件不存在，无需删除: {}", modelFilePath);
        }

        // 根据id删除模型
        removeById(modelId);
        return true;
    }

    /**
     * 查询模型数据
     * @param reqModel 请求参数
     * @return 模型数据
     */
    public Object listModel(Page<InspAlgorithmModel> page, ListModelReqModel reqModel) {
        // 获取请求参数中的模型名称
        String modelName = reqModel.getModelName();
        // 创建查询条件
        LambdaQueryWrapper<InspAlgorithmModel> wrapper = new LambdaQueryWrapper<>();
        // 如果模型名称不为空，则添加查询条件
        modelName = StrEscapeUtil.escapeChar(modelName);
        if (!StringUtils.isEmpty(modelName)) {
            wrapper.apply("name ILIKE CONCAT('%', {0}, '%')", modelName);
        }
        // 按照创建时间排序
        wrapper.orderByDesc(InspAlgorithmModel::getCreateTime);

        // 执行分页查询
        IPage<InspAlgorithmModel> modelPageResult = page(page, wrapper);
        List<InspAlgorithmModel> models = modelPageResult.getRecords();

        // 如果模型数据为空，则返回空列表
        if (models.isEmpty()) {
            return modelPageResult;
        }

        List<InspAlgorithmModel> updatedModels = models.stream().map(inspAlgorithmModel -> {
            // 查询并设置引用该模型的算法 name 列表
            List<String> algorithmNames = inspAlgorithmService.listAlgorithmNamesByModelCode(inspAlgorithmModel.getCode());
            inspAlgorithmModel.setReferencedAlgorithmNames(algorithmNames);
            return inspAlgorithmModel;
        }).collect(Collectors.toList());

        modelPageResult.setRecords(updatedModels);
        return  modelPageResult;

    }

    /**
     * 查找模型schema
     * @param unzipDir 模型文件
     * @return 模型数据
     */
    private InspAlgorithmModel findModelSchema(File unzipDir) {
        // 获取解压后的文件列表
        String[] listFiles = unzipDir.list();
        // 如果文件列表为空，抛出异常
        if (Objects.isNull(listFiles) || listFiles.length == 0) {
            throw new InspectionException("模型包内容不能为空");
        }

        // 获取解压后的文件路径
        String baseDir = unzipDir.getAbsolutePath();
        // 如果文件列表只有一个文件，则获取该文件的路径
        if (listFiles.length == 1) {
            baseDir = unzipDir + File.separator + listFiles[0];
        }

        // 获取schema.json文件的路径
        String schemaJsonPath = baseDir + File.separator + "schema.json";
        // 如果schema.json文件不存在，抛出异常
        if (!FileUtil.exist(schemaJsonPath)) {
            throw new InspectionException("模型包缺少缺少JSON定义文件");
        }
        String jsonStr = FileUtil.readUtf8String(schemaJsonPath);
        InspAlgorithmModel inspAlgorithmModel;
        try {
            inspAlgorithmModel = JSON.parseObject(jsonStr, InspAlgorithmModel.class);
            if (inspAlgorithmModel.getVersion().isEmpty()) {
                throw new InspectionException("模型版本号不能为空");
            }
            if (inspAlgorithmModel.getCode().isEmpty()) {
                throw new InspectionException("模型CODE不能为空");
            }
        } catch (Exception ex) {
            log.error("算法json内容解析失败", ex);
            throw new InspectionException("算法json内容解析失败");
        }

        return inspAlgorithmModel;
    }

    /**
     * 校验模型名称
     * @param modelName 模型名称
     */
    private void checkModelName(String modelName) {
        // 模型名称判空
        AssertUtil.isTrue(StrUtil.isNotBlank(modelName), "模型名称不能为空");

        // 算法名称长度校验
        AssertUtil.isTrue(modelName.length() <= 20, "模型名称最大长度为20");

        LambdaQueryWrapper<InspAlgorithmModel> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspAlgorithmModel::getName, modelName);
        int count = count(wrapper);
        AssertUtil.isTrue(count <= 0, "模型名称已存在");
    }

    /**
     * 校验模型CODE
     * @param modelCode 模型code
     */
    private void checkModelCode(String modelCode) {
        LambdaQueryWrapper<InspAlgorithmModel> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspAlgorithmModel::getCode, modelCode);
        int count = count(wrapper);
        if (count > 0) {
            throw new InspectionException("模型CODE已存在");
        }
    }

    /**
     * 根据模型id获取模型
     * @param id id
     * @return 模型
     */
    public InspAlgorithmModel getOneById(Serializable id) {
        InspAlgorithmModel byId = getById(id);
        if (Objects.isNull(byId)) {
            throw new InspectionException("模型信息不存在");
        }
        return byId;
    }

    /**
     * 检查模型是否上传，如果没上传抛出异常
     *
     * @param modelCode 模型编码
     */
    public void checkModelIsUpload(String modelCode) {
        LambdaQueryWrapper<InspAlgorithmModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InspAlgorithmModel::getCode, modelCode);
        int count = count(queryWrapper);
        if (count < 1) {
            throw new InspectionException("当前算法未上传相应的模型，请先上传模型");
        }
    }

    /**
     * 直接寻找根目录下的第一个文件夹解压
     * @param tempFile 文件
     * @return 根目录问价
     */
    private File zipFile(File tempFile) {
        File unzipDir;
        // 实际的模型内容目录
        File actualContentDir = null;
        try {
            unzipDir = ZipUtil.unzip(tempFile);
            File[] filesInUnzipRoot = unzipDir.listFiles();
            if (filesInUnzipRoot == null || filesInUnzipRoot.length != 1 || !filesInUnzipRoot[0].isDirectory()) {
                // 如果不符合 "解压后只有唯一的目录" 的预期，则抛出异常
                throw new InspectionException("模型压缩包结构不符合预期，解压后必须包含唯一的根目录。");
            }
            actualContentDir = filesInUnzipRoot[0];
            return actualContentDir;
        } catch (Exception exception) {
            throw new InspectionException("模型压缩包解压错误");
        }
    }
}
