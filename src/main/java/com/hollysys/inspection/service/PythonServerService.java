package com.hollysys.inspection.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.config.exceptions.NotSupportOnvifException;
import com.hollysys.inspection.constants.FileDirEnum;
import com.hollysys.inspection.constants.InspConstants;
import com.hollysys.inspection.constants.OnvifError;
import com.hollysys.inspection.constants.algorithm.AlgorithmType;
import com.hollysys.inspection.controller.base.Resp;
import com.hollysys.inspection.entity.InspChannelInfo;
import com.hollysys.inspection.model.algorithm.correct.CorrectReqModel;
import com.hollysys.inspection.model.algorithm.correct.CorrectRespModel;
import com.hollysys.inspection.model.algorithm.correct.PicCorrectParamModel;
import com.hollysys.inspection.model.algorithm.execute.AlgorithmExecutePyReq;
import com.hollysys.inspection.model.algorithm.execute.AlgorithmExecuteRsp;
import com.hollysys.inspection.model.algorithm.osd.OSDItem;
import com.hollysys.inspection.model.algorithm.param.data.Circle;
import com.hollysys.inspection.model.algorithm.param.data.Ellipse;
import com.hollysys.inspection.model.algorithm.param.data.Point;
import com.hollysys.inspection.model.algorithm.param.data.Square;
import com.hollysys.inspection.model.channel.RtspStreamModel;
import com.hollysys.inspection.model.channel.ScaleModel;
import com.hollysys.inspection.model.draw.MatchPositionModel;
import com.hollysys.inspection.model.draw.PyDrawShapeReqModel;
import com.hollysys.inspection.utils.ExecuteUtil;
import com.hollysys.inspection.utils.InspStringUtils;
import com.sun.net.httpserver.HttpPrincipal;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.Credentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.Principal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class PythonServerService {

    @Value("${py-server.url.opt.matching}")
    private String matchingUrl;

    @Value("${py-server.url.opt.transform-pic}")
    private String transformPicUrl;

    @Value("${py-server.url.opt.transform-points}")
    private String transformPointsUrl;

    @Value("${py-server.url.cv.algorithm-upload}")
    private String algorithmUploadCvUrl;

    @Value("${py-server.url.depl.algorithm-upload}")
    private String algorithmUploadDeplUrl;

    @Value("${py-server.url.opt.transform-circle}")
    private String transformCircleUrl;


    @Value("${py-server.url.opt.channel-scale-ip}")
    private String currentScaleIpUrl;

    @Value("${py-server.url.opt.channel-scale-rtsp}")
    private String currentScaleRtspUrl;

    @Value("${py-server.url.opt.draw-shape}")
    private String drawShapeUrl;

    @Value("${py-server.url.depl.algorithm-execute}")
    private String algorithmDeplExecuteUrl;

    @Value("${py-server.url.cv.algorithm-execute}")
    private String algorithmCvExecuteUrl;

    @Resource
    private RestTemplate restTemplate;

    @Value("${py-server.url.opt.channel-pic}")
    private String channelPicUrl;

    @Value("${py-server.url.opt.channel-stream}")
    private String channelStream;

    @Value("${py-server.url.opt.control-relative-operate}")
    private String relativeOperateUrl;
    @Value("${py-server.url.opt.point-set}")
    private String pointSetUrl;
    @Value("${py-server.url.opt.point-goto}")
    private String gotoPointUrl;
    @Value("${py-server.url.opt.point-goto-async}")
    private String gotoPointAsyncUrl;
    @Value("${py-server.url.opt.point-remove}")
    private String pointRemoveUrl;

    @Value("${py-server.url.opt.start-control-continuous}")
    private String startContinuousUrl;

    @Value("${py-server.url.opt.stop-control-continuous}")
    private String stopContinuousUrl;

    @Value("${py-server.url.opt.manufacturer-info}")
    private String manufacturerInfoUrl;

    @Value("${py-server.url.depl.gallery-cls-update}")
    private String galleryClsUpdateUrl;

    @Value("${file-server.root-dir}")
    private String fileServeRootDir;

    @Value("${py-server.url.cv.algorithm-remove}")
    private String algorithmRemoveCvUrl;

    @Value("${py-server.url.depl.algorithm-remove}")
    private String algorithmRemoveDeplUrl;

    private final static String IMG_PATH = "img_path";

    private ResponseEntity<Resp> postForEntity(String url, Object param) {
        return postForEntity(url, param, null);
    }

    private ResponseEntity<Resp> postForEntity(String url, Object param, MediaType contentType) {
        HttpHeaders requestHeaders = new HttpHeaders();
        if (Objects.isNull(contentType)) {
            contentType = MediaType.APPLICATION_JSON;
        }
        requestHeaders.setContentType(contentType);
        HttpEntity<Object> requestEntity = new HttpEntity<>(param, requestHeaders);
        ResponseEntity<Resp> rResponseEntity;
        try {
            rResponseEntity = restTemplate.postForEntity(url, requestEntity, Resp.class);
        } catch (RestClientException exception) {
            log.error("远程服务访问异常，url = {}", url, exception);
            throw new InspectionException("远程服务访问异常");
        }
        return rResponseEntity;
    }

    private ResponseEntity<Resp> getForEntity(String url) {
        ResponseEntity<Resp> rResponseEntity;
        try {
            rResponseEntity = restTemplate.getForEntity(url, Resp.class);
        } catch (RestClientException exception) {
            log.error("远程服务访问异常，url = {}", url, exception);
            throw new InspectionException("远程服务访问异常");
        }
        return rResponseEntity;
    }

    private boolean isSuccess(Resp body) {
        return body != null && (body.getCode() == 0 || body.getCode() == 200);
    }

    /**
     * 算法执行
     *
     * @param algorithmExecutePyReq 算法执行入参
     * @param algorithmType         算法类型
     */
    public AlgorithmExecuteRsp algorithmExecute(AlgorithmExecutePyReq algorithmExecutePyReq, String algorithmType) {
        String algorithmExecuteUrl;
        if (AlgorithmType.OPENCV.getKey().equals(algorithmType)) {
            algorithmExecuteUrl = algorithmCvExecuteUrl;
        } else if (AlgorithmType.DEEP_LEARNING.getKey().equals(algorithmType)) {
            algorithmExecuteUrl = algorithmDeplExecuteUrl;
        } else {
            throw new InspectionException("执行失败，算法类型错误");
        }
        ExecuteUtil.printExecuteLog(log, "algorithmExecute  url is {}", algorithmExecuteUrl);
        ExecuteUtil.printExecuteLog(log, "algorithmExecute  param is {}", JSONUtil.toJsonStr(algorithmExecutePyReq));
        ResponseEntity<Resp> result;
        try {
            result = postForEntity(algorithmExecuteUrl, algorithmExecutePyReq);
        } catch (Exception exception) {
            throw new InspectionException("算法服务调用出现异常");
        }

        Resp body = result.getBody();
        ExecuteUtil.printExecuteLog(log, "algorithmExecute  result is {}", JSONUtil.toJsonStr(body));

        if (Objects.isNull(body)) {
            throw new InspectionException("算法执行失败，算法服务返回结果为Null");
        }
        boolean success = isSuccess(body);
        AlgorithmExecuteRsp algorithmExecuteRsp;
        if (success) {
            String pointStr = JSONUtil.toJsonStr(body.getData());
            algorithmExecuteRsp = JSONObject.parseObject(pointStr, AlgorithmExecuteRsp.class);
            algorithmExecuteRsp.setIsSuccess(true);
        } else {
            algorithmExecuteRsp = new AlgorithmExecuteRsp();
            algorithmExecuteRsp.setIsSuccess(false);
            algorithmExecuteRsp.setMsg(body.getMsg());
        }
        return algorithmExecuteRsp;
    }

    /**
     * 场景匹配
     *
     * @param originImg     原图
     * @param tmpImgs       模板图
     * @param benchMarkImgs 基准图
     * @return 匹配坐标
     */
    public MatchPositionModel matchingPosition(String originImg, List<String> tmpImgs, List<String> benchMarkImgs) {
        JSONObject param = new JSONObject();
        param.put(IMG_PATH, originImg);
        param.put("tmp_paths", tmpImgs);
        param.put("bench_mark_paths", benchMarkImgs);

        ExecuteUtil.printExecuteLog(log, "matchingPosition  start, param is {}", JSONUtil.toJsonStr(param));
        ResponseEntity<Resp> result = postForEntity(matchingUrl, param);
        Resp body = result.getBody();
        if (isSuccess(body)) {
            MatchPositionModel matchPositionModel = new MatchPositionModel();
            String data = JSONUtil.toJsonStr(body.getData());
            JSONObject jsonObject = JSON.parseObject(data, JSONObject.class);
            Square square = Square.buildByObj(jsonObject.get("square"));
            matchPositionModel.setSquare(square);
            matchPositionModel.setIndex(jsonObject.getInteger("index"));

            ExecuteUtil.printExecuteLog(log, "matchingPosition  success, result is {}", JSONUtil.toJsonStr(matchPositionModel));
            return matchPositionModel;
        } else {
            log.error("matchingPosition  error, url is {}, param is {}, result is {}", matchingUrl,
                    JSONUtil.toJsonStr(param), JSONUtil.toJsonStr(body));
            return null;
        }
    }

    /**
     * 透视变换图片（梯形校正）
     *
     * @param reqModel 矫正参数
     * @return 矫正结果
     */
    public CorrectRespModel transformPic(CorrectReqModel reqModel) {
        // 计算校正后的图像的矩形四点坐标
        Square formSquare = reqModel.getSquare();
        float height1 = getDistance(formSquare.get(0), formSquare.get(1));
        float height2 = getDistance(formSquare.get(3), formSquare.get(2));
        float width1 = getDistance(formSquare.get(0), formSquare.get(3));
        float width2 = getDistance(formSquare.get(1), formSquare.get(2));

        int newWidth = NumberUtil.round(NumberUtil.max(width1, width2) * reqModel.getRatio(), 0).intValue();
        int newHeight = NumberUtil.round(NumberUtil.max(height1, height2), 0).intValue();
        Square toSquare = new Square(newWidth, newHeight);
        JSONObject param = new JSONObject();
        param.put(IMG_PATH, reqModel.getInputPath());
        param.put("form_square", formSquare);
        param.put("to_square", toSquare);
        String resultImg = doTransformPic(param);

        CorrectRespModel correctRespModel = new CorrectRespModel();
        correctRespModel.setRatio(reqModel.getRatio());
        correctRespModel.setFormSquare(formSquare);
        correctRespModel.setToSquare(toSquare);
        correctRespModel.setResultImg(resultImg);
        return correctRespModel;
    }


    public String transformPic(PicCorrectParamModel picCorrectParamModel, String inputPath) {
        JSONObject param = new JSONObject();
        param.put(IMG_PATH, inputPath);
        Square fromSquare = picCorrectParamModel.getFromSquare();
        param.put("form_square", fromSquare);
        Square toSquare = picCorrectParamModel.getToSquare();
        param.put("to_square", toSquare);
        return doTransformPic(param);
    }

    private String doTransformPic(JSONObject param) {
        ResponseEntity<Resp> result = postForEntity(transformPicUrl, param);
        Resp body = result.getBody();
        boolean isSuccess = isSuccess(body);
        ExecuteUtil.printExecuteLog(log, "correctPic transformPicUrl is {}", transformPicUrl);
        ExecuteUtil.printExecuteLog(log, "param: {}", JSONUtil.toJsonStr(param));
        ExecuteUtil.printExecuteLog(log, "result: {}", JSONUtil.toJsonStr(body));
        if (!isSuccess) {
            log.error("透视变换矫正失败");
            throw new InspectionException("透视变换矫正失败");
        }
        return body.getData().toString();
    }

    /**
     * 计算两点之间距离
     */
    private float getDistance(Point point1, Point point2) {
        int xOffset = point1.getX() - point2.getX();
        int yOffset = point1.getY() - point2.getY();
        return NumberUtil.sqrt((long) xOffset * xOffset + (long) yOffset * yOffset);
    }

    // public String cropPic(CropPicModel cropPicModel) {
    //     JSONObject param = new JSONObject();
    //     param.put("filePath", cropPicModel.getFilePath());
    //
    //     Square square = cropPicModel.getSquare();
    //     param.put("topLeftY", square.get(0).getY());
    //     param.put("bottomLeftY", square.get(1).getY());
    //     param.put("topLeftX", square.get(0).getX());
    //     param.put("topRightX", square.get(2).getX());
    //     String outPath = FileUtil.normalize(fileRootDir + PictureCutTypeEnum.SCENE.name() + File.separator);
    //     param.put("outPath", outPath);
    //     ResponseEntity<Resp> result = postForEntity(cropUrl, param);
    //     Resp body = result.getBody();
    //     if (isSuccess(body)) {
    //         return (String) body.getData();
    //     } else {
    //         logger.error("裁剪失败，稍后再试, url = {} , cropPicModel={} , result is {}", cropUrl, JSONUtil.toJsonStr(param),
    //                 JSONUtil.toJsonStr(body));
    //         throw new InspectionException("裁剪失败");
    //     }
    // }

    public List<Point> transformPoints(List<Point> points, Square formSquare, Square toSquare) {
        JSONObject param = new JSONObject();
        param.put("input_points", points);
        param.put("form_square", formSquare);
        param.put("to_square", toSquare);
        ResponseEntity<Resp> result = postForEntity(transformPointsUrl, param);
        Resp body = result.getBody();
        if (isSuccess(body)) {
            Object data = body.getData();
            return JSON.parseArray(JSONUtil.toJsonStr(data), Point.class);
        } else {
            log.error("透视变换点列表失败，稍后再试url = {} ,param={},result = {}",
                    transformPointsUrl,
                    JSONUtil.toJsonStr(param),
                    JSONUtil.toJsonStr(body));
            throw new InspectionException("透视变换点列表失败");
        }
    }

    /**
     * 算法文件上传至Python服务器
     *
     * @param algorithmFile 算法文件
     * @param code          算法code
     * @param algorithmType 算法类型
     */
    public void uploadAlgorithmToPy(File algorithmFile, String code, String algorithmType) {
        String algorithmUploadUrl;
        if (AlgorithmType.OPENCV.getKey().equals(algorithmType)) {
            algorithmUploadUrl = algorithmUploadCvUrl;
        } else if (AlgorithmType.DEEP_LEARNING.getKey().equals(algorithmType)) {
            algorithmUploadUrl = algorithmUploadDeplUrl;
        } else {
            throw new InspectionException("上传失败，算法类型错误...");
        }

        FileSystemResource resource = new FileSystemResource(algorithmFile);
        MultiValueMap<String, Object> param = new LinkedMultiValueMap<>();
        // 参数
        param.add("file", resource);
        param.add("name", code);
        log.debug("uploadAlgorithmToPy start....  code = {},  url = {}", code, algorithmUploadUrl);
        ResponseEntity<Resp> result;
        try {
            result = postForEntity(algorithmUploadUrl, param, MediaType.MULTIPART_FORM_DATA);
        } catch (Exception exception) {
            log.error("uploadAlgorithmToPy error....  code = {},  url = {}", code, algorithmUploadUrl, exception);
            throw new InspectionException("上传失败");
        }
        Resp body = result.getBody();
        if (!isSuccess(body)) {
            log.error("uploadAlgorithmToPy failed....  code = {}, result = {}", code, JSONUtil.toJsonStr(body));
            throw new InspectionException("上传失败");
        }
        log.debug("uploadAlgorithmToPy end....  code = {}", code);
    }

    /**
     * 算法从py服务删除
     *
     * @param code          算法code
     * @param algorithmType 算法类型
     */
    public void removeAlgorithmToPy(String code, String algorithmType) {
        String algorithmRemoveUrl;
        if (AlgorithmType.OPENCV.getKey().equals(algorithmType)) {
            algorithmRemoveUrl = algorithmRemoveCvUrl;
        } else if (AlgorithmType.DEEP_LEARNING.getKey().equals(algorithmType)) {
            algorithmRemoveUrl = algorithmRemoveDeplUrl;
        } else {
            throw new InspectionException("删除失败，算法类型错误...");
        }

        MultiValueMap<String, Object> param = new LinkedMultiValueMap<>();
        param.add("code", code);
        log.debug("removeAlgorithmToPy start....  code = {},  url = {}", code, algorithmRemoveUrl);
        ResponseEntity<Resp> result;

        try {
            result = postForEntity(algorithmRemoveUrl, param, MediaType.MULTIPART_FORM_DATA);
        } catch (Exception exception) {
            log.error("removeAlgorithmToPy error....  code = {},  url = {}", code, algorithmRemoveUrl, exception);
            throw new InspectionException("删除算法失败");
        }
        Resp body = result.getBody();
        if (!isSuccess(body)) {
            log.error("removeAlgorithmToPy failed....  code = {}, result = {}", code, JSONUtil.toJsonStr(body));
            throw new InspectionException("删除算法失败");
        }
        log.debug("removeAlgorithmToPy end....  code = {}", code);
    }

    /**
     * 透视变换圆形到椭圆
     *
     * @param circle     圆形参数
     * @param formSquare 原始待矫正四边形四点坐标
     * @param toSquare   矫正结果四边形四点坐标
     * @return 结果椭圆参数
     */
    public Ellipse circleToEllipse(Circle circle, Square formSquare, Square toSquare) {
        JSONObject param = new JSONObject();
        param.put("form_square", formSquare);
        param.put("to_square", toSquare);
        param.put("center", circle.getCenter());
        param.put("radius", circle.getRadius());
        ResponseEntity<Resp> result = postForEntity(transformCircleUrl, param);
        Resp body = result.getBody();
        if (!isSuccess(body)) {
            log.error("透视变换圆形到椭圆失败   url: {}  param: {}   result: {}", transformCircleUrl,
                    JSONUtil.toJsonStr(param), JSONUtil.toJsonStr(body));
            throw new InspectionException("透视变换圆形到椭圆失败");
        }

        Object data = body.getData();
        String dataJsonStr = JSONUtil.toJsonStr(data);
        JSONArray jsonArray = JSON.parseArray(dataJsonStr);
        JSONArray jsonArray0 = jsonArray.getJSONArray(0);
        JSONArray jsonArray1 = jsonArray.getJSONArray(1);
        Float angle = jsonArray.getFloat(2);
        Ellipse ellipse = new Ellipse();
        // TODO
        ellipse.setCenter(new Point(jsonArray0.getInteger(0), jsonArray0.getInteger(1)));
        ellipse.setAxes(new Integer[]{jsonArray1.getInteger(0), jsonArray1.getInteger(1)});
        ellipse.setAngle(angle);
        return ellipse;
    }

    public String drawShape(String imgUrl, List<OSDItem> osdItemList) {
        PyDrawShapeReqModel pyDrawShapeReqModel = new PyDrawShapeReqModel();
        pyDrawShapeReqModel.setImgUrl(imgUrl);
        pyDrawShapeReqModel.setOsdItemList(osdItemList);

        ExecuteUtil.printExecuteLog(log, "drawShape param: {}", JSONUtil.toJsonStr(pyDrawShapeReqModel));
        ExecuteUtil.printExecuteLog(log, "drawShapeUrl: {}", drawShapeUrl);
        ResponseEntity<Resp> result = postForEntity(drawShapeUrl, pyDrawShapeReqModel);
        Resp body = result.getBody();
        ExecuteUtil.printExecuteLog(log, "drawShape result: {}", JSONUtil.toJsonStr(body));
        if (!isSuccess(body)) {
            log.error("绘制图形到图像中失败");
            throw new InspectionException("绘制图形到图像中失败");
        }
        return body.getData().toString();
    }

    /**
     * 先通过Onvif协议获取图片截图地址
     * 再通过图片截图地址获取图片
     * 调用两次远程南横接口
     */
    public String getChannelCurrentPicOnvif(FileDirEnum cutType, String address, String username,
                                            String password) {
        // 创建输出流  输出到本地
        try {
            long start = System.currentTimeMillis();
            String format = String.format(channelPicUrl, address, username, password, false);
            ResponseEntity<Resp> res = getForEntity(format);
            Resp body = res.getBody();
            if (!isSuccess(body)) {
                log.error("获取摄像机截图路径失败，url = {}, result = {}", format, JSONUtil.toJsonStr(body));
                if (Objects.isNull(body)) {
                    throw new InspectionException("截图失败");
                }
                long code = body.getCode();
                OnvifError byCode = OnvifError.getByCode((int) code);
                if (Objects.isNull(byCode)) {
                    throw new InspectionException("截图失败");
                }
                throw new NotSupportOnvifException(byCode.getCode(), byCode.getMsg());
            }
            String snapUrl = body.getData().toString();

            Date dateNow = new Date();
            String dateStr = DateUtil.format(dateNow, InspConstants.yyyyMMddHHmmss);
            String parentPath = cutType.name();
            String filePath = fileServeRootDir + parentPath + File.separator + dateStr + RandomUtil.randomInt(1000, 10000) + ".JPEG";
            getMethodFDLib(snapUrl, address, filePath, username, password);
            log.debug("onvif截图任务执行完成，onvifUri[" + format + "]，path[" + filePath + "]，耗时" + (System.currentTimeMillis() - start) + "毫秒");
            return filePath;
        } catch (InspectionException e) {
            throw e;
        } catch (Exception e) {
            log.error("{}.,截图失败,稍后再试", address, e);
            throw new InspectionException("截图失败");
        }
    }

    private static CloseableHttpClient digestAuth(String host, String username, String pwd, int port) {
        CredentialsProvider credsProvider = new BasicCredentialsProvider();
        RequestConfig config = RequestConfig.custom()
                .setConnectTimeout(10000) // 连接超时（毫秒）
                .setConnectionRequestTimeout(3000) // 从连接池获取连接超时
                .setSocketTimeout(10000) // 数据传输超时（响应超时）
                .build();
        CloseableHttpClient httpclient = HttpClients.custom()
                .setDefaultCredentialsProvider(credsProvider)
                .setDefaultRequestConfig(config)
                .build();
        credsProvider.setCredentials(new AuthScope(host, port), new Credentials() {
            @Override
            public Principal getUserPrincipal() {
                return new HttpPrincipal(username, "");
            }

            @Override
            public String getPassword() {
                return pwd;
            }
        });

        return httpclient;
    }

    public static void getMethodFDLib(String snapshotUrl, String host, String path, String user, String pwd) throws IOException {
        URL url = new URL(snapshotUrl);
        int port = url.getPort();
        port = port == -1 ? 80 : port;
        CloseableHttpClient httpclient = digestAuth(host, user, pwd, port);
        try {
            HttpGet getMethod = new HttpGet(snapshotUrl);
            org.apache.http.HttpResponse response = httpclient.execute(getMethod);
            org.apache.http.HttpEntity entity = response.getEntity();

            FileUtil.mkParentDirs(path);
            OutputStream outputStream = Files.newOutputStream(Paths.get(path));
            entity.writeTo(outputStream);
            outputStream.close();
        } catch (Exception e) {
            log.error("onvif截图失败,url={}", url, e);
            throw new InspectionException("onvif截图失败");
        } finally {
            httpclient.close();
        }
    }

    public List<RtspStreamModel> getChannelStream(String address, String user, String pwd) {
        String formatUrl = String.format(channelStream, address, user, pwd);
        ResponseEntity<Resp> res;
        try {
            res = getForEntity(formatUrl);
        } catch (InspectionException exception) {
            throw exception;
        } catch (Exception exception) {
            log.error("获取stream失败,稍后再试", exception);
            log.error("getChannelStream url = {}", formatUrl);
            throw new InspectionException("获取stream失败");
        }

        Resp body = res.getBody();
        if (!isSuccess(body)) {
            log.error("获取stream失败,响应结果为失败状态 result body = {}", JSONUtil.toJsonStr(body));
            log.error("getChannelStream url = {}", formatUrl);
            if (Objects.isNull(body)) {
                throw new InspectionException("获取stream失败");
            }
            long code = body.getCode();
            OnvifError byCode = OnvifError.getByCode((int) code);
            if (Objects.isNull(byCode)) {
                throw new InspectionException("获取stream失败");
            }
            throw new InspectionException(byCode.getCode(), byCode.getMsg());
        } else {
            return JSONUtil.toList(JSONUtil.toJsonStr(body.getData()), RtspStreamModel.class);
        }
    }

    public void controlOperate(String address, String username, String password, int action, Double pStep, Double
            tStep, Double zStep) {
        String url = String.format(relativeOperateUrl, address, username, password, action);
        if (pStep != null) {
            url = url + "&p_step=" + pStep;
        }
        if (tStep != null) {
            url = url + "&t_step=" + tStep;
        }
        if (zStep != null) {
            url = url + "&z_step=" + zStep;
        }
        ResponseEntity<Resp> res = getForEntity(url);
        Resp body = res.getBody();
        if (!isSuccess(body)) {
            log.error("{}-相对云台控制操作失败,稍后再试", address);
            if (Objects.isNull(body)) {
                throw new InspectionException("云台控制操作失败");
            }
            long code = body.getCode();
            OnvifError byCode = OnvifError.getByCode((int) code);
            if (Objects.isNull(byCode)) {
                throw new InspectionException("云台控制操作失败");
            }
            throw new NotSupportOnvifException(byCode.getCode(), byCode.getMsg());
        }
    }

    public void startControlOperate(String address, String username, String password, int action) {
        String url = String.format(startContinuousUrl, address, username, password, action);
        ResponseEntity<Resp> res = getForEntity(url);
        Resp body = res.getBody();
        if (!isSuccess(body)) {
            log.error("{}-开始云台控制操作失败,稍后再试,{}", address, JSONUtil.toJsonStr(body));
            if (Objects.isNull(body)) {
                throw new InspectionException("开始云台控制操作失败");
            }
            long code = body.getCode();
            OnvifError byCode = OnvifError.getByCode((int) code);
            if (Objects.isNull(byCode)) {
                throw new InspectionException("开始云台控制操作失败");
            }
            throw new NotSupportOnvifException(byCode.getCode(), byCode.getMsg());
        }
    }

    public void stopControlOperate(String address, String username, String password) {
        String url = String.format(stopContinuousUrl, address, username, password);
        ResponseEntity<Resp> res = getForEntity(url);
        Resp body = res.getBody();
        if (!isSuccess(body)) {
            log.error("{}-停止云台控制操作失败,稍后再试", address);
            if (Objects.isNull(body)) {
                throw new InspectionException("停止云台控制操作失败");
            }
            long code = body.getCode();
            OnvifError byCode = OnvifError.getByCode((int) code);
            if (Objects.isNull(byCode)) {
                throw new InspectionException("停止云台控制操作失败");
            }
            throw new NotSupportOnvifException(byCode.getCode(), byCode.getMsg());
        }
    }

    public void setPoint(String address, String username, String password, String name, Integer number) {
        String url = String.format(pointSetUrl, address, username, password, name, number);
        ResponseEntity<Resp> res = getForEntity(url);
        Resp body = res.getBody();
        if (!isSuccess(body)) {
            log.error("{}-预置点设置失败,稍后再试，url = {}, result = {}", address, url, JSONUtil.toJsonStr(body));
            if (Objects.isNull(body)) {
                throw new InspectionException("预置点设置失败");
            }
            long code = body.getCode();
            OnvifError byCode = OnvifError.getByCode((int) code);
            if (Objects.isNull(byCode)) {
                throw new InspectionException("预置点设置失败");
            }
            throw new NotSupportOnvifException(byCode.getCode(), byCode.getMsg());
        }
    }

    /**
     * 同步跳转预置点
     */
    public boolean gotoPoint(String address, String username, String password, Integer number) {
        String url = String.format(gotoPointUrl, address, username, password, number);
        ResponseEntity<Resp> res = getForEntity(url);
        Resp body = res.getBody();
        if (!isSuccess(body)) {
            log.error("同步跳转预置点操作失败,稍后再试,url={},result={}", url, JSONUtil.toJsonStr(body));
            if (Objects.isNull(body)) {
                throw new InspectionException("同步跳转预置点失败");
            }
            long code = body.getCode();
            OnvifError byCode = OnvifError.getByCode((int) code);
            if (Objects.isNull(byCode)) {
                throw new InspectionException("同步跳转预置点失败");
            }
            throw new NotSupportOnvifException(byCode.getCode(), byCode.getMsg());
        }

        Object data = body.getData();
        return BooleanUtil.toBoolean(data.toString());
    }

    /**
     * 异步跳转预置点
     */
    public boolean gotoPointAsync(String address, String username, String password, Integer number) {
        String url = String.format(gotoPointAsyncUrl, address, username, password, number);
        ResponseEntity<Resp> res = getForEntity(url);
        Resp body = res.getBody();
        if (!isSuccess(body)) {
            log.error("{}-异步跳转预置点操作失败,稍后再试", address);
            if (Objects.isNull(body)) {
                throw new InspectionException("异步跳转预置点失败");
            }
            long code = body.getCode();
            OnvifError byCode = OnvifError.getByCode((int) code);
            if (Objects.isNull(byCode)) {
                throw new InspectionException("异步跳转预置点失败");
            }
            throw new NotSupportOnvifException(byCode.getCode(), byCode.getMsg());
        }
        Object data = body.getData();
        return BooleanUtil.toBoolean(data.toString());
    }

    public void removePoint(String address, String username, String password, Integer number) {
        String url = String.format(pointRemoveUrl, address, username, password, number);
        ResponseEntity<Resp> res = getForEntity(url);
        Resp body = res.getBody();
        if (!isSuccess(body)) {
            log.error("{}-移除预置点操作失败,稍后再试", address);
            if (Objects.isNull(body)) {
                throw new InspectionException("移除预置点操作失败");
            }
            long code = body.getCode();
            OnvifError byCode = OnvifError.getByCode((int) code);
            if (Objects.isNull(byCode)) {
                throw new InspectionException("移除预置点操作失败");
            }
            throw new NotSupportOnvifException(byCode.getCode(), byCode.getMsg());
        }
    }

    public ScaleModel getScaleByOnvif(InspChannelInfo byId) {
        String url = String.format(currentScaleIpUrl, byId.getAddress(), byId.getUsername(), byId.getPassword());
        ResponseEntity<Resp> res = getForEntity(url);
        Resp body = res.getBody();
        if (!isSuccess(body)) {
            log.error("onvif获取分辨率失败,响应结果为失败状态 result body = {}", JSONUtil.toJsonStr(body));
            throw new InspectionException("获取分辨率失败,请检查onvif配置是否正确");
        }
        return JSONUtil.toBean(JSONUtil.toJsonStr(body.getData()), ScaleModel.class);
    }

    public JSONObject getScaleByRtsp(InspChannelInfo byId) {
        String rtsp = InspStringUtils.replaceRTSP(byId.getRtsp(), byId.getUsername(), byId.getPassword());
        HashMap<Object, Object> map = new HashMap<>();
        map.put("rtsp", rtsp);
        ResponseEntity<Resp> res = postForEntity(currentScaleRtspUrl, map);
        Resp body = res.getBody();
        if (!isSuccess(body)) {
            log.error("rtsp获取分辨率失败,响应结果为失败状态 url = {} , result body = {}", currentScaleRtspUrl,
                    JSON.toJSONString(body));
            throw new InspectionException("获取分辨率失败,请检查rtsp流地址配置是否正确");
        } else {
            return JSON.parseObject(JSON.toJSONString(body.getData()));
        }
    }

    /**
     * onvif获取摄像头厂商信息
     *
     * @param address  摄像头地址
     * @param username 摄像头用户名
     * @param password 摄像头密码
     * @return 摄像头厂商信息
     */
    public String getCameraManufacturerInfo(String address, String username, String password) {
        // 登录摄像头建立连接
        getChannelStream(address, username, password);

        // 构建请求URL，Python接口接收参数是查询参数
        String url = String.format(manufacturerInfoUrl, address, username, password);

        ResponseEntity<Resp> responseEntity = getForEntity(url);
        Resp body = responseEntity.getBody();

        if (!isSuccess(body)) {
            log.error("onvif获取摄像头厂商失败: {}", JSONUtil.toJsonStr(body));
            if (Objects.isNull(body)) {
                throw new InspectionException("获取摄像头厂商信息失败，Python服务无响应。");
            }
        }

        // 如果成功，解析返回的数据。Python接口返回的是一个包含 "manufacturer" 字段的JSON对象
        Object data = body.getData();
        if (Objects.isNull(data)) {
            throw new InspectionException("获取摄像头厂商信息失败，Python服务返回数据为空。");
        }

        JSONObject jsonData = JSON.parseObject(JSONUtil.toJsonStr(data));

        String manufacturer = jsonData.getString("manufacturer");

        if (StringUtils.isEmpty(manufacturer)) {
            log.error("从Python服务获取的摄像头厂商信息为空或缺失。URL: {}", url);
            throw new InspectionException("获取摄像头厂商信息失败，Python服务返回数据为空。");
        }

        return manufacturer.toUpperCase();
    }
}
