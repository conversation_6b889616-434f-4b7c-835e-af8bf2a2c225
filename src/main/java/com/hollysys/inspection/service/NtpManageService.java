package com.hollysys.inspection.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.RuntimeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.cron.CronUtil;
import cn.hutool.cron.task.RunnableTask;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hollysys.inspection.config.annotations.OperateLog;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.constants.operatelog.BusinessClassify;
import com.hollysys.inspection.constants.operatelog.OperateType;
import com.hollysys.inspection.entity.InspNtpManageInfo;
import com.hollysys.inspection.mapper.InspNtpManageInfoMapper;
import com.hollysys.inspection.model.ntp.GetNtpConfigInfoRespModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * NTP校时相关服务层
 */
@Slf4j
@Service
public class NtpManageService extends ServiceImpl<InspNtpManageInfoMapper, InspNtpManageInfo> {

    private static final String SYNC_DATE_TASK_ID = "SyncDateFromNtpTaskId";

    @Value("${ntp.sync-enable}")
    boolean ntpSyncEnable;

    @Value("${ntp.sync-command}")
    private String ntpSyncCommandTemp;

    @Value("${ntp.date-format-temp}")
    private String dateFormatTemp;

    public String getSysDateNow() {
        DateTime date = DateUtil.date();
        return DateUtil.format(date, dateFormatTemp);
    }

    @OperateLog(operateType = OperateType.UPDATE, businessClassify = BusinessClassify.NTP, message = "设置系统时间")
    public Object setSystemDate(String dateStr) {
        if (StrUtil.isBlank(dateStr)) {
            throw new InspectionException("设置时间不能为空");
        }
        try {
            DateUtil.parse(dateStr, DatePattern.NORM_DATETIME_PATTERN);
        } catch (Exception exception) {
            log.error("设置时间格式错误, dateStr = {}, dateFormatTemp= {}", dateStr, dateFormatTemp, exception);
            throw new InspectionException("设置时间格式错误");
        }

        try {
            List<String> execForLines = RuntimeUtil.execForLines("date", "-s", dateStr);
            log.debug("setSystemDate success,  execForLines = {}", JSON.toJSONString(execForLines));
            return execForLines;
        } catch (Exception exception) {
            log.error("setSystemDate error, dateStr = {}", dateStr, exception);
            throw new InspectionException("设置系统时间失败");
        }
    }

    public void scheduleSyncDateFromNtp() {
        if (!ntpSyncEnable) {
            return;
        }

        List<InspNtpManageInfo> list = list();
        if (CollectionUtil.isEmpty(list)) {
            return;
        }

        InspNtpManageInfo manageInfo = list.get(0);
        Boolean available = manageInfo.getAvailable();
        if (BooleanUtil.isTrue(available)) {
            // 先移除旧的校时任务
            CronUtil.remove(SYNC_DATE_TASK_ID);
            // 添加新的定时任务
            SyncDateFromNtpTask syncDateFromNtpTask = new SyncDateFromNtpTask(manageInfo.getNtpServerHost(), manageInfo.getNtpServerPort());
            CronUtil.schedule(SYNC_DATE_TASK_ID, "*/" + manageInfo.getInterval() + " * * * *", new RunnableTask(syncDateFromNtpTask));

            log.debug("scheduleSyncDateFromNtp finish... manageInfo = {}", JSON.toJSONString(manageInfo));
        }
    }

    public void syncDateFromNtp(String ntpServerHost, Integer ntpServerPort) {
        if (StrUtil.isBlank(ntpServerHost)) {
            throw new InspectionException("NTP服务地址不能为空");
        }

        String command = String.format(ntpSyncCommandTemp, ntpServerHost, ntpServerPort);
        try {
            List<String> strings = RuntimeUtil.execForLines(command);
            log.debug("syncDateFromNtp success, command = {}, result = {}", command,
                    JSONUtil.toJsonPrettyStr(strings));
        } catch (Exception exception) {
            log.error("syncDateFromNtp error, command = {}", command, exception);
            throw new InspectionException("同步系统时间失败");
        }
    }

    public GetNtpConfigInfoRespModel getNtpConfigInfo() {
        GetNtpConfigInfoRespModel respModel = new GetNtpConfigInfoRespModel();
        List<InspNtpManageInfo> list = list();
        if (CollectionUtil.isNotEmpty(list)) {
            InspNtpManageInfo inspNtpManageInfo = list.get(0);
            BeanUtils.copyProperties(inspNtpManageInfo, respModel);
        } else {
            respModel.setAvailable(false);
        }

        respModel.setSysDateNow(getSysDateNow());
        respModel.setDateFormatTemp(dateFormatTemp);
        return respModel;
    }

    @Transactional
    @OperateLog(operateType = OperateType.UPDATE, businessClassify = BusinessClassify.NTP)
    public Object saveNtpDateFromNtp(InspNtpManageInfo reqModel) {
        String ntpServerHost = reqModel.getNtpServerHost();
        if (StrUtil.isBlank(ntpServerHost)) {
            throw new InspectionException("NTP服务地址不能为空");
        }
        Integer ntpServerPort = reqModel.getNtpServerPort();
        if (Objects.isNull(ntpServerPort)) {
            throw new InspectionException("NTP服务端口不能为空");
        }
        Integer interval = reqModel.getInterval();
        if (Objects.isNull(interval)) {
            throw new InspectionException("校时时间间隔不能为空");
        }
        if (interval < 1 || interval > 59) {
            throw new InspectionException("校时时间间隔允许范围为1-59");
        }
        Boolean available = reqModel.getAvailable();
        // 先移除旧的校时任务
        CronUtil.remove(SYNC_DATE_TASK_ID);
        if (BooleanUtil.isTrue(available)) {
            // 添加新的定时任务
            SyncDateFromNtpTask syncDateFromNtpTask = new SyncDateFromNtpTask(ntpServerHost, ntpServerPort);
            CronUtil.schedule(SYNC_DATE_TASK_ID, "*/" + interval + " * * * *", new RunnableTask(syncDateFromNtpTask));
        }

        // 数据库只允许存在一条数据，所以执行先删除再新增
        remove(new QueryWrapper<>());
        save(reqModel);
        return reqModel;
    }

    private class SyncDateFromNtpTask implements Runnable {

        private final String ntpServerHost;

        private final Integer ntpServerPort;

        public SyncDateFromNtpTask(String ntpServerHost, Integer ntpServerPort) {
            this.ntpServerHost = ntpServerHost;
            this.ntpServerPort = ntpServerPort;
        }

        @Override
        public void run() {
            syncDateFromNtp(ntpServerHost, ntpServerPort);
        }
    }
}
