package com.hollysys.inspection.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.config.redis.RedisHelper;
import com.hollysys.inspection.constants.InspConstants;
import com.hollysys.inspection.constants.ProjectNodeType;
import com.hollysys.inspection.constants.algorithm.QualityBitEnum;
import com.hollysys.inspection.constants.algorithm.param.DataType;
import com.hollysys.inspection.constants.algorithm.param.OutOrInEnum;
import com.hollysys.inspection.constants.variable.QualitySetType;
import com.hollysys.inspection.constants.variable.ValueReadType;
import com.hollysys.inspection.constants.variable.VariableType;
import com.hollysys.inspection.entity.InspAlgorithmInstance;
import com.hollysys.inspection.entity.InspAlgorithmParam;
import com.hollysys.inspection.entity.InspGlobalVariable;
import com.hollysys.inspection.entity.InspProjectTreeNode;
import com.hollysys.inspection.mapper.InspGlobalVariableMapper;
import com.hollysys.inspection.model.algorithm.execute.OutputValueObj;
import com.hollysys.inspection.model.dbbase.DbBaseReadPointRespModel;
import com.hollysys.inspection.model.dbbase.DbBaseRespModel;
import com.hollysys.inspection.model.dbbase.DbBaseWritePointReqModel;
import com.hollysys.inspection.model.dbbase.DbBaseWritePointRespModel;
import com.hollysys.inspection.model.variable.GlobalVariableListItem;
import com.hollysys.inspection.model.variable.OutputParamTreeItem;
import com.hollysys.inspection.model.variable.VariableRealtimeValue;
import com.hollysys.inspection.service.execute.QualityBitService;
import com.hollysys.inspection.service.platform.DbBaseApiService;
import com.hollysys.inspection.utils.AssertUtil;
import com.hollysys.inspection.utils.OutputParamUtil;
import com.hollysys.inspection.utils.PointNameUtil;
import com.hollysys.inspection.utils.StrEscapeUtil;
import com.hollysys.inspection.utils.tree.LambdaTreeHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 全局变量配置表(InspGlobalVariable)表服务接口
 *
 * <AUTHOR>
 * @since 2023-03-30 11:10:58
 */
@Service
@Slf4j
public class InspGlobalVariableService extends ServiceImpl<InspGlobalVariableMapper, InspGlobalVariable> {

    // 实时值和质量位在实时库中存在的最大超时时间
    private final int REALTIME_VALUE_TIMEOUT = 2;
    // 单位 天
    private final TimeUnit REALTIME_VALUE_TIMEOUT_UNIT = TimeUnit.DAYS;

    @Resource
    private ProjectTreeNodeService projectTreeNodeService;

    @Resource
    private QualityBitService qualityBitService;

    @Resource
    private InspAlgorithmInstanceService algorithmInstanceService;

    @Resource
    private InspAlgorithmParamService algorithmParamService;

    @Resource
    private DbBaseApiService dbBaseApiService;

    @Resource
    private RedisHelper redisHelper;

    public InspGlobalVariable getOneById(String globalVariableId) {
        InspGlobalVariable byId = getById(globalVariableId);
        AssertUtil.isTrue(Objects.nonNull(byId), "变量信息不存在");
        return byId;
    }

    public Object pageList(String current, String size) {
        long currentParam;
        long sizeParam;
        // 校验 current 参数
        if (StrUtil.isBlank(current)) {
            throw new InspectionException("current不能为空");
        }
        try {
            currentParam = Long.parseLong(current);
        } catch (NumberFormatException e) {
            throw new InspectionException("current必须为数字类型");
        }

        // 校验 size 参数
        if (StrUtil.isBlank(size)) {
            throw new InspectionException("size不能为空");
        }
        try {
            sizeParam = Long.parseLong(size);
        } catch (NumberFormatException e) {
            throw new InspectionException("size必须为数字类型");
        }

        Page<InspGlobalVariable> page = new Page<>(currentParam, sizeParam);
        return page(page);
    }

    private void checkGlobalVariable(InspGlobalVariable globalVariable) {
        String dataType = globalVariable.getDataType();
        AssertUtil.isTrue(DataType.outputDataTypeNames().contains(dataType), "非法的变量数据类型");

        String valType = globalVariable.getValType();
        AssertUtil.isTrue(EnumUtil.contains(VariableType.class, valType), "非法的读写类型");

        List<Integer> refreshIntervalArr = ListUtil.of(1, 3, 5, 10);
        if (VariableType.READ.name().equals(valType)) {
            // 变量为读点类型时，值刷新方式必填
            String valueReadType = globalVariable.getValueReadType();
            AssertUtil.isTrue(EnumUtil.contains(ValueReadType.class, valueReadType), "非法的变量值刷新方式");

            if (ValueReadType.LOOP_INTERVAL_QUERY.name().equals(valueReadType)) {
                Integer valueReadInterval = globalVariable.getValueReadInterval();
                AssertUtil.isTrue(refreshIntervalArr.contains(valueReadInterval), "非法的变量值刷新间隔");
            }

            String pointName = globalVariable.getPointName();
            if (StrUtil.isNotBlank(pointName)) {
                AssertUtil.isTrue(StrUtil.isNotBlank(PointNameUtil.pointName2NodeId(pointName)), "变量关联点项名格式错误");
            }
        } else {
            // 验证值绑定点项名是否重复是否
            String pointName = globalVariable.getPointName();
            String qualityPointName = globalVariable.getQualityPointName();
            AssertUtil.isFalse(StrUtil.isNotBlank(pointName) && StrUtil.equals(pointName, qualityPointName),
                    "提交的值关联点项名与质量位关联点项名重复");
            AssertUtil.isFalse(isDuplicatePointName(globalVariable.getId(), pointName),
                    "值关联点项名重复");
            AssertUtil.isFalse(isDuplicatePointName(globalVariable.getId(), qualityPointName),
                    "质量位关联点项名重复");
        }

        String qualityPointName = globalVariable.getQualityPointName();
        if (StrUtil.isNotBlank(qualityPointName)) {
            AssertUtil.isTrue(StrUtil.isNotBlank(PointNameUtil.pointName2NodeId(qualityPointName)), "质量位关联点项名格式错误");
        }

        String qualitySetType = globalVariable.getQualitySetType();
        AssertUtil.isTrue(EnumUtil.contains(QualitySetType.class, qualitySetType), "非法的质量位刷新方式");

        if (QualitySetType.TIMEOUT_SET_BAD.name().equals(qualitySetType)) {
            Integer qualityTimeout = globalVariable.getQualityTimeout();
            AssertUtil.isTrue(Objects.nonNull(qualityTimeout), "质量位超时置坏时间不允许为空");
            AssertUtil.isTrue(qualityTimeout >= 1 && qualityTimeout <= 60, "质量位超时时间允许范围为[1,60]");

            String qualityTimeoutUnit = globalVariable.getQualityTimeoutUnit();
            List<String> units = Arrays.asList(TimeUnit.HOURS.name(), TimeUnit.MINUTES.name(), TimeUnit.SECONDS.name());
            AssertUtil.isTrue(units.contains(qualityTimeoutUnit), "质量位超时置坏时间单位不合法");
        }

        // 变量名规则校验 名称只包含字母、数字和下划线，20个字符以内
        String valNameRegex = "^[A-Za-z_0-9]{1,20}$";
        String valName = globalVariable.getValName();
        AssertUtil.isTrue(StrUtil.isNotBlank(valName), "变量名不允许为空");
        AssertUtil.isTrue(valName.matches(valNameRegex), "变量名只包含字母、数字和下划线，20个字符以内");

        // 变量别名（中文名）规则校验 只能包含中文字符，20个字符以内
        String nickNameRegex = "^[\\u4E00-\\u9FA5]{1,20}$";
        String nickName = globalVariable.getNickName();
        AssertUtil.isTrue(StrUtil.isNotBlank(nickName), "变量中文名不允许为空");
        AssertUtil.isTrue(nickName.matches(nickNameRegex), "变量中文名只能包含中文，20个字符以内");

        InspGlobalVariable oneByName = lambdaQuery().eq(InspGlobalVariable::getValName, valName).one();
        String globalVariableId = globalVariable.getId();
        if (StrUtil.isBlank(globalVariableId)) {
            // 新建
            AssertUtil.isTrue(Objects.isNull(oneByName), "变量名重复");
        } else {
            // 更新
            if (Objects.nonNull(oneByName)) {
                // 如果用户提交的变量名称在数据库中已存在，并且不是本次修改的变量数据
                AssertUtil.isTrue(oneByName.getId().equals(globalVariableId), "变量名重复");
            }
        }
    }

    /**
     * 验证值绑定点项名或质量位绑定点项目是否重复与数据库中重复
     * 只有写点类型变量,值绑定的点项名不允许重复
     */
    private boolean isDuplicatePointName(String globalVariableId, String pointName) {
        if (StrUtil.isEmpty(pointName)) {
            return false;
        }
        if (StrUtil.isBlank(globalVariableId)) {
            // 新建
            List<InspGlobalVariable> list = lambdaQuery()
                    .eq(InspGlobalVariable::getValType, VariableType.WRITE.name())
                    .and(wrapper -> wrapper
                            .eq(InspGlobalVariable::getPointName, pointName)
                            .or()
                            .eq(InspGlobalVariable::getQualityPointName, pointName))
                    .list();
            return CollectionUtil.isNotEmpty(list);
        } else {
            // 更新
            List<InspGlobalVariable> list = lambdaQuery()
                    .eq(InspGlobalVariable::getValType, VariableType.WRITE.name())
                    .notIn(InspGlobalVariable::getId, globalVariableId)
                    .and(wrapper -> wrapper
                            .eq(InspGlobalVariable::getPointName, pointName)
                            .or()
                            .eq(InspGlobalVariable::getQualityPointName, pointName))
                    .list();
            return CollectionUtil.isNotEmpty(list);
        }
    }

    public InspGlobalVariable createVariable(InspGlobalVariable globalVariable) {
        // 参数校验
        checkGlobalVariable(globalVariable);

        LocalDateTime now = LocalDateTime.now();
        globalVariable.setCreateTime(now);
        globalVariable.setUpdateTime(now);
        save(globalVariable);
        return globalVariable;
    }

    public InspGlobalVariable updateGlobalVariable(InspGlobalVariable globalVariable) {
        String globalVariableId = globalVariable.getId();
        AssertUtil.isTrue(StrUtil.isNotBlank(globalVariableId), "变量ID不允许为空");

        InspGlobalVariable oneById = getOneById(globalVariableId);
        String valType = globalVariable.getValType();
        AssertUtil.isTrue(oneById.getValType().equals(valType), "变量的读写类型不允许修改");

        // 参数校验
        checkGlobalVariable(globalVariable);

        String dataType = globalVariable.getDataType();
        if (!oneById.getDataType().equals(dataType)) {
            // 修改变量值数据类型时，需要判断当前变量是否已经绑定输出参数，若已绑定，则禁止修改
            AssertUtil.isFalse(VariableType.WRITE.name().equals(valType) && StrUtil.isNotBlank(oneById.getOutputId()
            ), "已绑定输出参数的变量数据类型不允许修改");
        }

        if (VariableType.READ.name().equals(valType)) {
            // 更新读点的关联点项名时，需要将变量旧的实时值和质量位删除
            String pointNameDb = oneById.getPointName();
            if (StrUtil.isNotBlank(pointNameDb) && !pointNameDb.equals(globalVariable.getPointName())) {
                removeValueFromRealDb(oneById);
            }
            // 更新读点的质量位关联点项名时，需要将变量旧的质量位设置为默认值（好点）
            String qualityPointNameDb = oneById.getQualityPointName();
            if (StrUtil.isNotBlank(qualityPointNameDb) && !qualityPointNameDb.equals(globalVariable.getQualityPointName())) {
                saveQualityToRealDb(oneById, QualityBitEnum.Good);
            }
        }

        // 当质量位刷新方式修改为超时自动置坏时，获取实时库中质量位，重置超时时间为新的超时时间
        if (QualitySetType.TIMEOUT_SET_BAD.name().equals(globalVariable.getQualitySetType())) {
            String qualityFromRealDb = getQualityFromRealDb(oneById);
            if (Objects.nonNull(qualityFromRealDb)) {
                TimeUnit timeUnit = EnumUtil.fromString(TimeUnit.class, globalVariable.getQualityTimeoutUnit());
                redisHelper.setEx(getQualityKey(globalVariable), qualityFromRealDb, globalVariable.getQualityTimeout(),
                        timeUnit);
            }
        }

        globalVariable.setUpdateTime(LocalDateTime.now());
        updateById(globalVariable);
        return globalVariable;
    }

    @Transactional
    public boolean deleteGlobalVariable(String globalVariableId) {
        InspGlobalVariable oneById = getOneById(globalVariableId);
        boolean removeResult = removeById(globalVariableId);
        // 数据删除成功后，需要同时删除实时值和质量位
        if (removeResult) {
            removeValueFromRealDb(oneById);
        }
        return removeResult;
    }

    public IPage<GlobalVariableListItem> pageAll(Page<InspGlobalVariable> page, String searchStr, VariableType variableType) {
        String searchStrDecode = StrEscapeUtil.escapeChar(searchStr);
        // 按变量名、别名模糊搜索
        IPage<InspGlobalVariable> pageResult = lambdaQuery()
                .eq(Objects.nonNull(variableType), InspGlobalVariable::getValType, variableType)
                .and(StrUtil.isNotBlank(searchStr), wrapper -> wrapper
                        .apply("val_name ILIKE CONCAT('%', {0}, '%')", searchStrDecode)
                        .or()
                        .apply("nick_name ILIKE CONCAT('%', {0}, '%')", searchStrDecode)
                        .or()
                        .apply("point_name ILIKE CONCAT('%', {0}, '%')", searchStrDecode)
                )
                // 按创建时间倒叙展示
                .orderByDesc(InspGlobalVariable::getCreateTime).page(page);
        // 转化对象
        List<InspGlobalVariable> records = pageResult.getRecords();

        List<GlobalVariableListItem> itemList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(records)) {
            // 查询全部节点，组织成id-label map
            List<InspProjectTreeNode> list = projectTreeNodeService.list();
            Map<String, String> idLabelMap = list.stream().collect(Collectors.toMap(InspProjectTreeNode::getId, InspProjectTreeNode::getLabel));
            for (InspGlobalVariable record : records) {
                GlobalVariableListItem globalVariableListItem = new GlobalVariableListItem();
                BeanUtil.copyProperties(record, globalVariableListItem);
                // 设置变量绑定的输出信息
                setBindOutputData(idLabelMap, globalVariableListItem);
                // 设置输出实时值
                VariableRealtimeValue valueToRealDb = getValueFromRealDb(globalVariableListItem);
                if (Objects.nonNull(valueToRealDb)) {
                    globalVariableListItem.setValue(valueToRealDb.getValue().toString());
                    String format = DateUtil.format(DateUtil.date(valueToRealDb.getTimestamp()), DatePattern.NORM_DATETIME_MS_PATTERN);
                    globalVariableListItem.setVariableTime(format);

                    // 设置输出实时值质量位
                    String qualityFromRealDb = getQualityFromRealDb(globalVariableListItem);
                    globalVariableListItem.setQuality(qualityFromRealDb);
                } else {
                    globalVariableListItem.setValue("-");
                    globalVariableListItem.setVariableTime("-");
                    globalVariableListItem.setQuality("-");
                }
                itemList.add(globalVariableListItem);
            }
        }

        Page<GlobalVariableListItem> pageNew = new Page<>();
        BeanUtil.copyProperties(pageResult, pageNew);
        pageNew.setRecords(itemList);
        return pageNew;
    }

    private void setBindOutputData(Map<String, String> idLabelMap, GlobalVariableListItem globalVariableListItem) {
        String valType = globalVariableListItem.getValType();
        if (!VariableType.WRITE.name().equals(valType)) {
            // 非写点类型变量，不会进行输出绑定
            return;
        }

        String outputId = globalVariableListItem.getOutputId();
        String presetId = globalVariableListItem.getPresetId();
        String algorithmInstanceId = globalVariableListItem.getAlgorithmInstanceId();
        if (StrUtil.hasBlank(outputId, algorithmInstanceId, presetId)) {
            // 未进行绑定算法输出参数
            return;
        }
        // 获取预置点名称
        String presetName = idLabelMap.get(presetId);
        // 获取算法实例名称（算法名称#实例序号）
        String algorithmInstName = null;
        List<InspAlgorithmInstance> algorithmInstances = algorithmInstanceService.listByPresetId(presetId);
        for (int i = 0; i < algorithmInstances.size(); i++) {
            InspAlgorithmInstance algorithmInstance = algorithmInstances.get(i);
            if (algorithmInstanceId.equals(algorithmInstance.getId())) {
                algorithmInstName = OutputParamUtil.getAlgorithmInstName(algorithmInstance, i);
                break;
            }
        }
        AssertUtil.isTrue(StrUtil.isNotBlank(algorithmInstName), "算法实例名称获取失败");

        // 查询算法输出参数名称
        InspAlgorithmParam param = algorithmParamService.getOneById(outputId);
        String paramName = param.getLabel();

        // 拼接
        String join = OutputParamUtil.getOutputParamLabel(presetName, algorithmInstName, paramName);
        globalVariableListItem.setBindOutputName(join);
    }

    /**
     * 查询输出变量参数树结构
     */
    public List<OutputParamTreeItem> getOutputParamTree() {
        // 获取工程下的全部节点（区域、通道、预置点）
        List<InspProjectTreeNode> list = projectTreeNodeService.list();
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<OutputParamTreeItem> outputParamTreeItems = list.stream().map(item -> {
            OutputParamTreeItem outputParamTreeItem = new OutputParamTreeItem();
            BeanUtil.copyProperties(item, outputParamTreeItem);
            return outputParamTreeItem;
        }).collect(Collectors.toList());

        List<OutputParamTreeItem> presetNodes = outputParamTreeItems.stream()
                .filter(item -> ProjectNodeType.PRESET.equals(item.getType())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(presetNodes)) {
            // 查询全部已被绑定的输出参数实例
            List<InspGlobalVariable> hasBindVariable = lambdaQuery()
                    .isNotNull(InspGlobalVariable::getAlgorithmInstanceId)
                    .isNotNull(InspGlobalVariable::getOutputId)
                    .list();
            Set<String> hasBindIds = hasBindVariable.stream().map(item -> item.getAlgorithmInstanceId() + item.getOutputId())
                    .collect(Collectors.toSet());
            // 添加算法实例节点和算法输出参数实例节点
            for (OutputParamTreeItem presetNode : presetNodes) {
                String presetNodeId = presetNode.getId();
                List<InspAlgorithmInstance> algorithmInstances = algorithmInstanceService.listByPresetId(presetNodeId);
                for (int i = 0; i < algorithmInstances.size(); i++) {
                    InspAlgorithmInstance algorithmInstance = algorithmInstances.get(i);
                    String algorithmInstName = OutputParamUtil.getAlgorithmInstName(algorithmInstance, i);
                    // 构造算法实例节点
                    OutputParamTreeItem algorithmInstNode = new OutputParamTreeItem();
                    String algorithmInstanceId = algorithmInstance.getId();
                    algorithmInstNode.setId(algorithmInstanceId);
                    algorithmInstNode.setLabel(algorithmInstName);
                    algorithmInstNode.setType("algorithm-instance");
                    algorithmInstNode.setParentId(presetNodeId);
                    algorithmInstNode.setSortNo((long) i);
                    outputParamTreeItems.add(algorithmInstNode);

                    // 通过算法参数定义构造算法输出参数实例节点
                    List<InspAlgorithmParam> outputParams = algorithmParamService.listOutputByAlgorithmId(algorithmInstance.getAlgorithmId());
                    for (InspAlgorithmParam outputParam : outputParams) {
                        // 构造算法实例节点
                        OutputParamTreeItem outputParamNode = new OutputParamTreeItem();
                        String outputParamId = outputParam.getId();
                        outputParamNode.setId(outputParamId);
                        outputParamNode.setLabel(outputParam.getLabel());
                        outputParamNode.setType("output-param");
                        outputParamNode.setParentId(algorithmInstanceId);
                        outputParamNode.setSortNo(outputParam.getSortNo());
                        outputParamNode.setIsBind(hasBindIds.contains(algorithmInstanceId + outputParamId));
                        outputParamNode.setDataType(outputParam.getDataType());
                        outputParamTreeItems.add(outputParamNode);
                    }
                }
            }
        }
        // 获取视频分析节点id
        InspProjectTreeNode businessRootNode = projectTreeNodeService.getBusinessRootNode();
        return LambdaTreeHelper.<OutputParamTreeItem, OutputParamTreeItem, String>newInstance()
                .id(OutputParamTreeItem::getId, OutputParamTreeItem::getId)
                .childrenKey(OutputParamTreeItem::getChildren)
                .parentId(OutputParamTreeItem::getParentId, OutputParamTreeItem::getParentId)
                .label(OutputParamTreeItem::getLabel, OutputParamTreeItem::getLabel)
                .orderNo(OutputParamTreeItem::getSortNo, OutputParamTreeItem::getSortNo)
                .putExtra(OutputParamTreeItem::getType, OutputParamTreeItem::getType)
                .putExtra(OutputParamTreeItem::getIsBind, OutputParamTreeItem::getIsBind)
                .putExtra(OutputParamTreeItem::getDataType, OutputParamTreeItem::getDataType)
                .buildTreeList(outputParamTreeItems, businessRootNode.getId(), OutputParamTreeItem.class);
    }

    /**
     * 变量绑定输出参数值
     */
    public InspGlobalVariable bindOutputParam(InspGlobalVariable globalVariable) {
        String globalVariableId = globalVariable.getId();
        AssertUtil.isTrue(StrUtil.isNotBlank(globalVariableId), "变量ID不允许为空");
        InspGlobalVariable variable = getOneById(globalVariableId);

        String valType = variable.getValType();
        AssertUtil.isTrue(VariableType.WRITE.name().equals(valType), "只允许写点绑定输出参数值");

        String outputId = globalVariable.getOutputId();
        AssertUtil.isTrue(StrUtil.isNotBlank(outputId), "变量绑定的输出参数实例ID不允许为空");

        String algorithmInstanceId = globalVariable.getAlgorithmInstanceId();
        AssertUtil.isTrue(StrUtil.isNotBlank(outputId), "算法实例ID不允许为空");

        String outputIdOld = variable.getOutputId();
        String algorithmInstanceIdOld = variable.getAlgorithmInstanceId();
        if (outputId.equals(outputIdOld) && algorithmInstanceId.equals(algorithmInstanceIdOld)) {
            // 未换绑
            return variable;
        }

        InspAlgorithmParam param = algorithmParamService.getOneById(outputId);
        AssertUtil.isTrue(OutOrInEnum.OUT.name().equals(param.getOutOrIn()), "变量绑定的参数必须为输出参数");

        // 输出参数类型与变量定义类型一致性校验
        String dataTypeParam = param.getDataType();
        String dataTypeVar = variable.getDataType();
        AssertUtil.isTrue(StrUtil.equals(dataTypeParam, dataTypeVar), "变量与绑定的参数值数据类型必须一致");

        InspAlgorithmInstance algorithmInstance = algorithmInstanceService.getOneById(algorithmInstanceId);

        // 清除实时库中的值
        InspGlobalVariable oneById = getOneById(globalVariableId);
        removeValueFromRealDb(oneById);

        variable.setPresetId(algorithmInstance.getPresetId());
        variable.setAlgorithmInstanceId(algorithmInstanceId);
        variable.setOutputId(outputId);

        updateById(variable);
        return variable;
    }

    /**
     * 变量与输出参数值解绑
     */
    public boolean unbindOutputParam(InspGlobalVariable globalVariable) {
        String globalVariableId = globalVariable.getId();
        AssertUtil.isTrue(StrUtil.isNotBlank(globalVariableId), "变量ID不允许为空");
        // 清除实时库中的值
        InspGlobalVariable oneById = getOneById(globalVariableId);
        removeValueFromRealDb(oneById);

        return lambdaUpdate().set(InspGlobalVariable::getPresetId, null)
                .set(InspGlobalVariable::getAlgorithmInstanceId, null)
                .set(InspGlobalVariable::getOutputId, null)
                .eq(InspGlobalVariable::getId, globalVariableId).update();
    }

    /**
     * 存储变量实时值
     */
    public void saveRealtimeValue(InspAlgorithmInstance algorithmInstance, Map<String, OutputValueObj> algorithmOutput) {
        // 查询算法实例下被绑定的输出变量
        String algorithmInstanceId = algorithmInstance.getId();
        List<InspGlobalVariable> bindVariableList = lambdaQuery().eq(InspGlobalVariable::getAlgorithmInstanceId, algorithmInstanceId)
                .isNotNull(InspGlobalVariable::getOutputId)
                .list();
        if (CollectionUtil.isEmpty(bindVariableList)) {
            return;
        }

        // 查询算法的出参定义
        List<InspAlgorithmParam> outputParams = algorithmParamService.listOutputByAlgorithmId(algorithmInstance.getAlgorithmId());
        if (CollectionUtil.isEmpty(outputParams)) {
            log.error("存储变量实时值失败，变量已经绑定出参，但是算法实例下出参定义信息不存在");
            return;
        }
        Map<String, InspAlgorithmParam> mapById = outputParams.stream()
                .collect(Collectors.toMap(InspAlgorithmParam::getId, Function.identity()));
        for (InspGlobalVariable globalVariable : bindVariableList) {
            String outputId = globalVariable.getOutputId();
            InspAlgorithmParam outputParam = mapById.get(outputId);
            if (Objects.isNull(outputParam)) {
                log.error("存储变量实时值失败，出参[{}]定义信息不存在", outputId);
                continue;
            }

            Object variableRealtimeValue = getVariableRealtimeValue(outputParam, algorithmOutput);
            if (Objects.isNull(variableRealtimeValue)) {
                continue;
            }
            // 将算法输出值保存到redis
            saveValueToRealDb(globalVariable, variableRealtimeValue);
            // 将算法输出值质量位保存到redis
            boolean serverQualityBit = qualityBitService.getServerQualityBit();
            QualityBitEnum qualityBit;
            if (serverQualityBit) {
                // 服务质量位优先
                qualityBit = QualityBitEnum.Bad_OutOfService;
            } else {
                qualityBit = QualityBitEnum.Good;
            }
            saveQualityToRealDb(globalVariable, qualityBit);

            // 回写变量值和质量位到平台
            writeValueAndQualityToPlatform(globalVariable, variableRealtimeValue, qualityBit);
        }
    }

    private void writeValueAndQualityToPlatform(InspGlobalVariable globalVariable, Object value,
                                                QualityBitEnum qualityBitEnum) {
        List<DbBaseWritePointReqModel.WriteDataValue> data = new ArrayList<>();
        // 算法值回写
        if (Objects.nonNull(value)) {
            String pointName = globalVariable.getPointName();
            String valueNodeId = PointNameUtil.pointName2NodeId(pointName);
            if (StrUtil.isNotBlank(valueNodeId)) {
                DbBaseWritePointReqModel.WriteDataValue writeDataValue = new DbBaseWritePointReqModel.WriteDataValue();
                writeDataValue.setNodeId(valueNodeId);
                writeDataValue.setValue(value);
                data.add(writeDataValue);
            }
        }

        // 算法值质量位回写
        if (Objects.nonNull(qualityBitEnum)) {
            // 写点类型才会回写
            if (!VariableType.WRITE.name().equals(globalVariable.getValType())) {
                return;
            }
            String qualityPointName = globalVariable.getQualityPointName();
            String qualityNodeId = PointNameUtil.pointName2NodeId(qualityPointName);
            if (StrUtil.isNotBlank(qualityNodeId)) {
                DbBaseWritePointReqModel.WriteDataValue writeDataValue = new DbBaseWritePointReqModel.WriteDataValue();
                writeDataValue.setNodeId(qualityNodeId);
                // 质量位转化
                int platformQualityBit = QualityBitEnum.toPlatformQualityBit(qualityBitEnum);
                writeDataValue.setValue(platformQualityBit);
                data.add(writeDataValue);
            }
        }

        if (CollectionUtil.isEmpty(data)) {
            return;
        }

        DbBaseWritePointRespModel dbBaseWritePointRespModel = dbBaseApiService.writePoints(data);

        if (!DbBaseRespModel.isSuccess(dbBaseWritePointRespModel)) {
            log.error("writeValueToPlatform 回写失败，param = {}", JSONUtil.toJsonStr(data));
        }
    }

    /**
     * 获取变量绑定的算法输出结果值
     */
    private Object getVariableRealtimeValue(InspAlgorithmParam algorithmParam,
                                            Map<String, OutputValueObj> algorithmOutput) {
        String paramKey = algorithmParam.getKey();
        OutputValueObj valueObj = algorithmOutput.get(paramKey);
        if (Objects.isNull(valueObj)) {
            log.error("无法在算法返回的出参对象中找到[{}]结果", paramKey);
            return null;
        }

        // 算法输出值
        return valueObj.getValue();
    }

    /**
     * 按照读取间隔，批量从平台获取点项值，并设置到变量中
     */
    public void readAndSetFromPlatform(int readInterval) {
        // 按照间隔查询读点变量数据
        List<InspGlobalVariable> globalVariables = lambdaQuery()
                .eq(InspGlobalVariable::getValType, VariableType.READ)
                .eq(InspGlobalVariable::getValueReadType, ValueReadType.LOOP_INTERVAL_QUERY)
                .eq(InspGlobalVariable::getValueReadInterval, String.valueOf(readInterval))
                .list();
        refreshValueFromPlatform(globalVariables);
    }

    /**
     * 刷新变量实时值
     */
    public void refreshValueFromPlatform(List<InspGlobalVariable> globalVariables) {
        if (CollectionUtil.isEmpty(globalVariables)) {
            return;
        }

        // 数据过滤
        List<InspGlobalVariable> collect = globalVariables.stream()
                .filter(item -> VariableType.READ.name().equals(item.getValType()))
                .filter(item -> StrUtil.isNotBlank(item.getPointName()))
                .collect(Collectors.toList());
        // 批量点项名格式转化
        List<String> nodeIds = PointNameUtil.batchPointName2NodeId(collect);
        if (CollectionUtil.isEmpty(nodeIds)) {
            return;
        }

        Map<String, DbBaseReadPointRespModel.ReadDataValue> groupByNodeId = dbBaseApiService.readPointsToMap(nodeIds);
        if (CollectionUtil.isNotEmpty(groupByNodeId)) {
            for (InspGlobalVariable globalVariable : collect) {
                doRefreshValue(globalVariable, groupByNodeId);
            }
        }
    }

    private void doRefreshValue(InspGlobalVariable globalVariable, Map<String, DbBaseReadPointRespModel.ReadDataValue> groupByNodeId) {
        String pointName = globalVariable.getPointName();
        DbBaseReadPointRespModel.ReadDataValue readDataValue = groupByNodeId.get(pointName);
        if (Objects.nonNull(readDataValue)) {
            Object datumV = readDataValue.getV();
            if (Objects.nonNull(datumV)) {
                // 设置值到实时库
                saveValueToRealDb(globalVariable, datumV);
            }
        }

        String qualityPointName = globalVariable.getQualityPointName();
        DbBaseReadPointRespModel.ReadDataValue readQualityValue = groupByNodeId.get(qualityPointName);
        if (Objects.nonNull(readQualityValue)) {
            Object datumV = readQualityValue.getV();
            if (Objects.nonNull(datumV)) {
                QualityBitEnum qualityBitEnum = QualityBitEnum.fromPlatformQualityBit(datumV);
                // 设置值质量位到实时库
                saveQualityToRealDb(globalVariable, qualityBitEnum);
            } else {
                // 获取到值为null，默认为好点
                saveQualityToRealDb(globalVariable, QualityBitEnum.Good);
            }
        } else {
            // 没有取到值，默认为好点
            saveQualityToRealDb(globalVariable, QualityBitEnum.Good);
        }
    }

    private String getValueKey(InspGlobalVariable globalVariable) {
        return InspConstants.VARIABLE_VALUE_KEY + globalVariable.getValName();
    }

    private String getQualityKey(InspGlobalVariable globalVariable) {
        return InspConstants.VARIABLE_QUALITY_KEY + globalVariable.getValName();
    }

    /**
     * 保存值到实时库，设置超时时间为2小时，避免数据无限增加
     */
    private void saveValueToRealDb(InspGlobalVariable globalVariable, Object value) {
        VariableRealtimeValue valueToRealDb = getValueFromRealDb(globalVariable);
        if (Objects.isNull(valueToRealDb)) {
            valueToRealDb = new VariableRealtimeValue();
            valueToRealDb.setUnit(globalVariable.getUnit());
        }
        valueToRealDb.setValue(value);
        valueToRealDb.setTimestamp(DateUtil.current());

        String formatKey = getValueKey(globalVariable);
        redisHelper.setEx(formatKey, JSONUtil.toJsonStr(valueToRealDb), REALTIME_VALUE_TIMEOUT, REALTIME_VALUE_TIMEOUT_UNIT);
    }

    /**
     * 保存值质量位到实时库
     */
    private void saveQualityToRealDb(InspGlobalVariable globalVariable, QualityBitEnum qualityBitEnum) {
        // 质量位设置，如果质量位为超时置坏类型，则为redis设置超时时间，超时删除此Key，则为坏，否则设置超时时间为60小时，避免数据无限增加
        String qualitySetType = globalVariable.getQualitySetType();
        Integer qualityTimeout;
        TimeUnit qualityTimeoutUnit;
        if (QualitySetType.TIMEOUT_SET_BAD.name().equals(qualitySetType)) {
            qualityTimeout = globalVariable.getQualityTimeout();
            qualityTimeoutUnit = EnumUtil.fromString(TimeUnit.class, globalVariable.getQualityTimeoutUnit());
        } else {
            qualityTimeout = REALTIME_VALUE_TIMEOUT;
            qualityTimeoutUnit = REALTIME_VALUE_TIMEOUT_UNIT;
        }
        // 因为质量位有自己的超时逻辑，所以无法和值存储在一起
        redisHelper.setEx(getQualityKey(globalVariable), qualityBitEnum.name(), qualityTimeout, qualityTimeoutUnit);
    }

    /**
     * 从实时库获取实时值
     */
    private VariableRealtimeValue getValueFromRealDb(InspGlobalVariable globalVariable) {
        String formatKey = getValueKey(globalVariable);
        String valueModel = redisHelper.get(formatKey);
        if (StrUtil.isBlank(valueModel)) {
            return null;
        }
        return JSONUtil.toBean(valueModel, VariableRealtimeValue.class);
    }

    /**
     * 从实时库获取实时值的质量位
     */
    private String getQualityFromRealDb(InspGlobalVariable globalVariable) {
        String formatKey = getQualityKey(globalVariable);
        String valueModel = redisHelper.get(formatKey);
        if (StrUtil.isBlank(valueModel)) {
            // 质量位不存在，则默认为空
            return null;
        }
        return valueModel;
    }

    /**
     * 从实时库获取实时值和质量位
     */
    private void removeValueFromRealDb(InspGlobalVariable globalVariable) {
        String formatKey = getValueKey(globalVariable);
        redisHelper.delete(formatKey);

        String qualityKey = getQualityKey(globalVariable);
        redisHelper.delete(qualityKey);
    }

    public void unbindByAlgorithmInstanceIds(List<String> algorithmInstanceIds) {
        lambdaUpdate().set(InspGlobalVariable::getPresetId, null)
                .set(InspGlobalVariable::getAlgorithmInstanceId, null)
                .set(InspGlobalVariable::getOutputId, null)
                .in(InspGlobalVariable::getAlgorithmInstanceId, algorithmInstanceIds)
                .update();
    }

    /**
     * 获取点项实时值，如果值刷新类型为实施读取时，进行实时读取再返回
     */
    public Map<String, VariableRealtimeValue> listValueWithRefresh(List<String> valNames) {
        Map<String, VariableRealtimeValue> map = new HashMap<>();
        if (CollectionUtil.isEmpty(valNames)) {
            throw new InspectionException("变量名列表不能为空");
        }

        // 校验 valNames 列表中的每个元素
        for (String valName : valNames) {
            if (valName == null || valName.trim().isEmpty()) {
                throw new InspectionException("变量名列表中存在空的变量名");
            }
        }

        List<InspGlobalVariable> list = lambdaQuery().in(InspGlobalVariable::getValName, valNames).list();
        if (CollectionUtil.isEmpty(list)) {
            return map;
        }
        // 过滤出类型为实时刷新的变量，然后刷新实时值
        List<InspGlobalVariable> collect = list.stream()
                .filter(item -> ValueReadType.REAL_TIME_QUERY.name().equals(item.getValueReadType()))
                .collect(Collectors.toList());
        refreshValueFromPlatform(collect);

        for (InspGlobalVariable globalVariable : list) {
            VariableRealtimeValue valueFromRealDb = getValueFromRealDb(globalVariable);
            if (Objects.nonNull(valueFromRealDb)) {
                String qualityFromRealDb = getQualityFromRealDb(globalVariable);
                valueFromRealDb.setQuality(qualityFromRealDb);
                map.put(globalVariable.getValName(), valueFromRealDb);
            }
        }
        return map;
    }

    /**
     * 保存质量位（通过presetId）
     */
    public void saveQualityBitByPresetId(String presetId, QualityBitEnum qualityBit) {
        // 查询全部变量
        List<InspGlobalVariable> globalVariables = lambdaQuery().eq(InspGlobalVariable::getPresetId, presetId)
                .list();
        saveQualityBit(globalVariables, qualityBit);
    }

    /**
     * 保存质量位（通过algorithmInstId）
     */
    public void saveQualityBitByAlgorithmInstId(String algorithmInstId, QualityBitEnum qualityBit) {
        // 查询全部变量
        List<InspGlobalVariable> globalVariables = lambdaQuery().eq(InspGlobalVariable::getAlgorithmInstanceId, algorithmInstId)
                .list();
        saveQualityBit(globalVariables, qualityBit);
    }

    private void saveQualityBit(List<InspGlobalVariable> globalVariables, QualityBitEnum qualityBit) {
        if (CollectionUtil.isEmpty(globalVariables)) {
            return;
        }
        for (InspGlobalVariable globalVariable : globalVariables) {
            // 保存质量位到实时库
            saveQualityToRealDb(globalVariable, qualityBit);
            // 质量位回写
            writeValueAndQualityToPlatform(globalVariable, null, qualityBit);
        }
    }

    public void onQualityBitTimeout(String key) {
        if (key.startsWith(InspConstants.VARIABLE_QUALITY_KEY)) {
            // 因为质量位被删除只有超时一种场景，所以在此处对于超时的key，都重置为超时置坏
            redisHelper.setEx(key, QualityBitEnum.Bad_Timeout.name(), REALTIME_VALUE_TIMEOUT, REALTIME_VALUE_TIMEOUT_UNIT);
        }
    }
}
