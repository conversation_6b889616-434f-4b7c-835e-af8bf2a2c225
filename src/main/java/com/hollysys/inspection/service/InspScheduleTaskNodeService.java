package com.hollysys.inspection.service;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hollysys.inspection.constants.ScheduleTaskStatus;
import com.hollysys.inspection.entity.InspScheduleTaskNode;
import com.hollysys.inspection.mapper.InspScheduleTaskNodeMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * 通道运行周期调度信息表(InspScheduleTaskNode)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-20 10:08:49
 */
@Service
public class InspScheduleTaskNodeService extends ServiceImpl<InspScheduleTaskNodeMapper, InspScheduleTaskNode> {

    @Resource
    private InspSchedulePeriodService schedulePeriodService;

    public void updateStatus(String taskNodeId, ScheduleTaskStatus status) {
        InspScheduleTaskNode byId = getById(taskNodeId);
        if (Objects.isNull(byId)) {
            return;
        }
        byId.setStatus(status.name());

        updateById(byId);
    }

    public List<InspScheduleTaskNode> listByTaskIds(List<String> taskIds) {
        LambdaQueryWrapper<InspScheduleTaskNode> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(InspScheduleTaskNode::getTaskId, taskIds);
        wrapper.orderByAsc(InspScheduleTaskNode::getSortNo);
        return list(wrapper);
    }

    public List<InspScheduleTaskNode> listByChannelId(String channelId) {
        LambdaQueryWrapper<InspScheduleTaskNode> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(InspScheduleTaskNode::getChannelId, channelId);
        wrapper.orderByAsc(InspScheduleTaskNode::getSortNo);
        return list(wrapper);
    }

    public List<InspScheduleTaskNode> listByPresetId(String presetId) {
        LambdaQueryWrapper<InspScheduleTaskNode> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(InspScheduleTaskNode::getPresetId, presetId);
        wrapper.orderByAsc(InspScheduleTaskNode::getSortNo);
        return list(wrapper);
    }

    public void removeByModelIds(Collection<? extends Serializable> resultIds) {
        if (CollectionUtil.isEmpty(resultIds)) {
            return;
        }
        LambdaQueryWrapper<InspScheduleTaskNode> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(InspScheduleTaskNode::getPresetId, resultIds);
        remove(wrapper);
    }

    public void removeByTaskIds(List<String> taskIds) {
        if (CollectionUtil.isEmpty(taskIds)) {
            return;
        }
        LambdaQueryWrapper<InspScheduleTaskNode> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(InspScheduleTaskNode::getTaskId, taskIds);
        remove(wrapper);
        schedulePeriodService.removeByTaskIds(taskIds);
    }

    public List<InspScheduleTaskNode> getTaskByChannelId(String channelId) {
        LambdaQueryWrapper<InspScheduleTaskNode> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspScheduleTaskNode::getChannelId, channelId);
        return list(wrapper);
    }
}

