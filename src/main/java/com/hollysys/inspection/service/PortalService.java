package com.hollysys.inspection.service;

import cn.dev33.satoken.secure.SaSecureUtil;
import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.config.redis.RedisHelper;
import com.hollysys.inspection.entity.InspSystemUser;
import com.hollysys.inspection.model.portal.GetCaptchaRespModel;
import com.hollysys.inspection.model.portal.LoginReqModel;
import com.hollysys.inspection.utils.UUIDUtil;
import com.wf.captcha.SpecCaptcha;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 门户服务接口
 *
 * <AUTHOR>
 * @since 2022-12-10 23:07:19
 */
@Service
public class PortalService {

    @Resource
    private InspSystemUserService userService;

    @Resource
    private RedisHelper redisUtil;

    @Value("${captcha.show-of-times}")
    private int captchaShowOfTimes;

    @Value("${captcha.img-width}")
    private int captchaWidth;

    @Value("${captcha.img-height}")
    private int captchaHeight;

    @Value("${captcha.code-len}")
    private int captchaLen;

    @Value("${captcha.timeout}")
    private int captchaTimeout;

    private boolean isCaptchaEnable(String clientIp) {
        String getByClientIp = redisUtil.get(clientIp);
        if (StrUtil.isBlank(getByClientIp)) {
            return false;
        }

        int anInt = Integer.parseInt(getByClientIp);
        return anInt >= captchaShowOfTimes;
    }

    public SaTokenInfo login(LoginReqModel reqModel, String clientIp) {
        String username = reqModel.getUsername();
        String password = reqModel.getPassword();

        if (StrUtil.hasBlank(username, password)) {
            throw new InspectionException("用户名和密码不允许为空");
        }

        boolean captchaEnable = isCaptchaEnable(clientIp);
        if (captchaEnable) {
            String captchaKey = reqModel.getCaptchaKey();
            String captchaValue = reqModel.getCaptchaValue();
            if (StrUtil.hasBlank(captchaKey, captchaValue)) {
                throw new InspectionException("验证码参数错误");
            }
            // 验证码过期校验
            String captchaCode = redisUtil.get(captchaKey);
            if (Objects.isNull(captchaCode)) {
                throw new InspectionException("验证码已过期");
            }
            // 忽略大小写
            if (!captchaValue.equalsIgnoreCase(captchaCode)) {
                throw new InspectionException("验证码输入错误");
            } else {
                // 验证通过后删除验证码缓存
                redisUtil.delete(captchaKey);
            }
        }

        LambdaQueryWrapper<InspSystemUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspSystemUser::getUsername, username);

        // 密码MD5加密
        String encryptedPassword = SaSecureUtil.md5(password);
        wrapper.eq(InspSystemUser::getPassword, encryptedPassword);
        InspSystemUser systemUser = userService.getOne(wrapper);
        if (Objects.isNull(systemUser)) {
            throw new InspectionException("用户名或密码错误");
        }

        // 密码校验成功后登录，一行代码实现登录
        StpUtil.login(systemUser.getId());

        // 获取当前登录用户Token信息
        return StpUtil.getTokenInfo();
    }

    public boolean logout() {
        StpUtil.logout();
        return true;
    }

    public GetCaptchaRespModel getCaptcha() {
        SpecCaptcha specCaptcha = new SpecCaptcha(captchaWidth, captchaHeight, captchaLen);
        String text = specCaptcha.text();
        String uuid = UUIDUtil.simpleUUID();
        String key = "Captcha:" + uuid;
        redisUtil.setEx(key, text, captchaTimeout, TimeUnit.MINUTES);

        String base64 = specCaptcha.toBase64();
        return new GetCaptchaRespModel(key, base64);
    }
}

