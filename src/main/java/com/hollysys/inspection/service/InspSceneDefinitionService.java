package com.hollysys.inspection.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.constants.FileDirEnum;
import com.hollysys.inspection.entity.InspAlgorithmInstance;
import com.hollysys.inspection.entity.InspChannelInfo;
import com.hollysys.inspection.entity.InspPresetInfo;
import com.hollysys.inspection.entity.InspSceneDefinition;
import com.hollysys.inspection.mapper.InspSceneDefinitionMapper;
import com.hollysys.inspection.model.algorithm.configure.SceneForParamConfigModel;
import com.hollysys.inspection.model.algorithm.correct.PicCorrectParamModel;
import com.hollysys.inspection.model.channel.ScaleModel;
import com.hollysys.inspection.service.protocol.impl.CameraCtrlProxyService;
import com.hollysys.inspection.utils.AssertUtil;
import com.hollysys.inspection.utils.MinioUtil;
import com.hollysys.inspection.utils.PicUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * (InspSceneDefinition)表服务接口
 *
 * <AUTHOR>
 * @since 2023-02-02 16:29:38
 */
@Service
public class InspSceneDefinitionService extends ServiceImpl<InspSceneDefinitionMapper, InspSceneDefinition> {

    @Resource
    private InspAlgorithmInstanceService algorithmInstanceService;

    @Resource
    private InspPresetInfoService presetInfoService;

    @Resource
    private InspSceneDefinitionService sceneDefinitionService;

    @Resource
    private InspAlgorithmParamInstanceService algorithmParamInstanceService;

    @Resource
    private PythonServerService pythonServerService;
    @Resource
    private InspChannelInfoService inspChannelInfoService;

    @Resource
    private CameraCtrlProxyService cameraCtrlProxyService;

    @Value("${inspection.config.sceneCount}")
    private int sceneCount;

    public InspSceneDefinition getOneById(Serializable id) {
        InspSceneDefinition byId = super.getById(id);
        if (Objects.isNull(byId)) {
            throw new InspectionException("场景信息不存在");
        }
        return byId;
    }

    public List<InspSceneDefinition> listByPresetId(String presetId) {
        LambdaQueryWrapper<InspSceneDefinition> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspSceneDefinition::getPresetId, presetId);
        wrapper.orderByAsc(InspSceneDefinition::getCreateTime);
        return list(wrapper);
    }

    /**
     * 获取场景，按需进行透视变化
     *
     * @param sceneId    场景ID
     * @param presetInfo 预置点ID
     * @return 场景模型对象
     */
    public SceneForParamConfigModel getSceneModel(String sceneId, InspPresetInfo presetInfo) {
        InspSceneDefinition sceneDefinition = getOneById(sceneId);
        Boolean correctAvailable = presetInfo.getCorrectAvailable();
        // 对基准图进行透视变换
        PicCorrectParamModel correctParam = presetInfo.getCorrectParam();

        SceneForParamConfigModel sceneForParamConfigModel = new SceneForParamConfigModel();
        BeanUtils.copyProperties(sceneDefinition, sceneForParamConfigModel);
        sceneForParamConfigModel.setBenchPic(sceneDefinition.getBenchPic());
        sceneForParamConfigModel.setTmpPic(sceneDefinition.getTmpPic());

        if (Boolean.TRUE.equals(correctAvailable)) {
            String benchPic = sceneDefinition.getBenchPic();
            // 输出地址
            String outputPath = pythonServerService.transformPic(correctParam, benchPic);
            sceneForParamConfigModel.setConfigBenchPic(outputPath);
        } else {
            sceneForParamConfigModel.setConfigBenchPic(sceneDefinition.getBenchPic());
        }
        return sceneForParamConfigModel;
    }

    @Transactional
    public void newScene(InspSceneDefinition entity) {
        // 判断是否跳过模板匹配
        String presetId = entity.getPresetId();
        InspPresetInfo presetInfo = presetInfoService.getOneById(presetId);
        List<InspSceneDefinition> sceneDefinitions = this.listByPresetId(presetId);
        if (!sceneDefinitions.isEmpty() && sceneDefinitions.size() >= sceneCount) {
            throw new InspectionException("场景数量大于规定的" + sceneCount + "，请检查");
        } else {
            if (CollectionUtil.isNotEmpty(sceneDefinitions)) {
                List<String> collect = sceneDefinitions.stream().map(InspSceneDefinition::getName).collect(Collectors.toList());
                if (collect.contains(entity.getName())) {
                    throw new InspectionException("场景名重复");
                }

                // 数据库里已经添加过场景
                String channelId = presetInfo.getChannelId();
                InspChannelInfo byId = inspChannelInfoService.getOneById(channelId);
                ScaleModel scale = cameraCtrlProxyService.getDisplayResolution(byId);
                InspSceneDefinition sceneDefinition = sceneDefinitions.get(0);
                if (isNotEquals(sceneDefinition.getBenchShape(), scale)) {
                    String str = StrUtil.format("当前提交场景的基准图分辨率{}与已保存场景的基准图分辨率不一致", scale);
                    throw new InspectionException(str);
                }
            }
            // 去除前端提交链接中的参数
            String tmpPicNew = URLUtil.getPath(entity.getTmpPic());
            entity.setTmpPic(tmpPicNew);

            // 数据库里没有添加过场景，当前是第一个场景
            entity.setId(null);
            // 将场景图片从临时文件夹下移动到场景文件夹
            movePicToScene(null, entity);

            String benchPic = entity.getBenchPic();
            ScaleModel scaleByImg = PicUtil.getScaleByImgUrl(benchPic);
            String shape = scaleByImg.getWidth() + "-" + scaleByImg.getHeight();
            entity.setBenchShape(shape);
            super.save(entity);
            // 将benchPic添加到redis中
//            addBenchPicToRedis(entity);
        }
    }

//    /**
//     * 添加基准图片到redis中
//     * @param sceneDefinition 场景数据
//     */
//    private void addBenchPicToRedis(InspSceneDefinition sceneDefinition) {
//        String benchPic1 = sceneDefinition.getBenchPic();
//        File benchPic = LocalFileServerUtil.downloadToTemp(MinioUtil.urlToAbsolute(benchPic1));
//        String presetId = sceneDefinition.getPresetId();
//        String sceneId = sceneDefinition.getId();
//        byte[] benchPicData = FileUtil.readBytes(benchPic);
//        Map<String, byte[]> dataMap = new HashMap<>();
//        dataMap.put("image", benchPicData);
//        String streamKey ="BENCH-PIC:"+presetId+":"+sceneId;
//        MapRecord<String, String, byte[]> message = StreamRecords.newRecord()
//                .in(streamKey)
//                .withId(RecordId.autoGenerate())
//                .ofMap(dataMap);
//        redisUtil.xAdd(message);
//    }

//    /**
//     * 删除redis存储的场景基准图片
//     * @param sceneDefinition 场景数据
//     */
//    private void delBenchPicFromRedis(InspSceneDefinition sceneDefinition) {
//        String presetId = sceneDefinition.getPresetId();
//        String sceneId = sceneDefinition.getId();
//        redisUtil.delete("BENCH-PIC:"+presetId+":"+sceneId);
//    }

    /**
     * 将场景图片从临时文件夹下移动到场景文件夹
     * 删除旧场景之前存储的场景图片
     */
    private void movePicToScene(InspSceneDefinition oldEntity, InspSceneDefinition newEntity) {
        if (Objects.nonNull(oldEntity)) {
            String benchPicOld = oldEntity.getBenchPic();
            String tmpPicOld = oldEntity.getTmpPic();
            MinioUtil.removeObjects(ListUtil.of(benchPicOld, tmpPicOld));
        }

        String benchPic = newEntity.getBenchPic();
        // 前端传入的都是相对路径，需要转化成http绝对路径
        benchPic = MinioUtil.copyObject(MinioUtil.urlToAbsolute(benchPic), FileDirEnum.SCENE);
        newEntity.setBenchPic(benchPic);
        String tmpPic = newEntity.getTmpPic();
        tmpPic = MinioUtil.copyObject(MinioUtil.urlToAbsolute(tmpPic), FileDirEnum.SCENE);
        newEntity.setTmpPic(tmpPic);
    }

    @Transactional
    public void deleteByIds(List<String> idList) {
        if (CollectionUtil.isEmpty(idList)) {
            return;
        }
        idList.forEach(this::deleteById);
    }

    @Transactional
    public void deleteById(String sceneId) {
        InspSceneDefinition sceneDefinition = getOneById(sceneId);
        removeById(sceneId);
        // 删除参数实例
        algorithmParamInstanceService.deleteBySceneId(sceneId);

        // 当场景全部被删除时，需要将预置点下全部算法实例设置为未配置状态
        String presetId = sceneDefinition.getPresetId();
        List<InspSceneDefinition> inspSceneDefinitions = sceneDefinitionService.listByPresetId(presetId);
        if (CollectionUtil.isEmpty(inspSceneDefinitions)) {
            List<InspAlgorithmInstance> algorithmInstanceList = algorithmInstanceService.listByPresetId(presetId);
            if (CollectionUtil.isEmpty(algorithmInstanceList)) {
                return;
            }
            // 将预置点下全部算法实例设置为未配置状态
            for (InspAlgorithmInstance algorithmInstance : algorithmInstanceList) {
                algorithmInstance.setHasSaveConfig(false);
            }
            algorithmInstanceService.updateBatchById(algorithmInstanceList);
        }

        // 删除场景相关图片
        String benchPic = sceneDefinition.getBenchPic();
        String tmpPic = sceneDefinition.getTmpPic();
        MinioUtil.removeObjects(ListUtil.of(benchPic, tmpPic));

//        delBenchPicFromRedis(sceneDefinition);
    }

    public void validResolutionRatio(InspSceneDefinition inspSceneDefinition) {
        String presetId = inspSceneDefinition.getPresetId();
        InspPresetInfo presetInfo = presetInfoService.getOneById(presetId);
        List<InspSceneDefinition> sceneDefinitions = this.listByPresetId(presetId);
        if (!sceneDefinitions.isEmpty()) {
            String channelId = presetInfo.getChannelId();
            InspChannelInfo byId = inspChannelInfoService.getOneById(channelId);
            ScaleModel scale = cameraCtrlProxyService.getDisplayResolution(byId);
            sceneDefinitions.forEach(x -> {
                // 跳过自己
                if (!x.getName().equals(inspSceneDefinition.getName())) {
                    String benchShape = x.getBenchShape();
                    if (isNotEquals(benchShape, scale)) {
                        String format = StrUtil.format("{}分辨率{}与当前视频流实际分辨率{}不一致，请统一分辨率后再试", x.getName(), benchShape, scale);
                        throw new InspectionException(format);
                    }
                }
            });
        }
    }

    public boolean isNotEquals(String benchShape, ScaleModel scale) {
        // 坐标允许偏移范围，因为接入NVR后，通过海康SDK获取截图分辨率为1920*1088
        int offset = 10;
        String[] split = benchShape.split("-");
        int shapeW = NumberUtil.parseInt(split[0]);
        int shapeH = NumberUtil.parseInt(split[1]);

        double subW = NumberUtil.sub(shapeW, scale.getWidth());

        if (Math.abs(subW) > offset) {
            return true;
        }
        double subH = NumberUtil.sub(shapeH, scale.getHeight());
        if (Math.abs(subH) > offset) {
            return true;
        }
        log.debug("分辨率判断一致");
        return false;
    }

    // 场景名称校验
    public void validSceneName(String id, String presetId, String name) {
        if (StrUtil.isBlank(name)) {
            throw new InspectionException("场景名称不能为空");
        }
        // 重复校验
        List<InspSceneDefinition> repeatSceneNameList = this.baseMapper.selectList(new LambdaQueryWrapper<InspSceneDefinition>()
                .eq(InspSceneDefinition::getPresetId, presetId)
                .eq(InspSceneDefinition::getName, name).ne(InspSceneDefinition::getId, id));
        if (CollectionUtil.isNotEmpty(repeatSceneNameList)) {
            throw new InspectionException("场景名称已存在");
        }
    }

    public void removeByPresetIds(Collection<? extends Serializable> idList) {
        LambdaQueryWrapper<InspSceneDefinition> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(InspSceneDefinition::getPresetId, idList);
        List<InspSceneDefinition> list = list(wrapper);
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        // 删除场景数据
        remove(wrapper);
        // 删除场景相关图片
        List<String> picUrlList = new ArrayList<>();
        for (InspSceneDefinition sceneDefinition : list) {
            picUrlList.add(sceneDefinition.getBenchPic());
            picUrlList.add(sceneDefinition.getTmpPic());
            // 删除场景下的redis数据
//            delBenchPicFromRedis(sceneDefinition);
        }
        picUrlList = picUrlList.stream().filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        MinioUtil.removeObjects(picUrlList);
    }

    @Transactional
    public void updateScene(InspSceneDefinition inspSceneDefinition) {
        AssertUtil.isTrue(StrUtil.isNotBlank(inspSceneDefinition.getBenchPic()), "场景基准图不允许为空");
        String tmpPicNew = inspSceneDefinition.getTmpPic();
        AssertUtil.isTrue(StrUtil.isNotBlank(tmpPicNew), "场景模板截图不允许为空");
        // 去除前端提交链接中的参数
        tmpPicNew = URLUtil.getPath(tmpPicNew);
        inspSceneDefinition.setTmpPic(tmpPicNew);

        String sceneDefinitionId = inspSceneDefinition.getId();
        InspSceneDefinition byId = getOneById(sceneDefinitionId);
        // 将场景图片从临时文件夹下移动到场景文件夹
        movePicToScene(byId, inspSceneDefinition);
        // 不更新创建时间
        inspSceneDefinition.setCreateTime(null);
        inspSceneDefinition.setUpdateTime(LocalDateTime.now());
        // 重置场景的分辨率
        String benchPicNew = inspSceneDefinition.getBenchPic();
        ScaleModel scaleByImg = PicUtil.getScaleByImgUrl(benchPicNew);
        String shape = scaleByImg.getWidth() + "-" + scaleByImg.getHeight();
        inspSceneDefinition.setBenchShape(shape);

        updateById(inspSceneDefinition);
        // 判断图片路径是否修改，如果修改则删除之前旧图片
        String benchPicOld = byId.getBenchPic();
        if (!StrUtil.equals(benchPicOld, benchPicNew)) {
            // 删除旧的 图片
            FileUtil.del(benchPicOld);
        }
        String tmpPicOld = byId.getTmpPic();
        if (!StrUtil.equals(tmpPicOld, tmpPicNew)) {
            FileUtil.del(tmpPicOld);
        }
    }

    public void updateSceneName(String id, String presetId, String name) {
        if (StrUtil.isBlank(id)) {
            throw new InspectionException("场景ID不能为空");
        }
        if (StrUtil.isBlank(presetId)) {
            throw new InspectionException("预置点ID不能为空");
        }
        validSceneName(id, presetId, name);
        // 执行修改保存
        InspSceneDefinition inspSceneDefinition = this.getOneById(id);
        inspSceneDefinition.setName(name);
        this.baseMapper.updateById(inspSceneDefinition);
    }
}

