package com.hollysys.inspection.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.constants.ProjectNodeType;
import com.hollysys.inspection.entity.*;
import com.hollysys.inspection.mapper.InspThirdAlgorithmSubscriptionMapper;
import com.hollysys.inspection.model.alarm.config.AlarmRuleItemModel;
import com.hollysys.inspection.model.algorithm.execute.OutputValueObj;
import com.hollysys.inspection.model.thirdapp.AlgorithmInstanceResModel;
import com.hollysys.inspection.model.thirdapp.AlgorithmResultResModel;
import com.hollysys.inspection.model.thirdapp.AlgorithmSubscriptionReqModel;
import com.hollysys.inspection.model.thirdapp.PresentInfoResModel;
import com.hollysys.inspection.model.tree.InspProjectTreeNodeModel;
import com.hollysys.inspection.model.tree.InspThirdAppProjectTreeNodeModal;
import com.hollysys.inspection.model.tree.ThirdAppProjectTreeAndStateModel;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class InspThirdAlgorithmSubscriptionService extends ServiceImpl<InspThirdAlgorithmSubscriptionMapper, InspThirdAlgorithmSubscription> {

    @Resource
    private InspThirdAppService inspThirdAppService;

    @Resource
    private InspPresetInfoService inspPresetInfoService;

    @Resource
    private ProjectTreeNodeService projectTreeNodeService;

    @Resource
    private InspAlgorithmService inspAlgorithmService;

    @Resource
    private InspAlgorithmInstanceService inspAlgorithmInstanceService;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private InspProjectService inspProjectService;

    @Resource
    private InspChannelInfoService inspChannelInfoService;


    /**
     * 订阅算法
     *
     * @return 订阅结果
     */
    @Transactional
    public String subscriptionAlgorithm(AlgorithmSubscriptionReqModel algorithmSubscriptionReqModel, String appAuthorizationCode) {
        // 校验订阅算法或者算法实例是否存在与预置点中
        validateAlgorithm(algorithmSubscriptionReqModel);

        // 根据授权码寻找对应的appid
        String appId = findAppId(appAuthorizationCode);

        // 将请求模型转换为实体对象
        InspThirdAlgorithmSubscription entity = convertToEntity(algorithmSubscriptionReqModel, appId);

        // 查询是否已存在订阅记录
        InspThirdAlgorithmSubscription existing = lambdaQuery()
                .eq(InspThirdAlgorithmSubscription::getAppId, appId)
                .one();

        if (existing != null) {
            // 更新操作：保留原ID和创建时间 ，新增更新时间 并更新其余字段
            entity.setId(existing.getId());
            entity.setUpdateTime(LocalDateTime.now());
            updateById(entity);
            return "订阅更新成功";
        } else {
            // 新增操作
            save(entity);
            return "订阅新增成功";
        }
    }

    /**
     * 发送订阅算法
     * @param algorithmInstance 算法实例
     * @param output 输出结果
     */
    @Transactional
    public void sendSubscriptionAlgorithmResult(InspAlgorithmInstance algorithmInstance, Map<String, OutputValueObj> output, Map<String, AlarmRuleItemModel> alarmResult) {
        // 获取订阅信息
        List<InspThirdAlgorithmSubscription> subscriptions = list();

        // 检查订阅信息是否为空
        if (CollectionUtils.isEmpty(subscriptions)) {
            log.debug("没有找到任何算法订阅配置，无需发送通知。");
            return;
        }

        // 遍历订阅信息
        for (InspThirdAlgorithmSubscription subscription : subscriptions) {
            // 判断当前算法结果是否与订阅信息匹配
            if (shouldSendNotification(algorithmInstance, subscription, alarmResult)) {
                // 构建发送结构
                AlgorithmResultResModel matchedResults = getAlgorithmResultResModel(algorithmInstance, output, subscription);
                // 发送通知
                sendResult(subscription, matchedResults);
            }
        }
    }

    @NotNull
    private static AlgorithmResultResModel getAlgorithmResultResModel(InspAlgorithmInstance algorithmInstance, Map<String, OutputValueObj> output, InspThirdAlgorithmSubscription subscription) {
        AlgorithmResultResModel matchedResults = new AlgorithmResultResModel();
        matchedResults.setPresetId(algorithmInstance.getPresetId());
        matchedResults.setAlgorithmInstanceId(algorithmInstance.getId());
        matchedResults.setAlgorithmId(algorithmInstance.getAlgorithmId());
        matchedResults.setTimestamp(new Date());
        matchedResults.setAlarm(subscription.getSubscriptionType().equals("0")); // 0为是报警结果 1为不是
        matchedResults.setOutput(output);
        return matchedResults;
    }

    /**
     * 判断订阅信息和结果是否匹配
     * @param algorithmInstance 算法实例
     * @param subscription 订阅信息
     * @return 匹配结果
     */
    private boolean shouldSendNotification(InspAlgorithmInstance algorithmInstance, InspThirdAlgorithmSubscription subscription,
                                           Map<String, AlarmRuleItemModel> alarmResult) {

        // 预置点ID匹配
        boolean matchPresetId = subscription.getPresetId().equals(algorithmInstance.getPresetId());

        // 算法ID匹配
        boolean matchAlgorithmId = true;
        if (subscription.getAlgorithmId() != null && !StrUtil.isEmpty(subscription.getAlgorithmId())) {
            matchAlgorithmId = subscription.getAlgorithmId().equals(algorithmInstance.getAlgorithmId());
        }

        // 算法实例id匹配
        boolean matchAlgorithmInstanceId = true;
        if (!CollectionUtils.isEmpty(subscription.getAlgorithmInstanceIds())) {
            matchAlgorithmInstanceId = subscription.getAlgorithmInstanceIds().contains(algorithmInstance.getId());
        }

        // 判断是否存在有效的报警结果  用于该算法设置了报警 但未达到报警条件时 alarmResult就不为null
        boolean hasValidAlarm = false;
        if (alarmResult != null) {
            for (AlarmRuleItemModel alarmItem : alarmResult.values()) {
                // 只要找到一个非空的，就认为存在有效报警
                if (alarmItem != null) {
                    hasValidAlarm = true;
                    break;
                }
            }
        }

        // 根据 subscriptionType 判断是否需要报警时才发送算法结果
        boolean matchSubscriptionTypeAndAlarm = true;
        // 如果订阅类型是“0” (只发送报警时的算法结果)
        if ("0".equals(subscription.getSubscriptionType())) {
            // 只有当 hasValidAlarm 为 true 时才发送
            matchSubscriptionTypeAndAlarm = hasValidAlarm;
        }

        return matchPresetId && matchAlgorithmId && matchAlgorithmInstanceId && matchSubscriptionTypeAndAlarm;
    }

    /**
     * 获取当前工程树中所有预设点的信息
     *
     * @return 预设点信息列表
     */
    public List<PresentInfoResModel> getPresentInfos() {
        // 获取所有项目树节点
        List<InspProjectTreeNode> inspProjectTreeNodes = projectTreeNodeService.list();

        // 检查工程树数据是否为空
        if (CollectionUtils.isEmpty(inspProjectTreeNodes)) {
            throw new InspectionException("工程树数据为空");
        }

        // 获取所有算法实例信息
        List<InspAlgorithmInstance> inspAlgorithmInstances = inspAlgorithmInstanceService.list();

        // Map用于存储通道节点的ID和标签
        Map<String, String> channelMap = inspProjectTreeNodes.stream()
                .filter(node -> ProjectNodeType.CHANNEL.equals(node.getType())) // 过滤出类型为"channel"的节点
                .collect(Collectors.toMap(
                        InspProjectTreeNode::getId, // 将节点的ID作为键
                        InspProjectTreeNode::getLabel // 将节点的名字作为值
                ));

        // 创建预置点ID到算法实例列表的映射
        Map<String, List<InspAlgorithmInstance>> presetToInstancesMap = inspAlgorithmInstances.stream()
                .collect(Collectors.groupingBy(InspAlgorithmInstance::getPresetId)); // 将预置点ID作为键，该预置点下的算法实例列表作为值

        // 遍历所有预置点节点，并将其映射为 PresentInfoResModel 对象
        return inspProjectTreeNodes.stream()
                .filter(node -> ProjectNodeType.PRESET.equals(node.getType())) // 过滤出类型为"preset"的节点
                .map(preset -> createPresentInfoResModel(preset, channelMap, presetToInstancesMap)) // 调用方法创建 PresentInfoResModel
                .collect(Collectors.toList()); // 将结果收集为列表并返回
    }

    /**
     * 根据预置点信息、通道映射和预置点到实例的映射创建 PresentInfoResModel 对象。
     *
     * @param preset                预置点树节点信息
     * @param channelMap            通道ID到名称的映射
     * @param presetToInstancesMap  预置点ID到算法实例列表的映射
     * @return PresentInfoResModel 对象
     */
    private PresentInfoResModel createPresentInfoResModel(InspProjectTreeNode preset,
                                                          Map<String, String> channelMap,
                                                          Map<String, List<InspAlgorithmInstance>> presetToInstancesMap) {

        String channelId = preset.getParentId(); // 获取预置点所属的通道ID
        String channelLabel = channelMap.getOrDefault(channelId, ""); // 根据通道ID获取通道名称，如果不存在则使用空字符串
        String presetId = preset.getId(); // 获取预置点ID
        // 获取处理后的算法实例列表（包含排序和序号）
        List<AlgorithmInstanceResModel> algorithmInstancesWithOrder = getAlgorithmInstancesWithOrder(presetId, presetToInstancesMap);

        // 创建并返回 PresentInfoResModel 对象
        return new PresentInfoResModel(
                presetId,
                preset.getLabel(),
                channelId,
                channelLabel,
                algorithmInstancesWithOrder
        );
    }

    /**
     * 根据预置点ID获取排序并添加序号后的算法实例列表。
     *
     * @param presetId              预置点ID
     * @param presetToInstancesMap  预置点ID到算法实例列表的映射
     * @return 排序并添加序号后的 AlgorithmInstanceInfo 列表
     */
    private List<AlgorithmInstanceResModel> getAlgorithmInstancesWithOrder(
            String presetId,
            Map<String, List<InspAlgorithmInstance>> presetToInstancesMap) {

        // 获取当前预置点下的所有算法实例列表
        List<InspAlgorithmInstance> instancesForPreset = presetToInstancesMap.getOrDefault(presetId, Collections.emptyList());
        List<AlgorithmInstanceResModel> algorithmInstancesWithOrder = new ArrayList<>();

        if (!CollectionUtils.isEmpty(instancesForPreset)) {
            // 对当前预置点下的所有算法实例按创建时间进行升序排序
            instancesForPreset.sort(Comparator.comparing(InspAlgorithmInstance::getCreateTime));

            int order = 1;
            // 遍历排序后的实例列表，添加序号
            for (InspAlgorithmInstance instance : instancesForPreset) {
                String nameWithOrder = instance.getName() + "#" + order++;
                algorithmInstancesWithOrder.add(new AlgorithmInstanceResModel(instance.getId(), nameWithOrder));
            }
        }

        return algorithmInstancesWithOrder;
    }

    /**
     * 获取当前系统所有算法信息
     *
     * @return 算法信息列表
     */
    public List<InspAlgorithm> getAlgorithmInfos() {
        List<InspAlgorithm> list = inspAlgorithmService.list();
        // 检查算法数据是否为空
        if (CollectionUtils.isEmpty(list)) {
            throw new InspectionException("算法数据为空");
        }
        return list;
    }


    /**
     * 寻找appid
     *
     * @return appid
     */
    private String findAppId(String appAuthorizationCode) {
        // 获取所有第三方应用的数据列表
        List<InspThirdApp> inspThirdApps = inspThirdAppService.list();

        // 检查列表是否为空，如果为空则抛出异常
        if (CollectionUtils.isEmpty(inspThirdApps)) {
            throw new InspectionException("未找到任何第三方应用数据");
        }

        // 根据授权码查询应用
        InspThirdApp foundApp = inspThirdAppService.getOne(new LambdaQueryWrapper<InspThirdApp>()
                .eq(InspThirdApp::getLicenseCode, appAuthorizationCode));

        if (foundApp != null) {
            // 如果找到了，返回应用的ID
            return foundApp.getId();
        } else {
            throw new InspectionException("不存在该app");
        }
    }

    /**
     * 校验预置点id下的算法实例或算法id都在该预置点下有值
     * 该方法用于验证传入的预置点ID列表中的所有ID是否都存在于系统中的预置点信息中。
     */
    private void validateAlgorithm(AlgorithmSubscriptionReqModel algorithmSubscriptionReqModel) {
        // 获取订阅的预置点ID
        String presentId = algorithmSubscriptionReqModel.getPresetId();
        // 获取订阅的算法id和算法实例ids
        String subscribedAlgorithmId = algorithmSubscriptionReqModel.getAlgorithmId();
        List<String> subscribedInstanceIds = algorithmSubscriptionReqModel.getAlgorithmInstanceIds();

        // 如果算法ID和算法实例ID列表都为空，则抛出异常
        if (StrUtil.isEmpty(subscribedAlgorithmId) && CollectionUtils.isEmpty(subscribedInstanceIds)) {
            throw new InspectionException("算法id和算法实例ids不能同时为空");
        }

        // 校验算法实例ID列表中的每一项不能为空
        if (!CollectionUtils.isEmpty(subscribedInstanceIds)) {
            for (String instanceId : subscribedInstanceIds) {
                if (StrUtil.isEmpty(instanceId)) {
                    throw new InspectionException("算法实例ID列表中不能包含 null 或空字符串");
                }
            }
        }

        // 获取系统中所有的预置点信息列表
        List<InspPresetInfo> inspPresetInfos = inspPresetInfoService.list();
        
        // 如果系统中没有任何预置点信息，抛出异常
        if (CollectionUtils.isEmpty(inspPresetInfos)) {
            throw new InspectionException("未找到任何预置点");
        }

        // 提取系统中所有预置点的ID存入一个Set
        Set<String> existingPresetIds = inspPresetInfos.stream()
                .map(InspPresetInfo::getId) // 提取每个预置点的ID
                .collect(Collectors.toSet()); // 收集到一个Set集合中

        // 检查订阅的预置点ID是否存在于系统中
        if (!existingPresetIds.contains(presentId)) {
            throw new InspectionException("预置点ID: " + presentId + " 不存在");
        }

        // 根据预置点ID查找对应的算法实例
        List<InspAlgorithmInstance> algorithmInstancesForPreset = inspAlgorithmInstanceService.list(
                new QueryWrapper<InspAlgorithmInstance>().eq("preset_id", presentId)
        );

        // 校验订阅的算法ID是否存在于该预置点的算法实例中
        if (StrUtil.isNotEmpty(subscribedAlgorithmId)) {
            boolean algorithmIdExists = algorithmInstancesForPreset.stream()
                    .anyMatch(instance -> StrUtil.equals(instance.getAlgorithmId(), subscribedAlgorithmId));
            if (!algorithmIdExists) {
                throw new InspectionException("算法ID: " + subscribedAlgorithmId + " 在预置点 " + presentId + " 的可用算法实例中不存在");
            }
        }

        // 校验订阅的算法实例ID是否存在于该预置点的算法实例中
        if (!CollectionUtils.isEmpty(subscribedInstanceIds)) {
            Set<String> existingInstanceIdsForPreset = algorithmInstancesForPreset.stream()
                    .map(InspAlgorithmInstance::getId)
                    .collect(Collectors.toSet());
            List<String> notExistingSubscribedInstanceIds = subscribedInstanceIds.stream()
                    .filter(id -> !existingInstanceIdsForPreset.contains(id))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(notExistingSubscribedInstanceIds)) {
                throw new InspectionException("算法实例ID: " + String.join(", ", notExistingSubscribedInstanceIds) + " 在预置点 " + presentId + " 的可用算法实例中不存在");
            }
        }
    }

    /**
     * 转化为实体
     *
     * @param request 请求体数据
     * @param appCode 绑定的appid
     * @return entity 实体
     */
    private InspThirdAlgorithmSubscription convertToEntity(AlgorithmSubscriptionReqModel request, String appCode) {
        InspThirdAlgorithmSubscription entity = new InspThirdAlgorithmSubscription();
        // 设置表中参数
        entity.setSubscriptionType(request.getSubscriptionType());
        entity.setPresetId(request.getPresetId());
        entity.setAlgorithmId(request.getAlgorithmId());
        entity.setAlgorithmInstanceIds(request.getAlgorithmInstanceIds());
        entity.setCallbackUrl(request.getCallbackUrl());
        entity.setHttpMethod(request.getHttpMethod());
        entity.setCreateTime(LocalDateTime.now());
        entity.setAppId(appCode);
        return entity;
    }

    /**
     * 发送通知
     *
     * @param subscription 订阅信息
     * @param matchedResult 匹配的算法结果
     */
    private void sendResult(InspThirdAlgorithmSubscription subscription, AlgorithmResultResModel matchedResult) {
        try {
            // 校验接口发送方法是否正确
            HttpMethod method = HttpMethod.resolve(subscription.getHttpMethod().toUpperCase());
            if (method == null) {
                throw new InspectionException("无效的HTTP方法: " + subscription.getHttpMethod());
            }

            // 设置请求体
            HttpEntity<AlgorithmResultResModel> requestEntity = new HttpEntity<>(matchedResult);

            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(
                    subscription.getCallbackUrl(),
                    method,
                    requestEntity,
                    String.class
            );

            log.debug("发送订阅通知成功, URL: {}, 状态码: {}",
                    subscription.getCallbackUrl(),
                    response.getStatusCodeValue());
        } catch (Exception e) {
            log.error("发送订阅通知失败, URL: {}", subscription.getCallbackUrl(), e);
        }
    }

    /**
     * 获取工程树节点和状态
     *
     * @return 工程树节点和状态
     */
    @Transactional
    public ThirdAppProjectTreeAndStateModel getProjectTreeAndState() {
        InspProject project = inspProjectService.getFirstOne();
        if (Objects.isNull(project)) {
            // 初始化工程数据
            project = inspProjectService.createProject();
        }

        List<InspProjectTreeNodeModel> rawTree = projectTreeNodeService.getTree();

        List<InspThirdAppProjectTreeNodeModal> detailedTree = convertToDetailedTree(rawTree);

        traverseAndFillPresetInfo(detailedTree);

        ThirdAppProjectTreeAndStateModel projectTreeAndStateModel = new ThirdAppProjectTreeAndStateModel();
        projectTreeAndStateModel.setProject(project);
        projectTreeAndStateModel.setTree(detailedTree);

        return projectTreeAndStateModel;
    }

    /**
     * 递归遍历树，填充 presetInfo 字段 和 channelInfo 字段
     * @param nodes 树节点列表
     */
    private void traverseAndFillPresetInfo(List<InspThirdAppProjectTreeNodeModal> nodes) {
        if (nodes == null || nodes.isEmpty()) {
            return;
        }

        for (InspThirdAppProjectTreeNodeModal node : nodes) {
            // 检查当前节点的类型是否为预置点
            if (ProjectNodeType.PRESET.equals(node.getType())) {
                InspPresetInfo inspPresetInfo = inspPresetInfoService.getOneById(node.getId());
                if (!Objects.isNull(inspPresetInfo)) {
                    node.setPresetInfo(inspPresetInfo);
                }
            }
            // 检查当前节点的类型是否为通道
            if (ProjectNodeType.CHANNEL.equals(node.getType())) {
                InspChannelInfo inspChannelInfo = inspChannelInfoService.getOneById(node.getId());
                if (!Objects.isNull(inspChannelInfo)) {
                    node.setChannelInfo(inspChannelInfo);
                }
            }

            // 递归处理子节点
            if (node.getChildren() != null && !node.getChildren().isEmpty()) {
                traverseAndFillPresetInfo(node.getChildren());
            }
        }
    }

    private List<InspThirdAppProjectTreeNodeModal> convertToDetailedTree(List<InspProjectTreeNodeModel> rawNodes) {
        if (rawNodes == null || rawNodes.isEmpty()) {
            return new ArrayList<>();
        }
        return rawNodes.stream().map(rawNode -> {
            InspThirdAppProjectTreeNodeModal inspThirdAppProjectTreeNodeModal = new InspThirdAppProjectTreeNodeModal();
            BeanUtils.copyProperties(rawNode, inspThirdAppProjectTreeNodeModal);

            // 递归处理子节点
            if (rawNode.getChildren() != null && !rawNode.getChildren().isEmpty()) {
                inspThirdAppProjectTreeNodeModal.setChildren(convertToDetailedTree(rawNode.getChildren()));
            }
            return inspThirdAppProjectTreeNodeModal;
        }).collect(Collectors.toList());
    }
}
