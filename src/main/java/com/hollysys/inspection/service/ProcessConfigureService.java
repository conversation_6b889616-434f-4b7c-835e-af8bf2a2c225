package com.hollysys.inspection.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.entity.InspAlgorithmParamInstance;
import com.hollysys.inspection.entity.InspAlgorithmInstance;
import com.hollysys.inspection.entity.InspSceneDefinition;
import com.hollysys.inspection.model.algorithm.configure.CopyParamReqModel;
import com.hollysys.inspection.model.algorithm.configure.SwitchMatchEnableReqModel;
import com.hollysys.inspection.model.algorithm.correct.CorrectReqModel;
import com.hollysys.inspection.model.algorithm.correct.CorrectRespModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * (ChannelNode)表服务接口
 *
 * <AUTHOR>
 * @since 2023-02-01 15:36:00
 */
@Slf4j
@Service
public class ProcessConfigureService {

    @Resource
    private PythonServerService pythonServerService;

    @Resource
    private InspAlgorithmInstanceService algorithmInstanceService;

    @Resource
    private InspSceneDefinitionService sceneDefinitionService;

    @Resource
    private InspAlgorithmParamInstanceService algorithmParamInstanceService;

    public CorrectRespModel correct(CorrectReqModel reqModel) {
        // TODO 输出地址
        String outputPath = "outputPath";

        // 调用py服务矫正，返回矫正结果，结果图片存储在参数输出路径中
        CorrectRespModel correctRespModel = pythonServerService.transformPic(reqModel);
        correctRespModel.setResultImg(outputPath);
        return correctRespModel;
    }

    /**
     * copy场景配置到其他场景
     *
     * @param copyParamReqModel 请求参数
     */
    @Transactional
    public void copyToOtherScenes(CopyParamReqModel copyParamReqModel) {
        String fromSceneId = copyParamReqModel.getFromSceneId();
        String algorithmInstanceId = copyParamReqModel.getAlgorithmInstanceId();
        String presetId = copyParamReqModel.getPresetId();
        if (StrUtil.hasBlank(fromSceneId, algorithmInstanceId, presetId)) {
            throw new InspectionException("参数错误");
        }

        // 获取当前预置点下全部场景
        List<InspSceneDefinition> sceneDefinitions = sceneDefinitionService.listByPresetId(presetId);
        if (CollectionUtil.isEmpty(sceneDefinitions)) {
            throw new InspectionException("预置点下场景列表为空");
        }
        // 过滤掉源场景
        sceneDefinitions = sceneDefinitions.stream().filter(item -> !item.getId().equals(fromSceneId)).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(sceneDefinitions)) {
            throw new InspectionException("预置点下场景只有一个，无需同步参数");
        }

        // 复制源场景的全部参数
        copyParamToOtherScenes(copyParamReqModel, sceneDefinitions);
    }

    private void copyParamToOtherScenes(CopyParamReqModel copyParamReqModel, List<InspSceneDefinition> sceneDefinitions) {
        String fromSceneId = copyParamReqModel.getFromSceneId();
        String algorithmInstanceId = copyParamReqModel.getAlgorithmInstanceId();
        List<InspAlgorithmParamInstance> fromParams = algorithmParamInstanceService.listByInstanceIdAndSceneId(algorithmInstanceId, fromSceneId);
        if (CollectionUtil.isEmpty(fromParams)) {
            log.warn("复制场景参数到其余场景失败，源场景[{}]参数列表为空", fromSceneId);
            return;
        }
        for (InspSceneDefinition sceneDefinition : sceneDefinitions) {
            String currentSceneId = sceneDefinition.getId();
            // 删除当前算法示例场景的全部参数
            algorithmParamInstanceService.deleteByByInstanceIdAndSceneId(algorithmInstanceId, currentSceneId);

            // 新建参数
            for (InspAlgorithmParamInstance fromParam : fromParams) {
                fromParam.setId(null);
                fromParam.setSceneId(currentSceneId);
            }
            algorithmParamInstanceService.saveBatch(fromParams);
        }
    }

    /**
     * 切换模板匹配启用状态
     *
     * @param reqModel 请求参数
     */
    public void switchMatchEnable(SwitchMatchEnableReqModel reqModel) {
        String algorithmInstanceId  = reqModel.getAlgorithmInstanceId();
        Boolean matchEnable = reqModel.getMatchEnable();
        // 更新
        InspAlgorithmInstance oneById = algorithmInstanceService.getOneById(algorithmInstanceId);
        oneById.setMatchEnable(matchEnable);
        algorithmInstanceService.updateById(oneById);
    }
}
