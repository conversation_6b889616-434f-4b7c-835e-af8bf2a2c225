package com.hollysys.inspection.service;

import cn.darkjrong.license.core.common.pojo.params.LicenseExtraParam;
import cn.darkjrong.license.core.common.utils.EncryptionUtils;
import cn.darkjrong.license.core.common.utils.ServerInfoUtils;
import cn.darkjrong.license.verify.listener.LicenseVerifyListener;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.unit.DataSize;
import cn.hutool.core.io.unit.DataUnit;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.config.redis.RedisHelper;
import com.hollysys.inspection.entity.InspAlgorithm;
import com.hollysys.inspection.entity.InspChannelInfo;
import com.hollysys.inspection.model.license.LicenseReqModel;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class LicenseService {

    private static final String LICENSE_FILE_TYPE = "lic";

    private static final String LICENSE_IN_REDIS = "license";

    private static final String AUTH_TIME_IN_REDIS = "authTime";

    @Value("${license.verify.license-path}")
    private String licenseFilePath;

    @Value("${license.system-version}")
    private String systemVersion;

    @Value("${license.product-version}")
    private String productVersion;

    @Resource
    private RedisHelper redisUtils;

    @Resource
    private InspAlgorithmService inspAlgorithmService;

    @Resource
    private InspChannelInfoService channelNodeService;

    @Resource
    private LicenseVerifyListener licenseListenerTask;


    /**
     * 校验证书是否有效
     *
     * @return true
     */
    public String checkLicense() {
        licenseListenerTask.licenseListenerTask();
        return "true";
    }


    /**
     * 从redis中获取证书参数
     *
     * @return 证书参数
     */
    public LicenseReqModel getLicenseExtraParam() {
        // 获取通道数量
        int channelNodeCount = getChannelNodeCount();
        // 获取算法名称列表
        List<String> algorithmNameList = getAlgorithmNameList();
        // 获取算法数量
        int algorithmCount = getAlgorithmCount();

        // 获取证书参数
        LicenseExtraParam licenseExtraParamInRedis = getLicenseExtraParamInRedis();
        LicenseReqModel licenseReqModel = new LicenseReqModel();

        // 如果证书参数不为空，则将证书参数中的字段值复制到licenseReqModel中 并将过期日期赋值
        if (licenseExtraParamInRedis != null) {
            BeanUtils.copyProperties(licenseExtraParamInRedis, licenseReqModel);
            // 将证书过期时间存入接口返回数据
            licenseReqModel.setExpireTime(licenseExtraParamInRedis.getExpireTime());
        }

        // 将证书参数中的appCode字段进行加密来获取appcode
        String appCode = EncryptionUtils.encode(ServerInfoUtils.getServerInfos());
        // 证书产品版本
        licenseReqModel.setProductVersion(productVersion);
        // 证书系统版本
        licenseReqModel.setSystemVersion(systemVersion);
        // 证书授权时间
        String authTime = redisUtils.get(AUTH_TIME_IN_REDIS);
        licenseReqModel.setAuthTime(JSON.parseObject(authTime, Date.class));
        // 将appcode存入接口返回数据
        licenseReqModel.setAppCode(appCode);
        // 将当前通道数量存入接口返回数据
        licenseReqModel.setChannelNodeCount(channelNodeCount);
        // 将当前算法名字列表存入接口返回数据
        licenseReqModel.setAlgorithmNameList(algorithmNameList);
        // 将当前算法个数存入接口返回数据
        licenseReqModel.setAlgorithmCount(algorithmCount);
        return licenseReqModel;
    }

    /**
     * 获取当前通道数量
     *
     * @return 当前通道数量
     */
    public int getChannelNodeCount() {
        // 调用channelNodeService的list方法获取所有InspChannelNode对象的列表
        List<InspChannelInfo> list = channelNodeService.list();
        // 检查列表是否为空或为null，如果是，则返回0
        if (list == null || list.isEmpty()) {
            return 0;
        }
        List<String> codeList = list.stream().map(InspChannelInfo::getId).distinct().collect(Collectors.toList());
        return codeList.size();
    }

    /**
     * 获取数据库中当前算法名字列表
     *
     * @return 当前算法名字列表
     */
    public List<String> getAlgorithmNameList() {
        // 调用inspAlgorithmService的list方法获取所有InspAlgorithm对象的列表
        List<InspAlgorithm> list = inspAlgorithmService.list();
        // 如果列表为空或不存在，返回一个空的列表
        if (list == null || list.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> codeList = list.stream().map(InspAlgorithm::getCode).distinct().collect(Collectors.toList());
        return list.stream().filter(item -> codeList.contains(item.getCode()))
                .map(InspAlgorithm::getName)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 获取数据库中当前算法数量
     *
     * @return 当前算法数量
     */
    public int getAlgorithmCount() {
        // 调用inspAlgorithmService的list方法获取所有InspAlgorithm对象的列表
        List<InspAlgorithm> list = inspAlgorithmService.list();
        // 检查列表是否为空或为null，如果是，则返回0
        if (list == null || list.isEmpty()) {
            return 0;
        }
        List<String> codeList = list.stream().map(InspAlgorithm::getCode).distinct().collect(Collectors.toList());
        return codeList.size();
    }

    /**
     * 将redis中的数据转换为JSON字符串
     *
     * @return 转换后的字符串
     */
    public LicenseExtraParam getLicenseExtraParamInRedis() {
        Object o = redisUtils.get(LICENSE_IN_REDIS);
        if (o == null) {
            return null;
        }
        return JSONUtil.toBean(JSONUtil.toJsonStr(o), LicenseExtraParam.class);
    }


    /**
     * 将证书参数存入redis
     */
    public void saveLicenseExtraParam(String param) {
        redisUtils.set(LICENSE_IN_REDIS, param);
    }


    /**
     * 上传license文件
     *
     * @param licenseFile 上传的文件
     * @return 是否上传成功
     */
    public LicenseReqModel uploadLicenseFile(MultipartFile licenseFile) {
        if (Objects.isNull(licenseFile) || licenseFile.getSize() <= 0) {
            throw new InspectionException("上传文件不允许为空");
        }

        DataSize dataSize = DataSize.of(20, DataUnit.KILOBYTES);
        long maxSize = dataSize.toBytes();
        long size = licenseFile.getSize();
        if (size > maxSize) {
            throw new InspectionException("上传授权文件大小最大允许20KB");
        }

        String suffix = FileUtil.getSuffix(licenseFile.getOriginalFilename());
        if (!LICENSE_FILE_TYPE.equals(suffix)) {
            throw new InspectionException("只允许上传lic文件");
        }

        // 将licenseFile 存入到指定路径
        // 判断文件夹是否存在，不存在则创建
        File directory = new File(licenseFilePath);
        if (!directory.exists()) {
            if (!directory.mkdirs()) {
                throw new InspectionException("创建文件夹失败");
            }
        }

        // 检查目标文件是否已存在
        File targetFile = new File(licenseFilePath);
        if (!forceDelete(targetFile)) {
            // 强制删除源文件
            throw new InspectionException("文件删除失败");
        }

        // 将上传的文件保存到目标文件中
        try {
            licenseFile.transferTo(targetFile);
            // 更新后在手动校验一次证书
            licenseListenerTask.licenseListenerTask();
            // 校验成功后将当前时间存入redis中作为授权时间
            redisUtils.set(AUTH_TIME_IN_REDIS, JSON.toJSONString(new Date()));
        } catch (IOException e) {
            throw new InspectionException("文件上传失败");
        }
        return getLicenseExtraParam();
    }

    /**
     * 强制删除原证书文件
     *
     * @param file 要删除的文件
     * @return 是否删除成功
     */
    private boolean forceDelete(File file) {
        if (!file.exists()) {
            return true; // 文件不存在，无需删除
        }
        for (int i = 0; i < 10; i++) { // 尝试 10 次
            if (file.delete()) {
                return true; // 删除成功
            }
            System.gc(); // 触发垃圾回收，释放文件句柄
            try {
                Thread.sleep(100); // 等待 100 毫秒
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        return false; // 删除失败
    }
}

