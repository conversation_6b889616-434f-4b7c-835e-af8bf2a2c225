package com.hollysys.inspection.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.constants.channel.MtxSourceProtocol;
import com.hollysys.inspection.entity.InspChannelInfo;
import com.hollysys.inspection.entity.InspCommonConfig;
import com.hollysys.inspection.model.mediamtx.StreamModel;
import com.hollysys.inspection.utils.InspStringUtils;
import com.hollysys.inspection.utils.IpUtil;
import com.hollysys.inspection.utils.MyHttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MediamtxService {

    @Resource
    private DockerService dockerService;

    @Resource
    private InspCommonConfigService inspCommonConfigService;

    // 同步删除流的最大循环次数
    private final static int maxAttempts = 10;

    // 同步删除流的每次循环时间间隔
    private final static long delayMillis = 1000;

    @Value("${mediamtx.api.add-stream}")
    private String addStreamUrl;

    @Value("${mediamtx.api.del-stream}")
    private String delStreamUrl;

    @Value("${mediamtx.api.get-stream}")
    private String getStreamUrl;

    @Value("${mediamtx.api.list-stream}")
    private String listStreamUrl;

    @Value("${mediamtx.base-play-url}")
    private String basePlayUrl;

    @Value("${mediamtx.yml-file-path}")
    private String mediamtxYmlFilePath;

    @Value("${mediamtx.container-name}")
    private String containerName;

    @Value("${mediamtx.ip-file-path}")
    private String ipFilePath;

    private String getExistStream(InspChannelInfo channel) {
        String streamName = channel.getId();
        // 查询当前流是否已存在
        String stream = MyHttpUtil.get(getStreamUrl + streamName);
        if (JSONUtil.isTypeJSONObject(stream)) {
            JSONObject entries = JSONUtil.parseObj(stream);
            String name = entries.getStr("name");
            if (StrUtil.isNotBlank(name)) {
                return basePlayUrl + streamName;
            }
        }
        return null;
    }

    private boolean isPushSuccess(HttpResponse postResult) {
        int status = postResult.getStatus();
        String body = postResult.body();
        if (HttpStatus.HTTP_OK != status) {
            if (JSONUtil.isTypeJSON(body)) {
                JSONObject entries = JSONUtil.parseObj(body);
                String str = entries.getStr("error");
                return "path already exists".equals(str);
            }
            return false;
        }
        return true;
    }

    public String pushStream(InspChannelInfo channel) {
        String rtsp = channel.getRtsp();
        if (StrUtil.isEmpty(rtsp)) {
            throw new InspectionException("通道RTSP地址尚未完成，请完善相关配置");
        }

        String existStream = getExistStream(channel);
        if (StrUtil.isNotBlank(existStream)) {
            return existStream;
        }

        String rtspUri = InspStringUtils.replaceRTSP(rtsp, channel.getUsername(), channel.getPassword());
        Map<String, String> param = new HashMap<>();
        param.put("source", rtspUri);

        String sourceProtocol = channel.getSourceProtocol();
        if (StrUtil.isEmpty(sourceProtocol)) {
            sourceProtocol = MtxSourceProtocol.udp.name();
        }
        param.put("sourceProtocol", sourceProtocol);

        String streamName = channel.getId();
        String url = addStreamUrl + streamName;
        try (HttpResponse post = MyHttpUtil.post(url, param)) {
            if (isPushSuccess(post)) {
                return basePlayUrl + streamName;
            }
            int status = post.getStatus();
            String body = post.body();
            log.error("通道视频流播放地址请求出现异常，url = {}， param = {}，status = {}，result = {}", url,
                    JSONUtil.toJsonStr(param), status, body);
            throw new InspectionException("通道视频流播放地址请求失败");
        } catch (InspectionException e) {
            throw e;
        } catch (Exception e) {
            log.error("通道视频流播放地址请求出现异常，url = {}， param = {}", url, JSONUtil.toJsonStr(param), e);
            throw new InspectionException("通道视频流播放地址请求出现异常");
        }
    }

    public void deleteStream(InspChannelInfo channel) {
        String existStream = getExistStream(channel);
        if (StrUtil.isBlank(existStream)) {
            // 不存在流直接返回
            return;
        }
        deleteStreamByName(channel.getId());
    }

    public void deleteStreamByName(String streamName) {
        String url = delStreamUrl + streamName;
        try (HttpResponse delete = MyHttpUtil.delete(url, null)) {
            int status = delete.getStatus();
            if (HttpStatus.HTTP_OK != status) {
                log.error("踢除Mediamtx流失败，url = {}，status = {}， result = {}", url, status, delete.body());
            }
        }
    }

    /**
     * 同步 根据流名字删除流
     * @param streamName 流名字
     */
    public void asyncDeleteStreamByName(String streamName) {
        // 检查流是否存在于列表中，如果不存在则直接返回
        boolean streamExistsInitially = false;
        List<StreamModel> streams = listAllExistStream();
        for (StreamModel stream : streams) {
            if (streamName.equals(stream.getName())) {
                streamExistsInitially = true;
                break;
            }
        }
        if (!streamExistsInitially) {
            log.warn("流 {} 不存在于列表中，无需执行踢出操作。", streamName);
            return;
        }

        String url = delStreamUrl + streamName;
        try (HttpResponse delete = MyHttpUtil.delete(url, null)) {
            int status = delete.getStatus();
            if (HttpStatus.HTTP_OK != status) {
                log.error("同步踢除Mediamtx流失败，url = {}，status = {}， result = {}", url, status, delete.body());
            }
        }

        for (int i = 0; i < maxAttempts; i++) {
            boolean streamExists = false;
            List<StreamModel> currentStreams = listAllExistStream();

            for (StreamModel stream : currentStreams) {
                if (streamName.equals(stream.getName())) {
                    streamExists = true;
                    break;
                }
            }

            if (!streamExists) {
                log.debug("流 {} 已成功踢除，共尝试 {} 次。", streamName, i + 1);
                return;
            }

            if (i < maxAttempts - 1) {
                log.debug("流 {} 仍在列表中，等待一秒后重试 (第 {} 次)。", streamName, i + 1);
                try {
                    Thread.sleep(delayMillis);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("等待踢流检查时线程被中断。", e);
                    break;
                }
            }
        }

        // 如果循环结束仍然没有删除成功
        log.warn("在 {} 次尝试后，流 {} 仍存在。", maxAttempts, streamName);
    }

    /**
     * 获取全部流信息
     */
    public List<StreamModel> listAllExistStream() {
        List<StreamModel> objects = new ArrayList<>();
        // 查询当前流是否已存在
        String stream = MyHttpUtil.get(listStreamUrl);
        if (JSONUtil.isTypeJSONObject(stream)) {
            JSONObject entries = JSONUtil.parseObj(stream);
            JSONArray items = entries.getJSONArray("items");
            if (CollectionUtil.isNotEmpty(items)) {
                for (Object item : items) {
                    JSONObject parseObj = JSONUtil.parseObj(item);
                    StreamModel bean = JSONUtil.toBean(parseObj, StreamModel.class);
                    String str = parseObj.getStr("readyTime");
                    if (StrUtil.isNotEmpty(str)) {
                        // 由于JSONUtil未找到自定义转化器，所以自己实现时间转化
                        ZonedDateTime zonedDateTime = ZonedDateTime.parse(str);
                        LocalDateTime localDateTime = zonedDateTime.toLocalDateTime(); // 效果相同
                        bean.setReadyTime(localDateTime);
                    }
                    objects.add(bean);
                }
            }
        }
        return objects;
    }

    /**
     * 检查指定key的流是否已存在并且处于就绪状态.
     *
     * @param streamKey 需要检查的流的名称
     * @return 如果流存在且就绪，则返回 true，否则返回 false
     */
    public boolean isStreamReady(String streamKey) {
        // 获取所有存在的流信息
        List<StreamModel> existStreams = listAllExistStream();

        for (StreamModel stream : existStreams) {
            // 检查流的名称是否匹配且ready字段为true
            if (streamKey.equals(stream.getName()) && stream.getReady()) {
                return true;
            }
        }

        return false;
    }

    @Scheduled(fixedDelay = 10, timeUnit = TimeUnit.SECONDS)
    public void delNoPlayStream() {
        log.debug("开始执行删除无用流逻辑...");
        try {
            List<StreamModel> streamModels = listAllExistStream();
            if (CollectionUtil.isEmpty(streamModels)) {
                return;
            }
            // 过滤客户端为空、推流时间为10秒之前的视频流
            streamModels = streamModels.stream()
                    .filter(item -> CollectionUtil.isEmpty(item.getReaders()))
                    .filter(item -> {
                        Boolean ready = item.getReady();
                        // 流未准备好 也删除
                        if (!BooleanUtil.isTrue(ready)) {
                            return true;
                        }
                        LocalDateTime readyTime = item.getReadyTime();
                        // 推流时间为空 也删除
                        if (Objects.isNull(readyTime)) {
                            return true;
                        }
                        LocalDateTime now = LocalDateTimeUtil.now();
                        return LocalDateTimeUtil.between(readyTime, now, ChronoUnit.MINUTES) > 1;
                    })
                    .collect(Collectors.toList());
            if (CollectionUtil.isEmpty(streamModels)) {
                return;
            }
            log.debug("删除无客户端流：{}", JSONUtil.toJsonStr(streamModels));
            for (StreamModel streamModel : streamModels) {
                deleteStreamByName(streamModel.getName());
            }
        } catch (Exception e) {
            log.error("执行删除无用流逻辑出现异常...", e);
        }
    }

    /**
     * 更新mtx的配置文件 修改webrtcAdditionalHosts字段
     * @param mediaIps 媒体服务器ip
     */
    public void updateWebrtcAdditionalHosts(List<String> mediaIps) {
        try {
            String fileContent = FileUtil.readUtf8String(mediamtxYmlFilePath);
            // 建新的配置行
            String hostsListString = mediaIps.stream()
                    .map(String::trim)
                    .collect(Collectors.joining(","));
            String newHostsLine = "webrtcAdditionalHosts: [" + hostsListString + "]";

            // 使用正则表达式匹配配置
            String regex = "(?m)^webrtcAdditionalHosts:.*$";
            Pattern pattern = Pattern.compile(regex, Pattern.MULTILINE);
            Matcher matcher = pattern.matcher(fileContent);

            String newContent = "";
            if (matcher.find()) {
                // 如果找到匹配的行，则用新内容替换掉
                newContent = matcher.replaceAll(newHostsLine);
            } else {
                throw new InspectionException("未找到mtx配置文件中的 webrtcAdditionalHosts 字段");
            }

            // 将修改后的内容写回文件
            Files.write(Paths.get(mediamtxYmlFilePath), newContent.getBytes(StandardCharsets.UTF_8));

            // 重启 Mediamtx 服务
            dockerService.restartService(containerName);
            log.info("成功更新 mediamtx.yml 配置文件");

        } catch (Exception e) {
            log.error("更新配置文件时发生错误:", e);
            throw new InspectionException("更新配置文件失败: " + e.getMessage());
        }
    }

    /**
     * 获取物理机网卡IP列表
     * @return IP列表
     */
    public List<String> getNetworkInterfaces() {
        Path path = Paths.get(ipFilePath);
        if (!Files.exists(path)) {
            log.error("文件未找到: {}", ipFilePath);
            return Collections.emptyList();
        }

        try {
            List<String> ips = Files.lines(path)
                    .map(String::trim)
                    .filter(ip -> IpUtil.isIpv4(ip) && !ip.equals("127.0.0.1"))
                    .collect(Collectors.toList());

            if (ips.isEmpty()) {
                log.error("文件内容为空或不包含有效的IPv4地址。");
            } else {
                log.debug("已成功从文件中读取到宿主机IP列表: {}", ips);
            }

            return ips;
        } catch (IOException e) {
            log.error("读取文件失败: {}", ipFilePath, e);
            return Collections.emptyList();
        }
    }

    /**
     * 将获取到的网口列表第一个ip设置到系统配置多网口字段中
     */
    public void setIpForMtx() {
        List<String> networkInterfaces = getNetworkInterfaces();
        if (!networkInterfaces.isEmpty()) {
            InspCommonConfig inspCommonConfig = new InspCommonConfig();
            List<String> mtxIps = new ArrayList<>();
            mtxIps.add(networkInterfaces.get(0));
            inspCommonConfig.setMtxIps(mtxIps);
            inspCommonConfigService.updateCommonConfig(inspCommonConfig);
        }
    }
}
