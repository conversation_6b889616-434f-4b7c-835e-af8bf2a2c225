package com.hollysys.inspection.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hollysys.inspection.config.annotations.OperateLog;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.constants.InspConstants;
import com.hollysys.inspection.constants.ProjectNodeType;
import com.hollysys.inspection.constants.operatelog.BusinessClassify;
import com.hollysys.inspection.constants.operatelog.OperateType;
import com.hollysys.inspection.constants.project.DeployState;
import com.hollysys.inspection.constants.project.OnlineState;
import com.hollysys.inspection.entity.InspChannelInfo;
import com.hollysys.inspection.entity.InspProject;
import com.hollysys.inspection.entity.InspProjectTreeNode;
import com.hollysys.inspection.mapper.InspProjectMapper;
import com.hollysys.inspection.model.algorithm.configure.SaveScheduleConfigReqModel;
import com.hollysys.inspection.model.mes.MesPresetTreeModel;
import com.hollysys.inspection.model.project.DeployOrOnlineReqModel;
import com.hollysys.inspection.model.project.ProjectTreeAndStateModel;
import com.hollysys.inspection.model.project.SaveAllInfoReqModel;
import com.hollysys.inspection.model.tree.InspProjectTreeNodeModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 算法信息表(InspProject)表服务接口
 *
 * <AUTHOR>
 * @since 2023-05-05 11:15:28
 */
@Slf4j
@Service
public class InspProjectService extends ServiceImpl<InspProjectMapper, InspProject> {

    @Resource
    private ProjectTreeNodeService projectTreeNodeService;

    @Resource
    private InspChannelInfoService inspChannelInfoService;

    @Resource
    private InspPresetInfoService presetInfoService;

    @Transactional
    public InspProject createProject() {
        InspProject inspProject = new InspProject();
        // 创建新工程
        inspProject.setId(null);
        inspProject.setDeployState(DeployState.DEPLOYED.name());
        inspProject.setOnlineState(OnlineState.ONLINE.name());
        save(inspProject);

        // 初始化工程树节点信息
        List<InspProjectTreeNode> treeNodeList = new ArrayList<>();

        // 业务父级根节点（视频分析）
        InspProjectTreeNode projectTreeNode = new InspProjectTreeNode();
        projectTreeNode.setParentId(InspConstants.BOOT_NODE_PARENT_ID);
        projectTreeNode.setLabel("视频分析");
        projectTreeNode.setType(ProjectNodeType.BUSINESS_ROOT);
        projectTreeNode.setSortNo(1L);
        treeNodeList.add(projectTreeNode);

        projectTreeNodeService.saveBatch(treeNodeList);
        return inspProject;
    }

    /**
     * 获取工程树节点和状态
     *
     * @return 工程树节点和状态
     */
    @Transactional
    public ProjectTreeAndStateModel getProjectTreeAndState() {
        InspProject project = getFirstOne();
        if (Objects.isNull(project)) {
            // 初始化工程数据
            project = createProject();
        }

        List<InspProjectTreeNodeModel> tree = projectTreeNodeService.getTree();

        ProjectTreeAndStateModel projectTreeAndStateModel = new ProjectTreeAndStateModel();
        projectTreeAndStateModel.setProject(project);
        projectTreeAndStateModel.setTree(tree);

        return projectTreeAndStateModel;
    }

    /**
     * 工程数据一键保存
     *
     * @param reqModel 请求实体
     */
    @Transactional
    public void saveAllInfo(SaveAllInfoReqModel reqModel) {
        InspProject projectBaseInfo = reqModel.getProjectBaseInfo();
        if (Objects.nonNull(projectBaseInfo)) {
            InspProject project = new InspProject();
            project.setId(projectBaseInfo.getId());
            this.updateById(project);
        }

        List<InspChannelInfo> channelConfigInfoList = reqModel.getChannelConfigInfoList();
        if (CollectionUtil.isNotEmpty(channelConfigInfoList)) {
            for (InspChannelInfo channelConfigInfo : channelConfigInfoList) {
                inspChannelInfoService.updateChannelInfo(channelConfigInfo);
            }
        }

        List<InspProjectTreeNode> channelBaseInfoList = reqModel.getChannelBaseInfoList();
        if (CollectionUtil.isNotEmpty(channelBaseInfoList)) {
            for (InspProjectTreeNode channelBaseInfo : channelBaseInfoList) {
                projectTreeNodeService.updateById(channelBaseInfo);
            }
        }

        List<InspProjectTreeNode> presetBaseInfoList = reqModel.getPresetBaseInfoList();
        if (CollectionUtil.isNotEmpty(presetBaseInfoList)) {
            for (InspProjectTreeNode presetBaseInfo : presetBaseInfoList) {
                projectTreeNodeService.updateById(presetBaseInfo);
            }
        }

        List<SaveScheduleConfigReqModel> scheduleConfigModelList = reqModel.getScheduleConfigModelList();
        if (CollectionUtil.isNotEmpty(scheduleConfigModelList)) {
            for (SaveScheduleConfigReqModel scheduleConfigModel : scheduleConfigModelList) {
                presetInfoService.saveScheduleConfig(scheduleConfigModel);
            }
        }
    }

    /**
     * 部署或停止工程
     *
     * @param reqModel 请求参数实体
     */
    @OperateLog(operateType = OperateType.UPDATE, businessClassify = BusinessClassify.PROJECT, message = "部署或停止工程")
    public void deployOrStop(DeployOrOnlineReqModel reqModel) {
        String opeCode = reqModel.getOpeCode();
        if (StrUtil.hasBlank(opeCode)) {
            throw new InspectionException("参数错误");
        }
        if (!EnumUtil.contains(DeployState.class, opeCode)) {
            throw new InspectionException("操作编码错误");
        }

        // 如果当前请求的目标操作状态与工程当前状态一致，则直接返回
        InspProject byId = getFirstOne();
        String deployState = byId.getDeployState();
        if (opeCode.equals(deployState)) {
            return;
        }

        // 操作成功后，更新工程状态
        byId.setDeployState(opeCode);
        updateById(byId);
    }

    /**
     * 在线或离线操作
     *
     * @param reqModel 请求参数实体
     */
    @OperateLog(operateType = OperateType.UPDATE, businessClassify = BusinessClassify.PROJECT, message = "在线或离线工程")
    public void onlineOrOffline(DeployOrOnlineReqModel reqModel) {
        String opeCode = reqModel.getOpeCode();
        if (StrUtil.hasBlank(opeCode)) {
            throw new InspectionException("参数错误");
        }
        if (!EnumUtil.contains(OnlineState.class, opeCode)) {
            throw new InspectionException("操作编码错误");
        }

        // 如果当前请求的目标操作状态与工程当前状态一致，则直接返回
        InspProject byId = getFirstOne();
        String onlineState = byId.getOnlineState();
        if (opeCode.equals(onlineState)) {
            return;
        }

        // if (OnlineState.ONLINE.name().equals(opeCode)) {
        //     // 上线操作  修改工程状态为在线
        // } else {
        //     // 离线操作  修改工程状态为离线
        // }
        // 操作成功后，更新工程状态
        byId.setOnlineState(opeCode);
        updateById(byId);
    }

    public InspProject getFirstOne() {
        List<InspProject> list = list();
        if (CollectionUtil.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    /**
     * MES树列表查询
     */
    public List<MesPresetTreeModel> mesTreeList() {
        List<String> nodeTypeList = Arrays.asList(ProjectNodeType.BUSINESS_ROOT,
                ProjectNodeType.CHANNEL,
                ProjectNodeType.DIRECTORY,
                ProjectNodeType.PRESET);
        LambdaQueryWrapper<InspProjectTreeNode> wrapper = new LambdaQueryWrapper<InspProjectTreeNode>()
                .in(InspProjectTreeNode::getType, nodeTypeList)
                .orderByAsc(InspProjectTreeNode::getSortNo);
        List<InspProjectTreeNode> list = projectTreeNodeService.list(wrapper);
        return list.stream().map(inspProjectTreeNode -> {
            MesPresetTreeModel dto = new MesPresetTreeModel();
            dto.setId(inspProjectTreeNode.getId());
            String type = inspProjectTreeNode.getType();
            if (ProjectNodeType.BUSINESS_ROOT.equals(type)) {
                dto.setParentId("");
            } else {
                dto.setParentId(inspProjectTreeNode.getParentId());
            }
            dto.setDescription(inspProjectTreeNode.getDes());
            dto.setNodeName(inspProjectTreeNode.getLabel());
            dto.setNodeType(type);
            return dto;
        }).collect(Collectors.toList());
    }
}

