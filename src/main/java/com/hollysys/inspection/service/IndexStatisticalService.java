package com.hollysys.inspection.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.hollysys.inspection.constants.InspConstants;
import com.hollysys.inspection.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class IndexStatisticalService {

    private static final int SCALE = 4;

    @Resource
    private InspScheduleTaskTreeNodeService scheduleTaskTreeNodeService;

    @Resource
    private InspScheduleTaskNodeService scheduleTaskNodeService;

    @Resource
    private InspRecordAlgorithmService recordAlgorithmService;

    @Resource
    private InspAlarmRecordService alarmRecordService;

    @Resource
    private InspAlgorithmInstanceService algorithmInstanceService;

    /**
     * 获取近几日告警记录
     */
    private List<InspAlarmRecord> getAlarmByDays(int days) {
        return alarmRecordService.lambdaQuery().gt(InspAlarmRecord::getUpdateTime,
                        LocalDate.now().minusDays(days - 1L).atStartOfDay())  // 大于等于几天前 00:00:00
                .lt(InspAlarmRecord::getUpdateTime, LocalDateTime.now())  // 小于当前时间
                .list();
    }

    /**
     * 获取近几日执行记录数量
     */
    private int getExeRecordCountByDays(int days) {
        return recordAlgorithmService
                .lambdaQuery()
                .gt(InspRecordAlgorithm::getStartTime,
                        LocalDate.now().minusDays(days - 1L).atStartOfDay())  // 大于等于几天前 00:00:00
                .lt(InspRecordAlgorithm::getStartTime, LocalDateTime.now())  // 小于明天 00:00:00
                .count();
    }

    /**
     * 累计报警记录
     */
    private List<InspAlarmRecord> getSumAlarmRecords() {
        return getAlarmByDays(7);
    }


    /**
     * 累计报警数量
     */
    public int getSumAlarmCount() {
        return getSumAlarmRecords().size();
    }

    /**
     * 累计巡检记录数量
     */
    public int getSumExeRecordCount() {
        return getExeRecordCountByDays(7);
    }

    /**
     * 今日告警数
     */
    public int getTodayAlarmCount() {
        return getAlarmByDays(1).size();
    }

    /**
     * 今日巡检记录数
     */
    public int getTodayExeRecordCount() {
        return getExeRecordCountByDays(1);
    }

    /**
     * 总巡航点数
     */
    public int insPointCount() {
        return scheduleTaskNodeService.count();
    }

    /**
     * 巡检异常分类统计
     */
    public Map<String, Integer> getAlarmRankByAlgorithm() {
        List<InspAlarmRecord> alarmRecords = getSumAlarmRecords();
        if (CollectionUtil.isEmpty(alarmRecords)) {
            return new HashMap<>();
        }
        // 获取算法实例ID和算法名称的Map
        List<String> algorithmInstanceIds = alarmRecords.stream().map(InspAlarmRecord::getAlgorithmInstanceId)
                .distinct().collect(Collectors.toList());
        List<InspAlgorithmInstance> algorithmInstances = algorithmInstanceService.listByIds(algorithmInstanceIds);
        if (CollectionUtil.isEmpty(algorithmInstances)) {
            return new HashMap<>();
        }
        Map<String, String> nameMap = new HashMap<>();
        for (InspAlgorithmInstance algorithmInstance : algorithmInstances) {
            nameMap.put(algorithmInstance.getId(), algorithmInstance.getName());
        }

        Map<String, Integer> alarmCountByClass = new HashMap<>();
        for (InspAlarmRecord alarmRecord : alarmRecords) {
            String algorithmName = nameMap.get(alarmRecord.getAlgorithmInstanceId());
            if (Objects.isNull(algorithmName)) {
                continue;
            }
            Integer num = alarmCountByClass.get(algorithmName);
            if (Objects.isNull(num)) {
                num = 0;
            }
            alarmCountByClass.put(algorithmName, num + 1);
        }
        return MapUtil.sort(alarmCountByClass);
    }

    /**
     * 今日巡检报警率
     * 今日报警数量 / 今日巡检次数
     */
    public float todayAlarmRate() {
        // 今日报警数量
        int todayAlarmCount = getTodayAlarmCount();
        // 今日巡检次数
        int count = getTodayExeRecordCount();
        if (todayAlarmCount == 0 || count == 0) {
            return 0;
        }
        return new BigDecimal(todayAlarmCount).divide(new BigDecimal(count), SCALE, RoundingMode.CEILING)
                .multiply(BigDecimal.valueOf(100)).floatValue();
    }

    /**
     * 总巡检报警率
     * 总报警数量 / 总巡检次数
     */
    public float totalAlarmRate() {
        // 总报警数量
        int accumulateAlarmCount = getSumAlarmCount();
        // 总巡检次数
        int allExecuteRecords = getSumExeRecordCount();
        if (accumulateAlarmCount == 0 || allExecuteRecords == 0) {
            return 0;
        }
        return new BigDecimal(accumulateAlarmCount).divide(new BigDecimal(allExecuteRecords), SCALE, RoundingMode.CEILING)
                .multiply(BigDecimal.valueOf(100)).floatValue();
    }

    /**
     * 巡检报警排名（按照任务树二级目录）
     */
    public Map<String, Integer> getAlarmRank() {
        // 查询一级目录节点
        List<InspScheduleTaskTreeNode> bootNodes = scheduleTaskTreeNodeService.lambdaQuery()
                .eq(InspScheduleTaskTreeNode::getParentId, InspConstants.BOOT_NODE_PARENT_ID).list();
        if (CollectionUtil.isEmpty(bootNodes)) {
            return new HashMap<>();
        }
        InspScheduleTaskTreeNode taskTreeNode = bootNodes.get(0);
        bootNodes = scheduleTaskTreeNodeService.lambdaQuery().eq(InspScheduleTaskTreeNode::getParentId, taskTreeNode.getId()).list();
        Map<String, Integer> rankMap = new HashMap<>();

        // 查询七日报警ID集合
        List<InspAlarmRecord> totalAlarmRecords = getSumAlarmRecords();
        if (CollectionUtil.isEmpty(totalAlarmRecords)) {
            return new HashMap<>();
        }
        List<String> alarmRecordIds = totalAlarmRecords.stream().map(InspAlarmRecord::getId).collect(Collectors.toList());
        for (InspScheduleTaskTreeNode bootNode : bootNodes) {
            // 按照一级目录节点分组，查询预置点ID
            List<InspScheduleTaskTreeNode> allSubNodesByParentId = scheduleTaskTreeNodeService.getAllSubNodesByParentId(bootNode.getId());
            if (CollectionUtil.isEmpty(bootNodes)) {
                continue;
            }
            List<String> taskIds = allSubNodesByParentId.stream().map(InspScheduleTaskTreeNode::getId).collect(Collectors.toList());
            // 根据任务ID查询全部预置点ID
            List<InspScheduleTaskNode> inspScheduleTaskNodes = scheduleTaskNodeService.listByTaskIds(taskIds);
            if (CollectionUtil.isEmpty(inspScheduleTaskNodes)) {
                continue;
            }
            List<String> presetIds = inspScheduleTaskNodes.stream().map(InspScheduleTaskNode::getPresetId).collect(Collectors.toList());
            // 根据预置点ID查询报警记录
            List<InspAlarmRecord> alarmRecords = alarmRecordService.lambdaQuery()
                    .in(InspAlarmRecord::getId, alarmRecordIds)
                    .in(InspAlarmRecord::getPresetId, presetIds).list();

            rankMap.put(bootNode.getLabel(), alarmRecords.size());
        }
        return MapUtil.sortByValue(rankMap, true);
    }

    public Map<String, Integer> sumAlarmByDay() {
        // 查询7日报警数据
        List<InspAlarmRecord> alarmRecords = getAlarmByDays(7);
        if (CollectionUtil.isEmpty(alarmRecords)) {
            return new HashMap<>();
        }

        Map<String, Integer> result = new HashMap<>();
        for (InspAlarmRecord alarmRecord : alarmRecords) {
            LocalDateTime updateTime = alarmRecord.getUpdateTime();
            String key = DateUtil.format(updateTime, "MM-dd");
            Integer num = result.get(key);
            if (Objects.isNull(num)) {
                result.put(key, 1);
            } else {
                result.put(key, num + 1);
            }
        }
        return MapUtil.sort(result);
    }

    /**
     * 获取巡检综合评分
     * 按照预置点扣分，一天内一个预置点如出现报警扣一分，最高100分，最低0分
     */
    public int getInspScore() {
        int maxScore = 100;
        // 查询当天全部报警记录
        List<InspAlarmRecord> todayAlarmList = getAlarmByDays(1);
        if (CollectionUtil.isEmpty(todayAlarmList)) {
            return maxScore;
        }
        long count = todayAlarmList.stream().map(InspAlarmRecord::getPresetId).distinct().count();
        int inspScore = (int) (maxScore - count);
        if (inspScore < 0) {
            inspScore = 0;
        }
        return inspScore;
    }
}
