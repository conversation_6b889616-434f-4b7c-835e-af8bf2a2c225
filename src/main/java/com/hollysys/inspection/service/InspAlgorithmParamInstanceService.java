package com.hollysys.inspection.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.EnumUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hollysys.inspection.constants.algorithm.param.DataType;
import com.hollysys.inspection.entity.InspAlgorithmParam;
import com.hollysys.inspection.entity.InspAlgorithmParamInstance;
import com.hollysys.inspection.mapper.InspAlgorithmParamInstanceMapper;
import com.hollysys.inspection.utils.AssertUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 算法参数定义表(InspAlgorithmParamInstance)表服务接口
 *
 * <AUTHOR>
 * @since 2023-03-01 15:47:35
 */
@Service
public class InspAlgorithmParamInstanceService extends ServiceImpl<InspAlgorithmParamInstanceMapper, InspAlgorithmParamInstance> {

    @Resource
    private InspAlgorithmParamService algorithmParamService;

    public InspAlgorithmParamInstance getOneById(String paramInstId) {
        LambdaQueryWrapper<InspAlgorithmParamInstance> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspAlgorithmParamInstance::getId, paramInstId);
        InspAlgorithmParamInstance one = getOne(wrapper);
        AssertUtil.isTrue(Objects.nonNull(one), "参数实例不存在");
        return one;
    }

    /**
     * 将参数值转化为对应格式
     */
    public void valueStrToObj(List<InspAlgorithmParamInstance> paramInstances) {
        if (CollectionUtil.isEmpty(paramInstances)) {
            return;
        }
        // 查询算法参数定义
        String algorithmId = paramInstances.get(0).getAlgorithmId();
        List<InspAlgorithmParam> algorithmInputParams = algorithmParamService.listInputByAlgorithmId(algorithmId);
        Map<String, InspAlgorithmParam> mapByKey = algorithmInputParams.stream()
                .collect(Collectors.toMap(InspAlgorithmParam::getKey, Function.identity()));
        for (InspAlgorithmParamInstance paramInstance : paramInstances) {
            Object value = paramInstance.getValue();
            if (Objects.isNull(value)) {
                continue;
            }

            InspAlgorithmParam algorithmParam = mapByKey.get(paramInstance.getKey());
            if (Objects.isNull(algorithmParam)) {
                continue;
            }
            String dataType = algorithmParam.getDataType();
            DataType dataTypeEnum = EnumUtil.fromString(DataType.class, dataType);
            Object object = dataTypeEnum.valueStrToObj(value);
            paramInstance.setValue(object);
        }
    }

    public List<InspAlgorithmParamInstance> listByInstanceIdAndSceneId(String algorithmInstanceId, String sceneId) {
        LambdaQueryWrapper<InspAlgorithmParamInstance> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspAlgorithmParamInstance::getAlgorithmInstanceId, algorithmInstanceId);
        wrapper.eq(InspAlgorithmParamInstance::getSceneId, sceneId);
        return this.list(wrapper);
    }

    public List<InspAlgorithmParamInstance> listByAlgorithmInstanceId(String algorithmInstanceId) {
        LambdaQueryWrapper<InspAlgorithmParamInstance> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspAlgorithmParamInstance::getAlgorithmInstanceId, algorithmInstanceId);
        return this.list(wrapper);
    }

    public List<InspAlgorithmParamInstance> listByAlgorithmInstanceIds(List<String> algorithmInstanceIds) {
        LambdaQueryWrapper<InspAlgorithmParamInstance> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(InspAlgorithmParamInstance::getAlgorithmInstanceId, algorithmInstanceIds);
        return this.list(wrapper);
    }

    @Transactional
    public void updateParamList(String sceneId, String algorithmId, String algorithmInstanceId,
                                List<InspAlgorithmParamInstance> paramList) {
        if (CollUtil.isEmpty(paramList)) {
            return;
        }

        // 参数值检查
        checkParamValue(algorithmId, paramList);

        // 先删除
        LambdaQueryWrapper<InspAlgorithmParamInstance> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspAlgorithmParamInstance::getAlgorithmInstanceId, algorithmInstanceId);
        wrapper.eq(InspAlgorithmParamInstance::getSceneId, sceneId);
        remove(wrapper);

        // 再新增
        paramList.forEach(item -> {
            item.setAlgorithmId(algorithmId);
            item.setAlgorithmInstanceId(algorithmInstanceId);
            item.setSceneId(sceneId);
        });
        saveOrUpdateBatch(paramList);
    }

    /**
     * 参数值检查
     */
    public void checkParamValue(String algorithmId, List<InspAlgorithmParamInstance> paramList) {
        // 查询参数定义 入参定义集合为空则不需要进行参数校验
        List<InspAlgorithmParam> algorithmInputParams = algorithmParamService.listInputByAlgorithmId(algorithmId);
        if (CollUtil.isEmpty(algorithmInputParams)) {
            return;
        }

        // 定义中参数数量不为空，则在此参数实例也不能为空
        AssertUtil.isTrue(CollectionUtil.isNotEmpty(paramList), "算法参数未配置");

        List<String> algorithmIds = paramList.stream()
                .map(InspAlgorithmParamInstance::getAlgorithmId)
                .filter(algorithmId::equals)
                .distinct()
                .collect(Collectors.toList());
        AssertUtil.isTrue(algorithmIds.size() == 1, "算法参数实例不属于同一个算法");

        Map<String, InspAlgorithmParamInstance> paramInstanceMap = paramList.stream()
                .collect(Collectors.toMap(InspAlgorithmParam::getKey, Function.identity()));
        for (InspAlgorithmParam paramDefine : algorithmInputParams) {
            String key = paramDefine.getKey();
            InspAlgorithmParamInstance paramInstance = paramInstanceMap.get(key);
            AssertUtil.isTrue(Objects.nonNull(paramInstance), "算法参数[{}]未配置", paramDefine.getLabel());

            Object value = paramInstance.getValue();
            // 设置默认值
            if (Objects.isNull(value)) {
                paramInstance.setValue(paramDefine.getDefaultValue());
            }
            // 算法参数值校验
            checkValueByConstraints(value, paramDefine);
        }
    }

    /**
     * 参数值校验
     */
    private void checkValueByConstraints(Object value, InspAlgorithmParam paramDefine) {
        // 按参数类型校验参数值
        String dataType = paramDefine.getDataType();
        AssertUtil.isTrue(EnumUtil.contains(DataType.class, dataType), "算法参数类型定义错误");
        DataType dataTypeEnum = EnumUtil.fromString(DataType.class, dataType);
        dataTypeEnum.checkValue(value, paramDefine.getConstraints());
    }

    public void deleteBySceneId(String sceneId) {
        LambdaQueryWrapper<InspAlgorithmParamInstance> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspAlgorithmParamInstance::getSceneId, sceneId);
        remove(wrapper);
    }

    public void deleteByByInstanceIdAndSceneId(String algorithmInstanceId, String sceneId) {
        LambdaQueryWrapper<InspAlgorithmParamInstance> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspAlgorithmParamInstance::getSceneId, sceneId);
        wrapper.eq(InspAlgorithmParamInstance::getAlgorithmInstanceId, algorithmInstanceId);
        remove(wrapper);
    }

    public List<InspAlgorithmParamInstance> listChannelPicParam() {
        return lambdaQuery().eq(InspAlgorithmParamInstance::getDataType, DataType.CHANNEL_PIC.name())
                .list();
    }

    public List<InspAlgorithmParamInstance> listChannelPicParam(String channelId) {
        return lambdaQuery().eq(InspAlgorithmParamInstance::getDataType, DataType.CHANNEL_PIC.name())
                .like(InspAlgorithmParamInstance::getValue, channelId)
                .list();
    }
}
