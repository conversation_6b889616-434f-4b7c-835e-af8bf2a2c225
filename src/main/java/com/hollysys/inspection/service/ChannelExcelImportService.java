package com.hollysys.inspection.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import com.hollysys.inspection.config.exceptions.ChannelExistException;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.constants.CameraType;
import com.hollysys.inspection.constants.ProjectNodeType;
import com.hollysys.inspection.constants.channel.CameraCtrlProtocol;
import com.hollysys.inspection.constants.channel.VideoProtocolType;
import com.hollysys.inspection.entity.InspChannelInfo;
import com.hollysys.inspection.entity.InspProjectTreeNode;
import com.hollysys.inspection.model.channel.ChannelExcelItem;
import com.hollysys.inspection.model.tree.UpdateNodeInfoReqModel;
import com.hollysys.inspection.utils.AssertUtil;
import com.hollysys.inspection.utils.IpUtil;
import com.hollysys.inspection.utils.RtspUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class ChannelExcelImportService {

    @Resource
    private InspChannelInfoService inspChannelInfoService;

    @Resource
    private ProjectTreeNodeService projectTreeNodeService;

    @Transactional
    public void saveChannelExcelItem(ChannelExcelItem excelItem, Boolean isOverwrite) {
        // 参数详细校验
        String address = excelItem.getAddress();
        AssertUtil.isTrue(IpUtil.isIpv4(address), "通道IP地址格式错误");
        AssertUtil.isTrue(IpUtil.isReachableIp(address), "通道IP地址连通性校验失败");

        // 插入多级区域节点数据
        List<String> areaNames = excelItem.getAreaNames();
        InspProjectTreeNode treeNode = saveAreaList(areaNames);

        // 插入通道节点数据
        InspProjectTreeNode channelTreeNode = saveChannelTreeNode(treeNode, excelItem, isOverwrite);
        if (Objects.isNull(channelTreeNode)) {
            // 增量导入  直接跳过
            throw new ChannelExistException("通道已存在，跳过导入");
        }

        // 修改通道信息
        InspChannelInfo inspChannelInfo = inspChannelInfoService.getOneById(channelTreeNode.getId());
        BeanUtils.copyProperties(excelItem, inspChannelInfo);

        String rtsp = inspChannelInfo.getRtsp();
        if (Objects.isNull(inspChannelInfo.getChannelNum()) && StrUtil.isNotEmpty(rtsp)) {
            int channelNum = RtspUtil.getChannelNum(rtsp);
            inspChannelInfo.setChannelNum(channelNum);
        }

        // 转化摄像机接入协议，由label转化为枚举名称
        CameraCtrlProtocol cameraCtrlProtocol = CameraCtrlProtocol.fromLabel(inspChannelInfo.getCameraCtrlProtocol());
        if (Objects.nonNull(cameraCtrlProtocol)) {
            inspChannelInfo.setCameraCtrlProtocol(cameraCtrlProtocol.name());
        }

        // 转化摄像机类型字段
        CameraType cameraType = CameraType.fromLabel(inspChannelInfo.getDeviceType());
        if (Objects.nonNull(cameraType)) {
            inspChannelInfo.setDeviceType(cameraType.name());
        }

        String videoProtocolType = inspChannelInfo.getVideoProtocolType();
        AssertUtil.isTrue(EnumUtil.contains(VideoProtocolType.class, videoProtocolType), "视频接入协议配置错误");
        inspChannelInfoService.updateChannelInfo(inspChannelInfo);
    }

    /**
     * 覆盖导入：遇到相同IP通道节点，按提交的数据更新该通道信息
     * 增量导入：遇到相同IP通道节点，会跳过导入操作，保留原通道的数据不变
     */
    private InspProjectTreeNode saveChannelTreeNode(InspProjectTreeNode treeNode, ChannelExcelItem excelItem, Boolean isOverwrite) {
        String channelName = excelItem.getChannelName();
        // 根据节点名称寻找通道信息
        InspProjectTreeNode oneByName = projectTreeNodeService.getOneByName(channelName);
        if (Objects.isNull(oneByName)) {
            // 通道不重复 插入通道节点数据
            InspProjectTreeNode channelInfo = new InspProjectTreeNode();
            channelInfo.setParentId(treeNode.getId());
            channelInfo.setType(ProjectNodeType.CHANNEL);
            channelInfo.setLabel(channelName);
            channelInfo.setDes(excelItem.getChannelDesc());

            return projectTreeNodeService.createProjectNode(channelInfo);
        } else {
            // IP重复
            if (BooleanUtil.isTrue(isOverwrite)) {
                // 覆盖导入
                // 修改节点信息
                UpdateNodeInfoReqModel updateNodeInfoReqModel = new UpdateNodeInfoReqModel();
                updateNodeInfoReqModel.setId(oneByName.getId());
                updateNodeInfoReqModel.setLabel(channelName);
                updateNodeInfoReqModel.setDes(excelItem.getChannelDesc());
                updateNodeInfoReqModel.setParentId(treeNode.getId());
                return projectTreeNodeService.updateProjectNode(updateNodeInfoReqModel);
            } else {
                // 增量导入  直接跳过
                return null;
            }
        }
    }

    private InspProjectTreeNode saveAreaList(List<String> areaNames) {
        // 获取视频分析根节点
        InspProjectTreeNode businessRootNode = projectTreeNodeService.getBusinessRootNode();

        if (CollectionUtil.isEmpty(areaNames)) {
            return businessRootNode;
        }

        InspProjectTreeNode parentNode = businessRootNode;
        for (String areaName : areaNames) {
            InspProjectTreeNode projectNode = projectTreeNodeService.getOneByName(areaName);
            // 目录节点不存在则创建
            if (Objects.isNull(projectNode)) {
                projectNode = new InspProjectTreeNode();
                projectNode.setLabel(areaName);
                projectNode.setParentId(parentNode.getId());
                projectNode.setType(ProjectNodeType.DIRECTORY);
                projectNode = projectTreeNodeService.createProjectNode(projectNode);
            } else if (!ProjectNodeType.DIRECTORY.equals(projectNode.getType())) {
                throw new InspectionException(StrUtil.format("区域节点[{}]名称与其他类型节点名称重复", areaName));
            }
            parentNode = projectNode;
        }
        return parentNode;
    }
}
