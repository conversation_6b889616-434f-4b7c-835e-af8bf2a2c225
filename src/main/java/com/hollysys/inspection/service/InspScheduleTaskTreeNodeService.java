package com.hollysys.inspection.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.constants.InspConstants;
import com.hollysys.inspection.constants.ProjectNodeType;
import com.hollysys.inspection.constants.TaskTreeNodeEnum;
import com.hollysys.inspection.entity.InspScheduleTask;
import com.hollysys.inspection.entity.InspScheduleTaskTreeNode;
import com.hollysys.inspection.mapper.InspScheduleTaskTreeNodeMapper;
import com.hollysys.inspection.model.tree.InspProjectTreeNodeModel;
import com.hollysys.inspection.utils.AssertUtil;
import com.hollysys.inspection.utils.tree.LambdaTreeHelper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * (InspScheduleTaskTreeNode)表服务接口
 *
 * <AUTHOR>
 * @since 2023-02-03 15:49:14
 */
@Service
public class InspScheduleTaskTreeNodeService extends ServiceImpl<InspScheduleTaskTreeNodeMapper, InspScheduleTaskTreeNode> {

    @Resource
    private InspScheduleTaskService scheduleTaskService;

    @Value("${tree-node.directory-recursive.depth:5}")
    private int depth;

    public InspScheduleTaskTreeNode getOneById(String nodeId) {
        InspScheduleTaskTreeNode one = lambdaQuery().eq(InspScheduleTaskTreeNode::getId, nodeId).one();
        AssertUtil.isTrue(Objects.nonNull(one), "调度任务节点信息不存在");
        return one;
    }

    private List<InspScheduleTaskTreeNode> listByParentId(String nodeParentId) {
        return lambdaQuery().eq(InspScheduleTaskTreeNode::getParentId, nodeParentId).list();
    }

    /**
     * 查询任务节点树结构
     */
    public Object getTaskTree() {
        List<InspScheduleTaskTreeNode> list = list();
        return LambdaTreeHelper.<InspScheduleTaskTreeNode, InspProjectTreeNodeModel, String>newInstance()
                .id(InspProjectTreeNodeModel::getId, InspScheduleTaskTreeNode::getId)
                .childrenKey(InspProjectTreeNodeModel::getChildren)
                .parentId(InspProjectTreeNodeModel::getParentId, InspScheduleTaskTreeNode::getParentId)
                .label(InspProjectTreeNodeModel::getLabel, InspScheduleTaskTreeNode::getLabel)
                .orderNo(InspProjectTreeNodeModel::getSortNo, item -> item.getCreateTime().getTime())
                .putExtra(InspProjectTreeNodeModel::getType, InspScheduleTaskTreeNode::getType)
                .buildTreeList(list, InspConstants.BOOT_NODE_PARENT_ID, InspProjectTreeNodeModel.class);
    }

    @Transactional
    public InspScheduleTaskTreeNode createNode(InspScheduleTaskTreeNode reqModel) {
        checkTaskTreeNode(reqModel, true);

        // 创建节点
        InspScheduleTaskTreeNode taskTreeNode = new InspScheduleTaskTreeNode();
        taskTreeNode.setLabel(reqModel.getLabel());
        taskTreeNode.setType(reqModel.getType());
        taskTreeNode.setParentId(reqModel.getParentId());
        taskTreeNode.setCreateTime(new Date());
        save(taskTreeNode);

        // 创建任务数据
        if (TaskTreeNodeEnum.TASK.name().equals(reqModel.getType())) {
            InspScheduleTask scheduleTask = new InspScheduleTask();
            scheduleTask.setId(taskTreeNode.getId());
            scheduleTask.setName(reqModel.getLabel());
            scheduleTaskService.createTask(scheduleTask);
        }

        return taskTreeNode;
    }

    @Transactional
    public Object updateNode(InspScheduleTaskTreeNode reqModel) {
        checkTaskTreeNode(reqModel, false);

        lambdaUpdate()
                .set(InspScheduleTaskTreeNode::getLabel, reqModel.getLabel())
                .set(InspScheduleTaskTreeNode::getParentId, reqModel.getParentId())
                .eq(InspScheduleTaskTreeNode::getId, reqModel.getId())
                .update();

        // 修改任务数据
        if (TaskTreeNodeEnum.TASK.name().equals(reqModel.getType())) {
            InspScheduleTask scheduleTask = new InspScheduleTask();
            scheduleTask.setId(reqModel.getId());
            scheduleTask.setName(reqModel.getLabel());
            scheduleTaskService.updateTask(scheduleTask);
        }
        return reqModel;
    }

    private void checkTaskTreeNode(InspScheduleTaskTreeNode reqModel, boolean isCreate) {
        String label = reqModel.getLabel();
        AssertUtil.isTrue(StrUtil.isNotBlank(label), "节点名称不允许为空");
        if (isCreate) {
            InspScheduleTaskTreeNode one = lambdaQuery()
                    .eq(InspScheduleTaskTreeNode::getLabel, label)
                    .one();
            AssertUtil.isTrue(Objects.isNull(one), "节点名称已存在");
        } else {
            String modelId = reqModel.getId();
            AssertUtil.isTrue(StrUtil.isNotBlank(modelId), "节点ID不允许为空");

            InspScheduleTaskTreeNode one = lambdaQuery()
                    .eq(InspScheduleTaskTreeNode::getLabel, label)
                    .ne(InspScheduleTaskTreeNode::getId, modelId)
                    .one();
            AssertUtil.isTrue(Objects.isNull(one), "节点名称已存在");
        }

        String type = reqModel.getType();
        AssertUtil.isTrue(EnumUtil.contains(TaskTreeNodeEnum.class, type), "节点类型不正确");
        String parentId = reqModel.getParentId();
        if (StrUtil.isNotBlank(parentId)) {
            AssertUtil.isFalse(parentId.equals(reqModel.getId()), "父级节点不能是当前节点");
            InspScheduleTaskTreeNode oneById = lambdaQuery().eq(InspScheduleTaskTreeNode::getId, parentId).one();
            AssertUtil.isTrue(Objects.nonNull(oneById), "父级节点不存在");

            String oneByIdType = oneById.getType();
            AssertUtil.isTrue(EnumUtil.equals(TaskTreeNodeEnum.DIRECTORY, oneByIdType), "非目录节点不允许添加子节点");
        } else {
            reqModel.setParentId(InspConstants.BOOT_NODE_PARENT_ID);
        }

        // 校验目录节点深度
        if (TaskTreeNodeEnum.DIRECTORY.name().equals(reqModel.getType())) {
            // 获取父节点的深度
            int parentDepth = this.getBaseMapper().selectDirectoryTypeNodeDepth(reqModel.getParentId()).size();

            // 校验新节点的深度是否超过限制
            if (parentDepth >= this.depth) {
                throw new InspectionException(String.format("树节点嵌套深度不能超过%s层", this.depth));
            }
        }
    }

    @Transactional
    public Object removeTaskNode(String nodeId) {
        // 删除节点为目录结构，则需要递归删除包含目录下的全部子节点
        List<InspScheduleTaskTreeNode> subNodeList = getAllSubNodesByParentId(nodeId);

        List<String> ids = subNodeList.stream().map(InspScheduleTaskTreeNode::getId).distinct().collect(Collectors.toList());
        removeByIds(ids);
        scheduleTaskService.removeTask(ids);
        return true;
    }

    /**
     * 查询当前任务所在分组（判断任务所属的父二级目录是否为同一个，是则属于一组）
     */
    public List<String> getGroupTaskIdsByTaskId(String taskId) {
        // 查询一级目录节点
        List<InspScheduleTaskTreeNode> bootNodes = lambdaQuery()
                .eq(InspScheduleTaskTreeNode::getParentId, InspConstants.BOOT_NODE_PARENT_ID).list();
        if (CollectionUtil.isEmpty(bootNodes)) {
            return new ArrayList<>();
        }
        // 默认认为只有一个一级目录，查询二级目录节点
        InspScheduleTaskTreeNode taskTreeNode = bootNodes.get(0);
        bootNodes = lambdaQuery().eq(InspScheduleTaskTreeNode::getParentId, taskTreeNode.getId()).list();

        // 遍历每一个二级父节点，查询此节点下全部子节点
        for (InspScheduleTaskTreeNode bootNode : bootNodes) {
            List<InspScheduleTaskTreeNode> allSubNodesByParentId = getAllSubNodesByParentId(bootNode.getId());
            if (CollectionUtil.isEmpty(allSubNodesByParentId)) {
                continue;
            }
            List<String> taskIds = allSubNodesByParentId.stream()
                    .map(InspScheduleTaskTreeNode::getId).distinct().collect(Collectors.toList());
            if (taskIds.contains(taskId)) {
                return taskIds;
            }
        }
        return new ArrayList<>();
    }

    /**
     * 递归查询父节点下全部子节点（包含查询父级节点）
     */
    public List<InspScheduleTaskTreeNode> getAllSubNodesByParentId(String parentId) {
        List<InspScheduleTaskTreeNode> resultList = new ArrayList<>();
        InspScheduleTaskTreeNode treeNode = getOneById(parentId);
        resultList.add(treeNode);

        loopByParentId(parentId, resultList);
        return resultList;
    }

    private void loopByParentId(String parentId, List<InspScheduleTaskTreeNode> resultList) {
        List<InspScheduleTaskTreeNode> listByParentId = listByParentId(parentId);
        if (CollectionUtil.isNotEmpty(listByParentId)) {
            resultList.addAll(listByParentId);
            for (InspScheduleTaskTreeNode treeNode : listByParentId) {
                loopByParentId(treeNode.getId(), resultList);
            }
        }
    }
}

