package com.hollysys.inspection.service.platform;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.config.sso.HsmAuthProperties;
import com.hollysys.inspection.model.dbbase.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 数据底座接口服务
 **/
@Slf4j
@Service
public class DbBaseApiService {

    @Resource
    private RestTemplate restTemplate;

    // 1010105
    private static final int unauthenticatedCode = 1010105;

    private String authorization;

    @Resource
    private HsmAuthProperties hsmAuthProperties;

    /**
     * 数据底座写点
     */
    public DbBaseWritePointRespModel writePoints(List<DbBaseWritePointReqModel.WriteDataValue> data) {
        DbBaseWritePointReqModel dbBaseWritePointReqModel = new DbBaseWritePointReqModel();
        dbBaseWritePointReqModel.setData(data);
        DbBaseWritePointRespModel dbBaseWritePointRespModel = postForEntity(hsmAuthProperties.getRealSrvWritePointsUrl(), dbBaseWritePointReqModel, DbBaseWritePointRespModel.class);
        log.info("向平台回写点项值完成，url = {}，param = {}，result = {}", hsmAuthProperties.getRealSrvWritePointsUrl(),
                JSONUtil.toJsonStr(dbBaseWritePointReqModel),
                JSONUtil.toJsonStr(dbBaseWritePointRespModel));
        return dbBaseWritePointRespModel;
    }

    /**
     * 数据底座批量读取点项值
     */
    public DbBaseReadPointRespModel readPoints(List<String> nodeIds) {
        DbBaseReadPointReqModel dbBaseReadPointReqModel = new DbBaseReadPointReqModel();
        // 去重
        List<String> collect = nodeIds.stream().distinct().collect(Collectors.toList());
        dbBaseReadPointReqModel.setNodeIds(collect);
        return postForEntity(hsmAuthProperties.getRealSrvFindPointsUrl(), dbBaseReadPointReqModel, DbBaseReadPointRespModel.class);
    }

    /**
     * 数据底座读取单个点项值
     */
    public DbBaseReadPointRespModel.ReadDataValue readPoint(String nodeId) {
        Map<String, DbBaseReadPointRespModel.ReadDataValue> stringListMap = readPointsToMap(ListUtil.of(nodeId));
        return stringListMap.get(nodeId);
    }

    /**
     * 数据底座批量读取点项值，并将结果转化为map结构，key为nodeId，value为ReadDataValue
     */
    public Map<String, DbBaseReadPointRespModel.ReadDataValue> readPointsToMap(List<String> nodeIds) {
        DbBaseReadPointRespModel dbBaseReadPointRespModel = readPoints(nodeIds);
        if (!DbBaseRespModel.isSuccess(dbBaseReadPointRespModel)) {
            log.error("readPointsToMap 返回结果为失败，param = {}，result = {}"
                    , JSONUtil.toJsonStr(nodeIds), JSONUtil.toJsonStr(dbBaseReadPointRespModel));
            return new HashMap<>();
        }
        List<DbBaseReadPointRespModel.ReadDataValue> data = dbBaseReadPointRespModel.getData();
        if (CollectionUtil.isNotEmpty(data)) {
            return data.stream()
                    .collect(Collectors.toMap(DbBaseReadPointRespModel.ReadDataValue::getNodeId, Function.identity()));
        }
        return new HashMap<>();
    }

    /**
     * 数据底座读取点项值
     */
    public String getToken(String clientId, String secret) {
        DbBaseGetTokenReqModel reqModel = new DbBaseGetTokenReqModel();
        reqModel.setCode(clientId);
        reqModel.setSecret(secret);
        DbBaseGetTokenRespModel dbBaseGetTokenRespModel = doPostForEntity(hsmAuthProperties.getRealSrvGetTokenUrl(), reqModel, DbBaseGetTokenRespModel.class);
        if (!DbBaseRespModel.isSuccess(dbBaseGetTokenRespModel)) {
            throw new InspectionException("获取数据底座token失败, 请检查客户端ID和密钥");
        }
        return dbBaseGetTokenRespModel.getData();
    }

    private <P extends DbBaseRespModel> P postForEntity(String url, Object param, Class<P> returnClass) {
        if (StrUtil.isEmpty(authorization)) {
            authorization = getToken(hsmAuthProperties.getClientId(), hsmAuthProperties.getSecret());
        }
        P response = doPostForEntity(url, param, returnClass);
        if (unauthenticatedCode == response.getCode()) {
            // 如果未登录则登录一次
            authorization = getToken(hsmAuthProperties.getClientId(), hsmAuthProperties.getSecret());
            return doPostForEntity(url, param, returnClass);
        } else {
            return response;
        }
    }

    public <P extends DbBaseRespModel> P doPostForEntity(String url, Object param, Class<P> returnClass) {
        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.setContentType(MediaType.APPLICATION_JSON);
        requestHeaders.set(HttpHeaders.AUTHORIZATION, authorization);
        HttpEntity<Object> requestEntity = new HttpEntity<>(param, requestHeaders);
        ResponseEntity<P> rResponseEntity;
        try {
            rResponseEntity = restTemplate.postForEntity(url, requestEntity, returnClass);
        } catch (RestClientException exception) {
            log.error("DbBase远程服务访问异常，url = {}，param = {}", url, JSONUtil.toJsonStr(param), exception);
            throw new InspectionException("DbBase远程服务访问异常");
        }
        P entityBody = rResponseEntity.getBody();
        if (!DbBaseRespModel.isSuccess(entityBody)) {
            log.error("调用数据底座接口返回结果为失败，url = {}，reqModel = {}，respModel = {}", url, JSONUtil.toJsonStr(param),
                    JSONUtil.toJsonStr(entityBody));
        }
        return entityBody;
    }
}
