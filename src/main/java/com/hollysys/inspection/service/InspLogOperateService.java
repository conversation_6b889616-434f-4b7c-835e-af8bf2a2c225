package com.hollysys.inspection.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrFormatter;
import cn.hutool.core.text.csv.CsvWriter;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hollysys.inspection.constants.operatelog.BusinessClassify;
import com.hollysys.inspection.constants.operatelog.OperateType;
import com.hollysys.inspection.entity.InspChannelInfo;
import com.hollysys.inspection.entity.InspLogOperate;
import com.hollysys.inspection.entity.InspProjectTreeNode;
import com.hollysys.inspection.mapper.InspLogOperateMapper;
import com.hollysys.inspection.model.operatelog.OperateLogPageReqModel;
import com.hollysys.inspection.model.operatelog.OperateLogPageRespModel;
import com.hollysys.inspection.utils.FileNameUtil;
import com.hollysys.inspection.utils.LocalFileServerUtil;
import com.hollysys.inspection.utils.StrEscapeUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * (InspLogOperate)表服务接口
 *
 * <AUTHOR>
 * @since 2023-05-19 09:39:35
 */
@Service
public class InspLogOperateService extends ServiceImpl<InspLogOperateMapper, InspLogOperate> {

    /**
     * 按照默认规则拼接操作日志消息，并返回操作id集合
     *
     * @param message          注解中的消息体（前缀）
     * @param operateType      操作类型
     * @param businessClassify 业务类型
     * @param returnObj        方法返回的对象
     * @return InspLogOperate
     */
    public InspLogOperate buildMessageAndOperateIds(String message, OperateType operateType,
                                                    BusinessClassify businessClassify, Object returnObj) {
        if (StrUtil.isBlank(message)) {
            // message为空，则按照默认规则拼接
            String operateTypeDesc = operateType.getDesc();
            String businessClassifyDesc = businessClassify.getDesc();

            String messageTemp = "{}{}";
            message = StrFormatter.format(messageTemp, operateTypeDesc, businessClassifyDesc);
        }
        // 默认状态
        InspLogOperate operate = new InspLogOperate();
        operate.setMessage(message);
        operate.setOperateIds(new ArrayList<>());

        if (Objects.nonNull(returnObj)) {
            String objJsonStr = JSON.toJSONString(returnObj);
            if (JSONUtil.isTypeJSON(objJsonStr)) {
                // 统一转化为数组处理
                if (JSONUtil.isTypeJSONObject(objJsonStr)) {
                    List<Object> objects = ListUtil.of(returnObj);
                    objJsonStr = JSON.toJSONString(objects);
                }
                operate = buildByBusinessClassify(businessClassify, objJsonStr, message);
            }
        }
        return operate;
    }

    private InspLogOperate buildByBusinessClassify(BusinessClassify businessClassify, String objJsonStr, String message) {
        List<String> operateIds = new ArrayList<>();
        switch (businessClassify) {
            // case PROJECT:
            //     List<InspProject> projects = JSON.parseArray(objJsonStr, InspProject.class);
            //     String projectNames = projects.stream().map(InspProject::getName).filter(StrUtil::isNotBlank).collect(Collectors.joining("，"));
            //     operateIds = projects.stream().map(InspProject::getId).filter(StrUtil::isNotBlank).collect(Collectors.toList());
            //     if (StrUtil.isBlank(projectNames)) {
            //         break;
            //     }
            //     message += StrFormatter.format("，工程名称为[{}]", projectNames);
            //     break;
            case PROJECT_NODE:
                List<InspProjectTreeNode> treeNodes = JSON.parseArray(objJsonStr, InspProjectTreeNode.class);
                String nodeNames = treeNodes.stream().map(InspProjectTreeNode::getLabel).filter(StrUtil::isNotBlank).collect(Collectors.joining("，"));
                operateIds = treeNodes.stream().map(InspProjectTreeNode::getId).filter(StrUtil::isNotBlank).collect(Collectors.toList());
                if (StrUtil.isBlank(nodeNames)) {
                    break;
                }
                message += StrFormatter.format("，节点名称为[{}]", nodeNames);
                break;
            case CHANNEL:
                List<InspChannelInfo> channels = JSON.parseArray(objJsonStr, InspChannelInfo.class);
                String channelIps = channels.stream().map(InspChannelInfo::getAddress).filter(StrUtil::isNotBlank).collect(Collectors.joining("，"));
                operateIds = channels.stream().map(InspChannelInfo::getId).filter(StrUtil::isNotBlank).collect(Collectors.toList());
                if (StrUtil.isBlank(channelIps)) {
                    break;
                }
                message += StrFormatter.format("，通道IP为[{}]", channelIps);
                break;
            // case PRESET:
            //     break;
            // case ALGORITHM:
            //     break;
            // case NTP:
            //     break;
            default:
        }
        InspLogOperate operate = new InspLogOperate();
        operate.setMessage(message);
        operate.setOperateIds(operateIds);
        return operate;
    }

    public void log(String userName, OperateType operateType, BusinessClassify businessClassify, InspLogOperate operateObj) {
        InspLogOperate inspLogOperate = new InspLogOperate();

        inspLogOperate.setUserName(userName);
        inspLogOperate.setOperateType(operateType.name());
        inspLogOperate.setBusinessType(businessClassify.name());
        inspLogOperate.setOperateIds(operateObj.getOperateIds());
        inspLogOperate.setTime(LocalDateTime.now());
        inspLogOperate.setMessage(operateObj.getMessage());

        save(inspLogOperate);
    }

    public Object listPage(Page<InspLogOperate> page, OperateLogPageReqModel reqModel) {
        LambdaQueryWrapper<InspLogOperate> wrapper = new LambdaQueryWrapper<>();
        String startTime = reqModel.getStartTime();
        if (StrUtil.isNotBlank(startTime)) {
            DateTime start = DateUtil.parse(startTime, DatePattern.NORM_DATETIME_PATTERN);
            wrapper.gt(InspLogOperate::getTime, start);
        }

        String endTime = reqModel.getEndTime();
        if (StrUtil.isNotBlank(endTime)) {
            DateTime end = DateUtil.parse(endTime, DatePattern.NORM_DATETIME_PATTERN);
            wrapper.lt(InspLogOperate::getTime, end);
        }

        String searchText = reqModel.getSearchText();
        searchText = StrEscapeUtil.escapeChar(searchText);
        if (StrUtil.isNotBlank(searchText)) {
            wrapper.apply("message ILIKE CONCAT('%', {0}, '%')", searchText);
        }

        wrapper.orderByDesc(InspLogOperate::getTime);
        IPage<InspLogOperate> pageResult = page(page, wrapper);
        List<InspLogOperate> records = pageResult.getRecords();
        if (CollUtil.isEmpty(records)) {
            return pageResult;
        }

        List<OperateLogPageRespModel> logPageRespModels = records.stream().map(item -> {
            OperateLogPageRespModel pageModel = new OperateLogPageRespModel();
            BeanUtils.copyProperties(item, pageModel);
            // 操作类型
            String operateType = pageModel.getOperateType();
            if (EnumUtil.contains(OperateType.class, operateType)) {
                OperateType operateTypeEnum = EnumUtil.fromString(OperateType.class, operateType);
                pageModel.setOperateType(operateTypeEnum.getDesc());
            }

            // 业务划分
            String businessType = pageModel.getBusinessType();
            if (EnumUtil.contains(BusinessClassify.class, businessType)) {
                BusinessClassify businessTypeEnum = EnumUtil.fromString(BusinessClassify.class, businessType);
                pageModel.setBusinessType(businessTypeEnum.getDesc());
            }

            // 操作人用户名称
            pageModel.setUserName(pageModel.getUserName());

            // 操作时间
            String format = DateUtil.format(pageModel.getTime(), DatePattern.NORM_DATETIME_PATTERN);
            pageModel.setTimeStr(format);

            return pageModel;
        }).collect(Collectors.toList());

        Page<OperateLogPageRespModel> resultPage = new Page<>();
        BeanUtils.copyProperties(pageResult, resultPage);
        resultPage.setRecords(logPageRespModels);
        return resultPage;
    }

    /**
     * 备份操作日志
     *
     * @param recordLogs 操作日志记录
     * @return 备份文件
     */
    public File backup(List<InspLogOperate> recordLogs) {
        List<String[]> rows = new ArrayList<>();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

        // 添加 操作记录 CSV 文件头
        String[] headers = InspLogOperate.generateCsvHeader();
        rows.add(headers);

        // 添加操作记录
        for (InspLogOperate recordLog : recordLogs) {
            List<String> row = new ArrayList<>();
            JSONObject logEntries = JSONUtil.parseObj(recordLog);
            for (String key : headers) {
                Object object = logEntries.get(key);
                if (object != null) {
                    if (object instanceof LocalDateTime) {
                        row.add(((LocalDateTime) object).format(dateTimeFormatter));
                    } else {
                        row.add(JSONUtil.toJsonStr(object));
                    }
                } else {
                    row.add("");
                }
            }
            rows.add(row.toArray(new String[0]));
        }

        // 生成备份文件名 时间
        InspLogOperate recordFirst = recordLogs.get(0);
        InspLogOperate recordLast = recordLogs.get(recordLogs.size() - 1);
        String fileName = FileNameUtil.getBackFileName(recordFirst.getTime(), recordLast.getTime());

        // 操作日志记录文件
        File tempOperationLogFile = LocalFileServerUtil.getTempFile(fileName);

        // 写入csv文件
        try (CsvWriter writer = new CsvWriter(tempOperationLogFile, CharsetUtil.CHARSET_UTF_8)) {
            for (String[] row : rows) {
                writer.write(row);
            }
        }

        // 删除操作日志记录数据
        List<String> ids = recordLogs.stream().map(InspLogOperate::getId).collect(Collectors.toList());
        removeByIds(ids);

        return tempOperationLogFile;
    }
}

