package com.hollysys.inspection.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hollysys.inspection.config.annotations.OperateLog;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.constants.CameraType;
import com.hollysys.inspection.constants.InspConstants;
import com.hollysys.inspection.constants.ProjectNodeType;
import com.hollysys.inspection.constants.channel.MtxSourceProtocol;
import com.hollysys.inspection.constants.channel.VideoProtocolType;
import com.hollysys.inspection.constants.node.ChannelStatus;
import com.hollysys.inspection.constants.operatelog.BusinessClassify;
import com.hollysys.inspection.constants.operatelog.OperateType;
import com.hollysys.inspection.constants.project.OnlineState;
import com.hollysys.inspection.entity.*;
import com.hollysys.inspection.mapper.ProjectTreeNodeMapper;
import com.hollysys.inspection.model.BatchAreaModel;
import com.hollysys.inspection.model.tree.InspProjectTreeNodeModel;
import com.hollysys.inspection.model.tree.UpdateNodeInfoReqModel;
import com.hollysys.inspection.service.protocol.impl.CameraCtrlProxyService;
import com.hollysys.inspection.utils.AssertUtil;
import com.hollysys.inspection.utils.IpUtil;
import com.hollysys.inspection.utils.UUIDUtil;
import com.hollysys.inspection.utils.tree.LambdaTreeHelper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * (ProjectTreeNode)表服务接口
 *
 * <AUTHOR>
 * @since 2023-02-01 15:30:44
 */
@Service
public class ProjectTreeNodeService extends ServiceImpl<ProjectTreeNodeMapper, InspProjectTreeNode> {

    @Resource
    private InspProjectService projectService;

    @Resource
    private InspChannelInfoService inspChannelInfoService;

    @Resource
    private InspPresetInfoService presetInfoService;

    @Resource
    private CameraCtrlProxyService cameraCtrlProxyService;

    @Resource
    private InspAlgorithmParamInstanceService algorithmParamInstanceService;

    @Value("${tree-node.directory-recursive.depth:5}")
    private int depth;
    @Resource
    private InspAlarmRecordService alarmRecordService;

    @Resource
    private InspRecordPresetService recordPresetService;

    @Resource
    private InspRecordAlgorithmService recordAlgorithmService;

    @Resource
    private InspSceneCustomPicService inspSceneCustomPicService;

    public List<InspProjectTreeNodeModel> getTree() {
        List<InspProjectTreeNode> list = list();
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }

        InspProject byId = projectService.getFirstOne();
        if (!OnlineState.ONLINE.name().equals(byId.getOnlineState())) {
            // 如果当前工程为离线状态，则前端不展示节点状态
            list.forEach(item -> item.setStatus(null));
        }

        // 获取通道节点的相机类型
        List<InspChannelInfo> channelNodes = inspChannelInfoService.list();
        Map<String, InspChannelInfo> channelNodeMap = channelNodes.stream().collect(Collectors.toMap(InspChannelInfo::getId, item -> item));
        Map<String, String> map = list.stream()
                .filter(item -> ProjectNodeType.CHANNEL.equals(item.getType()))
                .map(item -> {
                    InspProjectTreeNodeModel model = new InspProjectTreeNodeModel();
                    InspChannelInfo channelInfo = channelNodeMap.get(item.getId());
                    if (Objects.isNull(channelInfo)) {
                        return null;
                    }
                    model.setId(channelInfo.getId());
                    model.setDeviceType(channelInfo.getDeviceType());
                    return model;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(InspProjectTreeNodeModel::getId, InspProjectTreeNodeModel::getDeviceType));

        return LambdaTreeHelper.<InspProjectTreeNode, InspProjectTreeNodeModel, String>newInstance()
                .id(InspProjectTreeNodeModel::getId, InspProjectTreeNode::getId)
                .childrenKey(InspProjectTreeNodeModel::getChildren)
                .parentId(InspProjectTreeNodeModel::getParentId, InspProjectTreeNode::getParentId)
                .label(InspProjectTreeNodeModel::getLabel, InspProjectTreeNode::getLabel)
                .orderNo(InspProjectTreeNodeModel::getSortNo, InspProjectTreeNode::getSortNo)
                .putExtra(InspProjectTreeNodeModel::getType, InspProjectTreeNode::getType)
                .putExtra(InspProjectTreeNodeModel::getStatus, InspProjectTreeNode::getStatus)
                .putExtra(InspProjectTreeNodeModel::getDes, InspProjectTreeNode::getDes)
                .putExtra(InspProjectTreeNodeModel::getUrl, InspProjectTreeNode::getUrl)
                .putExtra(InspProjectTreeNodeModel::getDeviceType, item -> map.get(item.getId()))
                .buildTreeList(list, InspConstants.BOOT_NODE_PARENT_ID, InspProjectTreeNodeModel.class);
    }

    /**
     * 获取通道数树结构  以BUSINESS_ROOT为根节点
     */
    public List<InspProjectTreeNodeModel> getChannelTree() {
        List<String> types = ListUtil.of(ProjectNodeType.CHANNEL, ProjectNodeType.BUSINESS_ROOT, ProjectNodeType.DIRECTORY);
        List<InspProjectTreeNode> treeNodes = lambdaQuery()
                .in(InspProjectTreeNode::getType, types)
                .list();
        if (CollUtil.isEmpty(treeNodes)) {
            return new ArrayList<>();
        }
        return LambdaTreeHelper.<InspProjectTreeNode, InspProjectTreeNodeModel, String>newInstance()
                .id(InspProjectTreeNodeModel::getId, InspProjectTreeNode::getId)
                .childrenKey(InspProjectTreeNodeModel::getChildren)
                .parentId(InspProjectTreeNodeModel::getParentId, InspProjectTreeNode::getParentId)
                .label(InspProjectTreeNodeModel::getLabel, InspProjectTreeNode::getLabel)
                .orderNo(InspProjectTreeNodeModel::getSortNo, InspProjectTreeNode::getSortNo)
                .buildTreeList(treeNodes, InspConstants.BOOT_NODE_PARENT_ID, InspProjectTreeNodeModel.class);
    }

    public InspProjectTreeNode getOneById(String nodeId) {
        InspProjectTreeNode projectTreeNode = getById(nodeId);
        if (Objects.isNull(projectTreeNode)) {
            throw new InspectionException("树节点信息不存在");
        }
        return projectTreeNode;
    }

    public List<InspProjectTreeNode> listByParentId(String parentId) {
        LambdaQueryWrapper<InspProjectTreeNode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InspProjectTreeNode::getParentId, parentId);
        return list(queryWrapper);
    }

    /**
     * 递归查询父节点下全部子节点（包含查询父级节点）
     */
    public List<InspProjectTreeNode> getAllSubNodesByParentId(String parentId) {
        List<InspProjectTreeNode> resultList = new ArrayList<>();
        InspProjectTreeNode treeNode = getOneById(parentId);
        resultList.add(treeNode);

        loopByParentId(parentId, resultList);
        return resultList;
    }

    public void loopByParentId(String parentId, List<InspProjectTreeNode> resultList) {
        List<InspProjectTreeNode> listByParentId = listByParentId(parentId);
        if (CollectionUtil.isNotEmpty(listByParentId)) {
            resultList.addAll(listByParentId);
            for (InspProjectTreeNode treeNode : listByParentId) {
                loopByParentId(treeNode.getId(), resultList);
            }
        }
    }

    @OperateLog(operateType = OperateType.UPDATE, businessClassify = BusinessClassify.PROJECT_NODE)
    @Transactional
    public InspProjectTreeNode updateProjectNode(UpdateNodeInfoReqModel projectTreeNode) {
        String treeNodeId = projectTreeNode.getId();
        String label = checkId(projectTreeNode, treeNodeId);

        InspProjectTreeNode treeNode = getOneById(treeNodeId);
        String type = treeNode.getType();
        if (ProjectNodeType.BUSINESS_ROOT.equals(type)) {
            throw new InspectionException("当前节点信息不允许修改");
        }

        if (!label.equals(treeNode.getLabel())) {
            // 名称被修改，名称重复交验
            checkNodeName(label);
            treeNode.setLabel(label);
        }

        String nodeDes = projectTreeNode.getDes();
        checkNodeDes(nodeDes);

        // 更新预置点使能字段
        if (type.equals(ProjectNodeType.PRESET)) {
            InspPresetInfo presetInfo = new InspPresetInfo();
            presetInfo.setId(projectTreeNode.getId());
            presetInfo.setAvailable(projectTreeNode.getAvailable());
            presetInfoService.updateById(presetInfo);
        }

        // 更新CHANNEL_PIC类型参数引用的名称
        List<InspAlgorithmParamInstance> channelPicParams = algorithmParamInstanceService.listChannelPicParam(treeNodeId);
        if (CollectionUtil.isNotEmpty(channelPicParams)) {
            // 组织新的参数
            InspProjectTreeNode newParamValue = new InspProjectTreeNode();
            newParamValue.setId(treeNode.getId());
            newParamValue.setLabel(treeNode.getLabel());
            String jsonStr = JSONUtil.toJsonStr(newParamValue);
            for (InspAlgorithmParamInstance channelPicParam : channelPicParams) {
                channelPicParam.setValue(jsonStr);
            }
            // 更新
            algorithmParamInstanceService.updateBatchById(channelPicParams);
        }

        treeNode.setDes(nodeDes);
        treeNode.setParentId(projectTreeNode.getParentId());
        boolean result = super.updateById(treeNode);
        if (!result) {
            throw new InspectionException("更新失败");
        }
        return treeNode;
    }

    private static String checkId(UpdateNodeInfoReqModel projectTreeNode, String treeNodeId) {
        if (StrUtil.isEmpty(treeNodeId)) {
            throw new InspectionException("节点ID不允许为空");
        }
        String label = projectTreeNode.getLabel();
        if (StrUtil.isEmpty(label)) {
            throw new InspectionException("节点名称不允许为空");
        }
        return label;
    }

    @Transactional
    @OperateLog(operateType = OperateType.DELETE, businessClassify = BusinessClassify.PROJECT_NODE)
    public Collection<InspProjectTreeNode> removeProjectNode(Collection<? extends Serializable> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            throw new InspectionException("节点ID集合不允许为空");
        }
        Collection<InspProjectTreeNode> projectTreeNodes = listByIds(idList);
        if (CollectionUtils.isEmpty(projectTreeNodes)) {
            throw new InspectionException("删除的数据不存在");
        }

        // 获取全部子节点
        List<InspProjectTreeNode> listAll = list();
        if (CollectionUtils.isEmpty(listAll)) {
            return new ArrayList<>();
        }
        Map<String, List<InspProjectTreeNode>> groupByPatentId = listAll.stream().collect(Collectors.groupingBy(InspProjectTreeNode::getParentId));
        List<String> resultIds = idList.stream().map(Object::toString).collect(Collectors.toList());
        for (Serializable nodeId : idList) {
            List<InspProjectTreeNode> childMenus = buildEntityList(groupByPatentId, nodeId.toString());
            if (!CollectionUtils.isEmpty(childMenus)) {
                List<String> ids = childMenus.stream().map(InspProjectTreeNode::getId).collect(Collectors.toList());
                resultIds.addAll(ids);
            }
        }
        // 删除节点详情
        inspChannelInfoService.removeByIds(resultIds);
        presetInfoService.removeByPresetIds(resultIds);
        removeRecord(resultIds);

        boolean result = super.removeByIds(resultIds);
        if (!result) {
            throw new InspectionException("删除失败");
        }

        return projectTreeNodes;
    }

    private void removeRecord(List<String> resultIds) {
        for (String id : resultIds) {
            InspProjectTreeNode projectTreeNode = getOneById(id);
            switch (projectTreeNode.getType()) {
                case ProjectNodeType.CHANNEL:
                    recordPresetService.removeByChannelId(id);
                    recordAlgorithmService.removeByChannelId(id);
                    alarmRecordService.removeByChannelIds(Collections.singleton(id));
                    inspSceneCustomPicService.removeByChannelIds(id);
                    break;
                case ProjectNodeType.PRESET:
                    alarmRecordService.removeByPresetIds(Collections.singleton(id));
                    recordPresetService.removeByPresetId(id);
                    recordAlgorithmService.removeByPresetId(id);
                    inspSceneCustomPicService.removeByPresetIds(id);
                    break;
            }
        }
    }

    private List<InspProjectTreeNode> buildEntityList(Map<String, List<InspProjectTreeNode>> groupByPatentId, String parentId) {
        List<InspProjectTreeNode> parentList = groupByPatentId.get(parentId);
        // 没有找到子节点
        if (CollectionUtils.isEmpty(parentList)) {
            return CollUtil.newArrayList();
        }

        List<InspProjectTreeNode> result = CollUtil.newArrayList();
        result.addAll(parentList);
        for (InspProjectTreeNode systemMenu : parentList) {
            List<InspProjectTreeNode> childMenus = buildEntityList(groupByPatentId, systemMenu.getId());
            result.addAll(childMenus);
        }
        return result;
    }

    @Transactional
    @OperateLog(operateType = OperateType.CREATE, businessClassify = BusinessClassify.PROJECT_NODE)
    public InspProjectTreeNode createProjectNode(InspProjectTreeNode projectTreeNode) {
        // 检验
        checkNodeInfo(projectTreeNode);

        LambdaQueryWrapper<InspProjectTreeNode> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(InspProjectTreeNode::getId, projectTreeNode.getParentId());
        InspProjectTreeNode parentNode = this.getOne(wrapper1);
        if (Objects.isNull(parentNode)) {
            throw new InspectionException("父级节点不存在");
        }

        String nodeType = projectTreeNode.getType();
        if (ProjectNodeType.DIRECTORY.equals(nodeType)) {
            int size = this.getBaseMapper().selectDirectoryTypeNodeDepth(projectTreeNode.getParentId()).size();
            if (size >= this.depth) {
                throw new InspectionException(String.format("区域节点嵌套深度不能超过%s层", this.depth));
            }
        }

        String parentType = parentNode.getType();
        List<String> subTypes = ProjectNodeType.getSubTypes(parentType);
        if (!subTypes.contains(nodeType)) {
            throw new InspectionException("当前节点下不允许添加此类型节点");
        }

        // 添加预置点时，需要校验父级通道是否配置完成
        if (ProjectNodeType.PRESET.equals(nodeType)) {
            InspChannelInfo parentChannel = inspChannelInfoService.getOneById(projectTreeNode.getParentId());

            String videoProtocolType = parentChannel.getVideoProtocolType();
            String cameraCtrlProtocol = parentChannel.getCameraCtrlProtocol();
            String address = parentChannel.getAddress();
            String username = parentChannel.getUsername();
            String password = parentChannel.getPassword();
            if (StrUtil.hasBlank(videoProtocolType, cameraCtrlProtocol, address, username, password)) {
                throw new InspectionException("当前节点父级通道未配置完成，无法添加预置点");
            }
        }

        // 设置节点排序号码
        LambdaQueryWrapper<InspProjectTreeNode> wrapper2 = new LambdaQueryWrapper<>();
        wrapper2.orderByDesc(InspProjectTreeNode::getSortNo);
        List<InspProjectTreeNode> list = this.list(wrapper2);
        if (CollUtil.isEmpty(list)) {
            projectTreeNode.setSortNo(0L);
        } else {
            long sortNo = list.get(0).getSortNo();
            projectTreeNode.setSortNo(sortNo + 1);
        }

        // 按节点类型新建节点详情数据
        String uuid = UUIDUtil.simpleUUID();
        projectTreeNode.setId(uuid);
        createDetailByType(projectTreeNode);

        this.save(projectTreeNode);
        return projectTreeNode;
    }

    private void checkNodeInfo(InspProjectTreeNode projectTreeNode) {
        String label = projectTreeNode.getLabel();
        checkNodeName(label);

        String des = projectTreeNode.getDes();
        checkNodeDes(des);
    }

    private void checkNodeDes(String des) {
        if (StrUtil.isNotBlank(des)) {
            AssertUtil.isTrue(des.length() <= 255, "节点描述信息不允许超过255字符");
        }
    }

    @Transactional
    public Object createBatchArea(BatchAreaModel batchAreaModel) {
        String areaPre = batchAreaModel.getAreaPre();
        String[] areaRange = batchAreaModel.getAreaRange();
        String areaFix = batchAreaModel.getAreaFix();
        if (areaRange == null || areaRange.length != 2) {
            throw new InspectionException("区域范围格式错误");
        }
        String range1 = areaRange[0];
        String range2 = areaRange[1];
        List<String> names = new ArrayList<>();
        try {
            int a = Integer.parseInt(range1);
            int b = Integer.parseInt(range2);
            if (a > b || a < 0 || b > 1000) {
                throw new InspectionException("区域范围格式错误");
            }
            for (int i = a; i <= b; i++) {
                names.add(areaPre + i + areaFix);
            }
        } catch (Exception e) {
            int charIndex1 = InspConstants.CHAR.indexOf(range1);
            int charIndex2 = InspConstants.CHAR.indexOf(range2);
            if (charIndex1 != -1 && charIndex2 != -1) {
                if (charIndex1 > charIndex2) {
                    throw new InspectionException("区域范围格式错误");
                }
                for (int i = charIndex1; i <= charIndex2; i++) {
                    names.add(areaPre + InspConstants.CHAR.get(i) + areaFix);
                }
            }
            int yishiIndex1 = InspConstants.YISHI.indexOf(range1);
            int yishiIndex2 = InspConstants.YISHI.indexOf(range2);
            if (yishiIndex1 != -1 && yishiIndex2 != -1) {
                if (yishiIndex1 > yishiIndex2) {
                    throw new InspectionException("区域范围格式错误");
                }
                for (int i = yishiIndex1; i <= yishiIndex2; i++) {
                    names.add(areaPre + InspConstants.YISHI.get(i) + areaFix);
                }
            }
        }
        if (CollectionUtils.isEmpty(names)) {
            throw new InspectionException("区域范围格式错误");
        }
        for (String s : names) {
            InspProjectTreeNode projectTreeNode = new InspProjectTreeNodeModel();
            projectTreeNode.setLabel(s);
            projectTreeNode.setParentId(batchAreaModel.getParentId());
            projectTreeNode.setType(batchAreaModel.getType());
            createProjectNode(projectTreeNode);
        }
        return null;
    }

    private void createDetailByType(InspProjectTreeNode projectTreeNode) {
        String type = projectTreeNode.getType();
        String nodeId = projectTreeNode.getId();
        switch (type) {
            case ProjectNodeType.CHANNEL:
                InspChannelInfo inspChannelInfo = new InspChannelInfo();
                inspChannelInfo.setId(nodeId);
                inspChannelInfo.setDeviceType(CameraType.GUN_CAMERA.name());
                inspChannelInfo.setVideoProtocolType(VideoProtocolType.RTSP.name());
                inspChannelInfo.setSourceProtocol(MtxSourceProtocol.udp.name());
                inspChannelInfoService.save(inspChannelInfo);
                projectTreeNode.setStatus(ChannelStatus.CONNECT_FAILED.name());
                break;
            case ProjectNodeType.PRESET:
                InspPresetInfo presetInfo = new InspPresetInfo();
                presetInfo.setId(nodeId);
                String channelId = projectTreeNode.getParentId();

                InspChannelInfo channelInfo = inspChannelInfoService.getOneById(channelId);
                // 设置当前预置点编号
                Integer presetNum = presetInfoService.getNewPresetNum(channelId);
                cameraCtrlProxyService.setPreset(channelInfo, presetNum, projectTreeNode.getLabel());
                presetInfo.setPresetNum(presetNum);

                presetInfo.setChannelId(channelId);
                presetInfoService.save(presetInfo);
                break;
            default:
        }
    }

    private void checkNodeName(String label) {
        AssertUtil.isTrue(StrUtil.isNotBlank(label), "节点名称不允许为空");

        String regex = "^[a-zA-Z0-9_\\u4e00-\\u9fa5]{1,20}$";
        AssertUtil.isTrue(label.matches(regex), "节点名称只允许包含数字、字母、汉字、下划线，20字符以内");

        LambdaQueryWrapper<InspProjectTreeNode> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspProjectTreeNode::getLabel, label);
        int countByLabel = this.count(wrapper);
        if (countByLabel > 0) {
            throw new InspectionException("节点名称重复");
        }
    }

    /**
     * 刷新通道连接状态
     */
    public void freshChannelNodeState() {
        // 刷新通道节点状态
        LambdaQueryWrapper<InspProjectTreeNode> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspProjectTreeNode::getType, ProjectNodeType.CHANNEL);
        List<InspProjectTreeNode> allChannelNodes = list(wrapper);
        if (CollUtil.isNotEmpty(allChannelNodes)) {
            // 根据节点ID集合，查询全部通道数据
            List<String> channelIds = allChannelNodes.stream().map(InspProjectTreeNode::getId).collect(Collectors.toList());
            LambdaQueryWrapper<InspChannelInfo> channelWrapper = new LambdaQueryWrapper<>();
            channelWrapper.in(InspChannelInfo::getId, channelIds);
            List<InspChannelInfo> allChannel = inspChannelInfoService.list(channelWrapper);

            HashMap<String, String> channelStatsMap = new HashMap<>();
            if (CollUtil.isNotEmpty(allChannel)) {
                allChannel.parallelStream().forEach(item -> {
                    InspProjectTreeNode channelInfo = new InspProjectTreeNode();
                    channelInfo.setId(item.getId());
                    String address = item.getAddress();
                    String channelStats = channelStatsMap.get(address);
                    if (StrUtil.isNotEmpty(channelStats)) {
                        channelInfo.setStatus(channelStats);
                    } else {
                        if (StrUtil.isEmpty(address)) {
                            channelInfo.setStatus(ChannelStatus.CONNECT_FAILED.name());
                        } else {
                            boolean reachableIp = IpUtil.isReachableIp(address);
                            if (reachableIp) {
                                channelInfo.setStatus(ChannelStatus.CONNECT_SUCCESS.name());
                            } else {
                                channelInfo.setStatus(ChannelStatus.CONNECT_FAILED.name());
                            }
                        }
                        channelStatsMap.put(address, channelInfo.getStatus());
                    }

                    updateById(channelInfo);
                });
            }
        }
    }

    public List<InspProjectTreeNode> getByParentId(String parentId) {
        LambdaQueryWrapper<InspProjectTreeNode> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspProjectTreeNode::getParentId, parentId);
        return list(wrapper);
    }

    @Transactional
    public boolean createProjectNodes(List<InspProjectTreeNode> projectTreeNodes) {
        if (CollUtil.isEmpty(projectTreeNodes)) {
            return false;
        }
        if (projectTreeNodes.size() > InspConstants.MAX_LIST_DIR_LIMIT) {
            return false;
        }
        for (InspProjectTreeNode projectTreeNode : projectTreeNodes) {
            if (!ProjectNodeType.DIRECTORY.equals(projectTreeNode.getType())) {
                throw new InspectionException("批量增加的最大数为500个");
            }
            createProjectNode(projectTreeNode);
        }
        return true;
    }

    public InspProjectTreeNode getBusinessRootNode() {
        LambdaQueryWrapper<InspProjectTreeNode> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspProjectTreeNode::getType, ProjectNodeType.BUSINESS_ROOT);
        List<InspProjectTreeNode> allBusinessRootNodes = list(wrapper);

        if (CollUtil.isEmpty(allBusinessRootNodes)) {
            throw new InspectionException("视频分析根节点不存在");
        }
        return allBusinessRootNodes.get(0);
    }

    public InspProjectTreeNode getOneByName(String nodeName) {
        LambdaQueryWrapper<InspProjectTreeNode> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspProjectTreeNode::getLabel, nodeName);
        List<InspProjectTreeNode> allBusinessRootNodes = list(wrapper);

        if (CollUtil.isNotEmpty(allBusinessRootNodes)) {
            return allBusinessRootNodes.get(0);
        }
        return null;
    }
}

