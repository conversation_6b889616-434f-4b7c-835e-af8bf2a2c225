package com.hollysys.inspection.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.io.FileUtil;
import cn.hutool.cron.CronUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.constants.FileDirEnum;
import com.hollysys.inspection.entity.InspAlarmRecord;
import com.hollysys.inspection.entity.InspLogOperate;
import com.hollysys.inspection.entity.InspRecordPreset;
import com.hollysys.inspection.model.backup.ExecuteBackupResult;
import com.hollysys.inspection.utils.MinioUtil;
import io.minio.messages.Item;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 系统备份归档服务层
 */
@Slf4j
@Service
public class SystemBackupService {

    @Value("${system-backup.enable}")
    private boolean enable;

    @Value("${system-backup.execute-record.interval}")
    private int executeBackupInterval;

    @Value("${system-backup.execute-record.interval-unit}")
    private TimeUnit executeBackupIntervalUnit;

    @Value("${system-backup.operation-log-record.interval}")
    private int operationLogBackupInterval;

    @Value("${system-backup.operation-log-record.interval-unit}")
    private TimeUnit operationLogBackupIntervalUnit;

    @Value("${system-backup.alarm-record.interval}")
    private int alarmBackupInterval;

    @Value("${system-backup.alarm-record.interval-unit}")
    private TimeUnit alarmBackupIntervalUnit;

    @Value("${system-backup.backup-check-interval}")
    private int backupCheckInterval;

    @Value("${system-backup.backup-check-interval-unit}")
    private TimeUnit backupCheckIntervalUnit;

    @Resource
    private InspRecordPresetService recordPresetService;

    @Resource
    private InspLogOperateService inspLogOperateService;

    @Resource
    private InspAlarmRecordService inspAlarmRecordService;

    @Resource
    private InspCommonConfigService inspCommonConfigService;

    /**
     * 初始化备份调度任务
     */
    public void initBackupSchedule() {
        if (!enable) {
            log.info("定时备份记录开关关闭，调度任务不启动");
            return;
        }

        // 开启备份文件夹占用检查任务
        startCheckBackupFileOfSize();

        // 开始执行记录定时备份任务
        startExecuteSchedule();

        // 开始操作记录定时备份任务
        startOperationLogSchedule();

        // 开始报警记录定时备份任务
        startAlarmLogSchedule();

        log.info("定时备份记录调度任务已经执行完成");
    }

    /**
     * 备份文件夹占用检查
     */
    private void startCheckBackupFileOfSize() {
        // 按照配置的时间间隔，启动定时任务
        String schedulingPattern = buildSchedulingPattern(backupCheckInterval, backupCheckIntervalUnit);

        CronUtil.schedule(schedulingPattern, new Runnable() {
            @Override
            public void run() {
                checkBackupFileSize();
            }
        });
    }

    /**
     * 执行记录定时备份
     */
    private void startExecuteSchedule() {
        // 按照配置的时间间隔，启动定时任务
        String schedulingPattern = buildSchedulingPattern(executeBackupInterval, executeBackupIntervalUnit);

        CronUtil.schedule(schedulingPattern, new Runnable() {
            @Override
            public void run() {
                backupExecuteRecord();
            }
        });
    }

    /**
     * 操作记录定时备份
     */
    private void startOperationLogSchedule() {
        // 按照配置的时间间隔，启动定时任务
        String schedulingPattern = buildSchedulingPattern(operationLogBackupInterval, operationLogBackupIntervalUnit);

        CronUtil.schedule(schedulingPattern, new Runnable() {
            @Override
            public void run() {
                backupOperationLogRecord();
            }
        });
    }

    /**
     * 报警记录定时备份
     */
    private void startAlarmLogSchedule() {
        // 按照配置的时间间隔，启动定时任务
        String schedulingPattern = buildSchedulingPattern(alarmBackupInterval, alarmBackupIntervalUnit);

        CronUtil.schedule(schedulingPattern, new Runnable() {
            @Override
            public void run() {
                backupAlarmRecord();
            }
        });
    }

    /**
     * 备份文件夹大小校验
     */
    private void checkBackupFileSize() {
        // 获取备份文件夹前缀
        List<String> backupPrefixes = ListUtil.of(
                FileDirEnum.BACKUP_ALARM_RECORD.name(),
                FileDirEnum.BACKUP_OPERATION_LOG_RECORD.name(),
                FileDirEnum.BACKUP_EXECUTE_RECORD.name()
        );

        // 获取最大备份文件夹大小
        double maxBackupSize = inspCommonConfigService.getCommonConfigCache().getSystemBackupConfig().getMaxBackupSize();
        ;

        // 获取当前所有备份文件夹的总大小
        double currentTotalSizeGB = MinioUtil.getSpecificBucketSize(backupPrefixes);

        log.debug("当前所有备份文件夹总大小：{} GB，最大限制：{} GB",
                String.format("%.2f", currentTotalSizeGB),
                maxBackupSize);

        if (currentTotalSizeGB > maxBackupSize) {

            log.debug("备份文件夹总大小超过 {} GB，开始清理最早的备份文件...", maxBackupSize);

            // 如果在备份文件时调用该方法 需要传入上传文件大小 并计算需要删除的空间为多少 才能达到最大限制之内 （当前文件夹大小 + 上传文件大小 - 最大限制大小）
            double deleteSize = currentTotalSizeGB - maxBackupSize;

            // 清理备份文件
            cleanUpOldestBackupFiles(backupPrefixes, deleteSize);
        } else {
            log.debug("当前备份文件夹占用空间小于等于最大限制 {} GB，无需清理", maxBackupSize);
        }
    }

    /**
     * 删除备份文件
     *
     * @param backupPrefixes 文件夹前缀
     */
    private void cleanUpOldestBackupFiles(List<String> backupPrefixes, double needDeleteSize) {
        List<Item> allBackupFiles = MinioUtil.listAllFileObjects(backupPrefixes);

        if (CollectionUtil.isEmpty(allBackupFiles)) {
            log.debug("没有找到需要清理的备份文件。");
            return;
        }

        double deleteSize = 0.0;
        // 按上传时间排序，最早的在前面
        allBackupFiles.sort(Comparator.comparing(Item::lastModified));

        // 存储需要删除的文件
        List<String> keys = new ArrayList<>();

        // 根据传入的needDeleteSize来删除文件 直到删除文件大小达到needDeleteSize
        for (Item itemToDelete : allBackupFiles) {
            if (deleteSize >= needDeleteSize) {
                break;
            }

            // 计算删除文件大小
            double fileSizeGB = (double) itemToDelete.size() / (1024 * 1024 * 1024);

            // 更新当前总大小
            deleteSize += fileSizeGB;

            // 增加要删除的文件名
            keys.add(itemToDelete.objectName());
        }

        try {
            // 调用删除方法
            MinioUtil.removeObjectsByKey(keys);

            log.debug("已删除最早的备份文件{}个", keys.size());
        } catch (Exception e) {
            log.error("删除备份文件失败", e);
        }
    }

    /**
     * 备份执行记录
     */
    private void backupExecuteRecord() {
        // 判断当前库中记录数量是否超过备份界限数量
        int count = recordPresetService.count();
        // 获取配置的备份的单页备份数量
        int executeSizeOfPage = inspCommonConfigService.getCommonConfigCache().getSystemBackupConfig()
                .getExecuteRecord().getBackupItemSize();
        // 获取配置的备份界限数量
        int executeRecordOutOfSize = inspCommonConfigService.getCommonConfigCache().getSystemBackupConfig()
                .getExecuteRecord().getOutOfSize();
        if (count <= executeRecordOutOfSize) {
            log.debug("当前预置点入库的执行记录个数小于等于备份界限数量{}，跳过备份操作", executeRecordOutOfSize);
            return;
        }

        // 计算需要备份的精确记录数量
        int recordsToBackupCount = count - executeRecordOutOfSize;

        // 需要跳过的记录数量

        log.info("执行记录备份开始，总记录数 = {}，需保留 = {}，实际需备份 = {}，跳过数量 = {}, 单个文件最大记录条数 = {}",
                count, executeRecordOutOfSize, recordsToBackupCount, executeRecordOutOfSize, executeSizeOfPage);

        // 记录备份时长
        TimeInterval timer = DateUtil.timer();

        // 构建查询条件
        LambdaQueryWrapper<InspRecordPreset> wrapper = new LambdaQueryWrapper<>();

        // 按照时间倒序排列，这样 LIMIT 和 OFFSET 会跳过最新的记录，获取到最旧的记录
        wrapper.orderByDesc(InspRecordPreset::getStartTime);

        // 注入 LIMIT 和 OFFSET SQL 可以直接从数据库层面精确控制获取的数据量和起始位置
        wrapper.last("LIMIT " + recordsToBackupCount + " OFFSET " + executeRecordOutOfSize);

        // 执行查询，获取需要备份的数据列表
        List<InspRecordPreset> needBackupDataList = recordPresetService.list(wrapper);

        // 查询记录判空
        if (CollectionUtil.isEmpty(needBackupDataList)) {
            log.debug("分页查询出的执行记录为空，跳过备份操作");
            return;
        }

        // 构建 MinIO 基础备份路径，包含时间戳子文件夹
        String baseMinioBackupPath = generateBackupFilePath(FileDirEnum.BACKUP_EXECUTE_RECORD);

        // 用于收集所有需要备份的图片 URL
        List<String> allDownloadedAndUploadedImgUrls = new ArrayList<>();

        // 分批备份（为什么不在上面循环内部写？因为要采用一边备份一边删除数据，查询和删除一起操作会有问题，所以先将全部数据查询出来）
        List<List<InspRecordPreset>> split = CollectionUtil.split(needBackupDataList, executeSizeOfPage);
        for (List<InspRecordPreset> recordPresets : split) {
            // 调用备份方法
            ExecuteBackupResult backupResult = recordPresetService.backup(recordPresets);
            // 获取备份文件列表
            File[] backupFileArray = backupResult.getBackupFiles();
            // 获取备份图片URL列表
            List<String> imageUrlsToDownload = backupResult.getImageUrlsToDownload();

            if (backupFileArray != null) {
                // 将备份文件移动到临时目录
                for (File backupFile : backupFileArray) {
                    try {
                        String uploadFile = MinioUtil.uploadFileToSpecificObject(baseMinioBackupPath, backupFile.getName(), FileUtil.getInputStream(backupFile));
                        log.debug("执行记录备份上传成功，上传文件路径 = {}, 用时 = {}", uploadFile, timer);
                    } catch (InspectionException e) {
                        log.error("执行记录备份失败", e);
                    }
                }
            }

            // 收集需要备份得图片URL
            allDownloadedAndUploadedImgUrls.addAll(imageUrlsToDownload);
        }

        // 将图片从原位置移动到备份文件夹
        moveAndDeleteImgs(allDownloadedAndUploadedImgUrls, baseMinioBackupPath);

        // 获取备份时长
        long intervalRestart = timer.intervalRestart();

        log.info("执行记录备份完成，用时 = {}毫秒", intervalRestart);
    }

    /**
     * 备份操作日志
     */
    private void backupOperationLogRecord() {
        // 判断当前库中记录数量是否超过备份界限数量
        int count = inspLogOperateService.count();
        // 获取配置的备份的单页备份数量
        int operationLogSizeOfPage = inspCommonConfigService.getCommonConfigCache().getSystemBackupConfig()
                .getOperationLogRecord().getBackupItemSize();
        // 获取配置的备份界限数量
        int operationLogRecordOutOfSize = inspCommonConfigService.getCommonConfigCache().getSystemBackupConfig().
                getOperationLogRecord().getOutOfSize();
        if (count <= operationLogRecordOutOfSize) {
            log.debug("当前操作记录入库的个数小于等于备份界限数量{}，跳过备份操作", operationLogRecordOutOfSize);
            return;
        }

        // 计算需要备份的精确记录数量
        int recordsToBackupCount = count - operationLogRecordOutOfSize;

        // 需要跳过的记录数量
        int offset = operationLogRecordOutOfSize;

        log.info("操作记录备份开始，总记录数 = {}，需保留 = {}，实际需备份 = {}，跳过数量 = {}, 单个文件最大记录条数 = {}",
                count, operationLogRecordOutOfSize, recordsToBackupCount, offset, operationLogSizeOfPage);

        // 记录备份时长
        TimeInterval timer = DateUtil.timer();

        // 构建查询条件
        LambdaQueryWrapper<InspLogOperate> wrapper = new LambdaQueryWrapper<>();

        // 按照时间倒序排列，这样 LIMIT 和 OFFSET 会跳过最新的记录，获取到最旧的记录
        wrapper.orderByDesc(InspLogOperate::getTime);

        // 注入 LIMIT 和 OFFSET SQL 可以直接从数据库层面精确控制获取的数据量和起始位置
        wrapper.last("LIMIT " + recordsToBackupCount + " OFFSET " + offset);

        // 执行查询，获取需要备份的数据列表
        List<InspLogOperate> needBackupDataList = inspLogOperateService.list(wrapper);

        // 查询记录判空
        if (CollectionUtil.isEmpty(needBackupDataList)) {
            log.debug("分页查询出的操作记录为空，跳过备份操作");
            return;
        }

        // 构建 MinIO 基础备份路径，包含时间戳子文件夹
        String baseMinioBackupPath = generateBackupFilePath(FileDirEnum.BACKUP_OPERATION_LOG_RECORD);

        // 分批备份
        List<List<InspLogOperate>> split = CollectionUtil.split(needBackupDataList, operationLogSizeOfPage);

        // 备份操作 返回备份文件
        for (List<InspLogOperate> recordLogs : split) {
            File backupFile = inspLogOperateService.backup(recordLogs);
            try {
                String uploadFile = MinioUtil.uploadFileToSpecificObject(baseMinioBackupPath, backupFile.getName(), FileUtil.getInputStream(backupFile));
                log.debug("操作记录备份csv文件成功，上传文件路径 = {}, 用时 = {}", uploadFile, timer);
            } catch (InspectionException e) {
                log.error("操作记录备份失败", e);
            }
        }

        // 获取备份时长
        long intervalRestart = timer.intervalRestart();

        log.info("操作记录备份完成，用时 = {}毫秒", intervalRestart);

    }

    /**
     * 备份报警日志
     */
    private void backupAlarmRecord() {
        // 判断当前库中记录数量是否超过备份界限数量
        int count = inspAlarmRecordService.count();
        // 获取配置的备份的单页备份数量
        int alarmSizeOfPage = inspCommonConfigService.getCommonConfigCache().getSystemBackupConfig().getAlarmRecord()
                .getBackupItemSize();
        // 获取配置的备份界限数量
        int alarmRecordOutOfSize = inspCommonConfigService.getCommonConfigCache().getSystemBackupConfig().getAlarmRecord()
                .getOutOfSize();
        if (count <= alarmRecordOutOfSize) {
            log.debug("当前报警记录个数小于等于备份界限数量{}，跳过备份操作", alarmRecordOutOfSize);
            return;
        }

        // 计算需要备份的精确记录数量
        int recordsToBackupCount = count - alarmRecordOutOfSize;

        // 需要跳过的记录数量
        int offset = alarmRecordOutOfSize;

        log.info("报警记录备份开始，总记录数 = {}，需保留 = {}，实际需备份 = {}，跳过数量 = {}, 单个文件最大记录条数 = {}",
                count, alarmRecordOutOfSize, recordsToBackupCount, offset, alarmSizeOfPage);

        // 记录备份时长
        TimeInterval timer = DateUtil.timer();

        // 构建查询条件
        LambdaQueryWrapper<InspAlarmRecord> wrapper = new LambdaQueryWrapper<>();

        // 按照时间倒序排列，这样 LIMIT 和 OFFSET 会跳过最新的记录，获取到最旧的记录
        wrapper.orderByDesc(InspAlarmRecord::getCreateTime);

        // 注入 LIMIT 和 OFFSET SQL 可以直接从数据库层面精确控制获取的数据量和起始位置
        wrapper.last("LIMIT " + recordsToBackupCount + " OFFSET " + offset);

        // 执行查询，获取需要备份的数据列表
        List<InspAlarmRecord> needBackupDataList = inspAlarmRecordService.list(wrapper);

        // 查询记录判空
        if (CollectionUtil.isEmpty(needBackupDataList)) {
            log.debug("分页查询出的报警记录为空，跳过备份操作");
            return;
        }

        // 构建 MinIO 基础备份路径，包含时间戳子文件夹
        String baseMinioBackupPath = generateBackupFilePath(FileDirEnum.BACKUP_ALARM_RECORD);

        // 分批备份
        List<List<InspAlarmRecord>> split = CollectionUtil.split(needBackupDataList, alarmSizeOfPage);

        // 记录需要备份的图片URL
        List<String> allImgUrls = new ArrayList<>();

        // 备份操作 返回备份文件
        for (List<InspAlarmRecord> recordLogs : split) {
            File backupFile = inspAlarmRecordService.backup(recordLogs);
            try {
                String uploadFile = MinioUtil.uploadFileToSpecificObject(baseMinioBackupPath, backupFile.getName(), FileUtil.getInputStream(backupFile));
                log.debug("报警记录备份csv文件上传成功，上传文件路径 = {}, 用时 = {}", uploadFile, timer);
            } catch (InspectionException e) {
                log.error("报警记录备份失败", e);
            }
            // 打包全部报警图片
            for (InspAlarmRecord record : recordLogs) {
                List<String> resultImgs = record.getResultImgs();
                if (CollectionUtil.isNotEmpty(resultImgs)) {
                    allImgUrls.addAll(resultImgs);
                }
            }
        }
        // 使用 Set 对 allImageUrls 进行去重
        Set<String> uniqueImageUrls = new HashSet<>(allImgUrls);

        // 将去重后的 Set 转换回 List
        List<String> uniqueImageList = new ArrayList<>(uniqueImageUrls);

        // 将图片从原位置移动到备份文件夹
        moveAndDeleteImgs(uniqueImageList, baseMinioBackupPath);

        // 获取备份时长
        long intervalRestart = timer.intervalRestart();

        log.info("报警记录备份完成，用时 = {}毫秒", intervalRestart);

    }

    /**
     * 生成备份文件夹路径
     *
     * @param prefix 前缀
     * @return 路径
     */
    private String generateBackupFilePath(FileDirEnum prefix) {
        // 根据时间生成文件夹名称
        String timeFolderName = DateUtil.format(LocalDateTime.now(), DatePattern.CHINESE_DATE_TIME_PATTERN);

        // 构建 MinIO 基础备份路径，包含时间戳子文件夹
        return prefix.name() + "/" + timeFolderName + "/";
    }

    /**
     * 移动并删除图片
     *
     * @param allImgUrls          图片url
     * @param baseMinioBackupPath 目标路径
     */
    private void moveAndDeleteImgs(List<String> allImgUrls, String baseMinioBackupPath) {
        for (String imgUrl : allImgUrls) {
            // 构建源对象
            String sourceObjectKey = MinioUtil.getObjKey(imgUrl);

            // 根据源对象取得文件名
            String fileName = FilenameUtils.getName(imgUrl);

            // 构建目标对象
            String destinationObjectKey = baseMinioBackupPath + "/" + fileName;

            try {
                MinioUtil.moveObject(sourceObjectKey, destinationObjectKey);
            } catch (InspectionException e) {
                log.error(e.getMessage(), e);
            }
        }

    }

    /**
     * 构建定时配置
     *
     * @param interval 间隔
     * @param unit     单位
     * @return 定时配置
     */
    private String buildSchedulingPattern(int interval, TimeUnit unit) {
        if (unit.equals(TimeUnit.DAYS)) {
            return "0 0 0 1/" + interval + " * ?";
        } else if (unit.equals(TimeUnit.HOURS)) {
            return "0 0 0/" + interval + " * * ?";
        } else if (unit.equals(TimeUnit.MINUTES)) {
            return "0 0/" + interval + " * * * ?";
        } else {
            throw new InspectionException("备份执行周期间隔配置错误");
        }
    }
}
