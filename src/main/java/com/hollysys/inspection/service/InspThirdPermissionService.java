package com.hollysys.inspection.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.entity.InspThirdApp;
import com.hollysys.inspection.entity.InspThirdInterface;
import com.hollysys.inspection.entity.InspThirdPermission;
import com.hollysys.inspection.mapper.InspThirdPermissionMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 第三方应用接口权限(InspThirdPermission)表服务接口
 *
 * <AUTHOR>
 * @since 2023-07-05 14:29:06
 */
@Slf4j
@Service
public class InspThirdPermissionService extends ServiceImpl<InspThirdPermissionMapper, InspThirdPermission> {

    // TODO 缓存到redis
    private static final ConcurrentHashMap<String, List<String>> PERMISSION_CACHE = new ConcurrentHashMap<>();

    @Resource
    private InspThirdAppService thirdAppService;

    @Resource
    private InspThirdInterfaceService thirdInterfaceService;

    public void removeByAppIds(List<String> appIdList) {
        LambdaQueryWrapper<InspThirdPermission> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(InspThirdPermission::getAppId, appIdList);
        remove(wrapper);
    }

    public List<InspThirdPermission> listByAppId(String appId) {
        LambdaQueryWrapper<InspThirdPermission> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspThirdPermission::getAppId, appId);
        return list(wrapper);
    }

    public void refreshPermissionCache(String appId) {
        List<String> permissionList = getPermissionListFromDb(appId);
        PERMISSION_CACHE.put(appId, permissionList);
    }

    public void checkPermission(String licence, String method, String url) {
        if (StrUtil.isBlank(licence)) {
            throw new InspectionException("认证标识为空，认证失败");
        }

        // 查询APP是否存在
        InspThirdApp byLicenseCode = thirdAppService.getByLicenseCode(licence);
        if (Objects.isNull(byLicenseCode)) {
            throw new InspectionException("第三方应用未注册或认证标识不合法");
        }

        // 判断应用开启状态
        if (!Boolean.TRUE.equals(byLicenseCode.getAvailable())) {
            throw new InspectionException("当前第三方应用相关功能已被禁用");
        }

        List<String> permissionList = getPermissionList(byLicenseCode.getId());
        String permissionStr = buildPermissionStr(method, url);
        String appCode = byLicenseCode.getAppCode();
        if (!permissionList.contains(permissionStr)) {
            log.error("APP[CODE = {}]调用开放API[{}]，权限检验失败", appCode, permissionStr);
            throw new InspectionException("接口未授权");
        }
        log.debug("APP[CODE = {}]调用开放API[{}]，权限检验通过", appCode, permissionStr);
    }

    /**
     * 获取第三方应用授权的API列表
     *
     * @param appId 应用主键
     * @return API列表
     */
    private List<String> getPermissionList(String appId) {
        // 读取缓存
        List<String> interfaces = PERMISSION_CACHE.get(appId);
        if (Objects.nonNull(interfaces)) {
            return interfaces;
        }

        interfaces = getPermissionListFromDb(appId);

        // 设置缓存
        PERMISSION_CACHE.put(appId, interfaces);
        return interfaces;
    }

    private List<String> getPermissionListFromDb(String appId) {
        List<InspThirdPermission> thirdPermissions = this.listByAppId(appId);
        if (CollectionUtil.isEmpty(thirdPermissions)) {
            return ListUtil.empty();
        }

        List<String> interfaceIds = thirdPermissions.stream().map(InspThirdPermission::getInterfaceId).collect(Collectors.toList());
        Collection<InspThirdInterface> thirdInterfaces = thirdInterfaceService.listByIds(interfaceIds);
        return thirdInterfaces.stream().map(item -> {
            String httpMethod = item.getHttpMethod();
            String url = item.getUrl();
            return buildPermissionStr(httpMethod, url);
        }).collect(Collectors.toList());
    }

    private String buildPermissionStr(String method, String url) {
        return method + ":" + url;
    }
}

