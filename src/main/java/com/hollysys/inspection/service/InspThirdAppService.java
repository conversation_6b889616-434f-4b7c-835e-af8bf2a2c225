package com.hollysys.inspection.service;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.constants.InspConstants;
import com.hollysys.inspection.entity.InspThirdApp;
import com.hollysys.inspection.entity.InspThirdInterface;
import com.hollysys.inspection.entity.InspThirdPermission;
import com.hollysys.inspection.mapper.InspThirdAppMapper;
import com.hollysys.inspection.model.BindInterfacesReqModel;
import com.hollysys.inspection.model.InspThirdAppModel;
import com.hollysys.inspection.utils.StrEscapeUtil;
import com.hollysys.inspection.utils.UUIDUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 第三方应用管理(InspThirdApp)表服务接口
 *
 * <AUTHOR>
 * @since 2023-07-05 14:28:35
 */
@Service
public class InspThirdAppService extends ServiceImpl<InspThirdAppMapper, InspThirdApp> {

    @Resource
    private InspThirdInterfaceService thirdInterfaceService;

    @Resource
    private InspThirdPermissionService thirdPermissionService;

    private static final String APP_NOT_FOUND = "应用不存在";

    public Object getAppDetail(String appId) {
        InspThirdApp byId = getById(appId);
        if (Objects.isNull(byId)) {
            throw new InspectionException(APP_NOT_FOUND);
        }
        return byId;
    }

    public Object newApp(InspThirdApp inspThirdApp) {
        String name = inspThirdApp.getName();
        if (StrUtil.isEmpty(name)) {
            throw new InspectionException("应用名称不允许为空");
        }

        LambdaQueryWrapper<InspThirdApp> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspThirdApp::getName, name);
        List<InspThirdApp> list = list(wrapper);
        if (CollUtil.isNotEmpty(list)) {
            throw new InspectionException("应用名称重复");
        }

        InspThirdApp entity = new InspThirdApp();
        entity.setName(name);
        entity.setAppCode(UUIDUtil.simpleUUID().toUpperCase());
        entity.setLicenseCode(Base64.encode(UUIDUtil.simpleUUID()));
        LocalDateTime dateTime = LocalDateTime.now();
        entity.setUpdateTime(dateTime);
        entity.setCreateTime(dateTime);
        save(entity);

        return entity;
    }

    public Object updateApp(InspThirdApp inspThirdApp) {
        String appId = inspThirdApp.getId();
        if (StrUtil.isEmpty(appId)) {
            throw new InspectionException("应用ID不允许为空");
        }
        InspThirdApp byId = getById(appId);
        if (Objects.isNull(byId)) {
            throw new InspectionException(APP_NOT_FOUND);
        }

        InspThirdApp entity = new InspThirdApp();
        entity.setId(appId);

        String name = inspThirdApp.getName();
        if (StrUtil.isNotBlank(name) && !name.equals(byId.getName())) {
            // 更新名称
            LambdaQueryWrapper<InspThirdApp> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(InspThirdApp::getName, name);
            wrapper.notIn(InspThirdApp::getId, appId);
            List<InspThirdApp> list = list(wrapper);
            if (CollUtil.isNotEmpty(list)) {
                throw new InspectionException("应用名称重复");
            }
            entity.setName(name);
        }
        Boolean available = inspThirdApp.getAvailable();
        if (Objects.nonNull(available)) {
            entity.setAvailable(available);
        }
        entity.setUpdateTime(LocalDateTime.now());
        updateById(entity);
        return entity;
    }

    @Transactional
    public Object removeApps(List<String> idList) {
        // 删除APP
        removeByIds(idList);
        // 删除APP绑定的接口权限
        thirdPermissionService.removeByAppIds(idList);
        return true;
    }

    public Object listAllInterface() {
        List<InspThirdInterface> thirdInterfaces = thirdInterfaceService.list();
        if (CollUtil.isEmpty(thirdInterfaces)) {
            return new ArrayList<>();
        }
        return thirdInterfaces.stream()
                .collect(Collectors.groupingBy(InspThirdInterface::getClassification));
    }

    @Transactional
    public void bindInterfaces(BindInterfacesReqModel reqModel) {
        String appId = reqModel.getAppId();
        if (StrUtil.isEmpty(appId)) {
            throw new InspectionException("应用ID不允许为空");
        }
        InspThirdApp byId = getById(appId);
        if (Objects.isNull(byId)) {
            throw new InspectionException(APP_NOT_FOUND);
        }

        // 删除APP历史绑定的接口权限
        thirdPermissionService.removeByAppIds(ListUtil.of(appId));

        // 添加新的权限关系
        List<String> interfaceIds = reqModel.getInterfaceIds();
        if (CollUtil.isNotEmpty(interfaceIds)) {
            Collection<InspThirdInterface> inspThirdInterfaces = thirdInterfaceService.listByIds(interfaceIds);
            if (CollUtil.isEmpty(inspThirdInterfaces)) {
                return;
            }

            List<InspThirdPermission> inspThirdPermissions = inspThirdInterfaces.stream().map(item -> {
                InspThirdPermission inspThirdPermission = new InspThirdPermission();
                inspThirdPermission.setAppId(appId);
                inspThirdPermission.setInterfaceId(item.getId());
                return inspThirdPermission;
            }).collect(Collectors.toList());

            thirdPermissionService.saveBatch(inspThirdPermissions);
        }

        // 刷新权限缓存
        thirdPermissionService.refreshPermissionCache(appId);
    }

    public InspThirdApp getByLicenseCode(String licenseCode) {
        LambdaQueryWrapper<InspThirdApp> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspThirdApp::getLicenseCode, licenseCode);
        List<InspThirdApp> list = list(wrapper);

        if (CollUtil.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    public InspThirdApp getByAppCode(String appCode) {
        LambdaQueryWrapper<InspThirdApp> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspThirdApp::getAppCode, appCode);
        List<InspThirdApp> list = list(wrapper);

        if (CollUtil.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    public Object listPage(Page<InspThirdApp> page, String appName, String appCode) {
        LambdaQueryWrapper<InspThirdApp> queryWrapper = new LambdaQueryWrapper<>();
        appName = StrEscapeUtil.escapeChar(appName);
        if (StrUtil.isNotBlank(appName)) {
            queryWrapper.apply("name ILIKE CONCAT('%', {0}, '%')", appName);
        }
        appCode = StrEscapeUtil.escapeChar(appCode);
        if (StrUtil.isNotBlank(appCode)) {
            queryWrapper.apply("app_code ILIKE CONCAT('%', {0}, '%')", appCode);
        }
        queryWrapper.orderByDesc(InspThirdApp::getCreateTime);

        IPage<InspThirdApp> pageResult = page(page, queryWrapper);
        List<InspThirdApp> records = pageResult.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            List<InspThirdAppModel> models = records.stream()
                    .map(x -> {
                        String createTimeStr = LocalDateTimeUtil.format(x.getCreateTime(), InspConstants.yyyy_MM_ddHHmmss);
                        String updateTimeStr = LocalDateTimeUtil.format(x.getUpdateTime(), InspConstants.yyyy_MM_ddHHmmss);
                        InspThirdAppModel inspThirdAppModel = new InspThirdAppModel();
                        BeanUtils.copyProperties(x, inspThirdAppModel);
                        inspThirdAppModel.setCreateTimeStr(createTimeStr);
                        inspThirdAppModel.setUpdateTimeStr(updateTimeStr);
                        return inspThirdAppModel;
                    })
                    .collect(Collectors.toList());

            Page<InspThirdAppModel> resultPage = new Page<>();
            BeanUtils.copyProperties(pageResult, resultPage);
            resultPage.setRecords(models);
            return resultPage;
        }
        return pageResult;
    }

    public Object listBindInterface(String appId) {
        List<InspThirdPermission> inspThirdPermissions = thirdPermissionService.listByAppId(appId);
        return inspThirdPermissions.stream().map(InspThirdPermission::getInterfaceId).collect(Collectors.toList());
    }
}

