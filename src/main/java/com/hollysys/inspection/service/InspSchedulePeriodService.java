package com.hollysys.inspection.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.Week;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.entity.InspSchedulePeriod;
import com.hollysys.inspection.mapper.InspSchedulePeriodMapper;
import com.hollysys.inspection.model.period.InspScheduleCopyModel;
import com.hollysys.inspection.model.period.InspSchedulePeriodModel;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 通道运行周期调度信息表(InspSchedulePeriod)表服务接口
 *
 * <AUTHOR>
 * @since 2023-02-02 17:37:41
 */
@Service
public class InspSchedulePeriodService extends ServiceImpl<InspSchedulePeriodMapper, InspSchedulePeriod> {

    @Transactional
    public boolean insert(InspSchedulePeriodModel inspSchedulePeriod) {
        String taskId = inspSchedulePeriod.getTaskId();
        List<InspSchedulePeriodModel.Periods> periods = inspSchedulePeriod.getPeriods();
        if (CollUtil.isEmpty(periods)) {
            // 删除当前通道的全部配置
            LambdaQueryWrapper<InspSchedulePeriod> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(InspSchedulePeriod::getTaskId, taskId);
            remove(wrapper);
            return true;
        }

        List<Integer> weekValues = Arrays.stream(Week.values()).map(Week::getIso8601Value).collect(Collectors.toList());
        Map<Integer, List<InspSchedulePeriodModel.Periods>> groupByWeek = periods.stream().collect(Collectors.groupingBy(InspSchedulePeriodModel.Periods::getWeek));
        Set<Integer> integers = groupByWeek.keySet();
        checkParam(inspSchedulePeriod, weekValues, groupByWeek, integers);

        // 先删除在新增
        LambdaQueryWrapper<InspSchedulePeriod> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspSchedulePeriod::getTaskId, taskId);
        remove(wrapper);

        for (InspSchedulePeriodModel.Periods periodModel : periods) {
            InspSchedulePeriod period = new InspSchedulePeriod();

            BeanUtils.copyProperties(periodModel, period);
            BeanUtils.copyProperties(inspSchedulePeriod, period);
            period.setId(null);
            super.save(period);
        }

        return true;
    }

    private static void checkParam(InspSchedulePeriodModel inspSchedulePeriod, List<Integer> weekValues, Map<Integer, List<InspSchedulePeriodModel.Periods>> groupByWeek, Set<Integer> integers) {
        if (!CollUtil.containsAll(weekValues, integers)) {
            throw new InspectionException("参数错误");
        }
        for (List<InspSchedulePeriodModel.Periods> value : groupByWeek.values()) {
            if (CollUtil.isNotEmpty(value) && value.size() > 1) {
                throw new InspectionException("参数错误");
            }
        }
    }

    public boolean copyToOther(InspScheduleCopyModel inspScheduleCopyModel) {
        String channelId = inspScheduleCopyModel.getFromChannelId();
        LambdaQueryWrapper<InspSchedulePeriod> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspSchedulePeriod::getTaskId, channelId);
        List<InspSchedulePeriod> list = list(wrapper);
        if (CollUtil.isNotEmpty(list)) {
            list.forEach(x -> {
                x.setTaskId(inspScheduleCopyModel.getTargetChannelId());
                x.setFromTaskId(inspScheduleCopyModel.getFromChannelId());
                x.setId(null);
            });
            saveBatch(list);
        }
        return true;
    }

    public InspSchedulePeriodModel listByTaskId(String taskId) {
        LambdaQueryWrapper<InspSchedulePeriod> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspSchedulePeriod::getTaskId, taskId);
        wrapper.orderByAsc(InspSchedulePeriod::getWeek);
        List<InspSchedulePeriod> list = list(wrapper);
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }

        List<InspSchedulePeriodModel.Periods> periods = list.stream().map(x -> {
            InspSchedulePeriodModel.Periods period = new InspSchedulePeriodModel.Periods();
            BeanUtils.copyProperties(x, period);
            return period;
        }).collect(Collectors.toList());

        InspSchedulePeriod schedulePeriod = list.get(0);
        InspSchedulePeriodModel inspSchedulePeriodModel = new InspSchedulePeriodModel();
        BeanUtils.copyProperties(schedulePeriod, inspSchedulePeriodModel);
        inspSchedulePeriodModel.setPeriods(periods);
        return inspSchedulePeriodModel;
    }

    public InspSchedulePeriod listByTaskIdAndWeekDay(int weekDay, String taskId) {
        LambdaQueryWrapper<InspSchedulePeriod> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspSchedulePeriod::getWeek, weekDay);
        wrapper.eq(InspSchedulePeriod::getTaskId, taskId);
        List<InspSchedulePeriod> list = list(wrapper);

        if (CollectionUtil.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    public List<InspSchedulePeriod> listByWeekDay(int weekDay) {
        LambdaQueryWrapper<InspSchedulePeriod> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspSchedulePeriod::getWeek, weekDay);
        return list(wrapper);
    }

    public void removeByTaskIds(Collection<? extends Serializable> taskIdList) {
        LambdaQueryWrapper<InspSchedulePeriod> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(InspSchedulePeriod::getTaskId, taskIdList);
        remove(wrapper);
    }
}
