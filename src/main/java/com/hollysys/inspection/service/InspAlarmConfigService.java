package com.hollysys.inspection.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.constants.AlarmModeType;
import com.hollysys.inspection.constants.algorithm.param.DataType;
import com.hollysys.inspection.entity.InspAlarmConfig;
import com.hollysys.inspection.entity.InspAlgorithmInstance;
import com.hollysys.inspection.entity.InspAlgorithmParam;
import com.hollysys.inspection.entity.InspProjectTreeNode;
import com.hollysys.inspection.mapper.InspAlarmConfigMapper;
import com.hollysys.inspection.model.alarm.config.AdvanceAlarmRuleInfo;
import com.hollysys.inspection.model.alarm.config.AlarmConfigItemModel;
import com.hollysys.inspection.model.alarm.config.AlarmConfigModel;
import com.hollysys.inspection.model.alarm.config.AlarmRuleItemModel;
import com.hollysys.inspection.utils.AssertUtil;
import com.hollysys.inspection.utils.OutputParamUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * (InspAlarmConfig)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-08 14:02:05
 */
@Service
public class InspAlarmConfigService extends ServiceImpl<InspAlarmConfigMapper, InspAlarmConfig> {

    @Resource
    private InspAlgorithmInstanceService algorithmInstanceService;

    @Resource
    private ProjectTreeNodeService projectTreeNodeService;

    @Resource
    private InspAlgorithmParamService algorithmParamService;

    @Resource
    private InspAlarmRecordService alarmRecordService;

    public List<InspAlarmConfig> listByAlgorithmInstId(String algorithmInstanceId) {
        return lambdaQuery()
                .eq(InspAlarmConfig::getAlgorithmInstanceId, algorithmInstanceId)
                .list();
    }

    public List<AlarmConfigModel> getConfigListByPreset(String presetId) {
        InspProjectTreeNode presetNode = projectTreeNodeService.getOneById(presetId);
        // 查询预置点下全部算法实例
        List<AlarmConfigModel> result = new ArrayList<>();
        List<InspAlgorithmInstance> algorithmInstances = algorithmInstanceService.listByPresetId(presetId);
        if (CollectionUtil.isEmpty(algorithmInstances)) {
            return result;
        }

        for (int i = 0; i < algorithmInstances.size(); i++) {
            InspAlgorithmInstance algorithmInstance = algorithmInstances.get(i);
            // 查询算法的出参定义
            String algorithmId = algorithmInstance.getAlgorithmId();
            List<InspAlgorithmParam> outputParams = algorithmParamService.listOutputByAlgorithmId(algorithmId);
            if (CollectionUtil.isEmpty(outputParams)) {
                // 如果算法没有出参定义，则无法配置报警
                continue;
            }
            List<AlarmConfigItemModel> alarmConfigs = new ArrayList<>();
            String algorithmInstName = OutputParamUtil.getAlgorithmInstName(algorithmInstance, i);
            for (InspAlgorithmParam outputParam : outputParams) {
                // 查询已入库数据
                InspAlarmConfig alarmConfig = lambdaQuery()
                        .eq(InspAlarmConfig::getAlgorithmInstanceId, algorithmInstance.getId())
                        .eq(InspAlarmConfig::getOutputParamId, outputParam.getId())
                        .one();
                if (Objects.isNull(alarmConfig)) {
                    // 如果已入库数据为空，则从定义数据中组织
                    alarmConfig = new InspAlarmConfig();
                    alarmConfig.setChannelId(presetNode.getParentId());
                    alarmConfig.setPresetId(presetId);
                    alarmConfig.setAlgorithmInstanceId(algorithmInstance.getId());
                    alarmConfig.setAlarmType(AlarmModeType.OFF_LIMIT_ALARM.name());
                }
                String dataType = outputParam.getDataType();
                alarmConfig.initRuleInfo(dataType);

                AlarmConfigItemModel alarmConfigItemModel = new AlarmConfigItemModel();
                BeanUtil.copyProperties(alarmConfig, alarmConfigItemModel);

                alarmConfigItemModel.setOutputParamId(outputParam.getId());
                alarmConfigItemModel.setOutputName(outputParam.getLabel());
                alarmConfigItemModel.setOutputDataType(dataType);

                alarmConfigs.add(alarmConfigItemModel);
            }

            AlarmConfigModel alarmConfigModel = new AlarmConfigModel();
            alarmConfigModel.setAlarmConfigs(alarmConfigs);
            alarmConfigModel.setAlgorithmInstanceName(algorithmInstName);

            result.add(alarmConfigModel);
        }
        return result;
    }

    @Transactional
    public Object saveAlarmConfig(List<AlarmConfigModel> reqModels) {
        if (CollectionUtil.isEmpty(reqModels)) {
            return reqModels;
        }
        // 多算法实例
        for (AlarmConfigModel reqModel : reqModels) {
            List<AlarmConfigItemModel> alarmConfigs = reqModel.getAlarmConfigs();
            if (CollectionUtil.isEmpty(alarmConfigs)) {
                return reqModel;
            }
            List<String> presetIds = alarmConfigs.stream().map(AlarmConfigItemModel::getPresetId)
                    .filter(StrUtil::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());
            AssertUtil.isTrue(presetIds.size() == 1, "当前提交的报警配置必须属于同一个预置点");

            List<String> algorithmInstanceIds = alarmConfigs.stream().map(AlarmConfigItemModel::getAlgorithmInstanceId)
                    .filter(StrUtil::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());
            AssertUtil.isTrue(algorithmInstanceIds.size() == 1, "提交的一组内报警配置必须属于同一个算法实例");

            // 算法实例多出参
            for (AlarmConfigItemModel alarmConfig : alarmConfigs) {
                // 参数检查
                String channelId = alarmConfig.getChannelId();
                String presetId = alarmConfig.getPresetId();
                String algorithmInstanceId = alarmConfig.getAlgorithmInstanceId();
                String outputParamId = alarmConfig.getOutputParamId();
                AssertUtil.isTrue(StrUtil.isAllNotBlank(channelId, presetId, algorithmInstanceId, outputParamId),
                        "通道ID、预置点ID、算法实例ID、出参ID不允许为空");

                String outputDataType = alarmConfig.getOutputDataType();
                AssertUtil.isTrue(EnumUtil.contains(DataType.class, outputDataType), "输出参数数据类型错误");
                DataType dataTypeEnum = EnumUtil.fromString(DataType.class, outputDataType);

                AdvanceAlarmRuleInfo ruleInfos;
                if (DataType.INTEGER.equals(dataTypeEnum) || DataType.FLOAT.equals(dataTypeEnum)) {
                    String alarmType = alarmConfig.getAlarmType();
                    AssertUtil.isTrue(EnumUtil.contains(AlarmModeType.class, alarmType), "报警类型错误");
                    // 设置非当前报警类型的规则信息为null，不进行数据库更新
                    AlarmModeType alarmModeType = EnumUtil.fromString(AlarmModeType.class, alarmType);

                    switch (alarmModeType) {
                        case OFF_LIMIT_ALARM:
                            alarmConfig.setRuleDeviation(null);
                            alarmConfig.setRuleDeep(null);
                            ruleInfos = alarmConfig.getRuleLimit();
                            break;
                        case DEVIATION_ALARM:
                            alarmConfig.setRuleDeep(null);
                            alarmConfig.setRuleLimit(null);
                            ruleInfos = alarmConfig.getRuleDeviation();
                            break;
                        case DEEP_ALARM:
                            alarmConfig.setRuleDeviation(null);
                            alarmConfig.setRuleLimit(null);
                            ruleInfos = alarmConfig.getRuleDeep();
                            break;
                        default:
                            throw new InspectionException("报警类型不合法");
                    }
                } else if (DataType.STRING.equals(dataTypeEnum)) {
                    ruleInfos = alarmConfig.getRuleStr();
                } else if (DataType.BOOLEAN.equals(dataTypeEnum)) {
                    ruleInfos = alarmConfig.getRuleBool();
                } else {
                    throw new InspectionException("输出参数数据类型不支持报警配置");
                }

                TreeSet<AlarmRuleItemModel> ruleInfo = ruleInfos.getRuleInfo();
                AssertUtil.isTrue(CollectionUtil.isNotEmpty(ruleInfo), "报警规则集合不允许为空");
                for (AlarmRuleItemModel alarmRuleItemModel : ruleInfo) {
                    AssertUtil.isTrue(Objects.nonNull(alarmRuleItemModel), "报警规则不允许为空");
                    String desc = alarmRuleItemModel.getDesc();
                    AssertUtil.isTrue(StrUtil.isBlank(desc) || desc.length() <= 64, "报警文本最大允许长度为64");
                }
                Optional<AlarmRuleItemModel> first = ruleInfo.stream().filter(AlarmRuleItemModel::isEnable).findFirst();
                if (!first.isPresent()) {
                    // 没有启动的报警规则，则需要将当前出参的实时报警恢复
                    alarmRecordService.recoverAlarm(algorithmInstanceId, outputParamId);
                }
            }
            // 先删除再新增，避免产生脏数据
            lambdaUpdate()
                    .eq(InspAlarmConfig::getAlgorithmInstanceId, algorithmInstanceIds.get(0))
                    .remove();
            List<InspAlarmConfig> collect = alarmConfigs.stream().map(item -> (InspAlarmConfig) item)
                    .collect(Collectors.toList());
            saveBatch(collect);
        }
        return reqModels;
    }
}

