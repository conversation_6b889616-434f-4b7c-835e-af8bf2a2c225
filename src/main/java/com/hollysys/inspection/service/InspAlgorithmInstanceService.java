package com.hollysys.inspection.service;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.constants.algorithm.param.DataType;
import com.hollysys.inspection.entity.*;
import com.hollysys.inspection.mapper.InspAlgorithmInstanceMapper;
import com.hollysys.inspection.utils.OutputParamUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 算法信息表(InspAlgorithmInstance)表服务接口
 *
 * <AUTHOR>
 * @since 2023-03-07 17:09:34
 */
@Service
public class InspAlgorithmInstanceService extends ServiceImpl<InspAlgorithmInstanceMapper, InspAlgorithmInstance> {

    @Resource
    private InspAlgorithmService algorithmService;

    @Resource
    private InspAlgorithmParamService algorithmParamService;

    @Resource
    private InspPresetInfoService presetInfoService;

    @Resource
    private InspAlgorithmParamInstanceService algorithmParamInstanceService;

    @Resource
    private InspGlobalVariableService inspGlobalVariableService;

    public InspAlgorithmInstance getOneById(Serializable id) {
        InspAlgorithmInstance byId = getById(id);
        if (Objects.isNull(byId)) {
            throw new InspectionException("算法实例信息不存在");
        }
        return byId;
    }

    public List<InspAlgorithmInstance> listByPresetId(String presetId) {
        LambdaQueryWrapper<InspAlgorithmInstance> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspAlgorithmInstance::getPresetId, presetId);
        // 升序
        wrapper.orderByAsc(InspAlgorithmInstance::getCreateTime);
        return list(wrapper);
    }

    public List<InspAlgorithmInstance> listByPresetIds(Collection<String> presetIds) {
        LambdaQueryWrapper<InspAlgorithmInstance> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(InspAlgorithmInstance::getPresetId, presetIds);
        return list(wrapper);
    }

    @Transactional
    public void removeByPresetIds(Collection<? extends Serializable> presetIds) {
        LambdaQueryWrapper<InspAlgorithmInstance> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(InspAlgorithmInstance::getPresetId, presetIds);
        List<InspAlgorithmInstance> algorithmInstances = list(wrapper);
        // 删除算法实例
        remove(wrapper);

        // 删除算法实例下的子算法参数
        List<String> algorithmInstanceIds = algorithmInstances.stream().map(InspAlgorithmInstance::getId).collect(Collectors.toList());
        removeByAlgorithmInstanceIds(algorithmInstanceIds);
    }

    @Transactional
    public void removeByAlgorithmInstanceIds(List<String> algorithmInstanceIds) {
        if (CollectionUtil.isEmpty(algorithmInstanceIds)) {
            return;
        }
        // 删除算法实例
        removeByIds(algorithmInstanceIds);

        // 删除算法实例参数
        LambdaQueryWrapper<InspAlgorithmParamInstance> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.in(InspAlgorithmParamInstance::getAlgorithmInstanceId, algorithmInstanceIds);
        algorithmParamInstanceService.remove(wrapper1);

        // 解绑相关变量的输出配置
        inspGlobalVariableService.unbindByAlgorithmInstanceIds(algorithmInstanceIds);
        // TODO 删除全部输出配置、报警配置、全局变量配置
    }

    @Transactional
    public InspAlgorithmInstance insertAlgorithmInstance(String presetId, String algorithmId,
                                                         List<InspSceneDefinition> sceneDefinitions) {
        InspAlgorithm algorithm = algorithmService.getOneById(algorithmId);
        // 入库算法实例
        InspAlgorithmInstance algorithmInstance = new InspAlgorithmInstance();
        BeanUtils.copyProperties(algorithm, algorithmInstance);
        algorithmInstance.setId(null);
        algorithmInstance.setPresetId(presetId);
        algorithmInstance.setAlgorithmId(algorithmId);
        algorithmInstance.setHasSaveConfig(false);
        LocalDateTime now = LocalDateTime.now();
        algorithmInstance.setCreateTime(now);
        algorithmInstance.setUpdateTime(now);
        save(algorithmInstance);

        // 入库算法参数实例，每个场景都需要入库一份
        for (InspSceneDefinition sceneDefinition : sceneDefinitions) {
            // 只需插入输入参数，因为对于输出参数不需要实例，多场景下输出参数也只有一份
            List<InspAlgorithmParam> algorithmParams = algorithmParamService.listInputByAlgorithmId(algorithmId);
            if (CollectionUtil.isNotEmpty(algorithmParams)) {
                List<InspAlgorithmParamInstance> paramInstances = algorithmParams.stream()
                        .map(InspAlgorithmParamInstance::buildByAlgorithmParam)
                        .peek(item -> {
                                    item.setAlgorithmInstanceId(algorithmInstance.getId());
                                    item.setSceneId(sceneDefinition.getId());
                                }
                        )
                        .collect(Collectors.toList());
                algorithmParamInstanceService.saveBatch(paramInstances);
            }
        }
        return algorithmInstance;
    }

    /**
     * 根据预置点ID集合，查询算法实例，并且按顺序修改算法实例名称（带序号）
     */
    public List<InspAlgorithmInstance> listWithInstName(List<String> presetIds) {
        List<InspAlgorithmInstance> result = new ArrayList<>();
        if (CollectionUtil.isEmpty(presetIds)) {
            // 查询全部的预置点ID
            List<InspPresetInfo> presetInfos = presetInfoService.list();
            presetIds = presetInfos.stream().map(InspPresetInfo::getId).collect(Collectors.toList());
        }
        if (CollectionUtil.isEmpty(presetIds)) {
            return result;
        }

        for (String presetId : presetIds) {
            List<InspAlgorithmInstance> algorithmInstances = listByPresetId(presetId);
            for (int i = 0; i < algorithmInstances.size(); i++) {
                InspAlgorithmInstance algorithmInstance = algorithmInstances.get(i);
                String algorithmInstName = OutputParamUtil.getAlgorithmInstName(algorithmInstance, i);
                algorithmInstance.setName(algorithmInstName);
            }
            result.addAll(algorithmInstances);
        }
        return result;
    }

    /**
     * 删除算法实例下全部图形参数（开启或关闭透视变换后调用）
     */
    public void removeDrawableParams(String algorithmInstanceId) {
        InspAlgorithmInstance algorithmInstance = getOneById(algorithmInstanceId);
        // 查询全部算法参数实例
        List<InspAlgorithmParamInstance> algorithmParamInstances =
                algorithmParamInstanceService.listByAlgorithmInstanceId(algorithmInstance.getId());
        if (CollectionUtil.isEmpty(algorithmParamInstances)) {
            return;
        }

        // 过滤出图形类型算法参数实例id
        List<String> paramIds = algorithmParamInstances.stream().filter(item -> DataType.drawableNames().contains(item.getDataType()))
                .map(InspAlgorithmParamInstance::getId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(paramIds)) {
            return;
        }
        // 设置参数实例值为null
        algorithmParamInstanceService.lambdaUpdate()
                .set(InspAlgorithmParamInstance::getValue, null)
                .in(InspAlgorithmParamInstance::getId, paramIds)
                .update();
        // 修改当前算法实例为未配置状态
        algorithmInstance.setHasSaveConfig(false);
        updateById(algorithmInstance);
    }
}

