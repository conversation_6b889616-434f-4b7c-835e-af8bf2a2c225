package com.hollysys.inspection.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hollysys.inspection.constants.InspConstants;
import com.hollysys.inspection.constants.algorithm.ExecuteResultStatus;
import com.hollysys.inspection.entity.InspRecordAlgorithm;
import com.hollysys.inspection.mapper.InspRecordAlgorithmMapper;
import com.hollysys.inspection.model.algorithm.execute.AlgorithmExecuteRsp;
import com.hollysys.inspection.model.algorithm.execute.record.AlgorithmRecordItemModel;
import com.hollysys.inspection.utils.MinioUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 算法执行记录表服务接口
 *
 * <AUTHOR>
 * @since 2023-02-01 15:36:00
 */
@Service
@Slf4j
public class InspRecordAlgorithmService extends ServiceImpl<InspRecordAlgorithmMapper, InspRecordAlgorithm> {

    @Resource
    private InspAlarmRecordService alarmRecordService;

    public List<InspRecordAlgorithm> listByRecordPresetId(String recordPresetId) {
        return lambdaQuery().eq(InspRecordAlgorithm::getRecordPresetId, recordPresetId)
                .orderByDesc(InspRecordAlgorithm::getStartTime)
                .list();
    }

    public void removeByChannelId(String channelId) {
        lambdaUpdate()
                .eq(InspRecordAlgorithm::getChannelId, channelId).remove();
    }

    public void removeByPresetId(String presetId) {
        lambdaUpdate()
                .eq(InspRecordAlgorithm::getPresetId, presetId).remove();
    }

    public void removeByAlgorithmInstId(String algorithmInstId) {
        lambdaUpdate()
                .eq(InspRecordAlgorithm::getAlgorithmInstanceId, algorithmInstId).remove();
    }

    public void removeByPresetIds(List<String> presetIds) {
        lambdaUpdate()
                .in(InspRecordAlgorithm::getPresetId, presetIds).remove();
    }

    public IPage<AlgorithmRecordItemModel> pageAllAlgorithmRecords(IPage<InspRecordAlgorithm> page, String nodeId, String startTime,
                                            String endTime, String searchStr, ExecuteResultStatus executeResultStatus) {
        Map<String, String> allIdNameMap = alarmRecordService.getAllIdNameMap(nodeId);
        if (CollectionUtil.isEmpty(allIdNameMap)) {
            return page.convert(inspAlarmRecord -> new AlgorithmRecordItemModel());
        }
        // 按searchStr模糊
        Map<String, String> idNameMapSearch = alarmRecordService.searchIdNameMap(allIdNameMap, searchStr);
        // 数据库查询
        IPage<InspRecordAlgorithm> recordPage;
        LambdaQueryChainWrapper<InspRecordAlgorithm> lambdaWrapper = lambdaQuery();
        if (StrUtil.isNotBlank(startTime)) {
            lambdaWrapper.gt(InspRecordAlgorithm::getStartTime, DateUtil.parse(startTime, InspConstants.yyyy_MM_ddHHmmss));
        }
        if (StrUtil.isNotBlank(endTime)) {
            lambdaWrapper.lt(InspRecordAlgorithm::getStartTime, DateUtil.parse(endTime, InspConstants.yyyy_MM_ddHHmmss));
        }
        // 按照成功或失败查询记录
        if (Objects.nonNull(executeResultStatus)) {
            lambdaWrapper.eq(InspRecordAlgorithm::getResultStatus, executeResultStatus.name());
        }
        // 按创建时间降序排列，新的执行记录排在前面
        lambdaWrapper.orderByDesc(InspRecordAlgorithm::getStartTime);
        if (Objects.isNull(idNameMapSearch)) {
            // idNameMap == null 说明前端未输入搜索文本
            recordPage = lambdaWrapper
                    .in(InspRecordAlgorithm::getChannelId, allIdNameMap.keySet())
                    .in(InspRecordAlgorithm::getPresetId, allIdNameMap.keySet())
                    .in(InspRecordAlgorithm::getAlgorithmInstanceId, allIdNameMap.keySet())
                    .page(page);
        } else {
            // idNameMap != null 说明前端输入了搜索文本
            if (CollectionUtil.isEmpty(idNameMapSearch)) {
                // 搜索文本未未命中数据
                recordPage = page;
                recordPage.setRecords(new ArrayList<>());
            } else {
                recordPage = lambdaWrapper
                        .and(wrapper -> wrapper
                                .in(InspRecordAlgorithm::getChannelId, idNameMapSearch.keySet())
                                .or()
                                .in(InspRecordAlgorithm::getPresetId, idNameMapSearch.keySet())
                                .or()
                                .in(InspRecordAlgorithm::getAlgorithmInstanceId, idNameMapSearch.keySet())
                        )
                        .page(page);
            }
        }
        // 对象转化
        return recordPage.convert(executeRecord -> {
            AlgorithmRecordItemModel algorithmRecordItemModel = new AlgorithmRecordItemModel();
            BeanUtil.copyProperties(executeRecord, algorithmRecordItemModel);
            // 设置通道名
            String channelId = executeRecord.getChannelId();
            String channelName = allIdNameMap.get(channelId);
            algorithmRecordItemModel.setChannelName(channelName);
            // 设置预置点名
            String presetId = executeRecord.getPresetId();
            String presetName = allIdNameMap.get(presetId);
            algorithmRecordItemModel.setPresetName(presetName);
            // 设置算法实例名
            String algorithmInstanceId = executeRecord.getAlgorithmInstanceId();
            String algorithmInstanceName = allIdNameMap.get(algorithmInstanceId);
            algorithmRecordItemModel.setAlgorithmInstanceName(algorithmInstanceName);
            // 图片路径转化为相对路径
            List<String> inputImages = algorithmRecordItemModel.getInputImages();
            algorithmRecordItemModel.setInputImages(MinioUtil.urlsToRelative(inputImages));
            String outputImage = algorithmRecordItemModel.getOutputImage();
            algorithmRecordItemModel.setOutputImage(MinioUtil.urlToRelative(outputImage));
            // AlgorithmExecuteRsp转化为map结构
            AlgorithmExecuteRsp executeResult = executeRecord.getExecuteResult();
            if (Objects.nonNull(executeResult)) {
                Map<String, Object> valueMap = executeResult.toValueMap();
                algorithmRecordItemModel.setAlgorithmValue(valueMap);
            }
            return algorithmRecordItemModel;
        });
    }
}

