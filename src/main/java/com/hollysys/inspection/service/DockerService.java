package com.hollysys.inspection.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hollysys.inspection.config.exceptions.InspectionException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class DockerService {

    @Value("${portainer.username}")
    private String username;
    @Value("${portainer.password}")
    private String password;
    @Value("${portainer.auth-url}")
    private String authUrl;
    @Value("${portainer.service-list-url}")
    private String serviceListUrl;
    @Value("${portainer.service-restart-url}")
    private String serviceRestartUrl;

    @Resource
    private RestTemplate restTemplate;

    private synchronized String getToken(String serviceName) {
        Map<String, Object> authMap = new HashMap<>();
        authMap.put("username", username);
        authMap.put("password", password);
        ResponseEntity<JSONObject> post = restTemplate.postForEntity(authUrl, authMap, JSONObject.class);
        JSONObject body = post.getBody();
        if (body == null) {
            log.info("{}稍后再试", serviceName);
            throw new RuntimeException("获取token 异常 ，稍后再试");
        }
        log.info("{}获取token成功", serviceName);
        return post.getBody().getString("jwt");
    }

    /**
     * 重启指定docker容器
     * @param serviceName 容器名字
     */
    public void restartService(String serviceName) {
        try {
            String jwtToken = getToken(serviceName);
            HttpHeaders authHeader = new HttpHeaders();
            authHeader.add("Authorization", "Bearer " + jwtToken);
            HttpEntity<JSONObject> entity = new HttpEntity<>(null, authHeader);
            ResponseEntity<JSONArray> exchange = restTemplate.exchange(serviceListUrl, HttpMethod.GET, entity, JSONArray.class);
            JSONArray resultList = exchange.getBody();
            if (resultList == null) {
                log.error("获取的容器列表为空");
                throw new InspectionException("获取的容器列表为空");
            }
            for (Object o : resultList) {
                LinkedHashMap<String, Object> result = (LinkedHashMap<String, Object>) o;
                List<String> names = (List<String>) result.get("Names");

                // 确保 names 列表不为空
                if (names != null && !names.isEmpty()) {
                    // 获取容器名称，并移除开头的斜杠
                    String containerName = names.get(0).replace("/", "");

                    if (containerName.equalsIgnoreCase(serviceName)) {
                        String id = (String) result.get("Id");
                        String restartUrl = String.format(serviceRestartUrl, id);

                        // 发送重启请求
                        restTemplate.exchange(restartUrl, HttpMethod.POST, entity, Void.class);
                        log.debug("{}重启成功", serviceName);
                        return;
                    }
                }
            }

            // 如果遍历完所有容器，仍未找到匹配的，则抛出异常
            log.error("未找到名为 {} 的容器", serviceName);
            throw new InspectionException("未找到匹配的容器");

        } catch (Exception e) {
            log.error("{}重启失败", serviceName, e);
        }
    }
}
