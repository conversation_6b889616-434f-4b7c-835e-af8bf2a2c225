package com.hollysys.inspection.service.sso;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.config.sso.HsmAuthProperties;
import com.hollysys.inspection.model.sso.*;
import com.hollysys.inspection.service.DataBaseNetworkService;
import com.hollysys.inspection.utils.MyHttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Description:
 * @Title: HsmAuthServiceImpl
 * @Package: com.hollysys.smartfactory.holliics.manager.service.impl
 * @Author: wuyuanbing
 * @CreateTime: 2025/3/4 14:27
 */
@Service
@Slf4j
public class HsmAuthTokenService {

    /**
     * 用来缓存refresh_Token, 刷新token时使用
     */
    private String refreshTokenCache = "";

    @Value("${db-base.auth-srv.enabled}")
    private Boolean authSrvEnabled;

    @Resource
    private HsmAuthProperties hsmAuthProperties;

    @Resource
    private DataBaseNetworkService dataBaseNetworkService;

    /**
     * 通过code交换token
     */
    public Object exchangeToken(String code, HttpServletResponse response) {
        try {
            if (StrUtil.isEmpty(code)) {
                throw new InspectionException("接收到的code为空，不可用");
            }
            String exAuthTokenUrl = hsmAuthProperties.getSsoUrl().concat(":")
                    .concat(hsmAuthProperties.getSso().getPort().toString())
                    .concat(hsmAuthProperties.getSso().getTokenPath());

            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("grant_type", "authorization_code");
            paramMap.put("client_id", hsmAuthProperties.getClientId());
            if (!StrUtil.isEmpty(hsmAuthProperties.getSecret())) {
                paramMap.put("client_secret", hsmAuthProperties.getSecret());
            }
            paramMap.put("code", code);
            String exTokenResp;
            try {
                exTokenResp = MyHttpUtil.get(exAuthTokenUrl, paramMap);
            } catch (Exception e) {
                // 捕获网络异常（如连接失败、超时）
                throw new InspectionException("认证服务响应异常，即将跳转底座登录页");
            }
            ExTokenResp exTokenModel = JSON.parseObject(exTokenResp, ExTokenResp.class);
            if (Objects.isNull(exTokenModel)) {
                throw new InspectionException("服务异常，交换token失败");
            }
            if (!Objects.equals(0, exTokenModel.getCode())) {
                throw new InspectionException("服务异常，交换token失败");
            }
            refreshTokenCache = exTokenModel.getRefresh_token();
            String authToken = exTokenModel.getAccess_token();
            if (StrUtil.isEmpty(authToken)) {
                throw new InspectionException("Token获取为空");
            }

            // 重定向到前端
            Map<String, String> redirectParam = new HashMap<>();
            redirectParam.put("access_token", authToken);
            redirectParam.put("expires_in", parseExpires(exTokenModel.getAccess_token_expires()).toString());
            String redirectUrl = parseParam(hsmAuthProperties.getRedirects(), redirectParam);
            log.debug("redirectUrl: {}", redirectUrl);
            response.sendRedirect(redirectUrl);
            return null;
        } catch (Exception e) {
            log.error("token交换异常", e);
            try {
                String redirectErrorUrl = hsmAuthProperties.getRedirects();
                response.sendRedirect(redirectErrorUrl);
                return null;
            } catch (Exception ioException) {
                log.error("重定向失败", ioException);
                throw new InspectionException("重定向到失败页面异常");
            }
        }
    }

    /**
     * 刷新token
     */
    public RefreshToken refreshToken() {
        String refreshUrl = hsmAuthProperties.getSsoUrl().concat(":")
                .concat(hsmAuthProperties.getSso().getPort().toString())
                .concat(hsmAuthProperties.getSso().getRefreshTokenPath());
        try {
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("grant_type", "refresh_token");
            paramMap.put("client_id", hsmAuthProperties.getClientId());
            if (!StrUtil.isEmpty(hsmAuthProperties.getSecret())) {
                paramMap.put("client_secret", hsmAuthProperties.getSecret());
            }
            paramMap.put("refresh_token", refreshTokenCache);

            if (StrUtil.isEmpty(refreshTokenCache)) {
                throw new InspectionException("后端刷新Token丢失");
            }
            String exTokenResp;
            try {
                exTokenResp = MyHttpUtil.get(refreshUrl, paramMap);
            } catch (Exception e) {
                // 捕获网络异常（如连接失败、超时）
                throw new InspectionException("认证服务响应异常，即将跳转底座登录页");
            }
            ExTokenResp exTokenModel = JSON.parseObject(exTokenResp, ExTokenResp.class);
            if (Objects.isNull(exTokenModel)) {
                throw new InspectionException("服务异常，交换token失败");
            }
            if (!Objects.equals(0, exTokenModel.getCode())) {
                throw new InspectionException("服务异常，交换token失败");
            }
            RefreshToken refreshToken = new RefreshToken();
            refreshToken.setAccess_token(exTokenModel.getAccess_token());
            refreshToken.setExpires_in(parseExpires(exTokenModel.getAccess_token_expires()).toString());
            refreshTokenCache = exTokenModel.getRefresh_token();
            return refreshToken;
        } catch (Exception e) {
            log.error("刷新token时服务发生异常", e);
            throw new InspectionException("服务异常，交换token失败");
        }
    }

    /**
     * 用户登出
     */
    public UserInfoModel getUserInfo() {
        // 获取认证Token
        String tokenValue = StpUtil.getTokenValue();
        if (StrUtil.isEmpty(tokenValue)) {
            return null;
        }
        GraphqlBody gl = new GraphqlBody();
        gl.setVariables(new HashMap<>());
        gl.setQuery(hsmAuthProperties.getSso().getUserInfoQuery());

        String url = hsmAuthProperties.getSsoUrl()
                .concat(hsmAuthProperties.getSso().getPath());
        // 组装接口请求头
        String getUserInfoResp;
        try {
            getUserInfoResp = MyHttpUtil.post(url, gl, tokenValue);
        } catch (Exception e) {
            // 捕获网络异常（如连接失败、超时）
            throw new InspectionException("认证服务响应异常，即将跳转底座登录页");
        }
        if (StrUtil.isEmpty(getUserInfoResp)) {
            log.error("获取平台用户信息失败，返回结果为空，url = {}，param = {}", url, JSONUtil.toJsonStr(gl));
            return null;
        }
        ApiRespModel bean = JSONUtil.toBean(getUserInfoResp, ApiRespModel.class);
        if (Objects.isNull(bean)) {
            log.error("获取平台用户信息失败，返回结果转化对象为空，url = {}，param = {}，result = {}", url,
                    JSONUtil.toJsonStr(gl), getUserInfoResp);
            return null;
        }
        if (!bean.isSuccess()) {
            log.error("获取平台用户信息失败，url = {}，param = {}，result = {}", url,
                    JSONUtil.toJsonStr(gl), getUserInfoResp);
            return null;
        }

        return JSONUtil.toBean(bean.getData(), UserInfoModel.class);
    }

    /**
     * 用户登出
     */
    public Object logOut(HttpServletRequest httpRequest) {
        try {
            String logoutUrl = hsmAuthProperties.getSsoUrl().concat(hsmAuthProperties.getSso().getPath());
            String token = httpRequest.getHeader(HttpHeaders.AUTHORIZATION);
            String decodeAccessToken = URLDecoder.decode(token, StandardCharsets.UTF_8.name());

            GraphqlBody gl = new GraphqlBody();
            gl.setQuery(hsmAuthProperties.getSso().getSignOutQuery());

            // 组装接口请求头
            String authValidResp;
            try {
                authValidResp = MyHttpUtil.post(logoutUrl, gl, decodeAccessToken);
            } catch (Exception e) {
                // 捕获网络异常（如连接失败、超时）
                throw new InspectionException("认证服务响应异常，即将跳转底座登录页");
            }
            if (StrUtil.isNotBlank(authValidResp)) {
                JSONObject authObject = JSON.parseObject(authValidResp);
                Integer status = authObject.getInteger("code");
                if (!Objects.equals(0, status)) {
                    throw new InspectionException("认证服务响应异常, " + authObject.getString("message"));
                }
                JSONObject data = authObject.getJSONObject("data");
                if (Objects.isNull(data)) {
                    throw new InspectionException("认证服务响应异常, " + authObject.getString("message"));
                }
                JSONObject signOutObject = data.getJSONObject("signOut");
                if (Objects.isNull(signOutObject)) {
                    throw new InspectionException("认证服务响应异常, " + authObject.getString("message"));
                }
                boolean signOut = signOutObject.getBooleanValue("data", false);

                if (signOut) {
                    refreshTokenCache = "";
                    return "操作成功";
                }
                throw new InspectionException("登出失败");
            }
            throw new InspectionException("服务异常，登出接口调用失败");
        } catch (Exception e) {
            log.error("调用登出接口异常", e);
            throw new InspectionException("登出失败");
        }
    }

    /**
     * 校验token是否有效
     */
    public void validSsoToken(String ssoToken) {
        String url = hsmAuthProperties.getSsoUrl()
                .concat(hsmAuthProperties.getSso().getPath());
        // 组装接口请求体
        GraphqlBody ql = new GraphqlBody();
        ql.setQuery(hsmAuthProperties.getSso().getQuery());
        String authValidResp;
        try {
            authValidResp = MyHttpUtil.post(url, ql, ssoToken);
        } catch (Exception e) {
            throw new InspectionException("认证服务响应异常，即将跳转底座登录页");
        }
        if (!StrUtil.isEmpty(authValidResp)) {
            JSONObject authObject = JSON.parseObject(authValidResp);
            Integer status = authObject.getInteger("code");
            if (!Objects.equals(0, status)) {
                throw new InspectionException("认证服务响应异常，即将跳转底座登录页 " + authObject.getString("message"));
            }
            JSONObject data = authObject.getJSONObject("data");
            if (Objects.isNull(data)) {
                throw new InspectionException("认证服务响应异常,即将跳转底座登录页");
            }
            JSONObject validToken = data.getJSONObject("validToken");
            if (Objects.isNull(validToken)) {
                throw new InspectionException("认证服务响应异常,即将跳转底座登录页");
            }
            boolean isValid = validToken.getBooleanValue("data", false);

            if (isValid) {
                return;
                // todo 当认证成功后刷新token，目前单点登录方案不需要刷新token
                // refresh(token)
            }
            throw new NotLoginException("Token 已失效,即将跳转底座登录页", null, null);
        }
        throw new InspectionException("认证服务响应异常,即将跳转底座登录页");
    }

    /**
     * description: 将参数整理到路径上
     *
     * @param
     * @return java.lang.String
     * <AUTHOR>
     * @date 2025/3/4 15:45
     */
    private String parseParam(String url, Map<String, String> paramMap) {
        if (Objects.isNull(paramMap)) {
            return url;
        }

        String parseUrl = url;
        for (Map.Entry<String, String> entry : paramMap.entrySet()) {
            String paramStr = entry.getKey().concat("=").concat(entry.getValue());
            String temp = parseUrl.endsWith("&") ? "" : "&";
            parseUrl = parseUrl.concat(parseUrl.contains("?") ? temp : "?").concat(paramStr);
        }
        return parseUrl;
    }

    /**
     * description: 获取有效时间，并转换为时间戳
     *
     * @param expires
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2025/3/4 17:11
     */
    private Long parseExpires(String expires) {
        Long seconds = 0L;
        if (!StrUtil.isEmpty(expires)) {
            if (expires.endsWith("h")) {
                try {
                    String pattern = "(.*?)h";
                    Pattern r = Pattern.compile(pattern);
                    Matcher m = r.matcher(expires);
                    if (m.find()) {
                        String number = m.group(1);
                        Long numberL = Long.parseLong(number);
                        seconds = numberL * 3600;
                    }
                } catch (Exception e) {
                    log.error("获取有效时间出错，{}", e.getMessage());
                    seconds = 0L;
                }
            }
        }

        Instant instant = Instant.now();
        return instant.toEpochMilli() + seconds * 1000;
    }

    /**
     * 获取SSO配置信息，前端使用
     */
    public SsoInfoModel getSsoConfig() {
        // 先测试底座IP地址是否可用
        dataBaseNetworkService.testDataBaseLogin(hsmAuthProperties.getServerIp());
        // 然后校验客户端id和密钥是否正确
        dataBaseNetworkService.validateDbBaseInfo(hsmAuthProperties.getClientId(), hsmAuthProperties.getSecret(), hsmAuthProperties.getServerIp());
        SsoInfoModel ssoInfoModel = new SsoInfoModel();
        String url = hsmAuthProperties.getSsoUrl().concat(":")
                .concat(hsmAuthProperties.getSso().getPort().toString());
        ssoInfoModel.setEnable(authSrvEnabled);
        ssoInfoModel.setPlatformUrl(url);
        ssoInfoModel.setClientId(hsmAuthProperties.getClientId());
        return ssoInfoModel;
    }
}
