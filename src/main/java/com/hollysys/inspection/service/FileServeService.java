package com.hollysys.inspection.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ArrayUtil;
import com.hollysys.inspection.constants.FileDirEnum;
import com.hollysys.inspection.utils.LocalFileServerUtil;
import com.hollysys.inspection.utils.MinioUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FileServeService {

    @Value("${file-server.root-dir}")
    private String fileRootDir;

    @Value("${schedule.remove-temp-dir.expire}")
    private Integer expire;

    @Value("${file-server.minio.endpoint}")
    private String minioEndpoint;

    @Value("${file-server.minio.access-key}")
    private String minioAk;

    @Value("${file-server.minio.secret-key}")
    private String minioSk;

    @Value("${file-server.minio.bucket}")
    private String minioBucket;

    @Value("${file-server.files-mapping-dir}")
    private String filesMapping;

    @Value("${file-server.minio.temp-file-expiration-days}")
    private int tempExpirationDays;

    @Value("${system-backup.execute-record.backup-file-expiration-days}")
    private int executeBackupFileExpirationDays;

    @Value("${system-backup.operation-log-record.backup-file-expiration-days}")
    private int operationLogBackupFileExpirationDays;

    @Value("${system-backup.alarm-record.backup-file-expiration-days}")
    private int alarmBackupFileExpirationDays;

    /**
     * 初始化文件服务相关逻辑
     */
    public void init() {
        // Minio服务初始化
        MinioUtil.init(minioEndpoint, minioAk, minioSk, minioBucket, filesMapping, tempExpirationDays,
                executeBackupFileExpirationDays, operationLogBackupFileExpirationDays, alarmBackupFileExpirationDays);

        // 本地文件服务初始化
        LocalFileServerUtil.init(fileRootDir + FileDirEnum.TEMP.name());
    }

    /**
     * 临时文件夹定时清除逻辑
     * 两小时清除一次，清除两小时之前创建的问题
     */
    @Scheduled(cron = "${schedule.remove-temp-dir.cron}")
    public void clearTemp() {
        String tempDir = fileRootDir + FileDirEnum.TEMP.name();
        List<File> files = FileUtil.loopFiles(tempDir, FileUtil::isFile);
        if (CollectionUtil.isEmpty(files)) {
            log.debug("临时目录下文件列表为空，跳过定时清除操作");
            return;
        }

        // 计算两小时之前的时间
        DateTime dateNow = DateUtil.date();
        DateTime dateTime = DateUtil.offsetHour(dateNow, expire);

        // 过滤满足时间范围的文件,判断文件的最后更新时间
        List<String> outOffTimeFiles = files.stream().filter(item -> {
            Date lastModifiedTime = FileUtil.lastModifiedTime(item);
            return DateUtil.compare(lastModifiedTime, dateTime) < 1;
        }).map(FileUtil::getAbsolutePath).collect(Collectors.toList());
        log.debug("当前系统时间：{}，查找文件最后更新时间在{}之前的文件个数为：{}", dateNow, dateTime, outOffTimeFiles.size());

        // 执行文件删除
        if (CollectionUtil.isNotEmpty(outOffTimeFiles)) {
            outOffTimeFiles.forEach(FileUtil::del);
        }

        // 清理TEMP下的空文件夹（除opt、cv、depl文件夹外）
        File fileDir = FileUtil.file(tempDir);
        File[] filesArr = fileDir.listFiles();
        if (ArrayUtil.isEmpty(filesArr)) {
            return;
        }
        List<String> list = Arrays.asList("opt", "cv", "depl");
        for (File file : filesArr) {
            String name = file.getName();
            if (list.contains(name)) {
                continue;
            }
            FileUtil.cleanEmpty(file);
        }
    }
}
