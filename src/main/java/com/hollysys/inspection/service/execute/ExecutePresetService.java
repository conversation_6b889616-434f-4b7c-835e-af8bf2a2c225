package com.hollysys.inspection.service.execute;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.hollysys.inspection.config.exceptions.execute.ExecuteBeforeException;
import com.hollysys.inspection.constants.CameraType;
import com.hollysys.inspection.entity.*;
import com.hollysys.inspection.listener.ExecutePresetListener;
import com.hollysys.inspection.model.algorithm.execute.AlgorithmExecuteRsp;
import com.hollysys.inspection.model.algorithm.osd.OSDItem;
import com.hollysys.inspection.model.channel.ScaleModel;
import com.hollysys.inspection.model.context.ExecuteAlgorithmCtx;
import com.hollysys.inspection.model.context.ExecutePresetCtx;
import com.hollysys.inspection.service.*;
import com.hollysys.inspection.service.protocol.impl.CameraCtrlProxyService;
import com.hollysys.inspection.utils.AssertUtil;
import com.hollysys.inspection.utils.OsdUtil;
import com.hollysys.inspection.utils.SleepUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * 预置点执行服务层
 */
@Slf4j
@Service
public class ExecutePresetService {

    @Resource
    private ExecutePresetListener executePresetListener;

    @Resource
    private InspProjectService projectService;

    @Resource
    private PythonServerService pythonServerService;

    @Resource
    private CameraCtrlProxyService cameraCtrlProxyService;

    @Resource
    private InspChannelInfoService inspChannelInfoService;

    @Resource
    private ExecuteAlgorithmService executeAlgorithmService;

    @Resource
    private InspSceneDefinitionService sceneDefinitionService;

    @Resource
    private InspPresetInfoService presetInfoService;

    @Resource
    private InspAlgorithmInstanceService algorithmInstanceService;

    @Resource
    private InspAlgorithmParamInstanceService algorithmParamInstanceService;

    @Resource
    private ProjectTreeNodeService projectTreeNodeService;

    /**
     * 预置点执行入口
     *
     * @param taskId    任务ID
     * @param clientKey 前端调用时,前端唯一标识
     */
    public void executePreset(String taskId, String clientKey, String presetId) {
        InspPresetInfo presetInfo = presetInfoService.getOneById(presetId);
        ExecutePresetCtx context = new ExecutePresetCtx();
        context.setClientKey(clientKey);
        context.setTaskId(taskId);
        context.setProject(projectService.getFirstOne());
        context.setPresetId(presetId);
        String channelId = presetInfo.getChannelId();
        context.setChannelId(channelId);
        context.setPresetInfo(presetInfo);
        context.setPresetTreeNodeInfo(projectTreeNodeService.getOneById(presetId));

        try {
            executePresetListener.onStart(context);
        } catch (Exception exception) {
            log.error("预置点执行onStart回调方法出现异常,presetId = {}", presetId, exception);
        }

        try {
            List<OSDItem> allOsdInfo = doExecutePreset(context, presetInfo);
            context.setAllOsdInfo(allOsdInfo);

            try {
                executePresetListener.beforeWaitPostDelay(context);
            } catch (Exception exception) {
                log.error("预置点执行beforeWaitPostDelay回调方法出现异常,presetId = {}", presetId, exception);
            }

            try {
                executePresetListener.onFinish(context);
            } catch (Exception exception) {
                log.error("预置点执行onFinish回调方法出现异常,presetId = {}", presetId, exception);
            }
        } catch (Exception exception) {
            log.error("预置点执行出现异常,presetId = {}", presetId, exception);
            try {
                executePresetListener.onException(context, exception);
            } catch (Exception e) {
                log.error("预置点执行onException回调方法出现异常,presetId = {}", presetId, exception);
            }
        }


        // 执行后等待（后置时间）
        int postExecuteDelay = presetInfo.getPostExecuteDelay();
        if (postExecuteDelay > 0) {
            String logStr = StrUtil.format("执行预置点[{}]完成后等待{}秒...", presetId, postExecuteDelay);
            log.info(logStr);
            SleepUtil.sleepSecond(postExecuteDelay);
        }
    }

    private List<OSDItem> doExecutePreset(ExecutePresetCtx context, InspPresetInfo presetInfo) {
        String presetId = presetInfo.getId();
        // 查询全部场景
        List<InspSceneDefinition> sceneDefinitions = sceneDefinitionService.listByPresetId(presetId);
        AssertUtil.isTrue(CollectionUtil.isNotEmpty(sceneDefinitions), () -> new ExecuteBeforeException("预置点执行失败,预置点下场景为空"));
        // 查询全部算法实例
        List<InspAlgorithmInstance> algorithmInstances = algorithmInstanceService.listByPresetId(presetId);
        AssertUtil.isTrue(CollectionUtil.isNotEmpty(algorithmInstances), () -> new ExecuteBeforeException("预置点执行失败,预置点下算法实例为空"));

        // 获取预置点的验证次数，按照验证次数进行多次执行（循环执行，执行成功则退出循环）
        int validTimes = presetInfo.getValidTimes();
        List<InspAlgorithmInstance> algorithmInstListCopy = CollectionUtil.newArrayList(algorithmInstances);
        List<InspAlgorithmInstance> succeedList = new ArrayList<>();

        InspSceneDefinition firstScene = sceneDefinitions.get(0);
        List<OSDItem> allOsdInfo = new ArrayList<>();
        List<AlgorithmExecuteRsp> successResultList = new ArrayList<>();

        ScaleModel lastDisplayResolution = null;
        for (int i = 0; i < validTimes; i++) {
            int time = i + 1;
            try {
                executePresetListener.onTimesStart(context, time);
            } catch (Exception e) {
                log.error("预置点执行onTimesStart回调方法出现异常,presetId = {}", presetId, e);
            }

            try {
                // 获取当前通道视频分辨率，与场景分辨率校验
                InspChannelInfo channelInfo = inspChannelInfoService.getOneById(presetInfo.getChannelId());
                ScaleModel displayResolution = cameraCtrlProxyService.getDisplayResolution(channelInfo);
                lastDisplayResolution = displayResolution;
                AssertUtil.isTrue(!sceneDefinitionService.isNotEquals(firstScene.getBenchShape(), displayResolution),
                        () -> {
                            String format = StrUtil.format("当前场景分辨率{}与当前视频流实际分辨率{}不一致",
                                    firstScene.getBenchShape(), displayResolution);
                            return new ExecuteBeforeException(format);
                        }
                );
                // 记录执行成功的算法集合，因为执行过程中会出现异常，导致失败的算法没有办法入failedList
                List<AlgorithmExecuteRsp> resultList = executeAtTimes(context, presetInfo, sceneDefinitions,
                        algorithmInstListCopy, succeedList);
                // 收集OSD
                for (AlgorithmExecuteRsp algorithmExecuteRsp : resultList) {
                    List<OSDItem> osdItems = CollectionUtil.emptyIfNull(algorithmExecuteRsp.getOsdInfo());
                    allOsdInfo.addAll(osdItems);
                }
                successResultList.addAll(resultList);

                try {
                    executePresetListener.onTimesFinish(context, time);
                } catch (Exception e) {
                    log.error("预置点执行onTimesFinish回调方法出现异常,presetId = {}", presetId, e);
                }

            } catch (Exception exception) {
                log.error("预置点执行第[{}]出现异常,presetId = {}", time, presetId, exception);
                try {
                    executePresetListener.onTimesException(context, time, exception);
                } catch (Exception e) {
                    log.error("预置点执行onTimesException回调方法出现异常,presetId = {}", presetId, e);
                }
            }

            if (algorithmInstListCopy.size() == succeedList.size()) {
                // 算法实例全部执行成功，则退出循环
                break;
            }
            // 找出algorithmInstListCopy有的，succeedList中没有的
            Collection<InspAlgorithmInstance> failedList = CollectionUtil.subtract(algorithmInstListCopy, succeedList);
            // 准备进入下次循环
            algorithmInstListCopy = CollectionUtil.newArrayList(failedList);
            succeedList.clear();
        }

        // 组装预置点描述和结果值OSD（左下角）
        InspProjectTreeNode treeNode = projectTreeNodeService.getOneById(presetId);
        OSDItem descOsdItem = OsdUtil.getDescOsdItem(treeNode.getDes(), lastDisplayResolution, successResultList);
        if (Objects.nonNull(descOsdItem)) {
            allOsdInfo.add(descOsdItem);
        }

        return allOsdInfo;
    }

    /**
     * 单次执行预置点，返回成功的执行结果对象集合
     */
    private List<AlgorithmExecuteRsp> executeAtTimes(ExecutePresetCtx presetCtx, InspPresetInfo presetInfo,
                                                     List<InspSceneDefinition> sceneList,
                                                     List<InspAlgorithmInstance> algorithmInstances,
                                                     List<InspAlgorithmInstance> succeedList) {
        String channelId = presetInfo.getChannelId();
        InspChannelInfo channelInfoById = inspChannelInfoService.getOneById(channelId);

        if (CameraType.PTZ_CAMERA.name().equals(channelInfoById.getDeviceType())) {
            // 跳转预置点坐标
            boolean gotoPreset = cameraCtrlProxyService.gotoPreset(channelInfoById, presetInfo);
            AssertUtil.isTrue(gotoPreset, () -> new ExecuteBeforeException("跳转预置点位置失败"));
        }

        // 等待前置延时时间 防止摄像头抖动影响算法结果
        int staySecond = presetInfo.getPreExecuteDelay();
        SleepUtil.sleepSecond(staySecond);

        List<AlgorithmExecuteRsp> resultList = new ArrayList<>();
        // 遍历执行算法实例
        for (InspAlgorithmInstance algorithmInstance : algorithmInstances) {

            ExecuteAlgorithmCtx algorithmCtx = new ExecuteAlgorithmCtx();
            algorithmCtx.setAlgorithmInstance(algorithmInstance);
            algorithmCtx.setChannelId(presetInfo.getChannelId());
            String presetId = presetInfo.getId();
            algorithmCtx.setPresetId(presetId);
            algorithmCtx.setAlgorithmInstanceId(algorithmInstance.getId());

            try {
                executePresetListener.onAlgorithmStart(presetCtx, algorithmCtx);
            } catch (Exception e) {
                log.error("预置点执行onAlgorithmStart回调方法出现异常,presetId = {}", presetId, e);
            }

            try {
                AlgorithmExecuteRsp algorithmExecuteRsp = executeAlgorithmService.executeAlgorithm(algorithmCtx,
                        algorithmInstance,
                        sceneList, null, null, algorithmInstance.getRoi());
                if (BooleanUtil.isTrue(algorithmExecuteRsp.getIsSuccess())) {
                    resultList.add(algorithmExecuteRsp);
                    succeedList.add(algorithmInstance);
                }

                algorithmCtx.setAlgorithmExecuteRsp(algorithmExecuteRsp);
                try {
                    executePresetListener.onAlgorithmFinish(presetCtx, algorithmCtx);
                } catch (Exception e) {
                    log.error("预置点执行onAlgorithmFinish回调方法出现异常,presetId = {}", presetId, e);
                }
            } catch (Exception exception) {
                log.error("预置点[{}]执行算法[{}]出现异常", presetId, algorithmInstance.getId(), exception);
                try {
                    executePresetListener.onAlgorithmException(presetCtx, algorithmCtx, exception);
                } catch (Exception e) {
                    log.error("预置点执行onAlgorithmException回调方法出现异常,presetId = {}", presetId, e);
                }
            }
        }
        return resultList;
    }
}
