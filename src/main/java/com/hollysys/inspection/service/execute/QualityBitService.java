package com.hollysys.inspection.service.execute;


import cn.hutool.core.util.BooleanUtil;
import com.hollysys.inspection.config.redis.RedisHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 质量位相关逻辑服务层
 * false为好点
 * true为坏点
 */
@Slf4j
@Service
public class QualityBitService {

    private static final String QUALITY_BIT_KEY_PREFIX = "server_quality_bit:";
    private static final String QUALITY_BIT_KEY_SERVICE = "service";
    private static final String QUALITY_BIT_KEY_OPT = "opt";
    private static final String QUALITY_BIT_KEY_CV = "cv";
    private static final String QUALITY_BIT_KEY_DEPL = "depl";

    @Resource
    private RedisHelper redisHelper;

    /**
     * 获取服务间整体质量位
     */
    public boolean getServerQualityBit() {
        Boolean aBoolean1 = getValeFromRedis(QUALITY_BIT_KEY_SERVICE);
        Boolean aBoolean2 = getValeFromRedis(QUALITY_BIT_KEY_OPT);
        Boolean aBoolean3 = getValeFromRedis(QUALITY_BIT_KEY_CV);
        Boolean aBoolean4 = getValeFromRedis(QUALITY_BIT_KEY_DEPL);

        log.debug("getServerQualityBit... result = {} {} {} {}", aBoolean1, aBoolean2, aBoolean3, aBoolean4);
        // 关键服务中任意服务出现故障则认为整体服务质量位为true
        return BooleanUtil.or(aBoolean1, aBoolean2, aBoolean3, aBoolean4);
    }

    private Boolean getValeFromRedis(String key) {
        String newKey = QUALITY_BIT_KEY_PREFIX + key;
        String string = redisHelper.get(newKey);
        Boolean booleanObject = BooleanUtil.toBooleanObject(string);
        // 如果不存在则返回true
        if (Objects.isNull(booleanObject)) {
            return true;
        }
        return booleanObject;
    }
}
