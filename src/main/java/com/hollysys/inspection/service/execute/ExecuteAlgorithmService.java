package com.hollysys.inspection.service.execute;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.config.exceptions.execute.ExecuteBeforeException;
import com.hollysys.inspection.constants.algorithm.UserOrSystemType;
import com.hollysys.inspection.constants.algorithm.param.DataType;
import com.hollysys.inspection.entity.*;
import com.hollysys.inspection.model.alarm.config.AlarmRuleItemModel;
import com.hollysys.inspection.model.algorithm.execute.AlgorithmExecutePyReq;
import com.hollysys.inspection.model.algorithm.execute.AlgorithmExecuteRsp;
import com.hollysys.inspection.model.algorithm.execute.RoiRqePyModel;
import com.hollysys.inspection.model.algorithm.osd.OSDItem;
import com.hollysys.inspection.model.algorithm.param.data.Square;
import com.hollysys.inspection.model.channel.ScaleModel;
import com.hollysys.inspection.model.context.ExecuteAlgorithmCtx;
import com.hollysys.inspection.model.draw.MatchPositionModel;
import com.hollysys.inspection.service.*;
import com.hollysys.inspection.service.protocol.impl.CameraCtrlProxyService;
import com.hollysys.inspection.service.protocol.impl.CameraProtocolRtsp;
import com.hollysys.inspection.utils.*;
import com.hollysys.inspection.utils.execute.ExecuteRspUtil;
import com.hollysys.inspection.utils.execute.TransformPicUtil;
import com.hollysys.inspection.utils.param.ParamCoordsUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;

/**
 * 算法执行服务层
 */
@Slf4j
@Service
public class ExecuteAlgorithmService {

    @Resource
    private PythonServerService pythonServerService;

    @Resource
    private InspChannelInfoService channelInfoService;

    @Resource
    private InspPresetInfoService presetInfoService;

    @Resource
    private InspAlarmRecordService alarmRecordService;

    @Resource
    private CameraProtocolRtsp cameraProtocolRtsp;

    @Resource
    private CameraCtrlProxyService cameraCtrlProxyService;

    @Resource
    private InspAlgorithmParamInstanceService algorithmParamInstanceService;

    /**
     * 算法实例执行入口
     */
    public AlgorithmExecuteRsp executeAlgorithm(ExecuteAlgorithmCtx algorithmCtx,
                                                InspAlgorithmInstance algorithmInstance,
                                                List<InspSceneDefinition> sceneList,
                                                List<String> images,
                                                List<InspAlgorithmParamInstance> paramList,
                                                Object roiObj) {
        // ROI检查
        if (algorithmInstance.getRoiRequired()) {
            AssertUtil.isTrue(Objects.nonNull(roiObj), "当前算法的ROI不允许为空");
            // 校验ROI的值是否与类型相匹配
            RoiUtil.checkType(algorithmInstance.getRoiDrawType(), JSONUtil.toJsonStr(roiObj));
        }

        String presetId = algorithmInstance.getPresetId();
        InspPresetInfo presetInfo = presetInfoService.getOneById(presetId);
        String channelId = presetInfo.getChannelId();
        InspChannelInfo channelInfo = channelInfoService.getOneById(channelId);
        algorithmCtx.setChannelId(channelId);
        // 如果传入的图片列表为null  则需要重新获取
        if (Objects.isNull(images)) {
            if (BooleanUtil.isTrue(algorithmInstance.getIsBatchProcessing())) {
                // 批量截取算法实例的执行输入图片
                images = getAlgorithmInputImages(channelInfo, algorithmInstance.getCutPicSize(),
                        algorithmInstance.getCutPicInterval());
            } else {
                String channelCurrentPic = cameraCtrlProxyService.getPic(channelInfo);
                images = ListUtil.of(channelCurrentPic);
            }
        }
        // 输入图片检查
        AssertUtil.isTrue(CollectionUtil.isNotEmpty(images), "算法执行输入图片集合不允许为空");
        algorithmCtx.setInputImages(images);

        Boolean matchEnable = algorithmInstance.getMatchEnable();
        String image0 = images.get(0);
        InspSceneDefinition matchedScene;
        Square matchedSquare;
        if (matchEnable) {
            // 模板匹配
            TemplateMatchResult templateMatchResult = templateMatch(image0, sceneList);
            if (Objects.isNull(templateMatchResult)) {
                throw new ExecuteBeforeException("场景匹配失败");
            }
            matchedScene = templateMatchResult.getMatchedScene();
            matchedSquare = templateMatchResult.getMatchedSquare();
        } else {
            matchedScene = sceneList.get(0);
            matchedSquare = matchedScene.getTmpSquare();
        }
        algorithmCtx.setSceneId(matchedScene.getId());

        // 如果传入的参数列表为null  则需要从数据库中获取参数实例
        if (Objects.isNull(paramList)) {
            // 获取算法实例参数
            paramList = algorithmParamInstanceService
                    .listByInstanceIdAndSceneId(algorithmInstance.getId(), matchedScene.getId());
            // 将参数值转化为对应格式
            algorithmParamInstanceService.valueStrToObj(paramList);

            algorithmCtx.setInputParams(paramList);
        }

        // 判断是否有通道图片类型参数，有则进行截图然后将截图地址设置到参数值中
        setChannelPicValue(paramList);

        // 进行算法参数值校验
        String algorithmId = algorithmInstance.getAlgorithmId();
        algorithmParamInstanceService.checkParamValue(algorithmId, paramList);

        // 参数值偏差、越限校准
        ScaleModel scaleByImg = PicUtil.getScaleByImgUrl(image0);
        if (Objects.nonNull(matchedSquare)) {
            // 1.进行算法参数坐标偏差校准（按照模板匹配结果） 2.对于超出图片边界的参数值进行处理
            ParamCoordsUtil.resetCoords(scaleByImg, matchedScene.getTmpSquare(), matchedSquare, paramList);
        } else {
            // 1.对于超出图片边界的参数值进行处理
            ParamCoordsUtil.resetCoords(scaleByImg, null, null, paramList);
        }

        // 组织算法执行参数
        AlgorithmExecutePyReq algorithmExecutePyReq = new AlgorithmExecutePyReq();
        if (BooleanUtil.isTrue(algorithmInstance.getIsPreset())) {
            algorithmExecutePyReq.setUserOrSystem(UserOrSystemType.system_inner.name());
        } else {
            algorithmExecutePyReq.setUserOrSystem(UserOrSystemType.user_define.name());
        }

        // 图片进行透视变换矫正
        Boolean correctAvailable = algorithmInstance.getCorrectEnable();
        if (BooleanUtil.isTrue(correctAvailable)) {
            try {
                // 透视变换
                List<String> correctImagesLocal = TransformPicUtil.transformPic(scaleByImg, algorithmInstance.getCorrectParam(),
                        matchedScene.getTmpSquare(), matchedSquare, images);
                algorithmExecutePyReq.setImages(correctImagesLocal);
            } catch (Exception exception) {
                log.error("透视变换出现异常", exception);
                throw new ExecuteBeforeException("透视变换出现异常");
            }
        } else {
            algorithmExecutePyReq.setImages(images);
        }

        algorithmExecutePyReq.setRoi(RoiRqePyModel.newInst(roiObj, algorithmInstance.getRoiDrawType(),
                algorithmInstance.getRoiRequired()));
        algorithmExecutePyReq.setCode(algorithmInstance.getCode());
        algorithmExecutePyReq.setInputParam(paramList);
        algorithmExecutePyReq.setSceneImage(matchedScene.getBenchPic());
        // 调用算法服务进行算法执行
        AlgorithmExecuteRsp algorithmExecuteRsp = pythonServerService.algorithmExecute(algorithmExecutePyReq,
                algorithmInstance.getType());

        // 算法结果精度保留
        ExecuteRspUtil.setValueByScale(algorithmExecuteRsp);

        Map<String, AlarmRuleItemModel> alarmResultMap;
        try {
            // 报警判断
            alarmResultMap = alarmRecordService
                    .checkOutputAlarm(algorithmInstance.getId(), algorithmExecuteRsp.getOutput());
            algorithmExecuteRsp.setAlarmResultMap(alarmResultMap);
        } catch (Exception e) {
            log.error("报警判断过程出现异常", e);
            alarmResultMap = new HashMap<>();
        }

        List<OSDItem> osdItems = OsdUtil.buildOsdBaseList(paramList, algorithmExecuteRsp);
        // 透视变换逆变换
        if (BooleanUtil.isTrue(correctAvailable)) {
            TransformPicUtil.retransformOsdCoords(osdItems, scaleByImg, algorithmInstance.getCorrectParam(),
                    matchedScene.getTmpSquare(), matchedSquare);
        }
        // 拆分输出OSD中文本
        List<OSDItem> textOsdList = OsdUtil.getTextOsdList(algorithmExecuteRsp.getOsdInfo());
        osdItems.addAll(textOsdList);

        String resultImgUrl = algorithmExecuteRsp.getResultImgUrl();
        // 如果算法未返回，则取第一张输入图片
        if (StrUtil.isBlank(resultImgUrl)) {
            log.info("算法未返回结果图片，取第一张输入图片");
            resultImgUrl = image0;
            algorithmExecuteRsp.setResultImgUrl(image0);
        }

        if (CollectionUtil.isNotEmpty(osdItems)) {
            // 设置全部的OSD到结果对象中，包含算法服务返回的和入参中需要绘制的
            algorithmExecuteRsp.setOsdInfo(osdItems);

            // 获取算法报警状态
            long count = alarmResultMap.values().stream().filter(Objects::nonNull).count();
            // 将OSD项绘制到结果图片
            OsdUtil.setOsdConfig(osdItems, OsdConfigUtil.getCvOsdConfig(), count > 0);
            String outputUrl = pythonServerService.drawShape(resultImgUrl, osdItems);
            algorithmExecuteRsp.setResultImgUrl(outputUrl);
        }

        return algorithmExecuteRsp;
    }

    /**
     * 判断是否有通道图片类型参数，有则进行截图然后将截图地址设置到参数值中
     */
    private void setChannelPicValue(List<InspAlgorithmParamInstance> paramList) {
        if (CollectionUtil.isEmpty(paramList)) {
            return;
        }
        for (InspAlgorithmParamInstance paramInstance : paramList) {
            String dataType = paramInstance.getDataType();
            if (DataType.CHANNEL_PIC.name().equals(dataType)) {
                Object valueObj = paramInstance.getValue();
                if (Objects.isNull(valueObj)) {
                    continue;
                }
                String channelId = null;
                String channelInfoJson = JSONUtil.toJsonStr(valueObj);
                try {
                    InspProjectTreeNode bean = JSONUtil.toBean(channelInfoJson, InspProjectTreeNode.class);
                    channelId = bean.getId();
                    InspChannelInfo channelInfo = channelInfoService.getOneById(channelId);
                    String pic = cameraProtocolRtsp.getPic(channelInfo);
                    File file = FileUtil.newFile(pic);
                    pic = MinioUtil.uploadFile(file.getName(), FileUtil.getInputStream(file));
                    paramInstance.setValue(pic);
                } catch (Exception exception) {
                    log.error("设置通道图片类型参数值出现异常，paramName = {}, channelId = {}", channelId, channelInfoJson, exception);
                    throw new InspectionException("设置通道图片类型参数值出现异常");
                }
            }
        }
    }

    /**
     * 执行模板匹配
     */
    private TemplateMatchResult templateMatch(String image, List<InspSceneDefinition> sceneList) {
        List<String> tempImgs = new ArrayList<>();
        List<String> benchImgs = new ArrayList<>();
        for (InspSceneDefinition scene : sceneList) {
            tempImgs.add(scene.getTmpPic());
            benchImgs.add(scene.getBenchPic());
        }
        MatchPositionModel matchingPosition = pythonServerService.matchingPosition(image, tempImgs, benchImgs);
        if (Objects.nonNull(matchingPosition)) {
            TemplateMatchResult templateMatchResult = new TemplateMatchResult();
            // 场景匹配成功
            InspSceneDefinition matchedScene = sceneList.get(matchingPosition.getIndex());
            log.info("场景匹配成功，匹配场景为：{}", matchedScene.getName());
            Square matchedSquare = matchingPosition.getSquare();
            templateMatchResult.setMatchedScene(matchedScene);
            templateMatchResult.setMatchedSquare(matchedSquare);
            return templateMatchResult;
        }
        return null;
    }

    /**
     * 批量截取算法实例的执行输入图片
     */
    public List<String> getAlgorithmInputImages(InspChannelInfo channelInfo, Integer cutPicSize, Integer cutPicInterval) {
        AssertUtil.isTrue(ObjectUtil.isAllNotEmpty(cutPicInterval, cutPicSize), "批量图片截图参数不正确");
        AssertUtil.isTrue(cutPicSize >= 1 && cutPicSize <= 10, "批量截图数量取值范围为1-10");
        AssertUtil.isTrue(cutPicInterval >= 50 && cutPicInterval <= 2000, "批量截图间隔取值范围为50-2000毫秒");

        List<String> channelPicList = new ArrayList<>();
        for (int i = 0; i < cutPicSize; i++) {
            // TODO 截图失败添加重试机制？
            String channelCurrentPic = cameraCtrlProxyService.getPic(channelInfo);
            channelPicList.add(channelCurrentPic);

            SleepUtil.sleepMillisecond(cutPicInterval);
        }
        return channelPicList;
    }
}
