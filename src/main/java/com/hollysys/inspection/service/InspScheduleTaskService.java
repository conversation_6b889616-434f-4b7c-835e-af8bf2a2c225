package com.hollysys.inspection.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hollysys.inspection.config.annotations.OperateLog;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.config.redis.RedisHelper;
import com.hollysys.inspection.constants.*;
import com.hollysys.inspection.constants.operatelog.BusinessClassify;
import com.hollysys.inspection.constants.operatelog.OperateType;
import com.hollysys.inspection.controller.websocket.WebSocketTaskServer;
import com.hollysys.inspection.entity.*;
import com.hollysys.inspection.mapper.InspScheduleTaskMapper;
import com.hollysys.inspection.model.period.InspSchedulePeriodModel;
import com.hollysys.inspection.model.schedule.ScheduleTaskDetailModel;
import com.hollysys.inspection.model.schedule.ScheduleTaskNodeModel;
import com.hollysys.inspection.model.schedule.UpdateTaskReqModel;
import com.hollysys.inspection.model.socket.TaskSocketMsg;
import com.hollysys.inspection.service.execute.ExecutePresetService;
import com.hollysys.inspection.utils.AssertUtil;
import com.hollysys.inspection.utils.IcsUtil;
import com.hollysys.inspection.utils.TimeSlotUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.hollysys.inspection.constants.InspConstants.TASK_MODE_RECOVER;
import static com.hollysys.inspection.constants.InspConstants.TASK_RECOVER_IN_SECONDS;

/**
 * 通道运行周期调度信息表(InspScheduleTask)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-15 16:28:55
 */
@Slf4j
@Service
public class InspScheduleTaskService extends ServiceImpl<InspScheduleTaskMapper, InspScheduleTask> {

    private static final String VARIABLE_NAME_REGEX = "^[A-Za-z_0-9]+$";

    // 任务名称校验规则
    private static final String LABEL_REGEX = "^[\\u4E00-\\u9FA5A-Za-z_0-9-]{1,20}$";

    @Resource
    private RedisHelper redisHelper;

    @Resource
    private ProjectTreeNodeService treeNodeService;

    @Resource
    private InspChannelInfoService inspChannelInfoService;

    @Resource
    private InspPresetInfoService presetInfoService;

    @Resource
    private WebSocketTaskServer webSocketTaskServer;

    @Resource
    private InspScheduleTaskNodeService scheduleTaskNodeService;

    @Resource
    private ExecutePresetService executePresetService;

    @Resource
    private InspSchedulePeriodService schedulePeriodService;

    public InspScheduleTask getByIdNotNull(Serializable id) {
        InspScheduleTask byId = super.getById(id);
        if (Objects.isNull(byId)) {
            log.error("任务信息不存在，ID = {}", id);
            throw new InspectionException("任务信息不存在");
        }
        return byId;
    }

    /**
     * 返回任务点详细信息（包含任务点名称、预置点全部信息），其中数据主键ID为预置点ID
     *
     * @param modeInstanceNodes 预置点集合
     * @return 任务点详细信息
     */
    private List<ScheduleTaskNodeModel> getTaskNodes(Collection<InspProjectTreeNode> modeInstanceNodes) {
        List<ScheduleTaskNodeModel> result = new ArrayList<>();
        Map<String, List<InspProjectTreeNode>> groupByParentId = modeInstanceNodes.stream().collect(Collectors.groupingBy(InspProjectTreeNode::getParentId));

        // 查询父级通道信息
        Set<String> channelIds = groupByParentId.keySet();
        Collection<InspProjectTreeNode> channelNodes = treeNodeService.listByIds(channelIds);
        if (CollectionUtil.isEmpty(channelNodes)) {
            return result;
        }

        // 查询预置点信息
        List<String> modeInstanceIds = modeInstanceNodes.stream().map(InspProjectTreeNode::getId).collect(Collectors.toList());
        Collection<InspPresetInfo> inspPresetInfos = presetInfoService.listByIds(modeInstanceIds);
        Map<String, InspPresetInfo> modeInstanceMap = inspPresetInfos.stream().collect(Collectors.toMap(InspPresetInfo::getId, item -> item));


        Map<String, String> channelIdLabelMap = channelNodes.stream().collect(Collectors.toMap(InspProjectTreeNode::getId, InspProjectTreeNode::getLabel));
        for (Map.Entry<String, List<InspProjectTreeNode>> entry : groupByParentId.entrySet()) {
            String key = entry.getKey();
            List<InspProjectTreeNode> value = entry.getValue();
            for (InspProjectTreeNode modeInstanceNode : value) {
                String channelNodeName = channelIdLabelMap.get(key);
                if (Objects.isNull(channelNodeName)) {
                    continue;
                }
                String modeInstanceNodeId = modeInstanceNode.getId();
                InspPresetInfo presetInfo = modeInstanceMap.get(modeInstanceNodeId);
                if (Objects.isNull(presetInfo)) {
                    continue;
                }

                ScheduleTaskNodeModel scheduleTaskNodeModel = new ScheduleTaskNodeModel();
                BeanUtils.copyProperties(presetInfo, scheduleTaskNodeModel);
                scheduleTaskNodeModel.setLabel(channelNodeName + "/" + modeInstanceNode.getLabel());
                result.add(scheduleTaskNodeModel);
            }
        }
        return result;
    }

    /**
     * 查询所有可选择的巡检点
     *
     * @return 可选择的巡检点
     */
    public List<ScheduleTaskNodeModel> listAllTaskNodes() {
        LambdaQueryWrapper<InspProjectTreeNode> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspProjectTreeNode::getType, ProjectNodeType.PRESET);
        List<InspProjectTreeNode> allModeInstanceNodes = treeNodeService.list(wrapper);
        if (CollectionUtil.isEmpty(allModeInstanceNodes)) {
            return new ArrayList<>();
        }
        List<ScheduleTaskNodeModel> result = getTaskNodes(allModeInstanceNodes);

        // 排序
        result.sort(Comparator.comparing(ScheduleTaskNodeModel::getLabel));
        return result;

    }

    /**
     * 查询任务下的全部任务点
     *
     * @param taskId   任务id
     * @param isFilter 是否过滤掉使能关闭的节点
     * @return 查询任务下的全部任务点
     */
    private List<ScheduleTaskNodeModel> listTaskNodesByTaskId(String taskId, boolean isFilter) {
        LambdaQueryWrapper<InspScheduleTaskNode> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspScheduleTaskNode::getTaskId, taskId);
        List<InspScheduleTaskNode> scheduleTaskNodeList = scheduleTaskNodeService.list(wrapper);
        if (CollectionUtil.isEmpty(scheduleTaskNodeList)) {
            return new ArrayList<>();
        }

        List<String> presetIds = scheduleTaskNodeList.stream().map(InspScheduleTaskNode::getPresetId).collect(Collectors.toList());
        Collection<InspProjectTreeNode> presetNodes = treeNodeService.listByIds(presetIds);
        if (CollectionUtil.isEmpty(presetNodes)) {
            return new ArrayList<>();
        }

        // 返回的数据主键是预置点ID
        List<ScheduleTaskNodeModel> taskNodes = getTaskNodes(presetNodes);
        if (isFilter) {
            // 过滤使能关闭的节点
            taskNodes = taskNodes.stream().filter(ScheduleTaskNodeModel::getAvailable).collect(Collectors.toList());
        }

        // 排序
        Map<String, InspScheduleTaskNode> mapById = scheduleTaskNodeList.stream().collect(Collectors.toMap(InspScheduleTaskNode::getPresetId, item -> item));
        taskNodes.forEach(item -> {
            InspScheduleTaskNode inspScheduleTaskNode = mapById.get(item.getId());
            if (Objects.nonNull(inspScheduleTaskNode)) {
                item.setSortNo(inspScheduleTaskNode.getSortNo());
                item.setStatus(inspScheduleTaskNode.getStatus());
                item.setTaskNodeId(inspScheduleTaskNode.getId());
            }
        });
        taskNodes.sort(Comparator.comparing(ScheduleTaskNodeModel::getSortNo));

        return taskNodes;
    }

    /**
     * 通过任务ID查询巡检任务详情
     *
     * @param taskId 任务id
     * @return 巡检任务详情
     */
    public ScheduleTaskDetailModel getTaskDetail(String taskId) {
        // 任务基本信息
        InspScheduleTask byId = getById(taskId);
        if (Objects.isNull(byId)) {
            throw new InspectionException("任务信息不存在");
        }
        ScheduleTaskDetailModel scheduleTaskDetailModel = new ScheduleTaskDetailModel();
        BeanUtils.copyProperties(byId, scheduleTaskDetailModel);

        List<ScheduleTaskNodeModel> scheduleTaskNodeModels = listTaskNodesByTaskId(taskId, false);
        scheduleTaskDetailModel.setTaskNodes(scheduleTaskNodeModels);

        InspSchedulePeriodModel schedulePeriodModel = schedulePeriodService.listByTaskId(taskId);
        scheduleTaskDetailModel.setSchedulePeriods(schedulePeriodModel);

        // 设置任务的手自动状态
        if (isManualTask(taskId)) {
            scheduleTaskDetailModel.setTaskMode(ScheduleTaskMode.MANUAL);
        } else {
            scheduleTaskDetailModel.setTaskMode(ScheduleTaskMode.AUTOMATIC);
        }
        return scheduleTaskDetailModel;
    }

    @OperateLog(operateType = OperateType.CREATE, businessClassify = BusinessClassify.SCHEDULE_TASK)
    public Object createTask(InspScheduleTask inspScheduleTask) {
        String name = inspScheduleTask.getName();
        if (StrUtil.isBlank(name)) {
            throw new InspectionException("名称不允许为空");
        }

        checkTaskName(name);

        // 名称重复校验
        LambdaQueryWrapper<InspScheduleTask> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(InspScheduleTask::getName, name);
        List<InspScheduleTask> listByName = list(wrapper1);
        if (CollectionUtil.isNotEmpty(listByName)) {
            throw new InspectionException("任务名称已存在");
        }

        // 设置排序号码
        LambdaQueryWrapper<InspScheduleTask> wrapper2 = new LambdaQueryWrapper<>();
        wrapper2.orderByDesc(InspScheduleTask::getSortNo);
        List<InspScheduleTask> list = this.list(wrapper2);
        if (CollectionUtil.isEmpty(list)) {
            inspScheduleTask.setSortNo(0);
        } else {
            Integer sortNo = list.get(0).getSortNo();
            inspScheduleTask.setSortNo(sortNo + 1);
        }

        save(inspScheduleTask);

        return inspScheduleTask;
    }


    @Transactional
    @OperateLog(operateType = OperateType.UPDATE, businessClassify = BusinessClassify.SCHEDULE_TASK)
    public Object updateTaskDetail(UpdateTaskReqModel reqModel) {
        // 验证任务是否存在
        String taskId = reqModel.getId();
        InspScheduleTask scheduleTask = getByIdNotNull(taskId);

        // 更新任务绑定的巡检点
        List<InspPresetInfo> taskNodes = reqModel.getTaskNodes();
        List<InspScheduleTaskNode> scheduleTaskNodeList = addTaskNode(taskId, taskNodes);

        // 先删除旧数据，在插入新数据
        LambdaQueryWrapper<InspScheduleTaskNode> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspScheduleTaskNode::getTaskId, taskId);
        scheduleTaskNodeService.remove(wrapper);

        if (CollectionUtil.isNotEmpty(scheduleTaskNodeList)) {
            try {
                scheduleTaskNodeService.saveBatch(scheduleTaskNodeList);
            } catch (DuplicateKeyException e) {
                log.error("保存调度任务配置失败，提交的数据中主键重复", e);
                throw new InspectionException("保存调度任务配置失败，提交的数据中主键重复");
            }
        }

        // 更新预置点信息
        if (CollectionUtil.isNotEmpty(taskNodes)) {
            List<InspPresetInfo> presetInfos = taskNodes.stream().map(item -> {
                InspPresetInfo presetInfo = new InspPresetInfo();
                presetInfo.setId(item.getId());
                presetInfo.setAvailable(item.getAvailable());
                presetInfo.setPreExecuteDelay(item.getPreExecuteDelay());
                presetInfo.setPostExecuteDelay(item.getPostExecuteDelay());
                presetInfo.setValidTimes(item.getValidTimes());

                return presetInfo;
            }).collect(Collectors.toList());
            presetInfoService.updateBatchById(presetInfos);
        }

        // 更新调度周期配置
        updateTaskScheduleInfo(reqModel);

        // 更新任务信息
        BeanUtils.copyProperties(reqModel, scheduleTask);
        updateById(scheduleTask);
        return scheduleTask;
    }

    private void updateTaskScheduleInfo(UpdateTaskReqModel reqModel) {
        // 调度间隔校验
        Integer interval = reqModel.getInterval();
        if (Objects.isNull(interval) || interval < 0 || interval > 100) {
            throw new InspectionException("调度间隔配置错误，请输入0-100的整数");
        }
        String intervalUnit = reqModel.getIntervalUnit();
        if (!Arrays.asList(TimeUnit.SECONDS.name(), TimeUnit.MINUTES.name(), TimeUnit.HOURS.name()).contains(intervalUnit)) {
            throw new InspectionException("调度间隔单位配置错误");
        }

        Boolean scheduleEnable = reqModel.getScheduleEnable();
        String triggerType = reqModel.getTriggerType();
        if (EnumUtil.equals(ScheduleTriggerType.TIME_TRIGGER, triggerType)) {
            String taskId = reqModel.getId();
            List<InspPresetInfo> taskNodes = reqModel.getTaskNodes();
            InspSchedulePeriodModel schedulePeriods = reqModel.getSchedulePeriods();
            schedulePeriods.setTaskId(taskId);

            if (scheduleEnable) {
                // 判断是否配置冲突，通道+调度时间级别
                checkSchedulePeriods(taskNodes, schedulePeriods, taskId);
            }
            schedulePeriodService.insert(schedulePeriods);
        } else if (EnumUtil.equals(ScheduleTriggerType.CONDITION_TRIGGER, triggerType)) {
            if (scheduleEnable) {
                // 判断是否配置冲突，通道级别
                checkConditionTriggerTaskNodes(reqModel);
            }

            String variableType = reqModel.getVariableType();
            if (EnumUtil.equals(ScheduleVariableType.DCS, variableType)) {
                String namespace = reqModel.getNamespace();
                String tag = reqModel.getTag();
                String item = reqModel.getItem();
                IcsUtil.checkIcsPointNotBlank(namespace, tag, item);
            } else if (EnumUtil.equals(ScheduleVariableType.INTERNAL, variableType)) {
                String variableName = reqModel.getVariableName();
                Boolean triggerValue = reqModel.getTriggerValue();
                AssertUtil.isTrue(StrUtil.isNotBlank(variableName) && Objects.nonNull(triggerValue), "条件触发，变量名和变量值不允许为空");
                // 变量名校验
                String taskId = reqModel.getId();
                checkVariableName(taskId, variableName);
            } else {
                throw new InspectionException("条件触发变量类型配置错误");
            }
        } else {
            throw new InspectionException("调度任务触发方式配置错误");
        }
    }

    private void checkConditionTriggerTaskNodes(UpdateTaskReqModel reqModel) {
        List<InspPresetInfo> taskNodes = reqModel.getTaskNodes();
        if (CollectionUtil.isEmpty(taskNodes)) {
            return;
        }

        List<String> channelIds = taskNodes.stream().map(InspPresetInfo::getChannelId).collect(Collectors.toList());
        LambdaQueryWrapper<InspScheduleTaskNode> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(InspScheduleTaskNode::getChannelId, channelIds);
        wrapper.ne(InspScheduleTaskNode::getTaskId, reqModel.getId());
        List<InspScheduleTaskNode> inspScheduleTaskNodes = scheduleTaskNodeService.list(wrapper);
        if (CollectionUtil.isNotEmpty(inspScheduleTaskNodes)) {
            throw new InspectionException("当前任务绑定的巡检点所属通道已被其他任务绑定");
        }
    }

    private void checkVariableName(String taskId, String variableName) {
        // 变量名称格式校验
        if (!variableName.matches(VARIABLE_NAME_REGEX) || variableName.length() > 15) {
            throw new InspectionException("变量名包含字母、下划线和数字、长度不超过15");
        }

        // 判断是否与已有任务绑定的变量重复
        LambdaQueryWrapper<InspScheduleTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.ne(InspScheduleTask::getId, taskId);
        wrapper.eq(InspScheduleTask::getVariableName, variableName);
        List<InspScheduleTask> list = list(wrapper);
        if (CollectionUtil.isNotEmpty(list)) {
            throw new InspectionException("变量已被其他任务绑定");
        }
    }

    private static List<InspScheduleTaskNode> addTaskNode(String taskId, List<InspPresetInfo> taskNodes) {
        List<InspScheduleTaskNode> scheduleTaskNodeList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(taskNodes)) {
            // 判断巡检点是重复绑定
            List<String> modeInstanceIds = taskNodes.stream().map(InspPresetInfo::getId).distinct().collect(Collectors.toList());
            if (taskNodes.size() != modeInstanceIds.size()) {
                throw new InspectionException("巡检点数据重复，请检查后提交");
            }

            for (int i = 0; i < taskNodes.size(); i++) {
                InspPresetInfo taskNode = taskNodes.get(i);
                InspScheduleTaskNode inspScheduleTaskNode = new InspScheduleTaskNode();
                inspScheduleTaskNode.setTaskId(taskId);
                inspScheduleTaskNode.setSortNo(i);
                inspScheduleTaskNode.setChannelId(taskNode.getChannelId());
                inspScheduleTaskNode.setPresetId(taskNode.getId());

                scheduleTaskNodeList.add(inspScheduleTaskNode);
            }
        }
        return scheduleTaskNodeList;
    }

    /**
     * 时间调度任务绑定的节点：
     * 1.不能与其他时间调度任务绑定的节点时间配置有交叉
     * 2.不能与其他条件触发调度任务绑定的节点通道配置有交叉
     */
    private void checkSchedulePeriods(List<InspPresetInfo> taskNodes, InspSchedulePeriodModel schedulePeriods, String taskId) {
        List<InspSchedulePeriodModel.Periods> periods = schedulePeriods.getPeriods();
        if (CollectionUtil.isEmpty(periods) || CollectionUtil.isEmpty(taskNodes)) {
            return;
        }
        // 获取数据库中其余全部任务
        LambdaQueryWrapper<InspScheduleTask> wrapperTask = new LambdaQueryWrapper<>();
        wrapperTask.ne(InspScheduleTask::getId, taskId);
        wrapperTask.isNotNull(InspScheduleTask::getTriggerType);
        List<InspScheduleTask> allDbTasks = list(wrapperTask);
        if (CollectionUtil.isEmpty(allDbTasks)) {
            return;
        }
        // 判断全部时间触发的任务中，预置点是否重复绑定
        Map<String, List<InspScheduleTask>> groupByTriggerType = allDbTasks.stream()
                .collect(Collectors.groupingBy(InspScheduleTask::getTriggerType));
        // 检查时间触发
        List<InspScheduleTask> timeTriggerTasks = groupByTriggerType.get(ScheduleTriggerType.TIME_TRIGGER.name());
        checkTimeTriggerPeriods(timeTriggerTasks, taskId, taskNodes, periods);

        // 检查条件触发触发
        List<InspScheduleTask> conditionTriggerTasks = groupByTriggerType.get(ScheduleTriggerType.CONDITION_TRIGGER.name());
        checkConditionTriggerPeriods(conditionTriggerTasks, taskId, taskNodes);
    }

    private void checkConditionTriggerPeriods(List<InspScheduleTask> conditionTriggerTasks, String taskId,
                                              List<InspPresetInfo> taskNodes) {
        if (CollectionUtil.isNotEmpty(conditionTriggerTasks)) {
            // 如果是条件触发的任务，绑定的通道不允许被其他任务绑定
            List<String> dbTaskIds = conditionTriggerTasks.stream().map(InspScheduleTask::getId)
                    .filter(item -> !taskId.equals(item))
                    .collect(Collectors.toList());
            // 查询全部任务巡检点
            List<InspScheduleTaskNode> scheduleTaskNodeList = scheduleTaskNodeService.listByTaskIds(dbTaskIds);
            if (CollectionUtil.isEmpty(scheduleTaskNodeList)) {
                return;
            }
            // 数据库中全部被绑定的通道ID
            List<String> collect = scheduleTaskNodeList.stream()
                    .map(InspScheduleTaskNode::getChannelId).distinct().collect(Collectors.toList());
            List<String> webChannelIds = taskNodes.stream().map(InspPresetInfo::getChannelId).distinct().collect(Collectors.toList());
            Collection<String> intersection = CollectionUtil.intersection(collect, webChannelIds);
            if (CollectionUtil.isNotEmpty(intersection)) {
                // String nextChannelId = intersection.iterator().next();
                // InspProjectTreeNode channelInfo = treeNodeService.getById(nextChannelId);
                //
                throw new InspectionException("当前巡检点所属通道已被其他条件触发的任务绑定");
            }
        }
    }

    private void checkTimeTriggerPeriods(List<InspScheduleTask> timeTriggerTasks, String taskId,
                                         List<InspPresetInfo> taskNodes, List<InspSchedulePeriodModel.Periods> periods) {
        if (CollectionUtil.isNotEmpty(timeTriggerTasks)) {
            List<String> dbTaskIds = timeTriggerTasks.stream().map(InspScheduleTask::getId)
                    .filter(item -> !taskId.equals(item))
                    .collect(Collectors.toList());

            // 判断时间是否冲突
            LambdaQueryWrapper<InspSchedulePeriod> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(InspSchedulePeriod::getTaskId, dbTaskIds);
            List<InspSchedulePeriod> othersSchedulePeriod = schedulePeriodService.list(wrapper);
            if (CollectionUtil.isEmpty(othersSchedulePeriod)) {
                return;
            }
            // 组织系统已经提交的调度配置数据为Map  key：channelId+weekDay   value：调度时间配置
            List<InspScheduleTaskNode> scheduleTaskNodeList = scheduleTaskNodeService.listByTaskIds(dbTaskIds);
            if (CollectionUtil.isEmpty(scheduleTaskNodeList)) {
                return;
            }
            Map<String, List<String>> oldDataMap = new HashMap<>();
            Map<String, List<InspScheduleTaskNode>> realGroupBuChannelId = scheduleTaskNodeList.stream().collect(Collectors.groupingBy(InspScheduleTaskNode::getChannelId));
            Map<String, List<InspSchedulePeriod>> groupByTaskId = othersSchedulePeriod.stream().collect(Collectors.groupingBy(InspSchedulePeriod::getTaskId));
            addSlot(oldDataMap, realGroupBuChannelId, groupByTaskId);

            // 遍历用户提交的数据进行判断
            loopCheck(taskNodes, oldDataMap, periods);
        }
    }

    private void loopCheck(List<InspPresetInfo> taskNodes, Map<String, List<String>> oldDataMap, List<InspSchedulePeriodModel.Periods> periods) {
        List<String> channelIds = taskNodes.stream().map(InspPresetInfo::getChannelId).distinct().collect(Collectors.toList());
        for (String channelId : channelIds) {
            for (InspSchedulePeriodModel.Periods period : periods) {
                String key = channelId + period.getWeek();
                List<String> strings = oldDataMap.get(key);
                if (CollectionUtil.isEmpty(strings)) {
                    continue;
                }
                // 时间是否交叉
                String userTimeSlot = period.getTimeSlot();
                for (String timeSlot : strings) {
                    if (TimeSlotUtil.hasIntersection(userTimeSlot, timeSlot)) {
                        log.error("同一通道在同一时间窗口只能被一个任务调度，重复key = {}, periods = {}", key,
                                JSONUtil.toJsonStr(periods));
                        throw new InspectionException("同一通道在同一时间窗口只能被一个任务调度");
                    }
                }
            }
        }
    }

    private static void addSlot(Map<String, List<String>> oldDataMap, Map<String, List<InspScheduleTaskNode>> realGroupBuChannelId, Map<String, List<InspSchedulePeriod>> groupByTaskId) {
        for (Map.Entry<String, List<InspScheduleTaskNode>> listEntry : realGroupBuChannelId.entrySet()) {
            String channelId = listEntry.getKey();
            List<InspScheduleTaskNode> relaByChannelId = listEntry.getValue();
            for (InspScheduleTaskNode taskRela : relaByChannelId) {
                String taskId1 = taskRela.getTaskId();
                List<InspSchedulePeriod> inspSchedulePeriods = groupByTaskId.get(taskId1);
                if (CollectionUtil.isEmpty(inspSchedulePeriods)) {
                    continue;
                }
                for (InspSchedulePeriod inspSchedulePeriod : inspSchedulePeriods) {
                    String key = channelId + inspSchedulePeriod.getWeek();
                    List<String> strings = oldDataMap.get(key);
                    if (Objects.isNull(strings)) {
                        strings = new ArrayList<>();
                        oldDataMap.put(key, strings);
                    }
                    strings.add(inspSchedulePeriod.getTimeSlot());
                }
            }
        }
    }

    @Transactional
    @OperateLog(operateType = OperateType.DELETE, businessClassify = BusinessClassify.SCHEDULE_TASK)
    public void removeTask(List<String> idList) {
        // 删除任务
        removeByIds(idList);

        // 删除任务绑定的任务点
        LambdaQueryWrapper<InspScheduleTaskNode> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(InspScheduleTaskNode::getTaskId, idList);
        scheduleTaskNodeService.remove(wrapper);

        // 删除任务绑定的调度配置
        schedulePeriodService.removeByTaskIds(idList);
    }

    public List<ScheduleTaskNodeModel> listTaskNodesWithStatusByTaskId(String taskId) {
        List<ScheduleTaskNodeModel> scheduleTaskNodeModels = listTaskNodesByTaskId(taskId, true);
        if (CollectionUtil.isEmpty(scheduleTaskNodeModels)) {
            return scheduleTaskNodeModels;
        }

        // 查找运行中的节点下标
        int runningIndex = -1;
        for (int i = 0; i < scheduleTaskNodeModels.size(); i++) {
            ScheduleTaskNodeModel scheduleTaskNodeModel = scheduleTaskNodeModels.get(i);
            if (ScheduleTaskStatus.RUNNING.name().equals(scheduleTaskNodeModel.getStatus())) {
                runningIndex = i;
                break;
            }
        }

        if (runningIndex < 0) {
            // 没有运行中的节点，设置第一个节点预览URL
            setPreviewUrl(scheduleTaskNodeModels, 0);
        } else {
            // 有运行中的节点，设置运行中节点、后一个节点所属通道的视频预览URL
            int nextIndex;
            if (runningIndex == scheduleTaskNodeModels.size() - 1) {
                nextIndex = 0;
            } else {
                nextIndex = runningIndex + 1;
            }

            setPreviewUrl(scheduleTaskNodeModels, runningIndex);
            setPreviewUrl(scheduleTaskNodeModels, nextIndex);
        }
        return scheduleTaskNodeModels;
    }

    private void setPreviewUrl(List<ScheduleTaskNodeModel> scheduleTaskNodeModels, int index) {
        ScheduleTaskNodeModel scheduleTaskNodeModel0 = scheduleTaskNodeModels.get(index);
        String channelId = scheduleTaskNodeModel0.getChannelId();
        String previewUrl = inspChannelInfoService.getPreviewInfo(channelId).getPreviewUrl();
        scheduleTaskNodeModel0.setPreviewUrl(previewUrl);
    }

    /**
     * 执行调度任务
     *
     * @param taskId 任务ID
     */
    public void executeTask(String taskId) {
        if (isManualTask(taskId)) {
            log.warn("当前任务非自动模式，跳过定时调度执行... taskId = {}", taskId);
            return;
        }

        // 获取任务下的（巡检点）预置点，单线程顺序执行
        List<ScheduleTaskNodeModel> scheduleTaskNodeModels = listTaskNodesByTaskId(taskId, true);
        if (CollectionUtil.isEmpty(scheduleTaskNodeModels)) {
            return;
        }

        for (ScheduleTaskNodeModel scheduleTaskNode : scheduleTaskNodeModels) {
            if (isManualTask(taskId)) {
                log.warn("当前任务非自动模式，跳过定时调度执行... taskId = {}", taskId);
                return;
            }

            ScheduleTaskMode channelMode = inspChannelInfoService.getChannelMode(scheduleTaskNode.getChannelId());
            if (!ScheduleTaskMode.AUTOMATIC.equals(channelMode)) {
                log.warn("当前通道为非自动模式，跳过当前预置点调度执行... taskId = {}，channelId = {}，presetId = {}", taskId,
                        scheduleTaskNode.getChannelId(), scheduleTaskNode.getId());
                continue;
            }

            String taskNodeId = scheduleTaskNode.getTaskNodeId();
            try {
                // 设置任务节点状态为运行中
                scheduleTaskNodeService.updateStatus(taskNodeId, ScheduleTaskStatus.RUNNING);

                // 发送WebSocket消息到前台，告知任务节点开始执行
                webSocketTaskServer.sendMessageToWeb(taskId);
                // 发送实时预置点位置
                String presetNodeId = scheduleTaskNode.getId();

                // ExecuteStartModel.SCHEDULE,
                executePresetService.executePreset(taskId, null, presetNodeId);
            } catch (Exception exception) {
                log.error("auto executePreset error ...", exception);
            } finally {
                // 设置任务节点状态为运行结束
                scheduleTaskNodeService.updateStatus(taskNodeId, ScheduleTaskStatus.COMPLETE);
            }
        }
    }

    @OperateLog(operateType = OperateType.UPDATE, businessClassify = BusinessClassify.SCHEDULE_TASK)
    public Object updateTask(InspScheduleTask reqModel) {
        String taskId = reqModel.getId();
        if (StrUtil.isEmpty(taskId)) {
            throw new InspectionException("任务ID不能为空");
        }
        String taskName = reqModel.getName();
        checkTaskName(taskName);

        // 校验任务名称唯一性
        LambdaQueryWrapper<InspScheduleTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InspScheduleTask::getName, taskName);
        queryWrapper.ne(InspScheduleTask::getId, taskId);
        List<InspScheduleTask> listByName = list(queryWrapper);
        if (CollectionUtil.isNotEmpty(listByName)) {
            throw new InspectionException("任务名称已存在");
        }

        LambdaUpdateWrapper<InspScheduleTask> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(InspScheduleTask::getName, taskName);
        wrapper.eq(InspScheduleTask::getId, taskId);
        return update(wrapper);
    }

    private void checkTaskName(String taskName) {
        if (StrUtil.isEmpty(taskName)) {
            throw new InspectionException("任务名称不能为空");
        }

        if (!taskName.matches(LABEL_REGEX)) {
            throw new InspectionException("任务名称只允许包含汉字、字母、下划线、中划线和数字，20个字符以内");
        }
    }

    /**
     * 任务暂停调度（切换为手动）
     */
    public void pauseTask(String taskId) {
        switchTaskMode(taskId, ScheduleTaskMode.MANUAL);
    }

    /**
     * 任务恢复调度（切换为自动）
     */
    public void resumeTask(String taskId) {
        switchTaskMode(taskId, ScheduleTaskMode.AUTOMATIC);
    }

    private void switchTaskMode(String taskId, ScheduleTaskMode model) {
        InspScheduleTask byId = getById(taskId);
        if (Objects.isNull(byId)) {
            throw new InspectionException("调度任务不存在");
        }
        // 切换到手动
        String taskModeKey = getTaskModeKey(taskId);
        if (ScheduleTaskMode.MANUAL.equals(model)) {
            redisHelper.setEx(taskModeKey, DateUtil.now(), TASK_RECOVER_IN_SECONDS, TimeUnit.SECONDS);
        }
        // 切换自动
        else {
            redisHelper.delete(taskModeKey);
        }

        // 发送消息到前端，刷新页面
        webSocketTaskServer.sendMessageToWeb(null, TaskSocketMsg.buildSwitchModel(model));
    }

    private boolean isManualTask(String taskId) {
        String key = getTaskModeKey(taskId);
        String value = redisHelper.get(key);
        return !Objects.isNull(value);
    }

    private String getTaskModeKey(String taskId) {
        return String.format("%s:%s", TASK_MODE_RECOVER, taskId);
    }
}

