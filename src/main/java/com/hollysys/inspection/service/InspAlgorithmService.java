package com.hollysys.inspection.service;

import cn.darkjrong.license.core.common.pojo.params.LicenseExtraParam;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.unit.DataSize;
import cn.hutool.core.io.unit.DataUnit;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hollysys.inspection.config.annotations.OperateLog;
import com.hollysys.inspection.config.annotations.TempFileCleanable;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.constants.algorithm.AlgorithmClassification;
import com.hollysys.inspection.constants.algorithm.AlgorithmType;
import com.hollysys.inspection.constants.algorithm.param.DataType;
import com.hollysys.inspection.constants.algorithm.param.OutOrInEnum;
import com.hollysys.inspection.constants.operatelog.BusinessClassify;
import com.hollysys.inspection.constants.operatelog.OperateType;
import com.hollysys.inspection.entity.InspAlgorithm;
import com.hollysys.inspection.entity.InspAlgorithmInstance;
import com.hollysys.inspection.entity.InspAlgorithmParam;
import com.hollysys.inspection.entity.InspProjectTreeNode;
import com.hollysys.inspection.mapper.InspAlgorithmMapper;
import com.hollysys.inspection.model.algorithm.SaveAlgorithmReqModel;
import com.hollysys.inspection.model.algorithm.schema.AlgorithmSchemaModel;
import com.hollysys.inspection.model.tree.AlgorithmTreeNodeModel;
import com.hollysys.inspection.utils.AssertUtil;
import com.hollysys.inspection.utils.LocalFileServerUtil;
import com.hollysys.inspection.utils.StrEscapeUtil;
import com.hollysys.inspection.utils.TempFileCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 算法信息表(InspAlgorithm)表服务接口
 *
 * <AUTHOR>
 * @since 2023-02-03 15:49:14
 */
@Slf4j
@Service
public class InspAlgorithmService extends ServiceImpl<InspAlgorithmMapper, InspAlgorithm> {

    private static final String ALGORITHM_FILE_TYPE = "zip";

    @Resource
    private PythonServerService pythonServerService;

    @Resource
    private InspAlgorithmInstanceService algorithmInstanceService;

    @Resource
    private InspAlgorithmParamService algorithmParamService;

    @Resource
    private ProjectTreeNodeService projectTreeNodeService;

    @Resource
    private InspModelService modelService;

    @Resource
    private LicenseService licenseService;

    public InspAlgorithm getOneById(Serializable id) {
        InspAlgorithm byId = getById(id);
        if (Objects.isNull(byId)) {
            throw new InspectionException("算法信息不存在");
        }
        return byId;
    }

    public AlgorithmSchemaModel getAlgorithmDetail(String algorithmId) {
        AlgorithmSchemaModel schemaModel = new AlgorithmSchemaModel();
        InspAlgorithm byId = getOneById(algorithmId);
        schemaModel.setAlgorithmMetadata(byId);
        List<InspAlgorithmParam> inspAlgorithmParams = algorithmParamService.listByAlgorithmId(algorithmId);
        if (CollectionUtil.isNotEmpty(inspAlgorithmParams)) {
            Map<String, List<InspAlgorithmParam>> groupingMap = inspAlgorithmParams.stream().collect(Collectors.groupingBy(InspAlgorithmParam::getOutOrIn));
            List<InspAlgorithmParam> inputParams = groupingMap.get(OutOrInEnum.IN.name());
            schemaModel.setInput(inputParams);
            List<InspAlgorithmParam> outputParams = groupingMap.get(OutOrInEnum.OUT.name());
            schemaModel.setOutput(outputParams);
        }
        return schemaModel;
    }

    @Transactional
    @TempFileCleanable
    @OperateLog(operateType = OperateType.CREATE, businessClassify = BusinessClassify.ALGORITHM, message = "添加算法")
    public Object newAlgorithm(SaveAlgorithmReqModel reqModel) {
        // 文件类型判断
        MultipartFile algorithmFile = reqModel.getAlgorithmFile();
        if (Objects.isNull(algorithmFile) || algorithmFile.getSize() <= 0) {
            throw new InspectionException("上传文件不允许为空");
        }
        DataSize dataSize = DataSize.of(50, DataUnit.MEGABYTES);
        long maxSize = dataSize.toBytes();
        long size = algorithmFile.getSize();
        if (size > maxSize) {
            throw new InspectionException("上传文件大小最大允许50MB");
        }

        String suffix = FileUtil.getSuffix(algorithmFile.getOriginalFilename());
        if (!ALGORITHM_FILE_TYPE.equals(suffix)) {
            throw new InspectionException("只允许上传zip压缩文件");
        }

        // 获取证书参数
        LicenseExtraParam licenseExtraParam = licenseService.getLicenseExtraParamInRedis();
        // 证书参数判空
        if (licenseExtraParam == null) {
            throw new InspectionException("证书参数为空");
        }
        // 获取授权算法code list
        List<String> algorithmCodes = licenseExtraParam.getAlgorithmCodes();

        // 判断授权算法code list 是否为空
        if (algorithmCodes == null || algorithmCodes.isEmpty()) {
            algorithmCodes = Collections.emptyList();
        }

        // 获取算法列表的长度
        int algorithmMaxSize = algorithmCodes.size();

        // 获取当前算法数量
        int algorithmCount = licenseService.getAlgorithmCount();
        // 判断当前算法数量是否超过授权数量
        if (algorithmCount >= algorithmMaxSize) {
            throw new InspectionException("算法数量超过授权最大数量" + algorithmMaxSize);
        }

        // 压缩包解压
        File tempFile = LocalFileServerUtil.saveToTemp(algorithmFile);
        // 添加解压文件至缓存
        TempFileCache.cache(tempFile.getParentFile());

        // 解压缩后查找算法code
        AlgorithmSchemaModel algorithmSchema = findAlgorithmSchema(tempFile);

        // 获取算法code
        String code = algorithmSchema.getAlgorithmMetadata().getCode();

        // 判断当前添加的算法code 是否在授权范围内
        if (!algorithmCodes.contains(code)) {
            throw new InspectionException("上传算法不在授权范围内");
        }

        InspAlgorithm algorithm = saveAlgorithmSchemaModel(reqModel.getAlgorithmName(), algorithmFile.getOriginalFilename(), algorithmSchema);

        // 算法文件上传至python服务
        pythonServerService.uploadAlgorithmToPy(tempFile, algorithm.getCode(), algorithm.getType());
        return algorithm;
    }

    @Transactional
    public InspAlgorithm saveAlgorithmSchemaModel(String algorithmName, String originalFilename,
                                                  AlgorithmSchemaModel algorithmSchema) {
        InspAlgorithm algorithm = algorithmSchema.getAlgorithmMetadata();

        // 检查算法所需要的模型是否已经上传 如果传了模型code才会检查
        String modelCode = algorithmSchema.getAlgorithmMetadata().getModelCode();
        if (StrUtil.isNotBlank(modelCode)) {
            modelService.checkModelIsUpload(algorithmSchema.getAlgorithmMetadata().getModelCode());
        }

        checkCodeDuplicate(algorithm.getCode());

        validateAlgorithmName(algorithmName);

        // 算法解析入库
        algorithm.setId(null);
        algorithm.setName(algorithmName);
        String classification = algorithm.getClassification();
        if (EnumUtil.contains(AlgorithmClassification.class, classification)) {
            algorithm.setClassification(classification);
        } else {
            algorithm.setClassification(AlgorithmClassification.OTHERS.name());
        }
        algorithm.setPackageFile(originalFilename);
        algorithm.setIsPreset(false);
        save(algorithm);

        // 算法参数定义数据入库
        algorithmParamService.insertParams(algorithm.getId(), algorithmSchema.getInput(), algorithmSchema.getOutput());
        return algorithm;
    }

    private void checkCodeDuplicate(String code) {
        LambdaQueryWrapper<InspAlgorithm> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspAlgorithm::getCode, code);
        int count = count(wrapper);
        if (count > 0) {
            throw new InspectionException("算法CODE已存在");
        }
    }

    @Transactional
    @TempFileCleanable
    @OperateLog(operateType = OperateType.UPDATE, businessClassify = BusinessClassify.ALGORITHM)
    public InspAlgorithm updateAlgorithm(SaveAlgorithmReqModel reqModel) {
        String algorithmId = reqModel.getAlgorithmId();
        if (StrUtil.isBlank(algorithmId)) {
            throw new InspectionException("算法ID不能为空");
        }

        InspAlgorithm algorithm = getOneById(algorithmId);
        if (Boolean.TRUE.equals(algorithm.getIsPreset())) {
            throw new InspectionException("系统预置算法无法修改");
        }

        String algorithmName = reqModel.getAlgorithmName();
        if (StrUtil.isBlank(algorithmName)) {
            throw new InspectionException("算法名称不能为空");
        }

        // 算法数据修改
        String name = algorithm.getName();
        if (!algorithmName.equals(name)) {
            // 判断是否被引用
            checkAlgorithmUnUse(algorithmId);
            // 修改名称
            validateAlgorithmName(algorithmName);
            algorithm.setName(algorithmName);
        }
        algorithm.setDescription(reqModel.getAlgorithmDes());

        // 算法包文件上传
        MultipartFile algorithmFile = reqModel.getAlgorithmFile();
        if (Objects.nonNull(algorithmFile)) {
            // 判断是否被引用
            checkAlgorithmUnUse(algorithmId);

            // 算法文件上传至python服务
            File tempFile = LocalFileServerUtil.saveToTemp(algorithmFile);
            TempFileCache.cache(tempFile.getParentFile());

            // 解压缩后查找算法
            AlgorithmSchemaModel algorithmSchema = findAlgorithmSchema(tempFile);
            InspAlgorithm findAlgorithm = algorithmSchema.getAlgorithmMetadata();
            String newCode = findAlgorithm.getCode();
            if (!algorithm.getCode().equals(newCode)) {
                // CODE发生变化，需要判断CODE是否重复
                checkCodeDuplicate(newCode);
            }
            // 除id和name字段外，都取算法包中的算法数据
            BeanUtils.copyProperties(findAlgorithm, algorithm, "id", "name");

            // 更新算法参数定义
            algorithmParamService.removeByAlgorithmId(algorithm.getId());
            algorithmParamService.insertParams(algorithm.getId(), algorithmSchema.getInput(), algorithmSchema.getOutput());

            // 算法文件不为空则修改
            algorithm.setPackageFile(algorithmFile.getOriginalFilename());

            // 算法文件上传至python服务
            pythonServerService.uploadAlgorithmToPy(tempFile, algorithm.getCode(), algorithm.getType());
        }

        updateById(algorithm);
        return algorithm;
    }

    private AlgorithmSchemaModel findAlgorithmSchema(File tempFile) {
        File unzipDir;
        try {
            unzipDir = ZipUtil.unzip(tempFile);
        } catch (Exception exception) {
            throw new InspectionException("算法压缩包解压错误");
        }

        String[] listFiles = unzipDir.list();
        if (Objects.isNull(listFiles) || listFiles.length == 0) {
            throw new InspectionException("算法包内容不能为空");
        }

        String baseDir = unzipDir.getAbsolutePath();
        if (listFiles.length == 1) {
            baseDir = unzipDir + File.separator + listFiles[0];
        }

        String pkgInfoPath = baseDir + File.separator + "PKG-INFO";
        if (!FileUtil.exist(pkgInfoPath)) {
            log.error("算法包中PKG-INFO文件未找到[{}]", pkgInfoPath);
            throw new InspectionException("算法包缺少PKG-INFO文件");
        }
        // 查询算法编码
        String algorithmCode = null;
        List<String> pkgInfoLines = FileUtil.readUtf8Lines(pkgInfoPath);
        for (String line : pkgInfoLines) {
            String trim = deleteWhitespace(line);
            String[] split = trim.split(":");
            String key = split[0];
            if ("Name".equals(key)) {
                algorithmCode = split[1];
                break;
            }
        }
        if (StrUtil.isBlank(algorithmCode)) {
            throw new InspectionException("PKG-INFO中缺少算法名称定义");
        }
        String schemaJsonPath = baseDir + File.separator + algorithmCode + File.separator + "schema.json";
        if (!FileUtil.exist(schemaJsonPath)) {
            log.error("算法包中json定义文件未找到[{}]", schemaJsonPath);
            throw new InspectionException("算法包缺少JSON定义文件");
        }
        String jsonStr = FileUtil.readUtf8String(schemaJsonPath);
        AlgorithmSchemaModel algorithmSchemaModel;
        try {
            algorithmSchemaModel = JSONUtil.toBean(jsonStr, AlgorithmSchemaModel.class);
        } catch (Exception ex) {
            log.error("算法json内容解析失败", ex);
            throw new InspectionException("算法json内容解析失败");
        }

        checkAlgorithmSchema(algorithmSchemaModel);

        // 算法CODE取PKG-INFO中的Name字段，不取schema.json中的code
        InspAlgorithm algorithm = algorithmSchemaModel.getAlgorithmMetadata();
        algorithm.setCode(algorithmCode);

        // 查询算法图标 需将图标文件放置到和算法json文件同一级目录
        String iconPath = baseDir + File.separator + algorithmCode + File.separator + "icon.svg";
        if (FileUtil.exist(iconPath)) {
            // 将svg文件的文本内容读出来
            try {
                String svgContent = FileUtil.readUtf8String(iconPath);
                algorithm.setIcon(svgContent);
            } catch (Exception e) {
                log.warn("读取算法[{}]图标文件[{}]内容失败。", algorithmCode, iconPath, e);
            }
        } else {
            log.warn("算法[{}]图标文件[{}]不存在", algorithmCode, iconPath);
        }

        // 设置算法描述
        String readmePath = baseDir + File.separator + algorithmCode + File.separator + "README.md";
        if (FileUtil.exist(readmePath)) {
            String readmeStr = FileUtil.readUtf8String(readmePath);
            algorithm.setDescription(readmeStr);
        }
        return algorithmSchemaModel;
    }

    /**
     * 删除字符串中全部空格
     */
    private String deleteWhitespace(String str) {
        if (StrUtil.isEmpty(str)) {
            return str;
        }
        final int sz = str.length();
        final char[] chs = new char[sz];
        int count = 0;
        for (int i = 0; i < sz; i++) {
            if (!Character.isWhitespace(str.charAt(i))) {
                chs[count++] = str.charAt(i);
            }
        }
        if (count == sz) {
            return str;
        }
        if (count == 0) {
            return StrUtil.EMPTY;
        }
        return new String(chs, 0, count);
    }

    private void validateAlgorithmName(String algorithmName) {
        AssertUtil.isTrue(StrUtil.isNotBlank(algorithmName), "算法名称不能为空");

        // 算法名称长度校验
        AssertUtil.isTrue(algorithmName.length() <= 20, "算法名称最大长度为20");

        LambdaQueryWrapper<InspAlgorithm> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspAlgorithm::getName, algorithmName);
        int count = count(wrapper);
        AssertUtil.isTrue(count <= 0, "算法名称已存在");
    }

    public void checkAlgorithmSchema(AlgorithmSchemaModel algorithmSchemaModel) {
        if (Objects.isNull(algorithmSchemaModel)) {
            throw new InspectionException("算法JSON结构错误");
        }

        InspAlgorithm inspAlgorithm = algorithmSchemaModel.getAlgorithmMetadata();
        if (Objects.isNull(inspAlgorithm)) {
            throw new InspectionException("算法JSON中，算法主信息不能为空");
        }

        // ROI类型校验
        String roiDrawType = inspAlgorithm.getRoiDrawType();
        AssertUtil.isTrue(DataType.roiTypeNames().contains(roiDrawType), "ROI绘制类型[{}]配置错误", roiDrawType);

        // 算法类型校验
        List<String> keys = AlgorithmType.keys();
        String algorithmType = inspAlgorithm.getType();
        AssertUtil.isTrue(keys.contains(algorithmType), "算法类型[{}]配置错误", algorithmType);
    }

    public List<AlgorithmTreeNodeModel> listAlgorithm(String algorithmName) {
        LambdaQueryWrapper<InspAlgorithm> wrapper = new LambdaQueryWrapper<>();

        algorithmName = StrEscapeUtil.escapeChar(algorithmName);
        if (StrUtil.isNotBlank(algorithmName)) {
            wrapper.apply("name ILIKE CONCAT('%', {0}, '%')", algorithmName);
        }
        // 获取所有算法
        List<InspAlgorithm> list = list(wrapper);

        // 获取证书参数
        LicenseExtraParam licenseExtraParam = licenseService.getLicenseExtraParamInRedis();
        // 获取授权算法code列表，如果为空则默认为空列表
        List<String> algorithmCodes = (licenseExtraParam != null && licenseExtraParam.getAlgorithmCodes() != null)
                ? licenseExtraParam.getAlgorithmCodes()
                : Collections.emptyList();

        List<AlgorithmTreeNodeModel> result = new ArrayList<>();
        if (CollectionUtil.isEmpty(list)) {
            return result;
        }
        // 按照AlgorithmClassification枚举顺序组装列表结构
        Map<String, List<InspAlgorithm>> collectMap = list.stream().collect(Collectors.groupingBy(InspAlgorithm::getClassification));
        for (AlgorithmClassification algorithmClassification : AlgorithmClassification.values()) {
            String classificationName = algorithmClassification.name();
            List<InspAlgorithm> inspAlgorithms = collectMap.get(classificationName);
            if (CollectionUtil.isEmpty(inspAlgorithms)) {
                continue;
            }
            AlgorithmTreeNodeModel algorithmTreeNodeModel = new AlgorithmTreeNodeModel();
            algorithmTreeNodeModel.setClassification(algorithmClassification.getLabel());
            // 设置授权列表
            algorithmTreeNodeModel.setAuthList(algorithmCodes);
            // 按创建时间排序（由小到大）
            inspAlgorithms.sort(Comparator.comparing(InspAlgorithm::getCreateTime));
            algorithmTreeNodeModel.setAlgorithmList(inspAlgorithms);
            result.add(algorithmTreeNodeModel);
        }
        return result;
    }

    /**
     * 检查算法是否被引用，如果被引用抛出异常
     *
     * @param algorithmId 算法ID
     */
    private void checkAlgorithmUnUse(String algorithmId) {
        // 判断是否被引用
        LambdaQueryWrapper<InspAlgorithmInstance> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspAlgorithmInstance::getAlgorithmId, algorithmId);
        List<InspAlgorithmInstance> list = algorithmInstanceService.list(wrapper);
        if (CollUtil.isNotEmpty(list)) {
            // 当前算法被引用
            List<String> presetIds = list.stream().map(InspAlgorithmInstance::getPresetId).collect(Collectors.toList());

            Collection<InspProjectTreeNode> projectTreeNodes = projectTreeNodeService.listByIds(presetIds);
            List<String> nodeNames = projectTreeNodes.stream().map(InspProjectTreeNode::getLabel).collect(Collectors.toList());
            log.error("当前算法已被引用，操作失败---{}", JSON.toJSONString(nodeNames));
            throw new InspectionException("当前算法已被引用，请先解除引用关系后重新操作");
        }
    }

    @OperateLog(operateType = OperateType.DELETE, businessClassify = BusinessClassify.ALGORITHM)
    public Object removeAlgorithm(String algorithmId) {
        // 判断是否被引用
        checkAlgorithmUnUse(algorithmId);
        // 根据ID查询算法数据
        InspAlgorithm algorithm = getOneById(algorithmId);
        if (Objects.isNull(algorithm)) {
            throw new InspectionException("删除失败，算法不存在");
        }
        // 删除py服务中算法相关文件
        pythonServerService.removeAlgorithmToPy(algorithm.getCode(), algorithm.getType());
        // 从库中删除算法
        removeById(algorithmId);
        return true;
    }

    /**
     * 检查模型是否被引用，如果被引用抛出异常
     *
     * @param modelCode 模型编码
     */
    public void checkModelUnUse(String modelCode) {
        LambdaQueryWrapper<InspAlgorithm> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InspAlgorithm::getModelCode, modelCode);
        int count = count(queryWrapper);
        if (count >= 1) {
            throw new InspectionException("当前模型已被引用，请先解除引用关系后重新操作");
        }
    }

    /**
     * 根据模型编码查询算法名字列表
     *
     * @param modelCode 模型编码
     * @return 算法名字列表
     */
    public List<String> listAlgorithmNamesByModelCode(String modelCode) {
        LambdaQueryWrapper<InspAlgorithm> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspAlgorithm::getModelCode, modelCode);
        List<InspAlgorithm> algorithms = list(wrapper);
        return algorithms.stream()
                .map(InspAlgorithm::getName)
                .collect(Collectors.toList());
    }
}

