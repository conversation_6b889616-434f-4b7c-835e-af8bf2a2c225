package com.hollysys.inspection.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.hollysys.inspection.constants.algorithm.param.DataType;
import com.hollysys.inspection.entity.*;
import com.hollysys.inspection.listener.TestAlgorithmListener;
import com.hollysys.inspection.model.algorithm.execute.AlgorithmExecuteReq;
import com.hollysys.inspection.model.algorithm.execute.AlgorithmExecuteRsp;
import com.hollysys.inspection.model.context.ExecuteAlgorithmCtx;
import com.hollysys.inspection.model.execute.GetTestInputPicReq;
import com.hollysys.inspection.service.execute.ExecuteAlgorithmService;
import com.hollysys.inspection.service.protocol.impl.CameraCtrlProxyService;
import com.hollysys.inspection.utils.AssertUtil;
import com.hollysys.inspection.utils.MinioUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * 算法执行服务接口
 *
 * <AUTHOR>
 * @since 2023-02-01 15:36:00
 */
@Service
@Slf4j
public class ProcessExecuteService {

    @Resource
    private ExecuteAlgorithmService executeAlgorithmService;

    @Resource
    private TestAlgorithmListener testAlgorithmListener;

    @Resource
    private InspSceneDefinitionService sceneDefinitionService;

    @Resource
    private InspChannelInfoService channelInfoService;

    @Resource
    private InspPresetInfoService presetInfoService;

    @Resource
    private InspAlgorithmInstanceService algorithmInstanceService;

    @Resource
    private CameraCtrlProxyService cameraCtrlProxyService;

    /**
     * 算法调试执行
     */
    public AlgorithmExecuteRsp testAlgorithm(AlgorithmExecuteReq reqModel) {
        String clientKey = reqModel.getClientKey();
        AssertUtil.isTrue(StrUtil.isNotBlank(clientKey), "算法调试时客户端唯一标识不允许为空");

        List<String> images = reqModel.getImages();
        AssertUtil.isTrue(CollectionUtil.isNotEmpty(images), "算法调试时输入图片集合不允许为空");

        InspAlgorithmInstance algorithmInstance = algorithmInstanceService.getOneById(reqModel.getAlgorithmInstanceId());

        List<InspAlgorithmParamInstance> paramList = reqModel.getParamList();
        Optional<String> any = paramList.stream().map(InspAlgorithmParamInstance::getDataType)
                .filter(DataType.CHANNEL_PIC.name()::equals).findAny();

        if (any.isPresent()) {
            // 如果包含CHANNEL_PIC类型参数  则进行实时截图  不采用前端输入图片
            InspPresetInfo presetInfo = presetInfoService.getOneById(algorithmInstance.getPresetId());
            InspChannelInfo channelInfo = channelInfoService.getOneById(presetInfo.getChannelId());
            String pic = cameraCtrlProxyService.getPic(channelInfo);
            images = ListUtil.of(pic);
        } else {
            // 对前端输入图片，进行相对路径转换为绝对路径供后端使用
            images = MinioUtil.urlsToAbsolute(images);
        }

        // 模板匹配（多输入图情况下，只匹配第一张图）
        String sceneId = reqModel.getSceneId();
        InspSceneDefinition sceneInfo = sceneDefinitionService.getOneById(sceneId);

        // 上下文初始化
        ExecuteAlgorithmCtx algorithmCtx = new ExecuteAlgorithmCtx();
        String instanceId = algorithmInstance.getId();
        algorithmCtx.setAlgorithmInstance(algorithmInstance);
        // algorithmCtx.setAlgorithmId(algorithmInstance.getAlgorithmId());
        // algorithmCtx.setAlgorithmInstanceId(instanceId);
        // algorithmCtx.setInputParamList(reqModel.getParamList());
        // algorithmCtx.setInputImages(images);
        // algorithmCtx.setPresetId(algorithmInstance.getPresetId());
        algorithmCtx.setClientKey(clientKey);
        try {
            testAlgorithmListener.onStart(algorithmCtx);
        } catch (Exception exception) {
            log.error("算法执行onStart回调方法出现异常,algorithmInstanceId = {}", instanceId, exception);
        }
        AlgorithmExecuteRsp algorithmExecuteRsp;
        try {
            // 调用执行
            algorithmExecuteRsp = executeAlgorithmService.executeAlgorithm(algorithmCtx, algorithmInstance,
                    ListUtil.of(sceneInfo), images, paramList, reqModel.getRoi());

            algorithmCtx.setAlgorithmExecuteRsp(algorithmExecuteRsp);

            try {
                testAlgorithmListener.onFinish(algorithmCtx);
            } catch (Exception exception) {
                log.error("算法执行onFinish回调方法出现异常,algorithmInstanceId = {}", instanceId, exception);
            }
        } catch (Exception exception) {
            log.error("executeAlgorithm 出现异常, algorithmInstance id = {}", instanceId, exception);

            try {
                testAlgorithmListener.onException(algorithmCtx, exception);
            } catch (Exception e) {
                log.error("算法执行onException回调方法出现异常,algorithmInstanceId = {}", instanceId, e);
            }

            algorithmExecuteRsp = new AlgorithmExecuteRsp();
            algorithmExecuteRsp.setIsSuccess(false);
            algorithmExecuteRsp.setResultImgUrl(images.get(0));
        }
        String servePath = algorithmExecuteRsp.getResultImgUrl();
        // 转化为相对路径
        servePath = MinioUtil.urlToRelative(servePath);
        // 拼接随机数,避免前端缓存
        servePath += "?timestamp=" + System.currentTimeMillis();
        algorithmExecuteRsp.setResultImgUrl(servePath);
        return algorithmExecuteRsp;
    }

    public List<String> getTestInputPic(GetTestInputPicReq reqModel) {
        List<String> picUrls;
        Integer cutPicSize = reqModel.getCutPicSize();
        Integer cutPicInterval = reqModel.getCutPicInterval();
        InspChannelInfo channelInfo = channelInfoService.getOneById(reqModel.getChannelId());
        if (ObjectUtil.isAllNotEmpty(cutPicInterval, cutPicSize)) {
            // 批量多张截图
            picUrls = executeAlgorithmService.getAlgorithmInputImages(channelInfo, reqModel.getCutPicSize(), reqModel.getCutPicInterval());
        } else {
            // 单张截图
            String picUrl = cameraCtrlProxyService.getPic(channelInfo);
            picUrls = ListUtil.of(picUrl);
        }
        return MinioUtil.urlsToRelative(picUrls);
    }
}

