package com.hollysys.inspection.service;

import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.config.sso.HsmAuthProperties;
import com.hollysys.inspection.model.DataBaseLoginReq;
import com.hollysys.inspection.model.dbbase.DbBaseGetTokenReqModel;
import com.hollysys.inspection.model.dbbase.DbBaseGetTokenRespModel;
import com.hollysys.inspection.model.dbbase.DbBaseRespModel;
import com.hollysys.inspection.service.platform.DbBaseApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 用于测试ip地址的连通性
 */
@Service
@Slf4j
public class DataBaseNetworkService {

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private HsmAuthProperties hsmAuthProperties;

    @Resource
    private DbBaseApiService dbBaseApiService;

    @Value("${db-base.real-srv.api.get-token}")
    private String getTokenApiUrl;

    @Value("${db-base.real-srv.api-base}")
    private String apiBasePort;

    @PostConstruct
    public void init() {
        int timeout = 3000; // 5秒

        // 配置请求超时参数
        RequestConfig config = RequestConfig.custom()
                .setConnectTimeout(timeout) // 连接超时
                .setSocketTimeout(timeout)   // 从服务器读取数据的超时
                .setConnectionRequestTimeout(timeout) // 从连接池中获取连接的超时时间
                .build();

        CloseableHttpClient httpClient = HttpClients.custom()
                .setDefaultRequestConfig(config)
                .build();

        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory(httpClient);
        this.restTemplate = new RestTemplate(factory);
    }

    public Object testDataBaseLogin(String dynamicIp) {
        if (dynamicIp == null || dynamicIp.trim().isEmpty()) {
            log.warn("IP地址为空，请配置底座IP。");
            throw new InspectionException("IP地址为空，请配置底座IP");
        }
        String url = "http://" + dynamicIp + hsmAuthProperties.getSso().getPath();

        // 构建请求
        DataBaseLoginReq requestBody = getDataBaseLoginReq();

        // 设置 HTTP Headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 构造 HttpEntity，包含请求体和Header
        HttpEntity<DataBaseLoginReq> requestEntity = new HttpEntity<>(requestBody, headers);

        // 发送请求
        try {
            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );

            // 检查状态码是否为 200
            if (response.getStatusCode().is2xxSuccessful()) {
                log.debug("接口调用成功，状态码: {}, URL: {}", response.getStatusCode(), url);
                return null;
            } else {
                log.error("当前服务ip非数据底座服务，状态码: {}, URL: {}, 响应体: {}", response.getStatusCode(), url, response.getBody());
                throw new InspectionException("当前服务ip非数据底座服务");
            }
        } catch (RestClientException e) {
            log.error("底座服务调用失败，错误: {}", e.getMessage());
            throw new InspectionException("当前服务ip非数据底座服务");
        }
    }

    /**
     *
     * @param clientId 客户端id
     * @param secret 密钥
     * @param ip ip地址
     * @return 结果
     */
    public String validateDbBaseInfo(String clientId, String secret, String ip) {
        DbBaseGetTokenReqModel reqModel = new DbBaseGetTokenReqModel();
        reqModel.setCode(clientId);
        reqModel.setSecret(secret);
        DbBaseGetTokenRespModel dbBaseGetTokenRespModel = dbBaseApiService.doPostForEntity(getTokenUrl(ip), reqModel, DbBaseGetTokenRespModel.class);
        if (!DbBaseRespModel.isSuccess(dbBaseGetTokenRespModel)) {
            throw new InspectionException("获取数据底座token失败, 请检查客户端ID和密钥");
        }
        return dbBaseGetTokenRespModel.getData();
    }

    /**
     * 构建校验地址
     * @param ip 地址
     * @return 地址
     */
    private String getTokenUrl(String ip) {
        String baseApi = "http://" + ip + ":" + apiBasePort;
        return baseApi + getTokenApiUrl;
    }

    /**
     * 构建请求体
     * @return 请求体
     */
    @NotNull
    private static DataBaseLoginReq getDataBaseLoginReq() {
        DataBaseLoginReq requestBody = new DataBaseLoginReq();
        requestBody.setOperationName("signIn");

        Map<String, String> variables = new HashMap<>();
        variables.put("name", "");
        variables.put("pwd", "");
        requestBody.setVariables(variables);

        requestBody.setQuery("mutation signIn($name: String!, $pwd: String!) {\n" +
                "  signIn(name: $name, pwd: $pwd) {\n" +
                "    token\n" +
                "    sid\n" +
                "    needUpdatePasswd\n" +
                "    passwdExpireAt\n" +
                "    tipUpdatePasswd\n" +
                "    login2FAConfig\n" +
                "    __typename\n" +
                "  }\n" +
                "}");
        return requestBody;
    }
}
