package com.hollysys.inspection.service.schedule;

import com.hollysys.inspection.service.InspGlobalVariableService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 调度定时从工业数据底座读取点项值服务
 * 分为四个方法：
 */
@Slf4j
@Service
public class VariableValueReadFromDbService {

    @Resource
    private InspGlobalVariableService globalVariableService;

    /**
     * 1秒执行一次
     */
    @Scheduled(fixedDelay = 1000)
    public void fixedDelay1000() {
        globalVariableService.readAndSetFromPlatform(1);
    }

    /**
     * 3秒执行一次
     */
    @Scheduled(fixedDelay = 3000)
    public void fixedDelay3000() {
        globalVariableService.readAndSetFromPlatform(3);
    }

    /**
     * 5秒执行一次
     */
    @Scheduled(fixedDelay = 5000)
    public void fixedDelay5000() {
        globalVariableService.readAndSetFromPlatform(5);
    }

    /**
     * 10秒执行一次
     */
    @Scheduled(fixedDelay = 10000)
    public void fixedDelay10000() {
        globalVariableService.readAndSetFromPlatform(10);
    }
}
