package com.hollysys.inspection.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.constants.ProjectNodeType;
import com.hollysys.inspection.entity.*;
import com.hollysys.inspection.entity.base.BaseEntity;
import com.hollysys.inspection.mapper.InspPresetInfoMapper;
import com.hollysys.inspection.model.ModelInfoModel;
import com.hollysys.inspection.model.algorithm.configure.AlgorithmConfigureGetModel;
import com.hollysys.inspection.model.algorithm.configure.AlgorithmConfigureSaveModel;
import com.hollysys.inspection.model.algorithm.configure.SaveScheduleConfigReqModel;
import com.hollysys.inspection.model.algorithm.configure.SceneForParamConfigModel;
import com.hollysys.inspection.model.algorithm.schema.AlgorithmSchemaModel;
import com.hollysys.inspection.model.channel.ScaleModel;
import com.hollysys.inspection.model.execute.OutputListItem;
import com.hollysys.inspection.model.preset.PresetConfigModel;
import com.hollysys.inspection.model.preset.PresetInfoDetail;
import com.hollysys.inspection.model.variable.VariableRealtimeValue;
import com.hollysys.inspection.service.protocol.impl.CameraCtrlProxyService;
import com.hollysys.inspection.utils.AssertUtil;
import com.hollysys.inspection.utils.MinioUtil;
import com.hollysys.inspection.utils.OutputParamUtil;
import com.hollysys.inspection.utils.StrEscapeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * (InspPresetInfo)表服务接口
 *
 * <AUTHOR>
 * @since 2023-02-02 15:50:34
 */
@Slf4j
@Service
public class InspPresetInfoService extends ServiceImpl<InspPresetInfoMapper, InspPresetInfo> {

    @Resource
    private ProjectTreeNodeService treeNodeService;

    @Resource
    private InspChannelInfoService inspChannelInfoService;

    @Resource
    private CameraCtrlProxyService cameraCtrlProxyService;

    @Resource
    private ProjectTreeNodeService projectTreeNodeService;

    @Resource
    private InspSceneDefinitionService sceneDefinitionService;

    @Resource
    private InspAlgorithmService algorithmService;

    @Resource
    private InspAlgorithmParamService algorithmParamService;

    @Resource
    private InspAlgorithmInstanceService algorithmInstanceService;

    @Resource
    private InspAlgorithmParamInstanceService paramInstanceService;

    @Resource
    private InspScheduleTaskNodeService scheduleTaskNodeService;

    @Value("${tree-node.channel.max-algorithm-instance-count:100}")
    private int maxAlgorithmCountChannel;

    @Value("${tree-node.preset.max-algorithm-instance-count:20}")
    private int maxAlgorithmCountOfPreset;

    @Resource
    private InspAlarmRecordService inspAlarmRecordService;

    @Resource
    private InspRecordPresetService recordPresetService;

    @Resource
    private InspRecordAlgorithmService recordAlgorithmService;

    @Resource
    private InspGlobalVariableService globalVariableService;

    public InspPresetInfo getOneById(Serializable id) {
        InspPresetInfo presetInfo = super.getById(id);
        if (Objects.isNull(presetInfo)) {
            throw new InspectionException("预置点信息不存在");
        }
        return presetInfo;
    }

    public PresetInfoDetail getDetailById(Serializable id) {
        InspPresetInfo presetInfo = getOneById(id);
        PresetInfoDetail presetInfoDetail = new PresetInfoDetail();
        BeanUtils.copyProperties(presetInfo, presetInfoDetail);

        String channelId = presetInfo.getChannelId();
        InspProjectTreeNode channelInfo = treeNodeService.getOneById(channelId);
        presetInfoDetail.setChannelName(channelInfo.getLabel());
        return presetInfoDetail;
    }

    public PresetConfigModel getForConfig(String presetId) {
        PresetConfigModel presetConfigModel = new PresetConfigModel();
        // 预置点
        PresetInfoDetail detailById = getDetailById(presetId);
        presetConfigModel.setPresetDetailInfo(detailById);

        // 场景信息
        List<InspSceneDefinition> listByPresetId = sceneDefinitionService.listByPresetId(presetId);
        for (InspSceneDefinition sceneDefinition : listByPresetId) {
            sceneDefinition.picUrlToRelative();
        }
        presetConfigModel.setSceneList(listByPresetId);
        // 已选中算法
        LambdaQueryWrapper<InspAlgorithmInstance> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspAlgorithmInstance::getPresetId, presetId);
        wrapper.orderByAsc(InspAlgorithmInstance::getCreateTime);
        List<InspAlgorithmInstance> algorithmInstances = algorithmInstanceService.list(wrapper);
        presetConfigModel.setAlgorithmSelectedList(algorithmInstances);
        // 全部算法
        List<InspAlgorithm> algorithmList = algorithmService.list();
        algorithmList.sort(Comparator.comparing(BaseEntity::getId));
        presetConfigModel.setAlgorithmAllList(algorithmList);
        // 调度配置信息
        SaveScheduleConfigReqModel saveScheduleConfigReqModel = new SaveScheduleConfigReqModel();
        BeanUtils.copyProperties(detailById, saveScheduleConfigReqModel);
        presetConfigModel.setScheduleConfigInfo(saveScheduleConfigReqModel);
        return presetConfigModel;
    }

    public Map<String, List<ModelInfoModel>> getPresetInfoList() {
        LambdaQueryWrapper<InspProjectTreeNode> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(InspProjectTreeNode::getType, ProjectNodeType.CHANNEL);
        List<InspProjectTreeNode> treeNodes = treeNodeService.list(lambdaQueryWrapper);
        Map<String, List<ModelInfoModel>> maps = new HashMap<>();
        treeNodes.forEach(x -> {
            LambdaQueryWrapper<InspProjectTreeNode> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(InspProjectTreeNode::getParentId, x.getId());
            List<InspProjectTreeNode> list = treeNodeService.list(wrapper);
            maps.put(x.getLabel(), list.stream().map(y -> {
                InspPresetInfo presetInfo = getOneById(y.getId());
                ModelInfoModel model = new ModelInfoModel();
                BeanUtils.copyProperties(presetInfo, model);
                model.setSchedule(presetInfo.getIsSchedule());
                model.setName(y.getLabel());
                return model;
            }).collect(Collectors.toList()));
        });
        return maps;
    }

    @Transactional
    public Object saveAlgorithmConfigure(AlgorithmConfigureSaveModel reqModel) {
        String algorithmId = reqModel.getAlgorithmId();
        String algorithmInstanceId = reqModel.getAlgorithmInstanceId();
        String presetId = reqModel.getPresetId();
        if (StrUtil.hasBlank(algorithmId, algorithmInstanceId, presetId)) {
            log.error("saveAlgorithmConfigure error... algorithmInstanceId:{},presetId:{}",
                    algorithmInstanceId, presetId);
            throw new InspectionException("参数错误");
        }
        // 数据一致性校验
        InspAlgorithmInstance algorithmInstance = algorithmInstanceService.getOneById(algorithmInstanceId);
        if (!algorithmId.equals(algorithmInstance.getAlgorithmId())) {
            throw new InspectionException("当前提交的算法和系统中已绑定的算法不一致");
        }

        // 保存算法实例
        algorithmInstance.setRoi(reqModel.getRoi());
        algorithmInstance.setHasSaveConfig(true);
        algorithmInstance.setMatchEnable(reqModel.getMatchEnable());
        if (BooleanUtil.isTrue(algorithmInstance.getIsBatchProcessing())) {
            Integer cutPicInterval = reqModel.getCutPicInterval();
            Integer cutPicSize = reqModel.getCutPicSize();
            AssertUtil.isTrue(ObjectUtil.isAllNotEmpty(cutPicInterval, cutPicSize), "批量图片输入未配置完成");

            boolean isIn = NumberUtil.isIn(BigDecimal.valueOf(cutPicInterval), BigDecimal.valueOf(50), BigDecimal.valueOf(2000));
            AssertUtil.isTrue(isIn, "批量图片输入截图间隔配置错误,允许50-2000");

            isIn = NumberUtil.isIn(BigDecimal.valueOf(cutPicSize), BigDecimal.valueOf(1), BigDecimal.valueOf(10));
            AssertUtil.isTrue(isIn, "批量图片输入截图张数配置错误,允许1-10");

            algorithmInstance.setCutPicInterval(cutPicInterval);
            algorithmInstance.setCutPicSize(cutPicSize);
        }
        algorithmInstanceService.updateById(algorithmInstance);

        Map<String, List<InspAlgorithmParamInstance>> paramMapWeb = reqModel.getParamMap();
        // 保存算法参数实例
        Map<String, List<InspAlgorithmParamInstance>> paramMap;
        if (!BooleanUtil.isTrue(algorithmInstance.getMatchEnable())) {
            // 如果当前算法实例场景匹配关闭 则只更新第一个场景的参数
            List<InspSceneDefinition> inspSceneDefinitions = sceneDefinitionService.listByPresetId(presetId);
            InspSceneDefinition sceneDefinition = inspSceneDefinitions.get(0);
            paramMap = new HashMap<>();
            String sceneId = sceneDefinition.getId();
            paramMap.put(sceneId, paramMapWeb.get(sceneId));
        } else {
            paramMap = paramMapWeb;
        }

        paramInstanceService.updateParamList(algorithmId, algorithmInstanceId, paramMap);
        return true;
    }

    @Transactional
    public Object addAlgorithm(InspAlgorithmInstance algorithmInstance) {
        String presetId = algorithmInstance.getPresetId();
        AssertUtil.isTrue(StrUtil.isNotBlank(presetId), "预置点ID不允许为空");
        String algorithmId = algorithmInstance.getAlgorithmId();
        AssertUtil.isTrue(StrUtil.isNotBlank(algorithmId), "算法ID不允许为空");

        List<InspSceneDefinition> sceneDefinitions = sceneDefinitionService.listByPresetId(presetId);
        AssertUtil.isTrue(CollectionUtil.isNotEmpty(sceneDefinitions), "预置点下的场景列表为空，无法添加算法实例");

        String instanceId = algorithmInstance.getId();
        if (StrUtil.isBlank(instanceId)) {
            // 验证算法实例个数
            checkAlgorithmInstanceCount(presetId);

            // 新增
            return algorithmInstanceService.insertAlgorithmInstance(presetId, algorithmId, sceneDefinitions);
        } else {
            InspAlgorithmInstance instanceById = algorithmInstanceService.getOneById(instanceId);
            String oldAlgorithmId = instanceById.getAlgorithmId();
            if (!oldAlgorithmId.equals(algorithmId)) {
                // 判断出算法实例所绑定的算法已被修改为其他算法，删除旧算法实例相关数据
                algorithmInstanceService.removeByAlgorithmInstanceIds(Collections.singletonList(instanceId));
                inspAlarmRecordService.removeByAlgorithmInstanceId(instanceId);

                // 算法实例重新入库
                instanceById = algorithmInstanceService.insertAlgorithmInstance(presetId, algorithmId, sceneDefinitions);
            }
            return instanceById;
        }
    }

    /**
     * 验证算法实例个数
     *
     * @param presetId 预置点ID
     */
    private void checkAlgorithmInstanceCount(String presetId) {
        // 同一通道各预置点下合计可添加算法总数不超过100个
        InspProjectTreeNode presetNode = treeNodeService.getOneById(presetId);
        String channelId = presetNode.getParentId();
        List<InspProjectTreeNode> presetNodeList = treeNodeService.listByParentId(channelId);
        Set<String> presetIdList = presetNodeList.stream().map(InspProjectTreeNode::getId).collect(Collectors.toSet());
        List<InspAlgorithmInstance> nodeList1 = algorithmInstanceService.listByPresetIds(presetIdList);
        if (nodeList1.size() >= this.maxAlgorithmCountChannel) {
            throw new InspectionException(String.format("同一通道各预置点下合计可添加算法总数不超过%s个", this.maxAlgorithmCountChannel));
        }

        // 同一预置点下可添加算法个数不超过20个
        List<InspAlgorithmInstance> nodeList2 = algorithmInstanceService.listByPresetId(presetId);
        if (nodeList2.size() >= this.maxAlgorithmCountOfPreset) {
            throw new InspectionException(String.format("同一通道各预置点下合计可添加算法总数不超过%s个", this.maxAlgorithmCountOfPreset));
        }
    }

    @Transactional
    public Object deleteAlgorithm(List<String> algorithmInstanceIds) {
        algorithmInstanceService.removeByAlgorithmInstanceIds(algorithmInstanceIds);
        algorithmInstanceIds.forEach(x -> {
            recordAlgorithmService.removeByAlgorithmInstId(x);
            inspAlarmRecordService.removeByAlgorithmInstanceId(x);
        });
        return true;
    }

    public AlgorithmConfigureGetModel getAlgorithmConfigure(String algorithmInstanceId) {
        AlgorithmConfigureGetModel algorithmConfigureSaveModel = new AlgorithmConfigureGetModel();
        algorithmConfigureSaveModel.setAlgorithmInstanceId(algorithmInstanceId);

        // 查询算法
        InspAlgorithmInstance algorithmInstance = algorithmInstanceService.getOneById(algorithmInstanceId);
        algorithmConfigureSaveModel.setMatchEnable(algorithmInstance.getMatchEnable());
        algorithmConfigureSaveModel.setAlgorithmId(algorithmInstance.getAlgorithmId());
        algorithmConfigureSaveModel.setCutPicSize(algorithmInstance.getCutPicSize());
        algorithmConfigureSaveModel.setCutPicInterval(algorithmInstance.getCutPicInterval());
        algorithmConfigureSaveModel.setAlgorithmDes(algorithmInstance.getDescription());
        algorithmConfigureSaveModel.setCorrectParam(algorithmInstance.getCorrectParam());
        algorithmConfigureSaveModel.setCorrectEnable(algorithmInstance.getCorrectEnable());


        // 设置全部场景
        String presetId = algorithmInstance.getPresetId();
        List<InspSceneDefinition> sceneDefinitions = sceneDefinitionService.listByPresetId(presetId);
        if (CollectionUtil.isEmpty(sceneDefinitions)) {
            throw new InspectionException("获取场景列表数据为空");
        }

        // 获取算法定义数据
        AlgorithmSchemaModel algorithmDetail = algorithmService.getAlgorithmDetail(algorithmInstance.getAlgorithmId());
        algorithmConfigureSaveModel.setIsBatchProcessing(algorithmDetail.getAlgorithmMetadata().getIsBatchProcessing());

        // 获取通道分辨率
        InspProjectTreeNode treeNode = treeNodeService.getOneById(presetId);
        ScaleModel scale = inspChannelInfoService.getScale(treeNode.getParentId());
        // 场景集合
        List<SceneForParamConfigModel> sceneList = new ArrayList<>();
        Map<String, List<InspAlgorithmParamInstance>> paramMap = new HashMap<>();
        for (InspSceneDefinition inspSceneDefinition : sceneDefinitions) {
            // 判断场景图片的分辨率和摄像头实际分辨率是否一致
            String benchShape = inspSceneDefinition.getBenchShape();
            if (sceneDefinitionService.isNotEquals(benchShape, scale)) {
                String format = StrUtil.format("{}分辨率{}与当前视频流实际分辨率{}不一致，请统一分辨率后再试",
                        inspSceneDefinition.getName(), benchShape, scale);
                throw new InspectionException(format);
            }

            // 图片路径转化为前端使用的相对路径
            inspSceneDefinition.picUrlToRelative();
            SceneForParamConfigModel sceneModel = new SceneForParamConfigModel();
            BeanUtils.copyProperties(inspSceneDefinition, sceneModel);
            sceneModel.setConfigBenchPic(MinioUtil.urlToRelative(sceneModel.getConfigBenchPic()));
            sceneList.add(sceneModel);

            // 组织场景参数Map
            String sceneId = inspSceneDefinition.getId();
            List<InspAlgorithmParamInstance> paramInstance = getParamInstance(algorithmInstanceId, algorithmDetail, sceneId);
            paramMap.put(sceneId, paramInstance);
        }

        algorithmConfigureSaveModel.setParamMap(paramMap);
        algorithmConfigureSaveModel.setSceneList(sceneList);
        algorithmConfigureSaveModel.setPresetId(presetId);

        // 设置ROI
        algorithmConfigureSaveModel.setRoi(algorithmInstance.getRoi());
        algorithmConfigureSaveModel.setRoiRequired(algorithmInstance.getRoiRequired());
        algorithmConfigureSaveModel.setRoiDrawType(algorithmInstance.getRoiDrawType());

        return algorithmConfigureSaveModel;
    }

    private List<InspAlgorithmParamInstance> getParamInstance(String algorithmInstanceId,
                                                              AlgorithmSchemaModel algorithmDetail,
                                                              String sceneId) {
        List<InspAlgorithmParam> inputDefine = algorithmDetail.getInput();
        if (CollectionUtil.isEmpty(inputDefine)) {
            // 参数定义为空，直接返回空集合
            return new ArrayList<>();
        }
        // 查询之前保存的参数实例并转化为map
        List<InspAlgorithmParamInstance> paramInstListDb = paramInstanceService.listByInstanceIdAndSceneId(algorithmInstanceId, sceneId);
        Map<String, InspAlgorithmParamInstance> keyEntityMap = null;
        if (CollectionUtil.isNotEmpty(paramInstListDb)) {
            keyEntityMap = paramInstListDb.stream()
                    .filter(item -> Objects.nonNull(item.getValue()))
                    .collect(Collectors.toMap(InspAlgorithmParamInstance::getKey, item -> item));
        }
        // 以参数定义为主，生成新的算法实例集合
        List<InspAlgorithmParamInstance> paramInstListNew = new ArrayList<>();
        for (InspAlgorithmParam paramDefine : inputDefine) {
            InspAlgorithmParamInstance paramInstance = InspAlgorithmParamInstance.buildByAlgorithmParam(paramDefine);
            paramInstance.setAlgorithmInstanceId(algorithmInstanceId);
            paramInstance.setSceneId(sceneId);
            // 已经入过数据库
            if (CollectionUtil.isNotEmpty(keyEntityMap)) {
                InspAlgorithmParamInstance instance = keyEntityMap.get(paramDefine.getKey());
                if (Objects.nonNull(instance)) {
                    paramInstance.setId(instance.getId());
                    paramInstance.setValue(instance.getValue());
                } else {
                    // 默认参数值为定义中的默认值
                    paramInstance.setValue(paramDefine.getDefaultValue());
                }
            } else {
                // 默认参数值为定义中的默认值
                paramInstance.setValue(paramDefine.getDefaultValue());
            }
            paramInstListNew.add(paramInstance);
        }
        // 参数排序
        return CollectionUtil.sort(paramInstListNew, Comparator.comparing(InspAlgorithmParamInstance::getSortNo));
    }

    public List<InspPresetInfo> listByChannelIds(Collection<? extends Serializable> channelIds) {
        LambdaQueryWrapper<InspPresetInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(InspPresetInfo::getChannelId, channelIds);
        return list(wrapper);
    }

    public InspPresetInfo saveScheduleConfig(SaveScheduleConfigReqModel reqModel) {
        Integer preExecuteDelay = reqModel.getPreExecuteDelay();
        Integer postExecuteDelay = reqModel.getPostExecuteDelay();
        Integer validTimes = reqModel.getValidTimes();
        if (Objects.isNull(preExecuteDelay) || Objects.isNull(postExecuteDelay) || Objects.isNull(validTimes)) {
            throw new InspectionException("延时时间和验证次数不允许为空");
        }
        if (preExecuteDelay < 0 || postExecuteDelay < 0) {
            throw new InspectionException("延时时间最小值为0");
        }

        if (validTimes <= 0) {
            throw new InspectionException("验证次数最小值为1");
        }
        InspPresetInfo byId = getOneById(reqModel.getPresetId());
        BeanUtils.copyProperties(reqModel, byId);

        updateById(byId);
        return byId;
    }

    public void removeByPresetIds(List<String> idList) {
        // 删除预置点下的全部场景
        sceneDefinitionService.removeByPresetIds(idList);

        // 删除预置点下的算法实例
        algorithmInstanceService.removeByPresetIds(idList);

        // 解绑预置点下的全局变量配置
        globalVariableService.unbindByAlgorithmInstanceIds(idList);

        // 删除调度节点数据
        scheduleTaskNodeService.removeByModelIds(idList);

        // 删除失败记录数据
        recordPresetService.removeByPresetIds(idList);
        recordAlgorithmService.removeByPresetIds(idList);
        super.removeByIds(idList);
    }

    public Object updatePTZStatus(List<ModelInfoModel> modelInfoModels) {
        if (CollectionUtils.isEmpty(modelInfoModels)) {
            return true;
        }
        modelInfoModels.forEach(x -> {
            String id = x.getId();
            if (StrUtil.isBlank(id)) {
                return;
            }
            InspPresetInfo inspPresetInfo = new InspPresetInfo();
            inspPresetInfo.setId(id);
            inspPresetInfo.setIsSchedule(x.isSchedule());
            updateById(inspPresetInfo);
        });
        return true;
    }

    public InspPresetInfo savePtz(InspPresetInfo presetInfo) {
        String presetInfoId = presetInfo.getId();
        InspPresetInfo byId = getOneById(presetInfoId);

        String channelId = byId.getChannelId();
        Integer presetNum = byId.getPresetNum();
        if (Objects.isNull(presetNum)) {
            presetNum = getNewPresetNum(channelId);
        }
        InspProjectTreeNode treeNode = treeNodeService.getById(presetInfoId);
        InspChannelInfo channelInfo = inspChannelInfoService.getById(channelId);

        cameraCtrlProxyService.setPreset(channelInfo, presetNum, treeNode.getLabel());
        updateById(byId);

        return byId;
    }

    public Integer getNewPresetNum(String channelId) {
        List<InspProjectTreeNode> inspProjectTreeNodes = treeNodeService.listByParentId(channelId);
        if (CollUtil.isNotEmpty(inspProjectTreeNodes)) {
            List<String> presetIds = inspProjectTreeNodes.stream()
                    .filter(item -> ProjectNodeType.PRESET.equals(item.getType()))
                    .map(InspProjectTreeNode::getId)
                    .collect(Collectors.toList());
            Collection<InspPresetInfo> inspModelInstances = listByIds(presetIds);
            if (CollUtil.isNotEmpty(inspModelInstances)) {
                OptionalInt max = inspModelInstances.stream().filter(item -> Objects.nonNull(item.getPresetNum()))
                        .mapToInt(InspPresetInfo::getPresetNum).max();
                if (max.isPresent()) {
                    return max.getAsInt() + 1;
                }
            }
        }
        return 51;
    }

    public Page<OutputListItem> listOutputParam(Page<OutputListItem> page, String nodeId, String searchName) {
        // 查询全部算法实例
        List<InspAlgorithmInstance> algorithmInstances;
        if (StrUtil.isNotBlank(nodeId)) {
            // 查询节点下全部预置点ID
            List<InspProjectTreeNode> allSubNodesByParentId = projectTreeNodeService.getAllSubNodesByParentId(nodeId);
            List<String> presetIds = allSubNodesByParentId.stream()
                    .filter(item -> ProjectNodeType.PRESET.equals(item.getType()))
                    .map(InspProjectTreeNode::getId)
                    .collect(Collectors.toList());
            if (CollectionUtil.isEmpty(presetIds)) {
                return page;
            }
            algorithmInstances = algorithmInstanceService.listByPresetIds(presetIds);
        } else {
            algorithmInstances = algorithmInstanceService.list();
        }


        if (CollectionUtil.isEmpty(algorithmInstances)) {
            return page;
        }
        // 将全部算法实例按照预置点ID分组
        Map<String, List<InspAlgorithmInstance>> groupByPresetId = algorithmInstances.stream()
                .collect(Collectors.groupingBy(InspAlgorithmInstance::getPresetId));

        // 重新组织
        algorithmInstances = new ArrayList<>();
        for (Map.Entry<String, List<InspAlgorithmInstance>> entry : groupByPresetId.entrySet()) {
            List<InspAlgorithmInstance> value = entry.getValue();
            if (CollectionUtil.isEmpty(value)) {
                continue;
            }
            value.sort(Comparator.comparing(InspAlgorithmInstance::getCreateTime));
            for (int i = 0; i < value.size(); i++) {
                InspAlgorithmInstance algorithmInstance = value.get(i);
                String algorithmInstName = OutputParamUtil.getAlgorithmInstName(algorithmInstance, i);
                algorithmInstance.setName(algorithmInstName);
            }
            algorithmInstances.addAll(value);
        }

        // 组装全量数据集合
        algorithmInstances.sort(Comparator.comparing(InspAlgorithmInstance::getCreateTime));
        List<OutputListItem> outputListItems = new ArrayList<>();
        for (InspAlgorithmInstance algorithmInstance : algorithmInstances) {
            List<InspAlgorithmParam> outputParams = algorithmParamService.listOutputByAlgorithmId(algorithmInstance.getAlgorithmId());
            if (CollectionUtil.isEmpty(outputParams)) {
                continue;
            }
            for (InspAlgorithmParam outputParam : outputParams) {
                VariableRealtimeValue valueObj = OutputParamUtil.readFromRedis(algorithmInstance.getId(),
                        outputParam.getKey());

                OutputListItem outputListItem = new OutputListItem();
                InspProjectTreeNode presetNode = projectTreeNodeService.getOneById(algorithmInstance.getPresetId());
                String join = OutputParamUtil.getOutputParamLabel(presetNode.getLabel(), algorithmInstance.getName(),
                        outputParam.getLabel());
                outputListItem.setOutputName(join);
                outputListItem.setValueType(outputParam.getDataType());
                if (Objects.nonNull(valueObj) && Objects.nonNull(valueObj.getValue())) {
                    outputListItem.setValue(valueObj.getValue().toString());
                    String format = DateUtil.format(DateUtil.date(valueObj.getTimestamp()), DatePattern.NORM_DATETIME_MS_PATTERN);
                    outputListItem.setValueTime(format);
                }
                outputListItems.add(outputListItem);
            }
        }
        // 按名称搜索
        if (StrUtil.isNotBlank(searchName)) {
            outputListItems = outputListItems.stream()
                    .filter(item -> StrUtil.containsIgnoreCase(item.getOutputName(), StrEscapeUtil.escapeChar(searchName)))
                    .collect(Collectors.toList());
        }
        // 分页返回
        int totalSize = outputListItems.size();
        int pageSize = (int) page.getSize();
        int totalPage = PageUtil.totalPage(totalSize, pageSize);
        page.setTotal(totalSize);
        page.setPages(totalPage);

        int current = (int) page.getCurrent();
        List<OutputListItem> records = ListUtil.page(current - 1, pageSize, outputListItems);
        page.setRecords(records);
        return page;
    }

    public boolean jumpToPreset(String presetId) {
        InspPresetInfo presetInfo = getOneById(presetId);
        String channelId = presetInfo.getChannelId();
        InspChannelInfo channelInfo = inspChannelInfoService.getOneById(channelId);
        return cameraCtrlProxyService.gotoPreset(channelInfo, presetInfo);
    }
}

