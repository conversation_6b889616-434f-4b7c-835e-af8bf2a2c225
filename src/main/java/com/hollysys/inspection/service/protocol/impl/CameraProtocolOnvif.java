package com.hollysys.inspection.service.protocol.impl;

import cn.hutool.core.util.StrUtil;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.constants.FileDirEnum;
import com.hollysys.inspection.constants.channel.CameraPtzAction;
import com.hollysys.inspection.constants.channel.DeviceManufacturer;
import com.hollysys.inspection.entity.InspChannelInfo;
import com.hollysys.inspection.entity.InspPresetInfo;
import com.hollysys.inspection.model.channel.RtspStreamModel;
import com.hollysys.inspection.model.sdk.*;
import com.hollysys.inspection.service.PythonServerService;
import com.hollysys.inspection.service.protocol.ICameraCtrlProtocol;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * Onvif协议
 */
@Slf4j
@Service
public class CameraProtocolOnvif implements ICameraCtrlProtocol {

    @Resource
    private PythonServerService pythonServerService;

    @Override
    public LoginResp login(InspChannelInfo channelInfo) {
        return null;
    }

    @Override
    public void logout(LoginResp loginResp) {
    }

    @Override
    public String getPic(InspChannelInfo channelInfo) {
        return pythonServerService.getChannelCurrentPicOnvif(FileDirEnum.TEMP, channelInfo.getAddress(),
                channelInfo.getUsername(), channelInfo.getPassword());
    }

    @Override
    public boolean gotoPresetAsync(InspChannelInfo channelInfo, InspPresetInfo presetInfo) {
        return pythonServerService.gotoPointAsync(channelInfo.getAddress(), channelInfo.getUsername(), channelInfo.getPassword(),
                presetInfo.getPresetNum());
    }

    @Override
    public boolean gotoPreset(InspChannelInfo channelInfo, InspPresetInfo presetInfo) {
        try {
            return pythonServerService.gotoPoint(channelInfo.getAddress(), channelInfo.getUsername(), channelInfo.getPassword(),
                    presetInfo.getPresetNum());
        } catch (Exception exception) {
            log.error("通道{}预置点跳转出现异常", channelInfo.getAddress(), exception);
            return false;
        }
    }

    @Override
    public void setPreset(InspChannelInfo channelInfo, Integer presetNum, String presetName) {
        pythonServerService.setPoint(channelInfo.getAddress(), channelInfo.getUsername(), channelInfo.getPassword(),
                presetName, presetNum);
    }

    @Override
    public void delPreset(InspChannelInfo channelInfo, InspPresetInfo presetInfo) {
        pythonServerService.removePoint(channelInfo.getAddress(), channelInfo.getUsername(), channelInfo.getPassword(),
                presetInfo.getPresetNum());
    }

    @Override
    public void startCameraPtz(InspChannelInfo channelInfo, CameraPtzAction action) {
        pythonServerService.startControlOperate(channelInfo.getAddress(), channelInfo.getUsername(),
                channelInfo.getPassword(), action.getOnvif());
    }

    @Override
    public void stopCameraPtz(InspChannelInfo channelInfo, CameraPtzAction action) {
        pythonServerService.stopControlOperate(channelInfo.getAddress(), channelInfo.getUsername(),
                channelInfo.getPassword());
    }

    @Override
    public List<RtspStreamModel> getRtspUrl(InspChannelInfo channelInfo) {
        try {
            return pythonServerService.getChannelStream(channelInfo.getAddress(), channelInfo.getUsername(),
                    channelInfo.getPassword());
        } catch (Exception exception) {
            log.error("获取视频通道的RTSP流地址出现异常", exception);
            throw new InspectionException("获取视频通道的RTSP流地址失败，请检查通道相关配置");
        }
    }

    @Override
    public List<RecordingFileResp> getRecordingFilesList(RecordingFileRes req) {
        return Collections.emptyList();
    }

    @Override
    public DownloadOptRes downloadRecordingFileByName(DownloadRecordingFileRes downloadRecordingFileRes) {
        return null;
    }

    // @Override
    // public ScaleModel getDisplayResolution(InspChannelInfo channelInfo) {
    //     // 分辨率由上层代理统一实现
    //     throw new InspectionException("此分辨率方法禁止调用");
    // }

    @Override
    public String getManufacturer(InspChannelInfo channelInfo) {
        if (StrUtil.isEmpty(channelInfo.getAddress()) || StrUtil.isEmpty(channelInfo.getUsername()) || StrUtil.isEmpty(channelInfo.getPassword())) {
            throw new InspectionException("请先填写通道地址，用户名，密码");
        }
        String cameraManufacturerInfo = pythonServerService.getCameraManufacturerInfo(channelInfo.getAddress(), channelInfo.getUsername(), channelInfo.getPassword());
        if (cameraManufacturerInfo.contains(DeviceManufacturer.DAHUA.name())) {
            return DeviceManufacturer.DAHUA.name();
        }
        if (cameraManufacturerInfo.contains(DeviceManufacturer.HIKVISION.name())) {
            return DeviceManufacturer.HIKVISION.name();
        }
        if (cameraManufacturerInfo.contains(DeviceManufacturer.UNIVIEW.name())) {
            return DeviceManufacturer.UNIVIEW.name();
        }
        String format = StrUtil.format("获取的厂商[{}]不在支持范围", cameraManufacturerInfo);
        log.error(format);
        throw new InspectionException(format);
    }

    @Override
    public String playBackByTime(PlayBackByTimeRes playBackByTimeRes) {
        return "";
    }
}
