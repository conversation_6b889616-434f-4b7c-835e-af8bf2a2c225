package com.hollysys.inspection.service.protocol.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.constants.InspConstants;
import com.hollysys.inspection.constants.channel.CameraPtzAction;
import com.hollysys.inspection.constants.channel.DeviceManufacturer;
import com.hollysys.inspection.entity.InspChannelInfo;
import com.hollysys.inspection.entity.InspPresetInfo;
import com.hollysys.inspection.model.channel.RtspStreamModel;
import com.hollysys.inspection.model.sdk.*;
import com.hollysys.inspection.sdk.ys.NetDEVSDKLib;
import com.hollysys.inspection.service.protocol.ICameraCtrlProtocol;
import com.hollysys.inspection.utils.LocalFileServerUtil;
import com.hollysys.inspection.utils.SdkTokenUtil;
import com.hollysys.inspection.utils.SleepUtil;
import com.sun.jna.Native;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.IntByReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.*;

/**
 * 宇视SDK协议
 */
@Slf4j
@Service
public class CameraProtocolYs implements ICameraCtrlProtocol {
    private static NetDEVSDKLib netDevSdk = null; // sdk实例
    private static final int START_STREAM_NUM = 1; // 起始码流号
    private static final int PORT = 80; // 登录端口
    private static final String RTSP_URL_FORMAT = "rtsp://{}/media{}/video{}"; // rtsp地址格式

    @Override
    public LoginResp login(InspChannelInfo channelInfo) {
        // 从缓存中获取摄像机的登录ID
        LoginResp loginCache = SdkTokenUtil.getLoginCache(channelInfo);
        if (Objects.nonNull(loginCache)) {
            return loginCache;
        }
        String ip = channelInfo.getAddress();
        String username = channelInfo.getUsername();
        String password = channelInfo.getPassword();

        // 创建设备登录信息结构体实例
        NetDEVSDKLib.NETDEV_DEVICE_LOGIN_INFO_S stDevLoginInfo = new NetDEVSDKLib.NETDEV_DEVICE_LOGIN_INFO_S();
        NetDEVSDKLib.NETDEV_SELOG_INFO_S loginInfo = new NetDEVSDKLib.NETDEV_SELOG_INFO_S();
        System.arraycopy(ip.getBytes(), 0, stDevLoginInfo.szIPAddr, 0, ip.getBytes().length);
        System.arraycopy(username.getBytes(), 0, stDevLoginInfo.szUserName, 0, username.getBytes().length);
        System.arraycopy(password.getBytes(), 0, stDevLoginInfo.szPassword, 0, password.getBytes().length);
        stDevLoginInfo.dwPort = PORT;
        stDevLoginInfo.dwLoginProto = 0;

        // 调用SDK的登录方法，传入登录信息结构体和登录日志信息结构体，返回用户ID
        Pointer userId = netDevSdk.NETDEV_Login_V30(stDevLoginInfo, loginInfo);

        if (userId != null) {
            // 截图需要调用此方法，原因暂时不知
            getChannelInfo(userId);
            // 将新的登录ID存入缓存中
            LoginResp loginResp = LoginResp.buildYsToken(channelInfo, userId);
            SdkTokenUtil.setLoginCache(loginResp);
            return loginResp;
        } else {
            log.error("登录设备 {} 失败，错误码：{}", ip, netDevSdk.NETDEV_GetLastError());
            throw new InspectionException("登录设备失败");
        }
    }

    /**
     * 获取当前通道信息
     *
     * @param userId 登录id
     */
    private void getChannelInfo(Pointer userId) {
        // 定义最大通道数
        int nMaxChlCount = 256;

        // 存储通道数量
        IntByReference dwChlCount = new IntByReference(nMaxChlCount);

        // 存储通道详细信息
        NetDEVSDKLib.NETDEV_VIDEO_CHL_DETAIL_INFO_EX_S[] stVideoChlList = (NetDEVSDKLib.NETDEV_VIDEO_CHL_DETAIL_INFO_EX_S[]) new NetDEVSDKLib.NETDEV_VIDEO_CHL_DETAIL_INFO_EX_S().toArray(nMaxChlCount);

        // 获取通道信息
        if (!netDevSdk.NETDEV_QueryVideoChlDetailListEx(userId, dwChlCount, stVideoChlList)) {
            throw new InspectionException("设备获取通道信息失败 >> " + netDevSdk.NETDEV_GetLastError());
        } else {
            // 如果获取成功，记录日志并更新通道数量和码流数量
            log.info("设备登录成功，获取通道信息成功，通道数: {}", dwChlCount.getValue());
        }
    }

    @Override
    public void logout(LoginResp loginResp) {
        Pointer ysToken = loginResp.getYsToken();
        // 检查netDevSdk和lUserID是否都不为空
        if (Objects.nonNull(netDevSdk) && Objects.nonNull(ysToken)) {

            // 调用netDevSdk的NETDEV_Logout方法进行登出操作，返回布尔值表示操作是否成功
            boolean result = netDevSdk.NETDEV_Logout(ysToken);

            // 如果登出失败
            if (!result) {
                log.error("宇视SDK登出失败，错误码: {}", netDevSdk.NETDEV_GetLastError());
            }

            // 资源清理
            // netDevSdk.NETDEV_Cleanup();
        }
    }

    /**
     * 抓图
     *
     * @param channelInfo 通道信息
     * @return 抓图照片路径
     */
    @Override
    public String getPic(InspChannelInfo channelInfo) {
        // 登录
        LoginResp login = login(channelInfo);
        Pointer loginId = login.getYsToken();

        Date dateNow = new Date();
        String dateStr = DateUtil.format(dateNow, InspConstants.yyyyMMddHHmmss);
        File tempFile = LocalFileServerUtil.getTempFile(dateStr + RandomUtil.randomInt(1000, 10000));
        String fileName = tempFile.getAbsolutePath();
        // 通过RTSP获取流编号，去最后一个字符-1
        String rtsp = channelInfo.getRtsp();
        String sub = StrUtil.sub(rtsp, rtsp.length() - 1, rtsp.length());
        int streamNum = NumberUtil.parseInt(sub) - 1;
        // 抓图
        boolean result = netDevSdk.NETDEV_CaptureNoPreview(loginId, channelInfo.getChannelNum(), streamNum,
                fileName, NetDEVSDKLib.NETDEV_PICTURE_FORMAT_E.NETDEV_PICTURE_JPG);
        if (!result) {
            log.error("抓图失败，错误码：{}", netDevSdk.NETDEV_GetLastError()); // 输出错误信息
            throw new InspectionException("抓图失败，错误码：" + netDevSdk.NETDEV_GetLastError()); // 抛出异常
        } else {
            log.debug("抓图成功: {}", fileName); // 记录成功信息
        }
        // 返回文件名
        return fileName + ".jpg";
    }

    /**
     * 同步跳转预置点
     *
     * @param channelInfo 通道信息
     * @param presetInfo  预置点信息
     * @return 跳转结果
     */
    @Override
    public boolean gotoPresetAsync(InspChannelInfo channelInfo, InspPresetInfo presetInfo) {
        // 调用跳转预置点异步方法
        boolean result = gotoPreset(channelInfo, presetInfo);

        // 等待4秒
        SleepUtil.sleepSecond(4);

        return result;
    }

    /**
     * 跳转预置点
     *
     * @param channelInfo 通道信息
     * @param presetInfo  预置点信息
     * @return 跳转结果
     */
    @Override
    public boolean gotoPreset(InspChannelInfo channelInfo, InspPresetInfo presetInfo) {
        // 登录
        LoginResp login = login(channelInfo);
        Pointer loginId = login.getYsToken();

        // 调用跳转预置点方法
        boolean result = netDevSdk.NETDEV_PTZPreset_Other(loginId, channelInfo.getChannelNum(),
                NetDEVSDKLib.NETDEV_PTZ_PRESETCMD_E.NETDEV_PTZ_GOTO_PRESET, "", presetInfo.getPresetNum());

        if (!result) {
            log.error("跳转预置点失败，错误码：{}", netDevSdk.NETDEV_GetLastError());
            return false;
        }
        return true;
    }

    /**
     * 设置预置点
     *
     * @param channelInfo 通道信息
     * @param presetNum   预置点号
     * @param presetName  预置点名字
     */
    @Override
    public void setPreset(InspChannelInfo channelInfo, Integer presetNum, String presetName) {
        // 登录
        LoginResp login = login(channelInfo);
        Pointer loginId = login.getYsToken();

        // 调用设置预置点方法
        boolean result = netDevSdk.NETDEV_PTZPreset_Other(loginId, channelInfo.getChannelNum(),
                NetDEVSDKLib.NETDEV_PTZ_PRESETCMD_E.NETDEV_PTZ_SET_PRESET, presetName, presetNum);

        if (!result) {
            log.error("设置预置点失败，错误码：{}", netDevSdk.NETDEV_GetLastError());
        } else {
            log.debug("设置预置点成功: {}", presetNum);
        }
    }

    /**
     * 删除预置点
     *
     * @param channelInfo 通道信息
     * @param presetInfo  预置点信息
     */
    @Override
    public void delPreset(InspChannelInfo channelInfo, InspPresetInfo presetInfo) {
        // 登录
        LoginResp login = login(channelInfo);
        Pointer loginId = login.getYsToken();

        // 调用删除预置点方法
        boolean result = netDevSdk.NETDEV_PTZPreset_Other(loginId, channelInfo.getChannelNum(),
                NetDEVSDKLib.NETDEV_PTZ_PRESETCMD_E.NETDEV_PTZ_CLE_PRESET, "", presetInfo.getPresetNum());

        if (!result) {
            log.error("删除预置点失败，错误码：{}", netDevSdk.NETDEV_GetLastError());
        } else {
            log.debug("删除预置点成功: {}", presetInfo.getPresetNum());
        }
    }

    /**
     * 开始云台操作
     *
     * @param channelInfo 通道信息
     * @param action      动作
     */
    @Override
    public void startCameraPtz(InspChannelInfo channelInfo, CameraPtzAction action) {
        setPTZControl(channelInfo, action.getYs());
    }

    /**
     * 停止云台操作
     *
     * @param channelInfo 通道信息
     * @param action      动作
     */
    @Override
    public void stopCameraPtz(InspChannelInfo channelInfo, CameraPtzAction action) {
        setPTZControl(channelInfo, NetDEVSDKLib.NETDEV_PTZ_E.NETDEV_PTZ_ALLSTOP);
    }

    /**
     * 获取rtsp地址
     *
     * @param channelInfo 通道信息，包含地址、用户名和密码等信息
     * @return rtsp地址列表，返回一个包含RTSP地址的列表
     */
    @Override
    public List<RtspStreamModel> getRtspUrl(InspChannelInfo channelInfo) {
        // 登录
        LoginResp login = login(channelInfo);
        // 获取全部通道信息
        // 定义最大通道数
        int nMaxChlCount = 5;
        IntByReference dwChlCount = new IntByReference(nMaxChlCount);
        // 存储通道详细信息
        NetDEVSDKLib.NETDEV_VIDEO_CHL_DETAIL_INFO_EX_S[] stVideoChlList = (NetDEVSDKLib.NETDEV_VIDEO_CHL_DETAIL_INFO_EX_S[]) new NetDEVSDKLib.NETDEV_VIDEO_CHL_DETAIL_INFO_EX_S().toArray(nMaxChlCount);

        // 获取通道信息
        boolean ret = netDevSdk.NETDEV_QueryVideoChlDetailListEx(login.getYsToken(), dwChlCount, stVideoChlList);
        if (!ret) {
            log.error("设备获取通道信息失败 >> {}", netDevSdk.NETDEV_GetLastError());
            throw new InspectionException("设备获取通道信息失败");
        }
        if (ArrayUtil.isEmpty(stVideoChlList)) {
            throw new InspectionException("设备获取设备通道列表为空");
        }
        List<RtspStreamModel> rtspUrls = new ArrayList<>();
        for (NetDEVSDKLib.NETDEV_VIDEO_CHL_DETAIL_INFO_EX_S chlDetailInfoExS : stVideoChlList) {
            if (chlDetailInfoExS.enStatus != 1) {
                continue;
            }

            int streamNum = chlDetailInfoExS.dwStreamNum;
            if (streamNum <= 0) {
                continue;
            }
            int channelNum = chlDetailInfoExS.dwChannelID;
            for (int i = 1; i <= streamNum; i++) {
                // 生成RTSP地址
                String rtspUrl = StrUtil.format(RTSP_URL_FORMAT, channelInfo.getAddress(), channelNum, i);
                // 添加到列表中
                RtspStreamModel rtspStreamModel = RtspStreamModel.createRtspStreamModel(channelNum, rtspUrl);
                rtspUrls.add(rtspStreamModel);
            }
        }
        return rtspUrls;
    }

    @Override
    public List<RecordingFileResp> getRecordingFilesList(RecordingFileRes req) {
        return Collections.emptyList();
    }

    @Override
    public DownloadOptRes downloadRecordingFileByName(DownloadRecordingFileRes downloadRecordingFileRes) {
        return null;
    }

    /**
     * 获取分辨率
     *
     * @param channelInfo 通道信息
     * @return 分辨率
     */
    // @Override
    // public ScaleModel getDisplayResolution(InspChannelInfo channelInfo) {
    //     return pythonServerService.getScaleByOnvif(channelInfo);
    // }
    @Override
    public String getManufacturer(InspChannelInfo channelInfo) {
        return DeviceManufacturer.UNIVIEW.name();
    }

    @Override
    public String playBackByTime(PlayBackByTimeRes playBackByTimeRes) {
        return "";
    }

    /**
     * 云台控制
     *
     * @param dwPTZCommand 控制命令
     */
    private void setPTZControl(InspChannelInfo channelInfo, Integer dwPTZCommand) {
        // 调用登录方法，获取用户ID
        LoginResp login = login(channelInfo);
        Pointer loginId = login.getYsToken();

        // 云台控制
        boolean status = netDevSdk.NETDEV_PTZControl_Other(loginId, channelInfo.getChannelNum(), dwPTZCommand, 3);

        if (!status) {
            log.error("云台控制操作失败，错误码：{}", netDevSdk.NETDEV_GetLastError());
        }
    }

    /**
     * 初始化sdk
     */
    public void init() {
        // 获取当前操作系统的名称
        String osName = System.getProperty("os.name");

        // 如果为null则创建SDK实例
        if (netDevSdk == null) {
            if (!createSDKInstance(osName)) {
                log.error("创建宇视SDK实例失败");
                throw new InspectionException("创建宇视SDK实例失败");
            }
        }

        // 初始化SDK
        boolean result = netDevSdk.NETDEV_Init();

        // 检查SDK初始化是否成功
        if (!result) {
            // 记录错误日志
            log.error("宇视SDK初始化失败，错误码: {}", netDevSdk.NETDEV_GetLastError());
            throw new InspectionException("宇视SDK初始化失败");
        } else {
            log.debug("宇视SDK初始化成功");
        }
    }

    /**
     * 创建sdk实例 读取库文件
     *
     * @param osName 操作系统名称
     * @return 初始化结果
     */
    private boolean createSDKInstance(String osName) {
        if (netDevSdk == null) {
            synchronized (NetDEVSDKLib.class) {
                String strDllPath = "";
                // 获取操作系统架构
                String osArch = System.getProperty("os.arch").toLowerCase();
                try {
                    if (osName.toLowerCase().startsWith("windows")) {
                        // win系统加载SDK库路径
                        strDllPath = System.getProperty("user.dir") + "\\libs\\ys_win64\\NetDEVSDK.dll";
                    } else if (osName.toLowerCase().startsWith("linux")) {
                        // Linux系统加载SDK库路径
                        if (osArch.contains("aarch")) {
                            // ARM 架构 Linux 系统加载 SDK 库路径
                            strDllPath = "/tmp/ys_linux_arm/libNetDEVSDK.so";
                        } else {
                            // 其他 Linux 架构 (例如 x86_64) 加载 SDK 库路径
                            strDllPath = "/libs/ys_linux64/libNetDEVSDK.so";
                        }
                    }

                    // 加载库
                    netDevSdk = Native.load(strDllPath, NetDEVSDKLib.class);
                    log.debug("加载宇视sdk库路径: {}", strDllPath);
                } catch (Exception ex) {
                    log.error("加载宇视SDK库失败,库路径: {}， 错误信息：{}", strDllPath, ex.getMessage());
                    return false;
                }
            }
        }
        return true;
    }
}

