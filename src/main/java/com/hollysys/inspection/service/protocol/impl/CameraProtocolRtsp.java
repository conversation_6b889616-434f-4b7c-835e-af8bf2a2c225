package com.hollysys.inspection.service.protocol.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.constants.InspConstants;
import com.hollysys.inspection.constants.channel.CameraPtzAction;
import com.hollysys.inspection.controller.base.Resp;
import com.hollysys.inspection.entity.InspChannelInfo;
import com.hollysys.inspection.entity.InspPresetInfo;
import com.hollysys.inspection.model.channel.RtspStreamModel;
import com.hollysys.inspection.model.sdk.*;
import com.hollysys.inspection.service.protocol.ICameraCtrlProtocol;
import com.hollysys.inspection.utils.AssertUtil;
import com.hollysys.inspection.utils.InspStringUtils;
import com.hollysys.inspection.utils.LocalFileServerUtil;
import com.hollysys.inspection.utils.MyHttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.*;

/**
 * 使用RTSP作为接入协议
 */
@Slf4j
@Service
public class CameraProtocolRtsp implements ICameraCtrlProtocol {

    @Value("${rtsp-pic-srv.get-pic-url}")
    private String getPicUrl;

    @Value("${rtsp-pic-srv.base-url}")
    private String baseUrl;

    @Override
    public LoginResp login(InspChannelInfo channelInfo) {
        return null;
    }

    @Override
    public void logout(LoginResp loginResp) {
    }

    @Override
    public String getPic(InspChannelInfo channelInfo) {
        String rtsp = channelInfo.getRtsp();
        AssertUtil.isTrue(StrUtil.isNotBlank(rtsp), "截图失败，RTSP地址未配置");
        String rtspUri = InspStringUtils.replaceRTSP(rtsp, channelInfo.getUsername(), channelInfo.getPassword());
        return getPicFromRtsp(rtspUri);
    }

    @Override
    public boolean gotoPresetAsync(InspChannelInfo channelInfo, InspPresetInfo presetInfo) {
        // method is empty
        return true;
    }

    @Override
    public boolean gotoPreset(InspChannelInfo channelInfo, InspPresetInfo presetInfo) {
        // method is empty
        return true;
    }

    @Override
    public void setPreset(InspChannelInfo channelInfo, Integer presetNum, String presetName) {
        // method is empty
    }

    @Override
    public void delPreset(InspChannelInfo channelInfo, InspPresetInfo presetInfo) {
        // method is empty
    }

    @Override
    public void startCameraPtz(InspChannelInfo channelInfo, CameraPtzAction action) {
        // method is empty
    }

    @Override
    public void stopCameraPtz(InspChannelInfo channelInfo, CameraPtzAction action) {
        // method is empty
    }

    @Override
    public List<RtspStreamModel> getRtspUrl(InspChannelInfo channelInfo) {
        // method is empty
        return new ArrayList<>();
    }

    @Override
    public List<RecordingFileResp> getRecordingFilesList(RecordingFileRes req) {
        return Collections.emptyList();
    }

    @Override
    public DownloadOptRes downloadRecordingFileByName(DownloadRecordingFileRes downloadRecordingFileRes) {
        return null;
    }

    // @Override
    // public ScaleModel getDisplayResolution(InspChannelInfo channelInfo) {
    //     // 分辨率由上层代理统一实现
    //     throw new InspectionException("此分辨率方法禁止调用");
    // }

    @Override
    public String getManufacturer(InspChannelInfo channelInfo) {
        return "";
    }

    @Override
    public String playBackByTime(PlayBackByTimeRes playBackByTimeRes) {
        return "";
    }

    private String getPicFromRtsp(String rtspUrl) {
        TimeInterval timer = DateUtil.timer();
        Map<String, Object> param = MapUtil.of("rtspUrl", rtspUrl);
        try (HttpResponse execute = MyHttpUtil.post(getPicUrl, param)) {
            String getPicResp = execute.body();
            Resp<?> bean = JSONUtil.toBean(getPicResp, Resp.class);
            if (!bean.isSuccess()) {
                log.error("获取图像失败：{}", bean.getMsg());
                throw new InspectionException("获取图像失败：" + bean.getMsg());
            }

            Date dateNow = new Date();
            String dateStr = DateUtil.format(dateNow, InspConstants.yyyyMMddHHmmss);
            File tempFile = LocalFileServerUtil.getTempFile(dateStr + ".JPEG");

            String picUrl = bean.getData().toString();
            MyHttpUtil.downloadFile(baseUrl + picUrl, tempFile);

            String outputPath = tempFile.getAbsolutePath();
            log.debug("图片提取成功: {}", outputPath);
            return outputPath;
        } catch (Exception e) {
            log.error("获取图像出现异常，getPicUrl = {}，param = {}", getPicUrl, JSONUtil.toJsonStr(param), e);
            throw new InspectionException("获取图像出现异常");
        } finally {
            long intervalRestart = timer.intervalRestart();
            log.debug("截图耗时：{}毫秒", intervalRestart);
        }
    }
}
