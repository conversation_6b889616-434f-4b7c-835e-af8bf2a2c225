package com.hollysys.inspection.service.protocol.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.constants.InspConstants;
import com.hollysys.inspection.constants.channel.CameraPtzAction;
import com.hollysys.inspection.constants.channel.DeviceManufacturer;
import com.hollysys.inspection.entity.InspChannelInfo;
import com.hollysys.inspection.entity.InspPresetInfo;
import com.hollysys.inspection.model.channel.RtspStreamModel;
import com.hollysys.inspection.model.sdk.*;
import com.hollysys.inspection.sdk.hik.HCNetSDK;
import com.hollysys.inspection.service.MediamtxService;
import com.hollysys.inspection.service.protocol.ICameraCtrlProtocol;
import com.hollysys.inspection.utils.LocalFileServerUtil;
import com.hollysys.inspection.utils.SdkTokenUtil;
import com.hollysys.inspection.utils.SleepUtil;
import com.sun.jna.Native;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.IntByReference;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 海康SDK协议
 */
@Slf4j
@Service
public class CameraProtocolHik implements ICameraCtrlProtocol {

    private static final Integer PORT = 8000; // 登录端口
    private static HCNetSDK hCNetSDK = null; // sdk实例
    private static final int RTSP_PORT = 554; // rtsp端口
    private static final String RTSP_URL_FORMAT = "rtsp://{}:{}/Streaming/Channels/{}?transportmode=unicast&profile={}"; // rtsp地址格式
    private static final Random random = new Random();
    @Value("${recording-file.base}")
    private String recordingFileBase;

    @Resource
    private MediamtxService mediamtxService;

    // 存储下载进度的 Map: 键为任务ID，值为进度（0-100）
    private final static ConcurrentHashMap<String, Integer> DOWNLOAD_PROGRESS = new ConcurrentHashMap<>();
    // 用于执行下载任务的线程池
    private final static ExecutorService executor = Executors.newFixedThreadPool(5);

    @Override
    public LoginResp login(InspChannelInfo channelInfo) {
        LoginResp loginCache = SdkTokenUtil.getLoginCache(channelInfo);
        if (Objects.nonNull(loginCache)) {
            return loginCache;
        }

        // 创建设备登录信息和设备信息对象
        HCNetSDK.NET_DVR_USER_LOGIN_INFO loginInfo = new HCNetSDK.NET_DVR_USER_LOGIN_INFO();
        HCNetSDK.NET_DVR_DEVICEINFO_V40 deviceInfo = new HCNetSDK.NET_DVR_DEVICEINFO_V40();

        String ip = channelInfo.getAddress();
        // 设置设备IP地址
        byte[] deviceAddress = new byte[HCNetSDK.NET_DVR_DEV_ADDRESS_MAX_LEN];
        byte[] ipBytes = ip.getBytes();
        System.arraycopy(ipBytes, 0, deviceAddress, 0, Math.min(ipBytes.length, deviceAddress.length));
        loginInfo.sDeviceAddress = deviceAddress;

        // 设置用户名和密码
        byte[] userName = new byte[HCNetSDK.NET_DVR_LOGIN_USERNAME_MAX_LEN];
        String psw = channelInfo.getPassword();
        byte[] password = psw.getBytes();
        String user = channelInfo.getUsername();
        System.arraycopy(user.getBytes(), 0, userName, 0, Math.min(user.length(), userName.length));
        System.arraycopy(password, 0, loginInfo.sPassword, 0, Math.min(password.length, loginInfo.sPassword.length));
        loginInfo.sUserName = userName;

        // 设置端口和登录模式
        loginInfo.wPort = CameraProtocolHik.PORT.shortValue();
        loginInfo.bUseAsynLogin = false; // 同步登录
        loginInfo.byLoginMode = 0; // 使用SDK私有协议

        // 执行登录操作
        int userId = hCNetSDK.NET_DVR_Login_V40(loginInfo, deviceInfo);
        if (userId == -1) {
            log.error("登录失败，错误码为: {}", hCNetSDK.NET_DVR_GetLastError());
            throw new InspectionException("摄像机登录失败");
        }

        // read()后，结构体中才有对应的数据
        deviceInfo.read();

        // 组织当前摄像机的通道号集合
        List<Integer> channelNums = getChannelNumber(deviceInfo.struDeviceV30, userId);

        // 加入缓存
        LoginResp loginResp = LoginResp.buildHikToken(channelInfo, userId);
        loginResp.setChannelNums(channelNums);
        SdkTokenUtil.setLoginCache(loginResp);
        // 返回登录结果
        return loginResp;
    }

    @Override
    public void logout(LoginResp loginResp) {
        Integer hikToken = loginResp.getHikToken();
        if (Objects.nonNull(hCNetSDK) && Objects.nonNull(hikToken)) {
            boolean result = hCNetSDK.NET_DVR_Logout(hikToken);
            // 如果登出失败
            if (!result) {
                log.error("海康设备登出失败，错误码: {}", hCNetSDK.NET_DVR_GetLastError());
            }
        }
    }

    // public void getPicWithAppendData(InspChannelInfo channelInfo) {
    //     // 登录到设备，获取用户ID
    //     LoginResp login = login(channelInfo);
    //
    //     HCNetSDK.NET_DVR_JPEGPICTURE_WITH_APPENDDATA param = new HCNetSDK.NET_DVR_JPEGPICTURE_WITH_APPENDDATA();
    //     param.read();
    //     // 热成像图片指针
    //     HCNetSDK.BYTE_ARRAY byteArrayJpegPic = new HCNetSDK.BYTE_ARRAY(2 * 1024 * 1024);
    //     param.pJpegPicBuff = byteArrayJpegPic.getPointer();
    //     // 可见光图片指针
    //     HCNetSDK.BYTE_ARRAY byteArrayVisiblePic = new HCNetSDK.BYTE_ARRAY(10 * 1024 * 1024);
    //     param.pVisiblePicBuff = byteArrayVisiblePic.getPointer();
    //     // 全屏测温数据指针
    //     HCNetSDK.BYTE_ARRAY byteArrayP2PData = new HCNetSDK.BYTE_ARRAY(2 * 1024 * 1024);
    //     param.pP2PDataBuff = byteArrayP2PData.getPointer();
    //     param.write();
    //     boolean result = hCNetSDK.NET_DVR_CaptureJPEGPicture_WithAppendData(login.getHikToken(), 2, param);
    //
    //     System.out.println(JSONUtil.toJsonStr(param));
    //
    //     if (!result) {
    //         log.error("获取测温截图数据失败，错误码：{}", hCNetSDK.NET_DVR_GetLastError()); // 输出错误信息
    //         throw new InspectionException("获取测温截图数据失败，错误码：" + hCNetSDK.NET_DVR_GetLastError()); // 抛出异常
    //     }
    //     // 热成像图片存储
    //     byteArrayJpegPic.read();
    //     Date dateNow = new Date();
    //     File tempDir = LocalFileServerUtil.getTempDir();
    //     String dateStr = DateUtil.format(dateNow, InspConstants.yyyyMMddHHmmss); // 格式化当前日期时间
    //     File tempFile = new File(tempDir, dateStr + "热成像" + ".JPEG");
    //     writeBytesToFile(byteArrayJpegPic.byValue, param.dwJpegPicLen, tempFile);
    //     System.out.println("热成像图片存储：" + tempFile.getAbsolutePath());
    //
    //     // 可见光图片存储
    //     byteArrayVisiblePic.read();
    //     tempFile = new File(tempDir, dateStr + "可见光" + ".JPEG");
    //     writeBytesToFile(byteArrayVisiblePic.byValue, param.dwVisiblePicLen, tempFile);
    //     System.out.println("可见光图片存储：" + tempFile.getAbsolutePath());
    //
    //     // TODO 全屏测温数据解析
    //     byteArrayP2PData.read();
    //     tempFile = new File(tempDir, dateStr + "测温数据" + ".txt");
    //     byte[] sub = ArrayUtil.sub(byteArrayP2PData.byValue, 0, param.dwP2PDataLen);
    //     FileUtil.writeUtf8String(Arrays.toString(sub), tempFile);
    //     // writeBytesToFile(byteArrayP2PData.byValue, param.dwP2PDataLen, tempFile);
    //     System.out.println("全屏测温数据长度：" + param.dwP2PDataLen);
    //     System.out.println(JSONUtil.toJsonStr(param));
    // }

    /**
     * 抓图
     * 由于采用设备转图方式，所以无法选择码流类型
     * 参考：https://open.hikvision.com/docs/docId?productId=5cda567cf47ae80dd41a54b3&version=%2F16e18c75bd644f1dbf9e8301d6fc9b73&tagPath=%E9%80%9A%E7%94%A8%E4%B8%8E%E8%A7%86%E9%A2%91-%E5%AE%9E%E6%97%B6%E9%A2%84%E8%A7%88-%E6%8A%93%E5%9B%BE
     *
     * @param channelInfo 通道信息
     * @return 文件路径
     */
    @Override
    public String getPic(InspChannelInfo channelInfo) {
        // getPicWithAppendData(channelInfo);
        // 登录到设备，获取用户ID
        LoginResp login = login(channelInfo);

        // 创建抓图参数对象
        HCNetSDK.NET_DVR_JPEGPARA jpegParma = new HCNetSDK.NET_DVR_JPEGPARA();
        jpegParma.read();
        jpegParma.wPicSize = 255;
        jpegParma.wPicQuality = 0;
        jpegParma.write();
        HCNetSDK.BYTE_ARRAY byte_array = new HCNetSDK.BYTE_ARRAY(10 * 1024 * 1024);
        IntByReference ret = new IntByReference(0);
        boolean result = hCNetSDK.NET_DVR_CaptureJPEGPicture_NEW(login.getHikToken(), channelInfo.getChannelNum(),
                jpegParma, byte_array.getPointer(), byte_array.size(), ret);

        if (!result) {
            log.error("抓图失败，错误码：{}", hCNetSDK.NET_DVR_GetLastError()); // 输出错误信息
            throw new InspectionException("抓图失败，错误码：" + hCNetSDK.NET_DVR_GetLastError()); // 抛出异常
        } else {
            byte_array.read();

            // 设置抓图文件路径
            Date dateNow = new Date();
            String dateStr = DateUtil.format(dateNow, InspConstants.yyyyMMddHHmmss); // 格式化当前日期时间
            File tempFile = LocalFileServerUtil.getTempFile(dateStr + RandomUtil.randomInt(1000, 10000) + ".JPEG");
            try {
                String path = tempFile.getAbsolutePath();
                writeBytesToFile(byte_array.byValue, ret.getValue(), tempFile);
                log.debug("抓图成功： {}", path);
                return path;
            } catch (IOException e) {
                log.error("抓图写入文件时发生错误", e);
                throw new InspectionException("抓图写入文件时发生错误");
            }
        }
    }

    /**
     * 同步转动预置点
     *
     * @param channelInfo 通道信息
     * @param presetInfo  预置点信息
     * @return 转动结果
     */
    @Override
    public boolean gotoPresetAsync(InspChannelInfo channelInfo, InspPresetInfo presetInfo) {
        // 调用转动预置点异步方法
        boolean result = gotoPreset(channelInfo, presetInfo);

        // 等待4秒
        SleepUtil.sleepSecond(4);

        return result;
    }

    /**
     * 异步转动预置点
     *
     * @param channelInfo 通道信息
     * @param presetInfo  预置点信息
     * @return 转动结果
     */
    @Override
    public boolean gotoPreset(InspChannelInfo channelInfo, InspPresetInfo presetInfo) {
        // 使用通道信息登录设备，获取用户ID
        LoginResp login = login(channelInfo);
        Integer hikToken = login.getHikToken();
        // 设置球机预置点
        if (hikToken != -1) {
            boolean status = hCNetSDK.NET_DVR_PTZPreset_Other(hikToken, channelInfo.getChannelNum(),
                    HCNetSDK.GOTO_PRESET, presetInfo.getPresetNum());
            if (status) {
                log.debug("转动预置点成功: {}", presetInfo.getPresetNum());
                return true;
            } else {
                log.error("转动预置点失败，错误码：{}", hCNetSDK.NET_DVR_GetLastError());
                return false;
            }
        }
        log.error("转动预置点失败，获取到登录token为：{}", hikToken);
        return false;
    }

    /**
     * 设置预置点
     *
     * @param channelInfo 通道信息
     * @param presetNum   预置点号
     * @param presetName  预置点名称
     */
    @Override
    public void setPreset(InspChannelInfo channelInfo, Integer presetNum, String presetName) {
        // 使用通道信息中的地址、端口、用户名和密码登录设备
        LoginResp login = login(channelInfo);
        Integer hikToken = login.getHikToken();

        // 检查登录是否成功
        if (hikToken != -1) {
            // 初始化参数输入结构体
            HCNetSDK.NET_DVR_PRESET_NAME snapParamIn = new HCNetSDK.NET_DVR_PRESET_NAME();
            // 读取结构体中的默认值
            snapParamIn.read();
            // 设置结构体大小
            snapParamIn.dwSize = snapParamIn.size();
            // 设置预置点编号
            snapParamIn.wPresetNum = (short) presetNum.intValue();

            try {
                // 将预置点名称转换为GBK编码的字节数组
                byte[] nameBytes = presetName.getBytes("GBK");
                // 计算需要复制的字节数，最多31个字节
                int copyLength = Math.min(nameBytes.length, 31);
                // 将名称字节数组复制到结构体中的名称字段
                System.arraycopy(nameBytes, 0, snapParamIn.byName, 0, copyLength);
                // 确保名称字段以null结尾
                snapParamIn.byName[copyLength] = 0;
                // 将修改后的结构体写回
                snapParamIn.write();
                // 调用SDK方法设置预置点名称
                boolean status = hCNetSDK.NET_DVR_SetDVRConfig(hikToken, HCNetSDK.NET_DVR_SET_PRESET_NAME,
                        channelInfo.getChannelNum(), snapParamIn.getPointer(), snapParamIn.dwSize);
                // 检查设置是否成功
                if (status) {
                    log.debug("设置预置点成功, 预置点号：{}", presetNum);
                } else {
                    log.error("设置预置点失败，错误码：{}", hCNetSDK.NET_DVR_GetLastError());
                }
            } catch (UnsupportedEncodingException e) {
                log.error("设置预置点名称失败, 错误: {}", e.getMessage());
            }
        }
    }

    /**
     * 删除预置点
     *
     * @param channelInfo 通道信息
     * @param presetInfo  预置点信息
     */
    @Override
    public void delPreset(InspChannelInfo channelInfo, InspPresetInfo presetInfo) {
        // 使用通道信息登录设备，获取用户ID
        LoginResp login = login(channelInfo);
        Integer hikToken = login.getHikToken();

        // 检查登录是否成功
        if (hikToken != -1) {
            // 调用SDK接口删除预置点
            boolean status = hCNetSDK.NET_DVR_PTZPreset_Other(hikToken, channelInfo.getChannelNum(),
                    HCNetSDK.CLE_PRESET, presetInfo.getPresetNum());
            // 检查删除操作是否成功
            if (status) {
                // 记录删除成功的日志
                log.debug("删除预置点成功");
            } else {
                // 记录删除失败的日志，并输出错误码
                log.error("删除预置点失败，错误码：{}", hCNetSDK.NET_DVR_GetLastError());
            }
        }
    }

    /**
     * 开始云台控制
     *
     * @param channelInfo 通道信息
     * @param action      动作
     */
    @Override
    public void startCameraPtz(InspChannelInfo channelInfo, CameraPtzAction action) {
        setPTZControl(channelInfo, channelInfo.getChannelNum(), action.getHik(), 0);
    }

    /**
     * 停止云台控制
     *
     * @param channelInfo 云台信息
     * @param action      动作
     */
    @Override
    public void stopCameraPtz(InspChannelInfo channelInfo, CameraPtzAction action) {
        setPTZControl(channelInfo, channelInfo.getChannelNum(), action.getHik(), 1);
    }

    /**
     * 获取rtsp地址
     *
     * @param channelInfo 通道信息
     * @return rtsp地址
     */
    @Override
    public List<RtspStreamModel> getRtspUrl(InspChannelInfo channelInfo) {
        // 登录设备，获取用户ID
        LoginResp login = login(channelInfo);
        // 创建一个列表用于存储RTSP URL
        List<RtspStreamModel> rtspUrls = new ArrayList<>();
        List<Integer> channelNums = login.getChannelNums();
        if (CollectionUtil.isEmpty(channelNums)) {
            throw new InspectionException("当前设备的通道号列表为空");
        }
        for (Integer channelNum : channelNums) {
            // 计算通道码（默认只取主码流）
            int channelCode = channelNum * 100 + 1;
            // 主码流配置
            String mainStreamProfile = "Profile_1";
            // 获取设备IP地址
            String ip = channelInfo.getAddress();
            // 主码流
            String mainStreamRtspUrl = StrUtil.format(RTSP_URL_FORMAT, ip, RTSP_PORT, channelCode, mainStreamProfile);

            RtspStreamModel rtspStreamModel = RtspStreamModel.createRtspStreamModel(channelNum, mainStreamRtspUrl);
            rtspUrls.add(rtspStreamModel);
        }
        return rtspUrls;
    }

    /**
     * 查询海康摄像头录像文件列表
     * @param recordingFileRes 请求体
     * @return 文件列表
     */
    @Override
    public List<RecordingFileResp> getRecordingFilesList(RecordingFileRes recordingFileRes) {
        // 使用通道信息登录设备，获取用户ID
        LoginResp login = login(recordingFileRes);
        Integer hikToken = login.getHikToken();
        List<RecordingFileResp> recordingFiles = new ArrayList<>();
        HCNetSDK.NET_DVR_FILECOND pFindCond = new HCNetSDK.NET_DVR_FILECOND();
        pFindCond.read();
        // 设置通道号
        pFindCond.lChannel = recordingFileRes.getChannelNum();

        // 设置查询文件类型为全部类型
        pFindCond.dwFileType = 0xff;
        pFindCond.dwIsLocked = 0xff;
        // 增加查询开始时间和结束时间
        pFindCond.struStartTime = convertToHikTime(recordingFileRes.getStartTime());
        pFindCond.struStopTime = convertToHikTime(recordingFileRes.getEndTime());

        // 最后，将整个 pFindCond 结构体的数据写入JNA
        pFindCond.write();

        // 调用接口获取查找句柄
        int lFindHandle = hCNetSDK.NET_DVR_FindFile_V30(hikToken, pFindCond);

        if (lFindHandle == -1) {
            log.error("获取设备录像文件列表失败, 错误码: {}", hCNetSDK.NET_DVR_GetLastError());
            throw new InspectionException("获取设备录像文件列表失败");
        }

        // 根据句柄查找录像文件
        HCNetSDK.NET_DVR_FINDDATA_V50 lpFindData = new HCNetSDK.NET_DVR_FINDDATA_V50();
        int findStatus;
        long startTime = System.currentTimeMillis();
        final long TIMEOUT_MS = 5000; // 5秒超时

        // 在循环中调用 NET_DVR_FindNextFile_V50获取录像文件 该接口一次只能获取一个文件信息 因此需要循环调用
        while (true) {
            // 检查超时
            if (System.currentTimeMillis() - startTime > TIMEOUT_MS) {
                log.error("查找录像文件超时，已等待 {} 毫秒", TIMEOUT_MS);
                break;
            }

            findStatus = hCNetSDK.NET_DVR_FindNextFile_V50(lFindHandle, lpFindData);

            if (findStatus == 1000) {
                // 将数据从底层内存读入 Java 结构体
                lpFindData.read();

                // 构建录像文件返回
                RecordingFileResp fileResp = getHikRecordingFileResp(lpFindData);

                recordingFiles.add(fileResp);
                log.debug("找到录像文件: {}", fileResp.getFileName());
            } else if (findStatus == 1002) {
                continue;
            } else if (findStatus == 1001 || findStatus == 1003) {
                log.debug("查找结束，共找到 {} 个文件", recordingFiles.size());
                break;
            } else if (findStatus == 1004 || findStatus == 1005) {
                log.error("查找文件时异常或超时，状态码: {}, 错误码： {}", findStatus, hCNetSDK.NET_DVR_GetLastError());
                break;
            } else {
                log.error("查找文件失败，状态码: {}, 错误码： {}", findStatus, hCNetSDK.NET_DVR_GetLastError());
                break;
            }
        }

        // 关闭文件查找句柄
        boolean closeResult = hCNetSDK.NET_DVR_FindClose_V30(lFindHandle);
        if (!closeResult) {
            log.error("关闭录像文件查找句柄失败, 错误码: {}", hCNetSDK.NET_DVR_GetLastError());
        } else {
            log.debug("已成功关闭查找句柄，共找到 {} 个文件", recordingFiles.size());
        }
        return recordingFiles;
    }

    /**
     * 根据文件名下载录像文件
     * @param downloadRecordingFileRes 文件信息
     * @return 下载路径
     */
    @Override
    public DownloadOptRes downloadRecordingFileByName(DownloadRecordingFileRes downloadRecordingFileRes) {
        // 先登录
        LoginResp login = login(downloadRecordingFileRes);
        Integer hikToken = login.getHikToken();

        // 检查文件夹
        Path downloadDirPath = Paths.get(recordingFileBase);
        if (!Files.exists(downloadDirPath)) {
            try {
                Files.createDirectories(downloadDirPath);
                log.debug("下载目录 {} 不存在，已创建。", recordingFileBase);
            } catch (Exception e) {
                log.error("创建下载目录失败: {}", recordingFileBase, e);
                throw new InspectionException("创建下载目录失败");
            }
        }

        // 构建下载路径
        String downloadPath = recordingFileBase + downloadRecordingFileRes.getFileName() + ".mp4";

        // 调用接口获取下载文件句柄
        int lFileHandle =  hCNetSDK.NET_DVR_GetFileByName(hikToken, downloadRecordingFileRes.getFileName(), downloadPath.getBytes());

        if (lFileHandle == -1) {
            log.error("获取设备录像文件失败, 错误码: {}", hCNetSDK.NET_DVR_GetLastError());
            throw new InspectionException("获取设备录像文件失败");
        }

        // 生成唯一的任务ID
        String taskId = UUID.randomUUID().toString();

        // 立即将任务ID和初始进度添加到 Map
        DOWNLOAD_PROGRESS.put(taskId, 0);

        executor.submit(() -> {
            try {
                // 构建开始下载接口参数
                IntByReference intP1 = new IntByReference(0);
                IntByReference intInlen = new IntByReference(0);

                // 开始下载
                boolean bPlayBackStart = hCNetSDK.NET_DVR_PlayBackControl_V40(lFileHandle, HCNetSDK.NET_DVR_PLAYSTART, intP1.getPointer(), 4, Pointer.NULL, intInlen);
                if (!bPlayBackStart) {
                    log.error("开始下载失败，错误码：{}", hCNetSDK.NET_DVR_GetLastError());
                    DOWNLOAD_PROGRESS.put(taskId, -1);
                    return; // 结束当前任务线程
                }

                // 获取下载进度
                while (true) {
                    int pos = hCNetSDK.NET_DVR_GetDownloadPos(lFileHandle);

                    if (pos == -1) {
                        // 下载失败
                        log.error("下载失败, 错误码: {}", hCNetSDK.NET_DVR_GetLastError());
                        DOWNLOAD_PROGRESS.put(taskId, -1);
                        break;
                    } else if (pos == 200) {
                        // 出现网络异常
                        log.error("下载失败，出现网络异常，错误码：{}", hCNetSDK.NET_DVR_GetLastError());
                        DOWNLOAD_PROGRESS.put(taskId, -1);
                        break;
                    } else if (pos == 100) {
                        // 下载完成
                        DOWNLOAD_PROGRESS.put(taskId, 100);
                        log.debug("下载完成");
                        break;
                    } else {
                        // 正常下载中 (0-99)
                        DOWNLOAD_PROGRESS.put(taskId, pos);
                        log.debug("下载中... {}%", pos);
                    }

                    // 每秒查询一次进度，避免频繁调用
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.error("线程中断", e);
                        DOWNLOAD_PROGRESS.put(taskId, -1);
                        break; // 中断时结束循环
                    }
                }
            } finally {
                // 确保无论发生什么，都会中止下载
                stopDownload(lFileHandle, taskId);
            }
        });
        DownloadOptRes downloadOptRes = new DownloadOptRes();
        downloadOptRes.setTaskId(taskId);
        downloadOptRes.setLFileHandle(lFileHandle);
        return downloadOptRes;
    }

    /**
    * 停止文件下载
    * @param lFileHandle 文件句柄，用于标识正在下载的文件
    * @param taskId 下载任务ID，用于标识特定的下载任务
    */
    public void stopDownload(int lFileHandle, String taskId) {
        // 停止下载
        boolean bStop = hCNetSDK.NET_DVR_StopGetFile(lFileHandle);
        if (!bStop) {
            log.error("停止下载失败，错误码：{}", hCNetSDK.NET_DVR_GetLastError());
            throw new InspectionException("停止下载失败");
        }

        // 清除下载进度任务
        DOWNLOAD_PROGRESS.remove(taskId);
    }

    /**
     * 返回下载进度
     * @param taskId 任务id
     * @return 进度
     */
    public int getDownloadProgress(String taskId) {
        Integer progress = DOWNLOAD_PROGRESS.get(taskId);

        if (progress == null || progress == -1) {
            return -1;
        } else {
            return progress;
        }
    }

    @Override
    public String getManufacturer(InspChannelInfo channelInfo) {
        return DeviceManufacturer.HIKVISION.name();
    }

    @Override
    public String playBackByTime(PlayBackByTimeRes playBackByTimeRes) {
        String ip = playBackByTimeRes.getAddress();
        int channelNum = playBackByTimeRes.getChannelNum();
        LocalDateTime startTime = playBackByTimeRes.getStartTime();
        LocalDateTime endTime = playBackByTimeRes.getEndTime();

        // LocalDateTime 转换为海康要求的 UTC 格式：YYYYMMDD'T'HHMMSS'Z'
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd'T'HHmmss'Z'");
        String formattedStartTime = startTime.format(formatter);
        String formattedEndTime = endTime.format(formatter);


        // 第一个数字是通道号，第二个是0，第三个是码流类型加1
        int streamType = playBackByTimeRes.getStreamType() + 1; // streamType 0为主码流，1为辅码流
        String playbackPath = String.format("%d0%d", channelNum, streamType);

        // 构建完整的 RTSP 回放 URL
        String rtspPlaybackUrl = String.format(
                "rtsp://%s:%d/Streaming/tracks/%s?starttime=%s&endtime=%s",
                ip,
                RTSP_PORT,
                playbackPath,
                formattedStartTime,
                formattedEndTime
        );

        log.debug("生成的RTSP回放地址: {}", rtspPlaybackUrl);

        // 调用 MediamtxService，让其处理拉流和分发
        InspChannelInfo channelInfo = new InspChannelInfo();

        channelInfo.setId(playBackByTimeRes.getClientKey());
        channelInfo.setUsername(playBackByTimeRes.getUsername());
        channelInfo.setPassword(playBackByTimeRes.getPassword());
        channelInfo.setRtsp(rtspPlaybackUrl);

        return mediamtxService.pushStream(channelInfo);
    }

    /**
     * 云台控制
     *
     * @param channelInfo  通道信息
     * @param lChannel     通道
     * @param dwPTZCommand 控制命令
     * @param dwStop       停止命令
     */
    private void setPTZControl(InspChannelInfo channelInfo, Integer lChannel, Integer dwPTZCommand, Integer dwStop) {
        // 调用登录方法，获取用户ID
        LoginResp login = login(channelInfo);
        Integer hikToken = login.getHikToken();
        // 检查用户ID是否有效（不等于-1表示登录成功）
        if (hikToken != -1) {
            // 记录日志，输出云台控制操作的详细信息
            log.debug("云台控制操作：lUserID：{}，lChannel：{}，dwPTZPresetCmd：{}，dwStop：{}，dwSpeed：{}", hikToken, lChannel, dwPTZCommand, dwStop, 3);
            // 带速度
            boolean status = hCNetSDK.NET_DVR_PTZControlWithSpeed_Other(hikToken, lChannel, dwPTZCommand, dwStop, 3);
            if (BooleanUtil.isTrue(status)) {
                log.debug("云台控制操作成功");
            } else {
                log.error("云台控制操作失败，错误码：{}", hCNetSDK.NET_DVR_GetLastError());
            }
        }
    }

    /**
     * 初始化
     */
    public void init() {
        // 加载动态库
        String osName = System.getProperty("os.name");
        String osArch = System.getProperty("os.arch").toLowerCase();
        // 加载海康SDK
        if (hCNetSDK == null) {
            if (!createSDKInstance(osName, osArch)) {
                log.error("加载海康sdk库失败");
                return;
            }

            if (!createPlayInstance(osName, osArch)) {
                log.error("加载海康播放库失败");
                return;
            }
        }

        // linux系统建议调用以下接口加载组件库
        if (osName.toLowerCase().startsWith("linux")) {
            String libPath = "/libs/hik_linux64/";
            if (osArch.contains("aarch")) {
                libPath = "/libs/hik_linux_arm/";
            }
            HCNetSDK.BYTE_ARRAY ptrByteArray1 = new HCNetSDK.BYTE_ARRAY(256);
            HCNetSDK.BYTE_ARRAY ptrByteArray2 = new HCNetSDK.BYTE_ARRAY(256);
            // 这里是库的绝对路径，请根据实际情况修改，注意改路径必须有访问权限
            String strPath1 = libPath + "libcrypto.so.1.1";
            String strPath2 = libPath + "libssl.so.1.1";
            System.arraycopy(strPath1.getBytes(), 0, ptrByteArray1.byValue, 0, strPath1.length());
            ptrByteArray1.write();
            hCNetSDK.NET_DVR_SetSDKInitCfg(HCNetSDK.NET_SDK_INIT_CFG_LIBEAY_PATH, ptrByteArray1.getPointer());
            System.arraycopy(strPath2.getBytes(), 0, ptrByteArray2.byValue, 0, strPath2.length());
            ptrByteArray2.write();
            hCNetSDK.NET_DVR_SetSDKInitCfg(HCNetSDK.NET_SDK_INIT_CFG_SSLEAY_PATH, ptrByteArray2.getPointer());
            String strPathCom = libPath;
            HCNetSDK.NET_DVR_LOCAL_SDK_PATH struComPath = new HCNetSDK.NET_DVR_LOCAL_SDK_PATH();
            System.arraycopy(strPathCom.getBytes(), 0, struComPath.sPath, 0, strPathCom.length());
            struComPath.write();
            hCNetSDK.NET_DVR_SetSDKInitCfg(HCNetSDK.NET_SDK_INIT_CFG_SDK_PATH, struComPath.getPointer());
        }

        // SDK初始化，一个程序进程只需要调用一次
        boolean result = hCNetSDK.NET_DVR_Init();
        if (!result) {
            log.error("海康SDK初始化失败，错误码: {}", hCNetSDK.NET_DVR_GetLastError());
            throw new InspectionException("海康SDK初始化失败"); // 初始化失败时抛出异常
        } else {
            log.debug("海康SDK初始化成功");
        }

    }

    /**
     * 创建sdk实例
     *
     * @param osName 操作系统名称
     * @return boolean
     */
    private boolean createSDKInstance(String osName, String osArch) {
        // 检查hCNetSDK是否为null，如果为null则需要进行初始化
        if (hCNetSDK == null) {
            // 使用HCNetSDK.class作为锁对象，确保线程安全
            synchronized (HCNetSDK.class) {
                // 初始化SDK库路径字符串
                String strDllPath = "";
                // 获取操作系统架构
                try {
                    // 将操作系统名称转换为小写，并检查是否以"windows"开头
                    if (osName.toLowerCase().startsWith("windows")) {
                        // win系统加载SDK库路径
                        strDllPath = System.getProperty("user.dir") + "\\libs\\hik_win64\\HCNetSDK.dll";
                    } else if (osName.toLowerCase().startsWith("linux")) {
                        // Linux系统
                        if (osArch.contains("aarch")) {
                            // ARM 架构 Linux 系统加载 SDK 库路径
                            strDllPath = "/libs/hik_linux_arm/libhcnetsdk.so";
                        } else {
                            // 其他 Linux 架构 (例如 x86_64) 加载 SDK 库路径
                            strDllPath = "/libs/hik_linux64/libhcnetsdk.so";
                        }
                    }
                    hCNetSDK = Native.load(strDllPath, HCNetSDK.class);
                    log.debug("加载海康sdk库路径:{} ", strDllPath);
                } catch (Exception ex) {
                    log.error("加载海康SDK库失败,库路径: {}， 错误信息：{}", strDllPath, ex.getMessage());
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 加载SDK播放库
     *
     * @param osName 操作系统名称
     * @return boolean
     */
    private static boolean createPlayInstance(String osName, String osArch) {
        // 检查hCNetSDK是否为null，如果为null则需要进行初始化
        if (hCNetSDK == null) {
            synchronized (HCNetSDK.class) {
                // 初始化SDK播放库路径字符串
                String strPlayPath = "";
                // 获取操作系统架构
                try {
                    if (osName.toLowerCase().startsWith("windows"))
                        //win系统加载库路径
                        strPlayPath = System.getProperty("user.dir") + "\\libs\\hik_win64\\PlayCtrl.dll";
                    else if (osName.toLowerCase().startsWith("linux"))
                        // Linux系统
                        if (osArch.contains("aarch")) {
                            // ARM 架构 Linux 系统加载 SDK 库路径
                            strPlayPath = "/libs/hik_linux_arm/libPlayCtrl.so";
                        } else {
                            // 其他 Linux 架构 (例如 x86_64) 加载 SDK 库路径
                            strPlayPath = "/libs/hik_linux64/libPlayCtrl.so";
                        }
                        hCNetSDK = Native.load(strPlayPath, HCNetSDK.class);
                        log.debug("加载海康sdk播放库路径:{} ", strPlayPath);
                } catch (Exception ex) {
                    log.error("加载海康SDK播放库失败,库路径: {}， 错误信息：{}", strPlayPath, ex.getMessage());
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 将字节数组写入到指定的文件路径。
     *
     * @param bytes 字节数组
     * @param file  文件保存路径
     * @throws IOException 如果文件写入过程中发生错误
     */
    private static void writeBytesToFile(byte[] bytes, int len, File file) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(file)) {
            fos.write(bytes, 0, len);
        }
    }

    /**
     * 获取设备通道
     */
    public List<Integer> getChannelNumber(HCNetSDK.NET_DVR_DEVICEINFO_V30 deviceInfo, int userId) {
        List<Integer> channelList = new ArrayList<>();

        IntByReference ibrBytesReturned = new IntByReference(0);// 获取IP接入配置参数
        HCNetSDK.NET_DVR_IPPARACFG m_strIpparaCfg = new HCNetSDK.NET_DVR_IPPARACFG();
        m_strIpparaCfg.write();
        Pointer lpIpParaConfig = m_strIpparaCfg.getPointer();
        boolean bRet = (deviceInfo.byIPChanNum != 0);

        if (!bRet) {
            // 设备不支持,则表示没有IP通道
            for (int iChannum = 0; iChannum < deviceInfo.byChanNum; iChannum++) {
                channelList.add(iChannum + deviceInfo.byStartChan);
            }
        } else {
            // 设备支持IP通道
            boolean ok = hCNetSDK.NET_DVR_GetDVRConfig(userId, HCNetSDK.NET_DVR_GET_IPPARACFG, 0,
                    lpIpParaConfig, m_strIpparaCfg.size(), ibrBytesReturned);
            if (!ok) {
                int lastError = hCNetSDK.NET_DVR_GetLastError();
                log.error("获取配置失败：{}", lastError);
                throw new InspectionException("获取设备配置失败");
            }
            m_strIpparaCfg.read();

            for (int iChannum = 0; iChannum < deviceInfo.byChanNum; iChannum++) {
                if (m_strIpparaCfg.byAnalogChanEnable[iChannum] == 1) {
                    channelList.add(iChannum + deviceInfo.byStartChan);
                }
            }

            for (int iChannum = 0; iChannum < HCNetSDK.MAX_IP_CHANNEL; iChannum++) {
                if (m_strIpparaCfg.struIPChanInfo[iChannum].byEnable == 1) {
                    int channelNum = hCNetSDK.NET_DVR_SDKChannelToISAPI(userId,
                            iChannum + deviceInfo.byStartDChan, true);
                    // String channelIP = (new String(m_strIpparaCfg.struIPDevInfo[iChannum].struIP.sIpV4));
                    // channelIP = channelIP.trim();
                    // devices = devices + "," + channelIP;
                    channelList.add(channelNum);
                }
            }
        }
        return channelList;
    }

    /**
     * 生成录像文件信息
     * @param lpFindData 录像文件信息
     * @return 录像文件信息
     */
    @NotNull
    private RecordingFileResp getHikRecordingFileResp(HCNetSDK.NET_DVR_FINDDATA_V50 lpFindData) {
        LocalDateTime fileStartTime = convertToLocalDateTime(lpFindData.struStartTime);
        LocalDateTime fileStopTime = convertToLocalDateTime(lpFindData.struStopTime);

        // 创建 HikRecordingFileResp 对象并填充数据
        RecordingFileResp fileResp = new RecordingFileResp();

        // 处理文件名 （ch0006_00000005305000000） 返回文件名由_ 符号分隔 后面的才是真实文件名 前面的是通道号
        String fullFileName = new String(lpFindData.sFileName).trim();

        String[] parts = fullFileName.split("_");

        if (parts.length != 2) {
            log.error("文件名格式不正确: {}", fullFileName);
            throw new InspectionException("文件名格式不正确");
        }
        String fileName = parts[1];
        String channelNumStr = parts[0];

        // 处理通道号
        int channelNumber = 0;
        try {
            // 移除 "ch" 前缀，并转换为整数
            channelNumber = Integer.parseInt(channelNumStr.substring(2));
        } catch (NumberFormatException | StringIndexOutOfBoundsException e) {
            log.error("通道号格式不正确: {}", channelNumStr);
            throw new InspectionException("通道号格式不正确");
        }

        fileResp.setFullFileName(fullFileName);
        fileResp.setFileName(fileName);
        fileResp.setChannelNum(channelNumber);
        fileResp.setFileSize(lpFindData.dwFileSize);
        fileResp.setStartTime(fileStartTime);
        fileResp.setEndTime(fileStopTime);
        fileResp.setStreamType(lpFindData.byStreamType);
        return fileResp;
    }

    /**
     * 将海康的时间结构体转换为 Java 的 LocalDateTime 对象
     */
    private LocalDateTime convertToLocalDateTime(HCNetSDK.NET_DVR_TIME_SEARCH timeSearch) {
        return LocalDateTime.of(timeSearch.wYear, timeSearch.byMonth, timeSearch.byDay,
                timeSearch.byHour, timeSearch.byMinute, timeSearch.bySecond);
    }

    /**
     * 将 Java 的 LocalDateTime 对象转换为海康的时间结构体
     */
    private HCNetSDK.NET_DVR_TIME convertToHikTime(LocalDateTime localDateTime) {
        HCNetSDK.NET_DVR_TIME hikTime = new HCNetSDK.NET_DVR_TIME();
        hikTime.dwYear = localDateTime.getYear();
        hikTime.dwMonth = localDateTime.getMonthValue();
        hikTime.dwDay = localDateTime.getDayOfMonth();
        hikTime.dwHour = localDateTime.getHour();
        hikTime.dwMinute = localDateTime.getMinute();
        hikTime.dwSecond = localDateTime.getSecond();
        return hikTime;
    }
}
