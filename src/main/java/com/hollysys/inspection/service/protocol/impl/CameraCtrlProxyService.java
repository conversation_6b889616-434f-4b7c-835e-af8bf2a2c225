package com.hollysys.inspection.service.protocol.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.hollysys.inspection.config.annotations.AutoClearOsd;
import com.hollysys.inspection.config.exceptions.ChannelGetPicException;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.constants.channel.CameraCtrlProtocol;
import com.hollysys.inspection.constants.channel.CameraPtzAction;
import com.hollysys.inspection.entity.InspChannelInfo;
import com.hollysys.inspection.entity.InspPresetInfo;
import com.hollysys.inspection.model.channel.RtspStreamModel;
import com.hollysys.inspection.model.channel.ScaleModel;
import com.hollysys.inspection.model.sdk.*;
import com.hollysys.inspection.service.protocol.ICameraCtrlProtocol;
import com.hollysys.inspection.utils.MinioUtil;
import com.hollysys.inspection.utils.PicUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.List;

/**
 * 摄像机接入协议服务层对外代理
 */
@Slf4j
@Service
public class CameraCtrlProxyService implements ICameraCtrlProtocol {

    @Override
    public LoginResp login(InspChannelInfo channelInfo) {
        return getCameraCtrlProtocol(channelInfo).login(channelInfo);
    }

    @Override
    public void logout(LoginResp loginResp) {
        getCameraCtrlProtocol(loginResp.getChannelInfo()).logout(loginResp);
    }

    @Override
    public String getPic(InspChannelInfo channelInfo) {
        try {
            String picPath = getCameraCtrlProtocol(channelInfo).getPic(channelInfo);
            if (!PicUtil.isReadable(picPath)) {
                log.error("截取图片不合法，再截取一次，channelInfo = {}", JSONUtil.toJsonStr(channelInfo));
                // 截取图片不合法，再截取一次
                picPath = getCameraCtrlProtocol(channelInfo).getPic(channelInfo);
            }
            // 截图文件上传到文件服务器
            File file = FileUtil.newFile(picPath);
            // 截图文件保存到redis stream，channeId作为streamKey，算法从redis中获取图片时通过当前通道ID直接获取
//            saveToRedis(channelInfo, file);
            return MinioUtil.uploadFile(file.getName(), FileUtil.getInputStream(file));
        } catch (Exception exception) {
            log.error("获取摄像机截图失败，channelInfo = {}", JSONUtil.toJsonStr(channelInfo), exception);
            throw new ChannelGetPicException("获取摄像机截图失败");
        }
    }

    // 保存文件到redis
//    private void saveToRedis(InspChannelInfo channelInfo, File file) {
//        // 组装streamKey，为screenshot+通道id
//        String streamKey = "screenshot:" + channelInfo.getId();
//        // 将文件转换成2进制
//        byte[] imageData = FileUtil.readBytes(file);
//        Map<String, byte[]> dataMap = new HashMap<>();
//        // 保存为键值对 image：imageData
//        dataMap.put("image", imageData);
//        // 组装message，消息ID自动生成，
//        MapRecord<String, String, byte[]> message = StreamRecords.newRecord()
//                .in(streamKey)
//                .withId(RecordId.autoGenerate())
//                .ofMap(dataMap);
//        // 添加
//        redisUtil.xAdd(message);
//        // 限制一个stream中的图片数为10张
//        redisUtil.xTrim(streamKey, 10);
//    }

    @Override
    @AutoClearOsd(channelId = "#channelInfo.id")
    public boolean gotoPresetAsync(InspChannelInfo channelInfo, InspPresetInfo presetInfo) {
        return getCameraCtrlProtocol(channelInfo).gotoPresetAsync(channelInfo, presetInfo);
    }

    @Override
    @AutoClearOsd(channelId = "#channelInfo.id")
    public boolean gotoPreset(InspChannelInfo channelInfo, InspPresetInfo presetInfo) {
        return getCameraCtrlProtocol(channelInfo).gotoPreset(channelInfo, presetInfo);
    }

    @Override
    public void setPreset(InspChannelInfo channelInfo, Integer presetNum, String presetName) {
        getCameraCtrlProtocol(channelInfo).setPreset(channelInfo, presetNum, presetName);
    }

    @Override
    public void delPreset(InspChannelInfo channelInfo, InspPresetInfo presetInfo) {
        getCameraCtrlProtocol(channelInfo).delPreset(channelInfo, presetInfo);
    }

    @Override
    @AutoClearOsd(channelId = "#channelInfo.id")
    public void startCameraPtz(InspChannelInfo channelInfo, CameraPtzAction action) {
        getCameraCtrlProtocol(channelInfo).startCameraPtz(channelInfo, action);
    }

    @Override
    public void stopCameraPtz(InspChannelInfo channelInfo, CameraPtzAction action) {
        getCameraCtrlProtocol(channelInfo).stopCameraPtz(channelInfo, action);
    }

    @Override
    public List<RtspStreamModel> getRtspUrl(InspChannelInfo channelInfo) {
        try {
            return getCameraCtrlProtocol(channelInfo).getRtspUrl(channelInfo);
        } catch (Exception exception) {
            log.error("获取视频通道的RTSP流地址失败", exception);
            throw new InspectionException("获取视频通道的RTSP流地址失败，请检查通道相关配置");
        }
    }

    @Override
    public List<RecordingFileResp> getRecordingFilesList(RecordingFileRes req) {
        return getCameraCtrlProtocol(req).getRecordingFilesList(req);
    }

    @Override
    public DownloadOptRes downloadRecordingFileByName(DownloadRecordingFileRes res) {
        return getCameraCtrlProtocol(res).downloadRecordingFileByName(res);
    }

    public ScaleModel getDisplayResolution(InspChannelInfo channelInfo) {
        String picPath = getCameraCtrlProtocol(channelInfo).getPic(channelInfo);
        return PicUtil.getScaleByLocalImg(picPath);
    }

    @Override
    public String getManufacturer(InspChannelInfo channelInfo) {
        return getCameraCtrlProtocol(channelInfo).getManufacturer(channelInfo);
    }

    @Override
    public String playBackByTime(PlayBackByTimeRes playBackByTimeRes) {
        return getCameraCtrlProtocol(playBackByTimeRes).playBackByTime(playBackByTimeRes);
    }


    /**
     * 根据协议类型获取service实现层
     */
    private ICameraCtrlProtocol getCameraCtrlProtocol(InspChannelInfo channelInfo) {
        String cameraCtrlProtocol = channelInfo.getCameraCtrlProtocol();
        CameraCtrlProtocol cameraCtrlProtocolEnum = CameraCtrlProtocol.valueOf(cameraCtrlProtocol);
        Class<? extends ICameraCtrlProtocol> protocolClass = cameraCtrlProtocolEnum.getProtocolClass();
        return SpringUtil.getBean(protocolClass);
    }
}
