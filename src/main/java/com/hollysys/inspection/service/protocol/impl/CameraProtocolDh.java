package com.hollysys.inspection.service.protocol.impl;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.constants.InspConstants;
import com.hollysys.inspection.constants.channel.CameraPtzAction;
import com.hollysys.inspection.constants.channel.DeviceManufacturer;
import com.hollysys.inspection.entity.InspChannelInfo;
import com.hollysys.inspection.entity.InspPresetInfo;
import com.hollysys.inspection.model.channel.RtspStreamModel;
import com.hollysys.inspection.model.sdk.*;
import com.hollysys.inspection.sdk.dahua.ErrorCode;
import com.hollysys.inspection.sdk.dahua.NetSDKLib;
import com.hollysys.inspection.service.protocol.ICameraCtrlProtocol;
import com.hollysys.inspection.utils.LocalFileServerUtil;
import com.hollysys.inspection.utils.SdkTokenUtil;
import com.hollysys.inspection.utils.SleepUtil;
import com.sun.jna.Memory;
import com.sun.jna.Native;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import com.sun.jna.ptr.IntByReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 大华SDK协议
 */
@Slf4j
@Service
public class CameraProtocolDh implements ICameraCtrlProtocol {
    private static NetSDKLib netSdk = null;
    private static NetSDKLib netConfig = null;
    private static final int START_CHAN_NUM = 0; // 起始通道号
    private static final String CFG_CMD_ENCODE = "Encode"; // 获取通道配置的命令
    private static final int RTSP_PORT = 554; // rtsp地址端口号
    private static final String RTSP_URL_FORMAT = "rtsp://{}:{}/cam/realmonitor?channel={}&subtype={}&unicast=true"; // rtsp地址格式
    private static final int CONFIG_BUFFER_SIZE = 2 * 1024 * 1024;

    // 创建截图超时缓存，超时时间为一秒  key:摄像机IP + 通道号
    private static final TimedCache<String, String> PIC_CACHE = CacheUtil.newTimedCache(1000);

    public void init(NetSDKLib.fDisConnect disConnect, NetSDKLib.fHaveReConnect haveReConnect, boolean openLog, String logDirPath) {
        // 加载动态库
        String osName = System.getProperty("os.name");

        // 加载海康SDK
        if (netSdk == null) {
            if (!createSDKInstance(osName)) {
                log.error("加载大华sdk库失败");
                throw new InspectionException("加载大华sdk库失败");
            }
        }

        boolean bInit = netSdk.CLIENT_Init(disConnect, null);
        if (!bInit) {
            log.error("初始化大华SDK失败");
        } else {
            log.debug("初始化大华SDK成功");
        }

        if (openLog) {
            // 打开日志
            NetSDKLib.LOG_SET_PRINT_INFO setLog = new NetSDKLib.LOG_SET_PRINT_INFO();
            File path = new File(logDirPath, "sdklog");
            if (!path.exists()) {
                FileUtil.mkdir(path);
            }
            String logPath = FileUtil.file(path, getDate() + ".log").getAbsolutePath();
            setLog.nPrintStrategy = 0;
            setLog.bSetFilePath = 1;
            System.arraycopy(logPath.getBytes(), 0, setLog.szLogFilePath, 0, logPath.getBytes().length);
            setLog.bSetPrintStrategy = 1;
            boolean bLogopen = netSdk.CLIENT_LogOpen(setLog);
            if (!bLogopen) {
                log.error("打开大华SDK日志失败");
            }
        }

        // 设置断线重连回调接口，设置过断线重连成功回调函数后，当设备出现断线情况，SDK内部会自动进行重连操作
        // 此操作为可选操作，但建议用户进行设置
        netSdk.CLIENT_SetAutoReconnect(haveReConnect, null);

        // 设置登录超时时间和尝试次数，可选
        int waitTime = 5000; // 登录请求响应超时时间设置为5S
        int tryTimes = 1;    // 登录时尝试建立链接1次
        netSdk.CLIENT_SetConnectTime(waitTime, tryTimes);

        // 设置更多网络参数，NET_PARAM的nWaittime，nConnectTryNum成员与CLIENT_SetConnectTime
        // 接口设置的登录设备超时时间和尝试次数意义相同,可选
        NetSDKLib.NET_PARAM netParam = new NetSDKLib.NET_PARAM();
        netParam.nConnectTime = 10000;      // 登录时尝试建立链接的超时时间
        netParam.nGetConnInfoTime = 3000;   // 设置子连接的超时时间
        netParam.nGetDevInfoTime = 3000;// 获取设备信息超时时间，为0默认1000ms
        netSdk.CLIENT_SetNetworkParam(netParam);
    }

    /**
     * 创建sdk实例
     *
     * @param osName 操作系统名称
     * @return boolean
     */
    private boolean createSDKInstance(String osName) {
        if (netSdk == null) {
            // 使用netsdk.class作为锁对象，确保线程安全
            synchronized (NetSDKLib.class) {
                // 初始化SDK库路径字符串
                String strDllPath = "";
                String strConfigPath = "";
                // 获取操作系统架构
                String osArch = System.getProperty("os.arch").toLowerCase();
                try {
                    // 将操作系统名称转换为小写，并检查是否以"windows"开头
                    if (osName.toLowerCase().startsWith("windows")) {
                        // win系统加载SDK库路径
                        strDllPath = System.getProperty("user.dir") + "\\libs\\dh_win64\\dhnetsdk.dll";
                        strConfigPath = System.getProperty("user.dir") + "\\libs\\dh_win64\\dhconfigsdk.dll";
                    } else if (osName.toLowerCase().startsWith("linux")) {
                        // Linux系统
                        if (osArch.contains("aarch")) {
                            // ARM 架构 Linux 系统加载 SDK 库路径
                            strDllPath = "/libs/dh_linux_arm/libdhnetsdk.so";
                            strConfigPath = "/libs/dh_linux_arm/libdhconfigsdk.so";
                        } else {
                            // 其他 Linux 架构 (例如 x86_64) 加载 SDK 库路径
                            strDllPath = "/libs/dh_linux64/libdhnetsdk.so";
                            strConfigPath = "/libs/dh_linux64/libdhconfigsdk.so";
                        }
                    }
                    netSdk = Native.load(strDllPath, NetSDKLib.class);
                    netConfig = Native.load(strConfigPath, NetSDKLib.class);
                    log.debug("加载大华sdk库路径:{} ", strDllPath);
                } catch (Exception ex) {
                    log.error("加载大华SDK库失败,库路径: {}", strDllPath, ex);
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 云台控制
     *
     * @param loginId 登录句柄
     * @param action  控制动作
     * @param dwStop  是否停止
     */
    private void ptzDHControl(NetSDKLib.LLong loginId, CameraPtzAction action, int dwStop, int channelNum) {
        boolean result = netSdk.CLIENT_DHPTZControlEx(loginId, channelNum,
                action.getDh(),
                0, 3, 0, dwStop);
        if (!result) {
            log.error("大华设备云台控制失败, 错误码：{}", getErrorCodePrint());
        }
    }

    @Override
    public LoginResp login(InspChannelInfo channelInfo) {
        // 从缓存中获取摄像机的登录ID
        LoginResp loginCache = SdkTokenUtil.getLoginCache(channelInfo);
        if (Objects.nonNull(loginCache)) {
            return loginCache;
        }
        String ip = channelInfo.getAddress();

        // 获取登录
        // 入参
        NetSDKLib.NET_IN_LOGIN_WITH_HIGHLEVEL_SECURITY pstInParam = new NetSDKLib.NET_IN_LOGIN_WITH_HIGHLEVEL_SECURITY();
        pstInParam.nPort = 80;
        pstInParam.szIP = channelInfo.getAddress().getBytes();
        pstInParam.szPassword = channelInfo.getPassword().getBytes();
        pstInParam.szUserName = channelInfo.getUsername().getBytes();
        // 出参
        NetSDKLib.NET_OUT_LOGIN_WITH_HIGHLEVEL_SECURITY pstOutParam = new NetSDKLib.NET_OUT_LOGIN_WITH_HIGHLEVEL_SECURITY();
        pstOutParam.stuDeviceInfo = new NetSDKLib.NET_DEVICEINFO_Ex();
        // 调用登录接口
        NetSDKLib.LLong lLong = netSdk.CLIENT_LoginWithHighLevelSecurity(pstInParam, pstOutParam);
        // 判断登录是否成功
        boolean isLogin = lLong.longValue() != 0;
        // 如果登录失败，则抛出异常
        if (!isLogin) {
            log.error("摄像机登录失败，address = {}, loginId = {}", ip, lLong.longValue());
            throw new InspectionException("摄像机登录失败");
        }

        // 组织当前摄像机的通道号集合
        List<Integer> channelNums = new ArrayList<>();
        int maxChannelNum = pstOutParam.stuDeviceInfo.byChanNum;
        for (int i = START_CHAN_NUM; i < maxChannelNum; i++) {
            channelNums.add(i);
        }

        // 将登录ID加入缓存
        LoginResp loginResp = LoginResp.buildDhToken(channelInfo, lLong);
        loginResp.setChannelNums(channelNums);
        SdkTokenUtil.setLoginCache(loginResp);
        return loginResp;
    }

    @Override
    public void logout(LoginResp loginResp) {
        boolean bRet = netSdk.CLIENT_Logout(loginResp.getDhToken());
        if (!bRet) {
            log.error("大华设备登出失败");
        }
    }

    private String getPicCacheKey(InspChannelInfo channelInfo) {
        String address = channelInfo.getAddress();
        Integer channelNum = channelInfo.getChannelNum();
        return address + "_" + channelNum;
    }

    /**
     * 摄像头抓图
     *
     * @param channelInfo 通道信息
     * @return 抓图地址
     */
    @Override
    public synchronized String getPic(InspChannelInfo channelInfo) {
        // 登录摄像头 返回权柄
        LoginResp login = login(channelInfo);
        NetSDKLib.LLong loginId = login.getDhToken();

        String picCacheKey = getPicCacheKey(channelInfo);
        // 获取缓存且不刷新缓存时间
        String cachePic = PIC_CACHE.get(picCacheKey, false);
        if (StrUtil.isNotBlank(cachePic)) {
            return cachePic;
        }

        // 初始化抓图参数输入结构体
        NetSDKLib.NET_IN_SNAP_PIC_TO_FILE_PARAM snapParamIn = new NetSDKLib.NET_IN_SNAP_PIC_TO_FILE_PARAM();
        // 初始化抓图参数输出结构体，指定缓冲区大小为1MB
        NetSDKLib.NET_OUT_SNAP_PIC_TO_FILE_PARAM snapParamOut = new NetSDKLib.NET_OUT_SNAP_PIC_TO_FILE_PARAM(1024 * 1024);

        // 设置抓图参数
        snapParamIn.stuParam.Channel = channelInfo.getChannelNum(); // 通道号
        snapParamIn.stuParam.Quality = 3; // 图片质量
        snapParamIn.stuParam.ImageSize = 1; // 0：QCIF,1：CIF,2：D1
        snapParamIn.stuParam.mode = 0; // -1:表示停止抓图, 0：表示请求一帧, 1：表示定时发送请求, 2：表示连续请求
        snapParamIn.stuParam.InterSnap = 5;
        snapParamIn.stuParam.CmdSerial = 1;

        // 设置抓图文件路径
        Date dateNow = new Date();
        String dateStr = DateUtil.format(dateNow, InspConstants.yyyyMMddHHmmss);
        File tempFile = LocalFileServerUtil.getTempFile(dateStr + RandomUtil.randomInt(1000, 10000) + ".JPEG");
        String fileName = tempFile.getAbsolutePath();


        // 将文件路径转换为字节数组并复制到输入结构体中
        System.arraycopy(fileName.getBytes(), 0, snapParamIn.szFilePath, 0, fileName.getBytes().length);
        final int timeOut = 5000;

        // 创建内存指针并清空
        Pointer pInbuf = new Memory(snapParamIn.size());
        pInbuf.clear(snapParamIn.size());

        // 将输入结构体数据复制到内存指针中
        setStructDataToPointer(snapParamIn, pInbuf, 0);

        // 创建输出内存指针并清空
        Pointer pOutbuf = new Memory(snapParamOut.size());
        pOutbuf.clear(snapParamOut.size());

        // 将输出结构体数据复制到内存指针中
        setStructDataToPointer(snapParamOut, pOutbuf, 0);

        // 调用SDK接口进行抓图
        if (!netSdk.CLIENT_SnapPictureToFile(loginId, pInbuf, pOutbuf, timeOut)) {

            // 如果抓图失败，释放内存并抛出异常
            Native.free(Pointer.nativeValue(pInbuf));// 清理内存
            Pointer.nativeValue(pInbuf, 0); // 防止重复回收

            Native.free(Pointer.nativeValue(pOutbuf));// 清理内存
            Pointer.nativeValue(pOutbuf, 0);
            log.error("抓图失败 错误码： {}", getErrorCodePrint());
            throw new InspectionException("获取截图出现异常");
        }

        // 从输出内存指针中获取抓图结果
        getPointerData(pOutbuf, snapParamOut);

        Native.free(Pointer.nativeValue(pInbuf));// 清理内存
        Pointer.nativeValue(pInbuf, 0); // 防止重复回收

        Native.free(Pointer.nativeValue(pOutbuf));// 清理内存
        Pointer.nativeValue(pOutbuf, 0);

        log.debug("抓图成功. {}", fileName);

        // 图片下载完之后回收指针
//        Native.free(Pointer.nativeValue(szPicBuf));
        Native.free(Pointer.nativeValue(pInbuf));
        Native.free(Pointer.nativeValue(pOutbuf));

        PIC_CACHE.put(picCacheKey, fileName);
        return fileName;
    }

    /**
     * 异步抓图   由于抓图监听器是全局唯一 无法使用CountDownLatch进行异步等待
     *
     * @param channelInfo 通道信息
     * @return 抓图地址
     */
    // @Override
    // public String getPic(InspChannelInfo channelInfo) {
    //     CountDownLatch countDownLatch = ThreadUtil.newCountDownLatch(1);
    //     // 设置抓图文件路径
    //     Date dateNow = new Date();
    //     String dateStr = DateUtil.format(dateNow, InspConstants.yyyyMMddHHmmss);
    //     File tempFile = LocalFileServerUtil.getTempFile(dateStr + RandomUtil.randomInt(1000, 10000) + ".JPEG");
    //     String picPath = tempFile.getAbsolutePath();
    //
    //     LoginResp login = login(channelInfo);
    //     NetSDKLib.fSnapRev fSnapRev = (lLoginID, pBuf, RevLen, EncodeType, CmdSerial, dwUser) -> {
    //         if (pBuf != null && RevLen > 0) {
    //             byte[] buf = pBuf.getByteArray(0, RevLen);
    //             FileUtil.writeBytes(buf, picPath);
    //             countDownLatch.countDown();
    //             log.info("门栓落下 countDownLatch : {}   countDownLatch.getCount() : {}", countDownLatch, countDownLatch.getCount());
    //         } else {
    //             log.error("截图失败,SDK返回文件长度为空:channelId {}", channelInfo.getId());
    //         }
    //     };
    //
    //     // 设置抓图回调: 图片主要在 SnapCallback.getInstance() invoke. 中返回
    //     netSdk.CLIENT_SetSnapRevCallBack(fSnapRev, null);
    //     NetSDKLib.SNAP_PARAMS snapParam = new NetSDKLib.SNAP_PARAMS();
    //     snapParam.Channel = 0; // 抓图通道
    //     snapParam.mode = 0; // 表示请求一帧
    //     snapParam.CmdSerial = 1; // 请求序列号，有效值范围 0~65535，超过范围会被截断
    //     /// 触发抓图动作
    //     if (!netSdk.CLIENT_SnapPictureEx(login.getDhToken(), snapParam, null)) {
    //         log.error("截图失败,SDK接口返回状态为失败，error code = {} channelId = {}", netSdk.CLIENT_GetLastError(),
    //                 channelInfo.getId());
    //         throw new RuntimeException("截图失败");
    //     }
    //
    //     try {
    //         log.info("countDownLatch start。。。。");
    //         boolean await = countDownLatch.await(3, TimeUnit.SECONDS);
    //         log.info("countDownLatch result is {}   countDownLatch.getCount() : {}", await, countDownLatch.getCount());
    //     } catch (InterruptedException e) {
    //         log.error("门闩等待出现异:channelId {}", channelInfo.getId(), e);
    //     }
    //
    //     log.info("截图完成");
    //     if (!FileUtil.exist(picPath)) {
    //         log.error("截图失败,转存文件不存在 channelId = {} picPath = {}", channelInfo.getId(), picPath);
    //         throw new RuntimeException("截图失败");
    //     }
    //     netSdk.CLIENT_SetSnapRevCallBack(null, null);
    //     return picPath;
    // }

    /**
     * 同步跳转预置点
     *
     * @param channelInfo 通道信息
     * @param presetInfo  预置点信息
     * @return 返回是否跳转成功
     */
    @Override
    public boolean gotoPresetAsync(InspChannelInfo channelInfo, InspPresetInfo presetInfo) {
        // 调用跳转预置点方法
        boolean result = gotoPreset(channelInfo, presetInfo);

        // 等待5秒
        SleepUtil.sleepSecond(5);

        return result;

    }

    /**
     * 异步跳转预置点
     *
     * @param channelInfo 通道信息
     * @param presetInfo  预置点信息
     * @return 返回是否跳转成功
     */
    @Override
    public boolean gotoPreset(InspChannelInfo channelInfo, InspPresetInfo presetInfo) {
        // 登录摄像头 返回权柄
        LoginResp login = login(channelInfo);
        NetSDKLib.LLong loginId = login.getDhToken();

        // 调用sdk接口跳转预置点
        boolean result = netSdk.CLIENT_DHPTZControlEx2(loginId, channelInfo.getChannelNum(),
                NetSDKLib.NET_PTZ_ControlType.NET_PTZ_POINT_MOVE_CONTROL, 0
                , presetInfo.getPresetNum(), 0, 0, null);  // Param2 为预置点编号 dwstop 0 为开始转动

        // 判断是否跳转成功
        if (result) {
            log.debug("成功跳转预置点 {}", presetInfo.getPresetNum());
            return true;
        } else {
            log.error("跳转预置点失败，错误码: {}", getErrorCodePrint());
            return false;
        }
    }

    /**
     * 设置预置点
     *
     * @param channelInfo 通道信息
     * @param presetNum   预置点编号
     * @param presetName  预置点名称
     */
    @Override
    public void setPreset(InspChannelInfo channelInfo, Integer presetNum, String presetName) {
        // 登录摄像头 返回权柄
        LoginResp login = login(channelInfo);
        NetSDKLib.LLong loginId = login.getDhToken();

        // 将预置点名称转换为UTF-8
        byte[] nameBytes = presetName.getBytes(StandardCharsets.UTF_8);

        // 创建一个Memory对象，用于存储预置点名称的字节数组
        Pointer pName = new Memory(nameBytes.length + 1);

        // 将字节数组写入Memory对象
        pName.write(0, nameBytes, 0, nameBytes.length);

        // 在字节数组末尾添加一个空字节，表示字符串结束
        pName.setByte(nameBytes.length, (byte) 0);

        // 设置预置点
        boolean result = netSdk.CLIENT_DHPTZControlEx2(loginId, channelInfo.getChannelNum(),
                NetSDKLib.NET_PTZ_ControlType.NET_PTZ_POINT_SET_CONTROL, 0 // lParam1: 预留参数
                , presetNum // lParam2: 预置点编号
                , 0, 0, pName); // lParam4 为预置点名称

        // 判断是否设置成功
        if (result) {
            log.debug("成功设置预置点 {}", presetNum);
        } else {
            log.error("设置预置点 {} 失败，错误码: {}", presetNum, getErrorCodePrint());
        }
    }

    /**
     * 删除预置点
     *
     * @param channelInfo 通道信息
     * @param presetInfo  预置点信息
     */
    @Override
    public void delPreset(InspChannelInfo channelInfo, InspPresetInfo presetInfo) {
        // 登录摄像头 返回权柄
        LoginResp login = login(channelInfo);
        NetSDKLib.LLong loginId = login.getDhToken();

        // 调用SDK接口删除预置点
        boolean result = netSdk.CLIENT_DHPTZControlEx2(loginId, channelInfo.getChannelNum(),
                NetSDKLib.NET_PTZ_ControlType.NET_PTZ_POINT_DEL_CONTROL, 0
                , presetInfo.getPresetNum(), 0, 0, null);  // Param2 为预置点编号

        // 判断是否删除成功
        if (result) {
            log.debug("成功删除预置点{}", presetInfo.getPresetNum());
        } else {
            log.error("删除预置点失败，错误码: {}", getErrorCodePrint());
        }
    }

    /**
     * 开始云台控制
     *
     * @param channelInfo 通道信息
     * @param action      控制动作
     */
    @Override
    public void startCameraPtz(InspChannelInfo channelInfo, CameraPtzAction action) {
        // 登录获取句柄
        LoginResp login = login(channelInfo);
        NetSDKLib.LLong loginId = login.getDhToken();

        // 调用云台控制方法
        ptzDHControl(loginId, action, 0, channelInfo.getChannelNum());
    }

    /**
     * 停止云台控制
     *
     * @param channelInfo 通道信息
     * @param action      控制动作
     */
    @Override
    public void stopCameraPtz(InspChannelInfo channelInfo, CameraPtzAction action) {
        // 登录获取句柄
        LoginResp login = login(channelInfo);
        NetSDKLib.LLong loginId = login.getDhToken();

        // 调用云台控制方法
        ptzDHControl(loginId, action, 1, channelInfo.getChannelNum());
    }

    /**
     * 获取rtsp地址
     *
     * @param channelInfo 通道信息
     * @return 返回rtsp地址列表
     */
    @Override
    public List<RtspStreamModel> getRtspUrl(InspChannelInfo channelInfo) {
        // 登录摄像头 返回权柄
        LoginResp login = login(channelInfo);
        NetSDKLib.LLong loginId = login.getDhToken();

        List<RtspStreamModel> result = new ArrayList<>();
        List<Integer> channelNums = login.getChannelNums();
        for (Integer channelNum : channelNums) {
            // 创建通道编码结构体
            NetSDKLib.CFG_ENCODE_INFO encodeInfo = new NetSDKLib.CFG_ENCODE_INFO();

            // 设置通道号
            encodeInfo.nChannelID = channelNum;
            // 获取通道编码配置
            boolean ret = getDevConfig(loginId, channelNum, CFG_CMD_ENCODE, encodeInfo);
            // 判断是否获取成功
            if (ret) {
                // 主码流
                int mainStreamCount = encodeInfo.nValidCountMainStream;

                // 子码流数量
                int subStreamCount = encodeInfo.nValidCountExtraStream;

                // 获取摄像头IP地址
                String ipAddress = channelInfo.getAddress();

                // 生成主码流 RTSP URL
                if (mainStreamCount > 0) {
                    String rtspUrl = StrUtil.format(RTSP_URL_FORMAT,
                            ipAddress, RTSP_PORT, channelNum + 1, 0); // subtype 0 为主码流
                    RtspStreamModel rtspStreamModel = RtspStreamModel.createRtspStreamModel(channelNum, rtspUrl);
                    result.add(rtspStreamModel);
                }

                // 生成子码流 RTSP URL
                if (subStreamCount > 0) {
                    String rtspUrl = StrUtil.format(RTSP_URL_FORMAT,
                            ipAddress, RTSP_PORT, channelNum + 1, 1); // subtype 从 1 开始递增

                    RtspStreamModel rtspStreamModel = RtspStreamModel.createRtspStreamModel(channelNum, rtspUrl);
                    result.add(rtspStreamModel);
                }
            } else {
                log.error("获取通道{}rtsp地址失败，错误码： {}", channelNum, getErrorCodePrint());
            }
        }
        return result;
    }

    @Override
    public List<RecordingFileResp> getRecordingFilesList(RecordingFileRes req) {
        return Collections.emptyList();
    }

    @Override
    public DownloadOptRes downloadRecordingFileByName(DownloadRecordingFileRes downloadRecordingFileRes) {
        return null;
    }

    /**
     * 获取主码流分辨率
     *
     * @param channelInfo 通道信息
     * @return 返回分辨率
     */
    // @Override
    // public ScaleModel getDisplayResolution(InspChannelInfo channelInfo) {
    //     // 分辨率由上层代理统一实现
    //     throw new InspectionException("此分辨率方法禁止调用");
    // }
    @Override
    public String getManufacturer(InspChannelInfo channelInfo) {
        return DeviceManufacturer.DAHUA.name();
    }

    @Override
    public String playBackByTime(PlayBackByTimeRes playBackByTimeRes) {
        return "";
    }

    /**
     * 获取内存的数据
     */
    private void getPointerData(Pointer pNativeData, Structure pJavaStu) {
        getPointerDataToStruct(pNativeData, 0, pJavaStu);
    }

    /**
     * 将内存数据赋值给结构体
     */
    public void getPointerDataToStruct(Pointer pNativeData, long OffsetOfpNativeData, Structure pJavaStu) {
        pJavaStu.write();
        Pointer pJavaMem = pJavaStu.getPointer();
        pJavaMem.write(0, pNativeData.getByteArray(OffsetOfpNativeData, pJavaStu.size()), 0,
                pJavaStu.size());
        pJavaStu.read();
    }

    /**
     * 设置结构体到内存数据中
     */
    public void setStructDataToPointer(Structure pJavaStu, Pointer pNativeData, long OffsetOfpNativeData) {
        pJavaStu.write();
        Pointer pJavaMem = pJavaStu.getPointer();
        pNativeData.write(OffsetOfpNativeData, pJavaMem.getByteArray(0, pJavaStu.size()), 0, pJavaStu.size());
    }

    /**
     * 获取当前时间
     */
    public String getDate() {
        SimpleDateFormat simpleDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return simpleDate.format(new java.util.Date()).replace(" ", "_").replace(":", "-");
    }

    /**
     * 获取通道配置
     */
    public boolean getDevConfig(NetSDKLib.LLong hLoginHandle, int nChn, String strCmd, Structure cmdObject) {
        // 获取通道配置
        IntByReference error = new IntByReference(0);
        IntByReference retLen = new IntByReference(0);
        byte[] strBuffer = new byte[CONFIG_BUFFER_SIZE];

        if (!netSdk.CLIENT_GetNewDevConfig(hLoginHandle, strCmd, nChn, strBuffer, CONFIG_BUFFER_SIZE, error, 5000, null)) {
            log.error("获取 {} 配置失败，通道 {}，错误码: {}", strCmd, nChn, netSdk.CLIENT_GetLastError());
            return false;
        }

        cmdObject.write();
        if (!netConfig.CLIENT_ParseData(strCmd, strBuffer, cmdObject.getPointer(), cmdObject.size(), retLen.getPointer())) {
            log.error("解析 {} 配置失败，通道 {}，错误信息: {}", strCmd, nChn, getErrorCodePrint());
            return false;
        }
        cmdObject.read();

        return true;
    }

    /**
     * 打印错误码
     */
    public String getErrorCodePrint() {
        return "\n{error code: (0x80000000|" + (netSdk.CLIENT_GetLastError() & 0x7fffffff) + ").参考  NetSDKLib.java }"
                + " - {error info:" + ErrorCode.getErrorCode(netSdk.CLIENT_GetLastError()) + "}\n";
    }
}
