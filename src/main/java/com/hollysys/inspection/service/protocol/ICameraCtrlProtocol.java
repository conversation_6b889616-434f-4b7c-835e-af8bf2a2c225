package com.hollysys.inspection.service.protocol;

import com.hollysys.inspection.constants.channel.CameraPtzAction;
import com.hollysys.inspection.entity.InspChannelInfo;
import com.hollysys.inspection.entity.InspPresetInfo;
import com.hollysys.inspection.model.channel.RtspStreamModel;
import com.hollysys.inspection.model.sdk.*;

import java.util.List;

/**
 * 摄像机控制接入协议接口层
 */
public interface ICameraCtrlProtocol {

    /**
     * 登录摄像机
     */
    LoginResp login(InspChannelInfo channelInfo);

    /**
     * 登出摄像机
     */
    void logout(LoginResp loginResp);

    /**
     * 获取一张摄像机实时截图，返回本地文件路径
     */
    String getPic(InspChannelInfo channelInfo);

    /**
     * 预置点位置跳转异步（不等待）
     */
    boolean gotoPresetAsync(InspChannelInfo channelInfo, InspPresetInfo presetInfo);

    /**
     * 预置点位置跳转同步（等待跳转成功或失败）
     */
    boolean gotoPreset(InspChannelInfo channelInfo, InspPresetInfo presetInfo);

    /**
     * 设置当前通道画面为一个预置点，返回设置成功的预置点编号
     */
    void setPreset(InspChannelInfo channelInfo, Integer presetNum, String presetName);

    /**
     * 预置点删除
     */
    void delPreset(InspChannelInfo channelInfo, InspPresetInfo presetInfo);

    /**
     * 相机PTZ控制启动
     */
    void startCameraPtz(InspChannelInfo channelInfo, CameraPtzAction action);

    /**
     * 相机PTZ控制停止
     */
    void stopCameraPtz(InspChannelInfo channelInfo, CameraPtzAction action);

    /**
     * 获取通道的全部RTSP流地址
     */
    List<RtspStreamModel> getRtspUrl(InspChannelInfo channelInfo);

    /**
     * 获取摄像头像文件列表
     */
    List<RecordingFileResp> getRecordingFilesList(RecordingFileRes req);

    /**
     * 根据录像文件名字下载录像文件
     */
    DownloadOptRes downloadRecordingFileByName(DownloadRecordingFileRes downloadRecordingFileRes);

    /**
     * 获取视频的分辨率
     */
    // ScaleModel getDisplayResolution(InspChannelInfo channelInfo);

    /**
     * 获取SDK存储的父级目录
     */
    default String getLibsRootDir() {
        return "/etc";
    }

    /**
     * 获取摄像头厂商
     */
    String getManufacturer(InspChannelInfo channelInfo);

    /**
     * 录像回放
     */
    String playBackByTime(PlayBackByTimeRes playBackByTimeRes);
}
