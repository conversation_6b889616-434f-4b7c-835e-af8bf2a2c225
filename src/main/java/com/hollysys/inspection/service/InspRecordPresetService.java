package com.hollysys.inspection.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.csv.CsvWriter;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hollysys.inspection.constants.InspConstants;
import com.hollysys.inspection.constants.algorithm.ExecuteResultStatus;
import com.hollysys.inspection.entity.InspRecordAlgorithm;
import com.hollysys.inspection.entity.InspRecordPreset;
import com.hollysys.inspection.mapper.InspRecordPresetMapper;
import com.hollysys.inspection.model.algorithm.execute.record.PresetRecordItemModel;
import com.hollysys.inspection.model.backup.ExecuteBackupResult;
import com.hollysys.inspection.utils.FileNameUtil;
import com.hollysys.inspection.utils.LocalFileServerUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 预置点执行记录表服务接口
 *
 * <AUTHOR>
 * @since 2023-02-01 15:36:00
 */
@Service
@Slf4j
public class InspRecordPresetService extends ServiceImpl<InspRecordPresetMapper, InspRecordPreset> {

    @Resource
    private InspRecordAlgorithmService recordAlgorithmService;

    @Resource
    private InspAlarmRecordService alarmRecordService;

    // 创建日期时间格式化器
    private final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(InspConstants.yyyy_MM_ddHHmmss);

    @Transactional
    public ExecuteBackupResult backup(List<InspRecordPreset> recordPresets) {
        // 创建两个列表，用于存储预置点执行记录和算法执行记录的行数据
        List<String[]> rows1 = new ArrayList<>();
        List<String[]> rows2 = new ArrayList<>();

        // 使用Set来自动去重图片URL
        Set<String> allImageUrlsToDownload = new HashSet<>();

        // 创建一个列表，用于存储所有的算法执行记录
        List<InspRecordAlgorithm> allRecordAlgorithms = new ArrayList<>();

        // 添加 预置点执行记录 CSV 文件头
        String[] presetHeaders = InspRecordPreset.generateCsvHeader();
        rows1.add(presetHeaders);

        // 添加算法执行记录 CSV 文件头
        String[] algorithmHeaders = InspRecordAlgorithm.generateCsvHeader();
        rows2.add(InspRecordAlgorithm.generateCsvHeader());

        //  遍历预置点执行记录列表
        for (InspRecordPreset recordPreset : recordPresets) {
            // 对象转化为行数据
            List<String> row = objToRow(presetHeaders, recordPreset);

            // 将当前预置点执行记录的行数据添加到列表中
            rows1.add(row.toArray(new String[0]));

            // 在算法执行记录寻找该预置点的执行记录
            List<InspRecordAlgorithm> inspRecordAlgorithms = recordAlgorithmService
                    .listByRecordPresetId(recordPreset.getId());

            // 如果没有找到对应的算法执行记录，则跳过
            if (CollectionUtil.isEmpty(inspRecordAlgorithms)) {
                continue;
            }

            // 将找到的算法执行记录添加到总列表中
            allRecordAlgorithms.addAll(inspRecordAlgorithms);

            // 遍历找到的算法执行记录
            for (InspRecordAlgorithm recordAlgorithm : inspRecordAlgorithms) {
                // 对象转化为行数据
                List<String> algorithmRow = objToRow(algorithmHeaders, recordAlgorithm);
                // 将当前算法执行记录的行数据添加到列表中
                rows2.add(algorithmRow.toArray(new String[0]));

                // 收集图片URL
                if (CollectionUtil.isNotEmpty(recordAlgorithm.getInputImages())) {
                    // 输入图片
                    allImageUrlsToDownload.addAll(recordAlgorithm.getInputImages());
                }
                if (StrUtil.isNotBlank(recordAlgorithm.getOutputImage())) {
                    // 输出图片
                    allImageUrlsToDownload.add(recordAlgorithm.getOutputImage());
                }
            }
        }

        log.debug("根据预置点执行记录寻找到对应算法执行记录 {} 条", allRecordAlgorithms.size());

        // 生成备份文件名 时间
        InspRecordPreset recordFirst = recordPresets.get(0);
        InspRecordPreset recordLast = recordPresets.get(recordPresets.size() - 1);
        String fileName = FileNameUtil.getBackFileName(recordFirst.getStartTime(), recordLast.getStartTime());

        // 预置点执行记录文件
        File tempPresetFile = LocalFileServerUtil.getTempFile(fileName.replace(".csv", "-预置点执行记录.csv"));

        // 写入预置点执行记录 csv 文件
        try (CsvWriter writer = new CsvWriter(tempPresetFile, CharsetUtil.CHARSET_UTF_8)) {
            for (String[] row : rows1) {
                writer.write(row);
            }
        }

        // 算法执行记录文件
        File tempAlgorithmFile = LocalFileServerUtil.getTempFile(
                fileName.replace(".csv", "-算法执行记录.csv")
        );

        // 写入算法执行记录 csv 文件
        try (CsvWriter writer = new CsvWriter(tempAlgorithmFile, CharsetUtil.CHARSET_UTF_8)) {
            for (String[] row : rows2) {
                writer.write(row);
            }
        }

        // 删除预置点执行记录数据
        List<String> presetIds = recordPresets.stream().map(InspRecordPreset::getId).collect(Collectors.toList());
        removeByIds(presetIds);

        // 删除算法执行记录数据
        List<String> algorithmIds = allRecordAlgorithms.stream().map(InspRecordAlgorithm::getId).collect(Collectors.toList());
        recordAlgorithmService.removeByIds(algorithmIds);
        // 返回包含备份文件和图片URL列表的包装对象
        return new ExecuteBackupResult(
                new File[]{tempPresetFile, tempAlgorithmFile},
                new ArrayList<>(allImageUrlsToDownload) // 将Set转换为List返回
        );
    }

    private List<String> objToRow(String[] presetHeaders, Object dataObj) {
        List<String> row = new ArrayList<>();
        // 将预置点执行记录转换为 JSON 对象
        JSONObject presetEntries = JSONUtil.parseObj(dataObj);

        // 遍历预置点执行记录 CSV 文件头
        for (String key : presetHeaders) {
            Object object = presetEntries.get(key);

            // 如果值不为空
            if (object != null) {
                if (object instanceof LocalDateTime) {
                    row.add(((LocalDateTime) object).format(dateTimeFormatter));
                } else if (object instanceof Integer) {
                    row.add(Integer.toString((Integer) object));
                } else {
                    row.add(JSONUtil.toJsonStr(object));
                }
            } else {
                row.add("");
            }
        }
        return row;
    }

    public void removeByChannelId(String channelId) {
        lambdaUpdate()
                .eq(InspRecordPreset::getChannelId, channelId)
                .remove();
    }

    public void removeByPresetId(String presetId) {
        lambdaUpdate()
                .eq(InspRecordPreset::getPresetId, presetId)
                .remove();
    }

    public void removeByPresetIds(List<String> presetIds) {
        lambdaUpdate()
                .in(InspRecordPreset::getPresetId, presetIds)
                .remove();
    }

    public IPage<?> pageAllPresetRecords(IPage<InspRecordPreset> page, String nodeId, String startTime,
                                         String endTime, String searchStr, ExecuteResultStatus executeResultStatus) {
        Map<String, String> allIdNameMap = alarmRecordService.getAllIdNameMap(nodeId);
        if (CollectionUtil.isEmpty(allIdNameMap)) {
            return page;
        }
        // 按searchStr模糊
        Map<String, String> idNameMapSearch = alarmRecordService.searchIdNameMap(allIdNameMap, searchStr);
        // 数据库查询
        IPage<InspRecordPreset> recordPage;
        LambdaQueryChainWrapper<InspRecordPreset> lambdaWrapper = lambdaQuery();
        if (StrUtil.isNotBlank(startTime)) {
            lambdaWrapper.gt(InspRecordPreset::getStartTime, DateUtil.parse(startTime, InspConstants.yyyy_MM_ddHHmmss));
        }
        if (StrUtil.isNotBlank(endTime)) {
            lambdaWrapper.lt(InspRecordPreset::getStartTime, DateUtil.parse(endTime, InspConstants.yyyy_MM_ddHHmmss));
        }
        // 按照成功或失败查询记录
        if (Objects.nonNull(executeResultStatus)) {
            lambdaWrapper.eq(InspRecordPreset::getResultStatus, executeResultStatus.name());
        }
        // 按创建时间降序排列，新的执行记录排在前面
        lambdaWrapper.orderByDesc(InspRecordPreset::getStartTime);
        if (Objects.isNull(idNameMapSearch)) {
            // idNameMap == null 说明前端未输入搜索文本
            recordPage = lambdaWrapper
                    .in(InspRecordPreset::getChannelId, allIdNameMap.keySet())
                    .in(InspRecordPreset::getPresetId, allIdNameMap.keySet())
                    .page(page);
        } else {
            // idNameMap != null 说明前端输入了搜索文本
            if (CollectionUtil.isEmpty(idNameMapSearch)) {
                // 搜索文本未未命中数据
                recordPage = page;
                recordPage.setRecords(new ArrayList<>());
            } else {
                recordPage = lambdaWrapper
                        .and(wrapper -> wrapper
                                .in(InspRecordPreset::getChannelId, idNameMapSearch.keySet())
                                .or()
                                .in(InspRecordPreset::getPresetId, idNameMapSearch.keySet())
                        )
                        .page(page);
            }
        }
        // 对象转化
        return recordPage.convert(executeRecord -> {
            PresetRecordItemModel presetRecordItemModel = new PresetRecordItemModel();
            BeanUtil.copyProperties(executeRecord, presetRecordItemModel);
            // 设置通道名
            String channelId = executeRecord.getChannelId();
            String channelName = allIdNameMap.get(channelId);
            presetRecordItemModel.setChannelName(channelName);
            // 设置预置点名
            String presetId = executeRecord.getPresetId();
            String presetName = allIdNameMap.get(presetId);
            presetRecordItemModel.setPresetName(presetName);

            return presetRecordItemModel;
        });
    }
}

