package com.hollysys.inspection.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hollysys.inspection.config.redis.RedisHelper;
import com.hollysys.inspection.entity.InspCommonConfig;
import com.hollysys.inspection.mapper.InspCommonConfigMapper;
import com.hollysys.inspection.model.DataBaseConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * (InspCommonConfig)表服务接口
 *
 * <AUTHOR>
 * @since 2024-01-17 10:33:18
 */
@Slf4j
@Service
public class InspCommonConfigService extends ServiceImpl<InspCommonConfigMapper, InspCommonConfig> {

    private InspCommonConfig COMMON_CONFIG = null;

    @Value("${db-base.auth-srv.enabled}")
    private Boolean dbBaseAuthSrvEnabled;

    @Resource
    private LogConfigService logConfigService;

    private static final String CONFIG_FINISH = "config";

    @Resource
    private RedisHelper redisUtils;

    @Resource
    private DataBaseNetworkService dataBaseNetworkService;

    @Resource
    private MediamtxService mediamtxService;

    /**
     * 刷新缓存
     * @return 配置
     */
    public synchronized InspCommonConfig freshCache() {
        InspCommonConfig commonConfig = getCommonConfigFromDb();
        // 写入缓存
        if (Objects.nonNull(commonConfig)) {
            if (Objects.isNull(COMMON_CONFIG)) {
                COMMON_CONFIG = new InspCommonConfig();
            }
            BeanUtils.copyProperties(commonConfig, COMMON_CONFIG);
            // 每次刷新配置设置日志级别
            logConfigService.setLogLevel(COMMON_CONFIG.getLoggingLevel());
        }
        return commonConfig;
    }

    /**
     * 从数据库获取配置
     * @return 数据
     */
    public InspCommonConfig getCommonConfigFromDb() {
        List<InspCommonConfig> list = list();
        if (CollectionUtil.isEmpty(list)) {
            return new InspCommonConfig();
        }
        return list.get(0);
    }

    /**
     * 更新配置
     * @param param 配置
     */
    @Transactional
    public void updateCommonConfig(InspCommonConfig param) {
        // 从数据库获取配置记录
        InspCommonConfig currentDbConfig = getCommonConfigFromDb();

        // 提前保存旧的流媒体IP列表，用于后面的比较
        List<String> oldMtxIps = currentDbConfig.getMtxIps();

        // 将现有配置和传入的更新配置都转换为 JSONObject
        JSONObject currentJson = JSONUtil.parseObj(currentDbConfig);
        JSONObject updateJson = JSONUtil.parseObj(param);

        // 更新配置字段 并且只有传入的配置字段不为null时才覆盖
        for (String key : updateJson.keySet()) {
            Object value = updateJson.get(key);
            if (value != null) {
                currentJson.set(key, value);
            }
        }

        // 转换为InspCommonConfig对象
        InspCommonConfig mergedConfig = JSONUtil.toBean(currentJson, InspCommonConfig.class);

        // 确保ID不变
        mergedConfig.setId(currentDbConfig.getId());

        // 判断是否更新了流媒体配置
        List<String> newMtxIps = mergedConfig.getMtxIps();

        // 将两个列表转换为 Set，便于后续进行比较是否发生了变化
        Set<String> oldIpSet = oldMtxIps != null ? new HashSet<>(oldMtxIps) : new HashSet<>();
        Set<String> newIpSet = newMtxIps != null ? new HashSet<>(newMtxIps) : new HashSet<>();

        // 如果新的IP列表与旧的IP列表不相等，则执行更新操作
        if (!newIpSet.equals(oldIpSet) && newMtxIps != null) {
            mediamtxService.updateWebrtcAdditionalHosts(newMtxIps);
        }

        // 更新
        updateById(mergedConfig);
    }

    /**
     * 获取指定配置
     * @param itemName 字段
     * @return 配置
     */
    public Object getCommonConfigItemCache(String itemName) {
        InspCommonConfig commonConfigCache = getCommonConfigCache();
        JSONObject entries = JSONUtil.parseObj(commonConfigCache);
        return entries.get(itemName);
    }

    /**
     * 刷新缓存
     * @return 配置
     */
    public InspCommonConfig getCommonConfigCache() {
        return freshCache();
    }



    /**
     * 判断前端是否需要跳转到单点登录配置页
     * @return 结果
     */
    public Object needDbaseConfig() {
        // 校验目前单点登录的的ip是否有效
        InspCommonConfig commonConfig = getCommonConfigCache();
        DataBaseConfig dataBaseConfig = commonConfig.getDataBaseConfig();
        dataBaseNetworkService.testDataBaseLogin(dataBaseConfig.getDbBaseServerIp());
        dataBaseNetworkService.validateDbBaseInfo(dataBaseConfig.getDbBaseClientId(), dataBaseConfig.getDbBaseSecret(),dataBaseConfig.getDbBaseServerIp());
        return null;
    }

    /**
     * 是否为单点登录
     * @return 结果
     */
    public boolean dbBaseEnabled() {
        return dbBaseAuthSrvEnabled;
    }

    /**
     * 判断是否是第一次配置
     * @return 结果
     */
    public boolean isFirstConfig() {
        return redisUtils.get(CONFIG_FINISH) == null;
    }

    /**
     * 配置完成存入redis标识
     * @return 结果
     */
    public boolean finishConfig() {
        // 如果是第一次配置 则存入redis一个标识
        if (redisUtils.get(CONFIG_FINISH) == null) {
            redisUtils.set(CONFIG_FINISH,"finish");
            return true;
        } else {
            return false;
        }
    }
}

