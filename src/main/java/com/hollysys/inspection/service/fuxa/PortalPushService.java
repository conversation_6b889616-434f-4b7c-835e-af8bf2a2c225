package com.hollysys.inspection.service.fuxa;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class PortalPushService {
//    @Resource
//    private WebSocketPowerServer webSocketPowerServer;
//
//    @Resource
//    private FileServeService fileServeService;
//
//    @Value("${power.publish-topic.img_python_path1}")
//    private String powerImgPythonTopic1;
//    @Value("${power.publish-topic.img_python_path2}")
//    private String powerImgPythonTopic2;
//    @Value("${power.publish-topic.img_python_path3}")
//    private String powerImgPythonTopic3;
//    @Value("${power.publish-topic.img_python_path4}")
//    private String powerImgPythonTopic4;

    //    @Scheduled(fixedDelay = 200)
    public void pushMessageToFuxa() {
//        if (CacheUtils.IMG_MAP.isEmpty() || CacheUtils.IMG_MAP.size() < 4) {
//            return;
//        }
//        List<String> imgPaths = new ArrayList<>();
//        String powerImg1 = CacheUtils.IMG_MAP.get(powerImgPythonTopic1);
//        String powerImg2 = CacheUtils.IMG_MAP.get(powerImgPythonTopic2);
//        String powerImg3 = CacheUtils.IMG_MAP.get(powerImgPythonTopic3);
//        String powerImg4 = CacheUtils.IMG_MAP.get(powerImgPythonTopic4);
//        powerImg1 = fileServeService.toServePath(powerImg1);
//        powerImg2 = fileServeService.toServePath(powerImg2);
//        powerImg3 = fileServeService.toServePath(powerImg3);
//        powerImg4 = fileServeService.toServePath(powerImg4);
//        imgPaths.add(powerImg1);
//        imgPaths.add(powerImg2);
//        imgPaths.add(powerImg3);
//        imgPaths.add(powerImg4);
//        webSocketPowerServer.sendMessage(imgPaths);
    }
}
