package com.hollysys.inspection.service;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import com.hollysys.inspection.config.exceptions.InspectionException;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 系统日志级别配置
 */
@Service
public class LogConfigService {
    private static final String packageName = "com.hollysys.inspection";

    private static final List<String> VALID_LOG_LEVELS = new ArrayList<>(Arrays.asList(
            "OFF", "FATAL", "ERROR", "WARN", "INFO", "DEBUG", "TRACE", "ALL"
    ));

    public void setLogLevel(String levelString) {
        if (levelString == null || levelString.isEmpty()) {
            throw new InspectionException("日志级别不能为空");
        }

        // 校验传入的日志级别是否在有效日志级别范围中
        if (!VALID_LOG_LEVELS.contains(levelString.toUpperCase())) {
            throw new InspectionException("无效的日志级别");
        }

        LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();

        // 获取Logger 实例 如果该Logger不存在，Logback会自动创建
        Logger logger = loggerContext.getLogger(packageName);
        // 将字符串转换为 Logback 的 Level 对象
        Level level = Level.valueOf(levelString.toUpperCase());
        // 设置 Logger 的级别
        logger.setLevel(level);
    }
}
