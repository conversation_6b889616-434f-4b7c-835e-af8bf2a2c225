package com.hollysys.inspection.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.model.sdk.*;
import com.hollysys.inspection.service.protocol.impl.CameraCtrlProxyService;
import com.hollysys.inspection.utils.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Service
public class VideoPlayBackService {

    @Value("${recording-file.base}")
    private String recordingFileBase;

    @Resource
    private CameraCtrlProxyService cameraCtrlProxyService;


    /**
     * 查询摄像头录像文件
     * @param recordingFileRes 摄像头信息
     * @return 文件信息
     */
    public List<RecordingFileResp> getRecordingFileList(RecordingFileRes recordingFileRes) {
        return cameraCtrlProxyService.getRecordingFilesList(recordingFileRes);
    }

    /**
     * 下载录像文件
     * @param downloadRecordingFileRes 请求体
     * @return 下载结果
     */
    public DownloadOptRes downloadRecordingFileByName(DownloadRecordingFileRes downloadRecordingFileRes) {
        return cameraCtrlProxyService.downloadRecordingFileByName(downloadRecordingFileRes);
    }

    /**
     * 回放录像
     * @param playBackByTimeRes 请求体
     * @return rtsp地址
     */
    public String playBackByTime(PlayBackByTimeRes playBackByTimeRes) {
        String clientKey = playBackByTimeRes.getClientKey();
        AssertUtil.isTrue(StrUtil.isNotBlank(clientKey), "客户端关键标识不允许为空");
        return cameraCtrlProxyService.playBackByTime(playBackByTimeRes);
    }

    /**
     * 导出下载到服务端的录像文件
     * @param fileName 文件名
     * @return 文件
     */
    public Object exportRecordingFile(String fileName) {
        Path fullPath = Paths.get(recordingFileBase).resolve(fileName).normalize();
        try {
            // 将文件路径转换为可供发送的UrlResource
            UrlResource resource = new UrlResource(fullPath.toUri());

            // 检查文件是否存在
            if (resource.exists() && resource.isReadable()) {
                // 设置HTTP响应头，指示浏览器进行下载
                HttpHeaders headers = new HttpHeaders();
                headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + URLUtil.encode(fileName) + "\"");
                headers.add(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "Content-Disposition");

                return ResponseEntity.ok()
                        .headers(headers)
                        .contentType(MediaType.APPLICATION_OCTET_STREAM)
                        .body(resource);
            } else {
                log.error("文件不存在或无法访问: {}", fileName);
                throw new InspectionException("录像文件不存在或无法访问：" + fileName);
            }
        } catch (Exception e) {
            log.error("无效的文件路径: {}", fileName, e);
            throw new InspectionException("无效的文件路径");
        }
    }

    /**
     * 定时任务：执行录像文件清理
     */
    @Scheduled(cron = "${recording-file.folder-clean.cron}")
    public void cleanupAllFiles() {
        log.debug("开始执行录像文件文件夹清理任务...");

        File directory = FileUtil.file(recordingFileBase);
        if (!directory.exists() || !directory.isDirectory()) {
            log.error("清理失败，指定路径 {} 不存在或不是一个目录。", recordingFileBase);
            return;
        }

        File[] filesArray = FileUtil.ls(recordingFileBase);
        if (filesArray == null || filesArray.length == 0) {
            log.debug("文件夹 {} 中没有文件，无需清理。", recordingFileBase);
            return;
        }

        List<File> files = Arrays.asList(filesArray);
        log.debug("发现 {} 个文件需要清理。", files.size());

        long fiveMinutesAgo = System.currentTimeMillis() - 5 * 60 * 1000;

        for (File file : files) {
            // 先判断是否为文件
            if (file.isFile()) {
                // 获取文件的最后修改时间
                long lastModified = file.lastModified();

                // 如果文件的修改时间在5分钟之前，则进行删除
                if (lastModified < fiveMinutesAgo) {
                    FileUtil.del(file);
                    log.debug("已删除文件: {}", file.getAbsolutePath());
                } else {
                    log.debug("文件 {} 修改时间未超过5分钟，跳过清理。", file.getAbsolutePath());
                }
            }
        }

        log.debug("录像文件文件夹清理任务完成。");
    }
}
