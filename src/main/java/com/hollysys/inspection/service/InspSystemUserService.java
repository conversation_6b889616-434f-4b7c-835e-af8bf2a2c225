package com.hollysys.inspection.service;

import cn.dev33.satoken.secure.SaSecureUtil;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.entity.InspSystemUser;
import com.hollysys.inspection.mapper.InspSystemUserMapper;
import com.hollysys.inspection.model.ChangePasswordReq;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 算法信息表(InspSystemUser)表服务接口
 *
 * <AUTHOR>
 * @since 2023-05-17 16:10:22
 */
@Service
public class InspSystemUserService extends ServiceImpl<InspSystemUserMapper, InspSystemUser> {

    public Object changePassword(ChangePasswordReq reqModel) {
        String userId = StpUtil.getLoginIdAsString();
        InspSystemUser byId = getById(userId);
        if (Objects.isNull(byId)) {
            throw new InspectionException("用户不存在");
        }
        String oldPassword = reqModel.getOldPassword();
        String newPassword = reqModel.getNewPassword();
        if (StrUtil.hasBlank(oldPassword, newPassword)) {
            throw new InspectionException("参数错误");
        }
        String oldEncryptedPassword = SaSecureUtil.md5(oldPassword);
        if (!oldEncryptedPassword.equals(byId.getPassword())) {
            throw new InspectionException("旧密码输入错误");
        }

        String newEncryptedPassword = SaSecureUtil.md5(newPassword);
        byId.setPassword(newEncryptedPassword);
        return updateById(byId);
    }
}

