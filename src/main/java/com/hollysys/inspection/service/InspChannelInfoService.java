package com.hollysys.inspection.service;

import cn.darkjrong.license.core.common.pojo.params.LicenseExtraParam;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.io.unit.DataSize;
import cn.hutool.core.io.unit.DataUnit;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hollysys.inspection.config.annotations.TempFileCleanable;
import com.hollysys.inspection.config.exceptions.ChannelExistException;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.config.redis.RedisHelper;
import com.hollysys.inspection.constants.CameraType;
import com.hollysys.inspection.constants.MsgLevel;
import com.hollysys.inspection.constants.ProjectNodeType;
import com.hollysys.inspection.constants.ScheduleTaskMode;
import com.hollysys.inspection.constants.channel.CameraCtrlProtocol;
import com.hollysys.inspection.constants.channel.DeviceManufacturer;
import com.hollysys.inspection.constants.channel.MtxSourceProtocol;
import com.hollysys.inspection.constants.channel.VideoProtocolType;
import com.hollysys.inspection.controller.websocket.ImportFromExcelServer;
import com.hollysys.inspection.entity.*;
import com.hollysys.inspection.mapper.InspChannelInfoMapper;
import com.hollysys.inspection.model.ChannelPreviewInfo;
import com.hollysys.inspection.model.channel.*;
import com.hollysys.inspection.model.channelexcel.ImportReqModel;
import com.hollysys.inspection.model.socket.ImportExcelMsg;
import com.hollysys.inspection.service.protocol.impl.CameraCtrlProxyService;
import com.hollysys.inspection.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.InputStream;
import java.io.Serializable;
import java.net.URL;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.hollysys.inspection.constants.InspConstants.CHANNEL_MODE_RECOVER;
import static com.hollysys.inspection.constants.InspConstants.TASK_RECOVER_IN_SECONDS;

/**
 * (ChannelNode)表服务接口
 *
 * <AUTHOR>
 * @since 2023-02-01 15:36:00
 */
@Slf4j
@Service
public class InspChannelInfoService extends ServiceImpl<InspChannelInfoMapper, InspChannelInfo> {

    @Resource
    private InspPresetInfoService presetInfoService;

    @Resource
    private CameraCtrlProxyService cameraCtrlProxyService;

    @Resource
    private InspScheduleTaskNodeService scheduleTaskNodeService;

    @Resource
    private ProjectTreeNodeService projectTreeNodeService;

    @Resource
    private ChannelExcelImportService channelExcelImportService;

    @Resource
    private MediamtxService mediamtxService;

    @Resource
    private LicenseService licenseService;

    @Resource
    private ImportFromExcelServer importFromExcelServer;

    @Resource
    private InspAlgorithmInstanceService algorithmInstanceService;

    @Resource
    private InspAlgorithmParamInstanceService algorithmParamInstanceService;

    @Resource
    private RedisHelper redisHelper;

    @Resource
    private SqlSession sqlSession;

    @Value("${rtsp.validation.timeout:10}")
    private Integer restValidationTimeout;

    @Override
    public boolean save(InspChannelInfo entity) {
        checkChannelSize();
        return super.save(entity);
    }

    /**
     * 比较通道数量和证书授权通道数量的比较结果
     */
    private void checkChannelSize() {
        // 获取证书参数
        LicenseExtraParam licenseExtraParam = licenseService.getLicenseExtraParamInRedis();
        // 证书参数判空
        if (licenseExtraParam == null) {
            throw new InspectionException("证书参数为空");
        }

        // 获取证书中的最大通道数量
        int channelMaxSize = 0;
        if (licenseExtraParam.getMaxChannelSize() != null) {
            channelMaxSize = licenseExtraParam.getMaxChannelSize();
        }

        // 获取当前通道数量
        int channelCount = licenseService.getChannelNodeCount();

        // 如果当前通道数量大于等于证书中的最大通道数量
        if (channelCount >= channelMaxSize) {
            throw new InspectionException("通道数量超过授权最大数量" + channelMaxSize);
        }
    }

    public InspChannelInfo getOneById(String channelId) {
        sqlSession.clearCache();
        InspChannelInfo channelInfo = getById(channelId);
        if (Objects.isNull(channelInfo)) {
            throw new InspectionException("通道信息不存在");
        }
        return channelInfo;
    }

    /**
     * 获取RTSP流地址
     *
     * @param channel onvif认证信息
     * @return RTSP流地址
     */
    public Map<Integer, List<RtspStreamModel>> getRtspByChannelInfo(InspChannelInfo channel) {
        // checkChannelIpInfo(channel);
        List<RtspStreamModel> rtspUrls = cameraCtrlProxyService.getRtspUrl(channel);
        if (CollectionUtil.isEmpty(rtspUrls)) {
            throw new InspectionException("获取设备RTSP地址列表为空");
        }

        // 只取主码流
        Map<Integer, List<RtspStreamModel>> collect = rtspUrls.stream()
                .collect(Collectors.groupingBy(RtspStreamModel::getChannelNum));
        Map<Integer, List<RtspStreamModel>> result = new HashMap<>();
        collect.forEach((key, value) -> {
            ListUtil.sort(value, Comparator.comparing(RtspStreamModel::getRtspUrl));
            value = ListUtil.of(value.get(0));
            result.put(key, value);
        });
        return MapUtil.sort(result);
    }

    /**
     * 获取摄像头厂商
     *
     * @param channel onvif认证信息
     * @return 摄像头厂商
     */
    public String getCameraManufacture(InspChannelInfo channel) {
        return cameraCtrlProxyService.getManufacturer(channel);
    }

    private void checkChannelIpInfo(InspChannelInfo channel) {
        String address = channel.getAddress();
        AssertUtil.isTrue(StrUtil.isNotBlank(address), "通道地址不允许为空");
        AssertUtil.isTrue(IpUtil.isIpv4(address), "通道地址IPV4格式校验失败");

        String username = channel.getUsername();
        AssertUtil.isTrue(StrUtil.isNotBlank(username), "用户名不允许为空");

        String password = channel.getPassword();
        AssertUtil.isTrue(StrUtil.isNotBlank(password), "用户密码不允许为空");

        String cameraCtrlProtocol = channel.getCameraCtrlProtocol();
        AssertUtil.isTrue(EnumUtil.contains(CameraCtrlProtocol.class, cameraCtrlProtocol), "通道接入协议错误");

        String videoProtocolType = channel.getVideoProtocolType();
        AssertUtil.isTrue(EnumUtil.contains(VideoProtocolType.class, videoProtocolType), "通道视频协议错误");

        String rtsp = channel.getRtsp();
        if (VideoProtocolType.RTSP.name().equals(videoProtocolType) && StrUtil.isNotBlank(rtsp)) {
            AssertUtil.isTrue(StrUtil.isNotBlank(rtsp), "RTSP地址不允许为空");
            AssertUtil.isTrue(rtsp.startsWith("rtsp://"), "rtsp格式不正确");

            InspChannelInfo oneByRtsp = getOneByRtsp(rtsp);
            AssertUtil.isTrue(Objects.isNull(oneByRtsp) || oneByRtsp.getId().equals(channel.getId()), "rtsp流地址重复");

            // 校验rtsp中的IP和address是都一致
            String hostFromRtspUrl = getHostFromRtspUrl(rtsp);
            AssertUtil.isTrue(address.equals(hostFromRtspUrl), "rtsp地址中IP地址与通道地址不一致");

            // TODO 超时时间为空或小于等于0时，不校验直接通过
            String rtspUrl = InspStringUtils.replaceRTSP(rtsp, username, password);
            if (this.restValidationTimeout != null && this.restValidationTimeout != 0) {
                boolean rtspUsable = RtspUtil.isRtspUsable(rtspUrl, this.restValidationTimeout);
                AssertUtil.isTrue(rtspUsable, "RTSP地址连接校验失败");
            }
            // RTSP协议时，设置sipName为null，无需入库
            // channel.setSipName(null);
        }

        // if (VideoProtocolType.GB28181.name().equals(videoProtocolType)) {
        //     String sipName = channel.getSipName();
        //     AssertUtil.isTrue(StrUtil.isNotBlank(sipName), "GB28181协议下SIP用户名不允许为空");
        //     AssertUtil.isTrue(sipName.length() >= 10, "SIP用户名最小长度为10");
        //     // SIP用户名必须以摄像机的IP后两个网段结尾，比如IP=***************  SIP用户名结尾必须是253103
        //     boolean isSipNameEndWithIp = isSipNameEndWithIp(sipName, address);
        //     AssertUtil.isTrue(isSipNameEndWithIp, "SIP用户名必须以摄像机的IP后两个网段结尾");
        //     // SIP用户名重复性校验，全局唯一
        //     InspChannelInfo one = lambdaQuery()
        //             .eq(InspChannelInfo::getSipName, sipName)
        //             .eq(InspChannelInfo::getVideoProtocolType, VideoProtocolType.GB28181.name())
        //             .notIn(InspChannelInfo::getId, channel.getId())
        //             .one();
        //     AssertUtil.isTrue(Objects.isNull(one), "SIP用户名已存在");
        //
        //     String videoChannelId = channel.getVideoChannelId();
        //     AssertUtil.isTrue(StrUtil.isNotBlank(videoChannelId), "GB28181协议下SIP通道ID不允许为空");
        //     AssertUtil.isTrue(videoChannelId.length() >= 10, "SIP通道ID最小长度为10");
        //     isSipNameEndWithIp = isSipNameEndWithIp(videoChannelId, address);
        //     AssertUtil.isTrue(isSipNameEndWithIp, "SIP通道ID必须以摄像机的IP后两个网段结尾");
        //     // SIP通道ID重复性校验，全局唯一
        //     one = lambdaQuery()
        //             .eq(InspChannelInfo::getVideoChannelId, videoChannelId)
        //             .eq(InspChannelInfo::getVideoProtocolType, VideoProtocolType.GB28181.name())
        //             .notIn(InspChannelInfo::getId, channel.getId())
        //             .one();
        //     AssertUtil.isTrue(Objects.isNull(one), "SIP通道ID已存在");
        // }
    }

    @Transactional
    public boolean updateChannelInfo(InspChannelInfo inspChannelInfo) {
        // 参数校验
        String videoProtocolType = inspChannelInfo.getVideoProtocolType();
        AssertUtil.isTrue(EnumUtil.contains(VideoProtocolType.class, videoProtocolType), "视频接入协议配置错误");

        String type = inspChannelInfo.getDeviceType();
        AssertUtil.isTrue(EnumUtil.contains(CameraType.class, type), "通道类型配置错误");

        String cameraCtrlProtocol = inspChannelInfo.getCameraCtrlProtocol();
        AssertUtil.isTrue(EnumUtil.contains(CameraCtrlProtocol.class, cameraCtrlProtocol), "通道接入协议配置错误");

        String sourceProtocol = inspChannelInfo.getSourceProtocol();
        AssertUtil.isTrue(EnumUtil.contains(MtxSourceProtocol.class, sourceProtocol), "视频传输协议配置错误");

        String manufacturer = inspChannelInfo.getManufacturer();
        AssertUtil.isTrue(EnumUtil.contains(DeviceManufacturer.class, manufacturer), "通道厂商配置错误");

        // // 如果选择的是VIDEO_RTSP类型，则无需校验IP、用户名、密码等信息
        // if (!CameraCtrlProtocol.VIDEO_RTSP.name().equals(cameraCtrlProtocol)) {
        //     checkChannelIpInfo(inspChannelInfo);
        // }
        checkChannelIpInfo(inspChannelInfo);

        String rtsp = inspChannelInfo.getRtsp();
        String address = inspChannelInfo.getAddress();
        String username = inspChannelInfo.getUsername();
        String password = inspChannelInfo.getPassword();
        String channelInfoId = inspChannelInfo.getId();
        // String sipName = inspChannelInfo.getSipName();
        // String videoChannelId = inspChannelInfo.getVideoChannelId();

        InspChannelInfo channelInfo = getOneById(channelInfoId);
        boolean update = lambdaUpdate().eq(InspChannelInfo::getId, channelInfoId)
                .set(InspChannelInfo::getCameraCtrlProtocol, cameraCtrlProtocol)
                .set(InspChannelInfo::getAddress, address)
                .set(InspChannelInfo::getRtsp, rtsp)
                .set(InspChannelInfo::getChannelNum, inspChannelInfo.getChannelNum())
                .set(InspChannelInfo::getVideoProtocolType, videoProtocolType)
                .set(InspChannelInfo::getSourceProtocol, sourceProtocol)
                // .set(InspChannelInfo::getSipName, sipName)
                // .set(InspChannelInfo::getVideoChannelId, videoChannelId)
                .set(InspChannelInfo::getUsername, username)
                .set(InspChannelInfo::getPassword, password)
                .set(InspChannelInfo::getDeviceType, type)
                .set(InspChannelInfo::getManufacturer, manufacturer)
                .update();

        if (update && VideoProtocolType.RTSP.name().equals(videoProtocolType)) {
            // 当address|username、password、RTSP、sourceProtocol发生变化，需要踢出已有的视频流
            boolean isChange = !StrUtil.equals(address, channelInfo.getAddress())
                    || !StrUtil.equals(username, channelInfo.getUsername())
                    || !StrUtil.equals(password, channelInfo.getPassword())
                    || !StrUtil.equals(rtsp, channelInfo.getRtsp())
                    || !StrUtil.equals(sourceProtocol, channelInfo.getSourceProtocol());
            if (isChange) {
                mediamtxService.deleteStream(channelInfo);
                log.debug("通道信息发生变化，踢出视频流成功");
            }
        }
        return update;
    }

    private String getHostFromRtspUrl(String rtspUrl) {
        String address = rtspUrl.toLowerCase();
        if (address.startsWith("rtsp://")) {
            String replace = address.replace("rtsp://", "http://");
            URL url = URLUtil.url(replace);
            return url.getHost();
        } else {
            return rtspUrl;
        }
    }

    public ScaleModel getScale(String channelId) {
        InspChannelInfo channel = getOneById(channelId);
        return cameraCtrlProxyService.getDisplayResolution(channel);
    }

    public Object listPage(Page<InspChannelInfo> page) {
        IPage<InspChannelInfo> pageList = this.page(page);

        Page<InspProjectTreeNode> treeNodePage = new Page<>(page.getCurrent(), page.getSize());
        LambdaQueryWrapper<InspProjectTreeNode> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspProjectTreeNode::getType, ProjectNodeType.CHANNEL);
        wrapper.orderByAsc(InspProjectTreeNode::getSortNo);
        IPage<InspProjectTreeNode> channelNodePage = projectTreeNodeService.page(treeNodePage, wrapper);
        List<InspProjectTreeNode> channelNodeList = channelNodePage.getRecords();

        if (ObjectUtil.isNotEmpty(channelNodeList)) {
            List<ChannelListRespModel> modelList = channelNodeList.parallelStream().map(x -> {
                String channelId = x.getId();
                InspChannelInfo channelNodeInfo = getOneById(channelId);
                ChannelListRespModel channelListRespModel = new ChannelListRespModel();
                BeanUtils.copyProperties(channelNodeInfo, channelListRespModel);
                // 设置通道预览地址
                try {
                    String previewUrl = getPreviewInfo(channelId).getPreviewUrl();
                    channelListRespModel.setUrl(previewUrl);
                } catch (Exception ex) {
                    log.error("查询视频分屏展示列表时，获取通道视频预览地址失败，channelId = {}", channelId);
                }
                // 设置通道名称
                channelListRespModel.setChannelName(x.getLabel());
                return channelListRespModel;
            }).collect(Collectors.toList());

            Page<ChannelListRespModel> objectPage = new Page<>();
            BeanUtils.copyProperties(pageList, objectPage);
            objectPage.setRecords(modelList);
            return objectPage;
        }
        return pageList;
    }

    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        // 判断CHANNEL_PIC类型参数是否有引用当前删除节点
        List<InspAlgorithmParamInstance> channelPicParams = algorithmParamInstanceService.listChannelPicParam();
        if (CollectionUtil.isNotEmpty(channelPicParams)) {
            channelPicParams = channelPicParams.stream()
                    .filter(item -> Objects.nonNull(item.getValue()))
                    .collect(Collectors.toList());
            for (InspAlgorithmParamInstance channelPicParam : channelPicParams) {
                Object value = channelPicParam.getValue();
                InspProjectTreeNode treeNode = JSONUtil.toBean(JSONUtil.parseObj(value), InspProjectTreeNode.class);
                if (idList.contains(treeNode.getId())) {
                    // 被引用
                    String algorithmInstanceId = channelPicParam.getAlgorithmInstanceId();
                    InspAlgorithmInstance algorithmInstance = algorithmInstanceService.getOneById(algorithmInstanceId);
                    InspProjectTreeNode presetNode = projectTreeNodeService.getOneById(algorithmInstance.getPresetId());
                    String format = StrUtil.format("当前通道被预置点[{}]下的算法[{}]引用无法删除", presetNode.getLabel(),
                            algorithmInstance.getName());
                    throw new InspectionException(format);
                }
            }
        }

        super.removeByIds(idList);

        // 删除调度任务节点数据
        List<InspPresetInfo> presetInfos = presetInfoService.listByChannelIds(idList);
        if (CollectionUtil.isNotEmpty(presetInfos)) {
            List<String> collect = presetInfos.stream().map(InspPresetInfo::getId).collect(Collectors.toList());
            scheduleTaskNodeService.removeByModelIds(collect);
        }
        return true;
    }

    public ChannelPreviewInfo getPreviewInfo(String channelId) {
        InspChannelInfo byId = getOneById(channelId);
        String previewUrl;
        // 判断通道的视频接入协议类型
        String videoProtocolType = byId.getVideoProtocolType();
        if (VideoProtocolType.RTSP.name().equals(videoProtocolType)) {
            // 从mediamtx服务获取视频流
            previewUrl = mediamtxService.pushStream(byId);
        } else {
            // // 通过GB28181协议获取HTTP-FLV协议流
            // previewUrl = srsService.getGb28181Stream(byId);
            throw new InspectionException("视频接入协议不在支持范围");
        }

        ChannelPreviewInfo channelPreviewInfo = new ChannelPreviewInfo();
        channelPreviewInfo.setPreviewUrl(previewUrl);
        channelPreviewInfo.setDeviceType(byId.getDeviceType());
        channelPreviewInfo.setVideoProtocolType(videoProtocolType);
        channelPreviewInfo.setChannelId(channelId);
        channelPreviewInfo.setChannelNum(byId.getChannelNum());

        InspProjectTreeNode treeNode = projectTreeNodeService.getOneById(channelId);
        channelPreviewInfo.setChannelName(treeNode.getLabel());

        channelPreviewInfo.setChannelMode(getChannelMode(channelId).name());
        return channelPreviewInfo;
    }

    public Object exportToExcelTemplate() {
        String tempFileName = "通道导入模板.xlsx";
        InputStream streamSafe = ResourceUtil.getStreamSafe(tempFileName);
        if (Objects.isNull(streamSafe)) {
            throw new InspectionException("模板文件不存在");
        }
        File tempfile = LocalFileServerUtil.saveToTemp(streamSafe, tempFileName);
        UrlResource resource = new UrlResource(URLUtil.getURL(tempfile));
        // 设置响应头
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + URLUtil.encode(tempFileName) + "\"");
        headers.add(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "Content-Disposition");
        headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);

        // 返回文件
        return ResponseEntity.ok()
                .headers(headers)
                .body(resource);
    }

    public ResponseEntity<UrlResource> exportToExcel() {
        String tempFileName = "通道导入模板.xlsx";
        InputStream streamSafe = ResourceUtil.getStreamSafe(tempFileName);
        if (Objects.isNull(streamSafe)) {
            throw new InspectionException("模板文件不存在");
        }

        // 查询全部通道节点数据
        LambdaQueryWrapper<InspProjectTreeNode> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspProjectTreeNode::getType, ProjectNodeType.CHANNEL);
        wrapper.orderByAsc(InspProjectTreeNode::getSortNo);
        List<InspProjectTreeNode> list = projectTreeNodeService.list(wrapper);

        UrlResource resource;
        if (CollectionUtil.isNotEmpty(list)) {
            // 构造数据
            List<ChannelExcelItem> channelExcelItems = buildChannelExcelItems(list);
            File tempfile = LocalFileServerUtil.saveToTemp(streamSafe, tempFileName);
            // 写入数据
            ChannelExcelUtil.writeExcel(channelExcelItems, tempfile);
            resource = new UrlResource(URLUtil.getURL(tempfile));

        } else {
            URL url = ResourceUtil.getResource(tempFileName);
            resource = new UrlResource(url);
        }
        // 设置新的导出文件名称
        String newFileName = "通道导出-" + DateUtil.now() + ".xlsx";
        // 设置响应头
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + URLUtil.encode(newFileName) + "\"");
        headers.add(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "Content-Disposition");
        headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);

        // 返回文件
        return ResponseEntity.ok()
                .headers(headers)
                .body(resource);
    }

    private List<ChannelExcelItem> buildChannelExcelItems(List<InspProjectTreeNode> list) {
        List<ChannelExcelItem> result = new ArrayList<>();
        for (InspProjectTreeNode treeNode : list) {
            InspChannelInfo channelInfo = getOneById(treeNode.getId());
            if (StrUtil.isBlank(channelInfo.getAddress())) {
                // 新建的通道节点,还未配置
                continue;
            }
            ChannelExcelItem channelExcelItem = new ChannelExcelItem();
            BeanUtils.copyProperties(channelInfo, channelExcelItem);

            // 转化CameraCtrlProtocol为label
            String cameraCtrlProtocol = channelExcelItem.getCameraCtrlProtocol();
            CameraCtrlProtocol cameraCtrlProtocolEnum = CameraCtrlProtocol.valueOf(cameraCtrlProtocol);
            channelExcelItem.setCameraCtrlProtocol(cameraCtrlProtocolEnum.getLabel());

            // 转化CameraType为label
            String deviceType = channelExcelItem.getDeviceType();
            CameraType cameraType = CameraType.valueOf(deviceType);
            channelExcelItem.setDeviceType(cameraType.getLabel());

            channelExcelItem.setChannelName(treeNode.getLabel());
            channelExcelItem.setChannelDesc(treeNode.getDes());
            // 设置多级区域名称
            List<String> areaNames = new ArrayList<>();
            setAreaNames(treeNode, areaNames);
            // 逆置
            ListUtil.reverse(areaNames);
            channelExcelItem.setAreaNames(areaNames);

            result.add(channelExcelItem);
        }
        return result;
    }

    /**
     * 递归设置父级名称
     */
    private void setAreaNames(InspProjectTreeNode treeNode, List<String> areaNames) {
        String parentId = treeNode.getParentId();
        InspProjectTreeNode byId = projectTreeNodeService.getOneById(parentId);
        if (ProjectNodeType.BUSINESS_ROOT.equals(byId.getType())) {
            return;
        }
        areaNames.add(byId.getLabel());
        setAreaNames(byId, areaNames);
    }


    @TempFileCleanable
    public Object importFromExcel(ImportReqModel reqModel) {
        // 文件大小 格式校验
        MultipartFile excelFile = reqModel.getExcelFile();
        if (Objects.isNull(excelFile) || excelFile.getSize() <= 0) {
            throw new InspectionException("上传文件不允许为空");
        }
        DataSize dataSize = DataSize.of(200, DataUnit.KILOBYTES);
        long maxSize = dataSize.toBytes();
        long size = excelFile.getSize();
        if (size > maxSize) {
            throw new InspectionException("上传文件大小最大允许200KB");
        }
        String suffix = FileUtil.getSuffix(excelFile.getOriginalFilename());
        if (!"xlsx".equals(suffix)) {
            throw new InspectionException("只允许上传xlsx文件");
        }

        File tempFile = LocalFileServerUtil.saveToTemp(excelFile);
        TempFileCache.cache(tempFile.getParentFile());

        List<ChannelExcelItem> lists = ChannelExcelUtil.readExcel(tempFile.getAbsolutePath());
        if (CollectionUtil.isEmpty(lists)) {
            throw new InspectionException("从Excel文件中读取通道信息数据为空");
        }
        // 数据校验（只校验非空）
        checkExcelItemList(lists);

        ThreadUtil.execute(() -> {
            float progress = 0.0f;
            for (int i = 0; i < lists.size(); i++) {
                ChannelExcelItem excelItem = lists.get(i);
                try {
                    String msgStr = StrUtil.format("开始导入通道[{}]", excelItem.getChannelName());
                    ImportExcelMsg msg = ImportExcelMsg.create(MsgLevel.INFO, msgStr, progress);
                    importFromExcelServer.sendLogToWeb(msg);

                    channelExcelImportService.saveChannelExcelItem(excelItem, reqModel.getIsOverwrite());
                } catch (ChannelExistException exception) {
                    String msgStr = StrUtil.format("跳过[{}]通道导入，通道已存在", excelItem.getChannelName());
                    ImportExcelMsg msg = ImportExcelMsg.create(MsgLevel.WARN, msgStr, null);
                    importFromExcelServer.sendLogToWeb(msg);
                } catch (Exception exception) {
                    String msgStr = StrUtil.format("导入通道[{}]失败，失败原因为：{}", excelItem.getChannelName(), exception.getMessage());
                    log.error(msgStr, exception);
                    ImportExcelMsg msg = ImportExcelMsg.create(MsgLevel.ERROR, msgStr, null);
                    importFromExcelServer.sendLogToWeb(msg);
                } finally {
                    // 进度发送
                    progress = 1.0f * i / lists.size();
                    String msgStr = StrUtil.format("通道[{}]导入结束", excelItem.getChannelName());
                    importFromExcelServer.sendLogToWeb(ImportExcelMsg.create(MsgLevel.INFO, msgStr, progress));
                }
            }
            importFromExcelServer.sendLogToWeb(ImportExcelMsg.create(MsgLevel.INFO, null, 1f));
        });
        return null;
    }

    private void checkExcelItemList(List<ChannelExcelItem> lists) {
        for (ChannelExcelItem excelItem : lists) {
            String channelName = excelItem.getChannelName();
            AssertUtil.isTrue(StrUtil.isNotBlank(channelName), "通道名称不允许为空");
            String address = excelItem.getAddress();
            AssertUtil.isTrue(StrUtil.isNotBlank(address), "通道连接IP地址不允许为空");
            String username = excelItem.getUsername();
            AssertUtil.isTrue(StrUtil.isNotBlank(username), "通道用户名不允许为空");
            String password = excelItem.getPassword();
            AssertUtil.isTrue(StrUtil.isNotBlank(password), "通道用户密码不允许为空");
            // String videoProtocolType = excelItem.getVideoProtocolType();
            // AssertUtil.isTrue(StrUtil.isNotBlank(videoProtocolType), "视频接入协议不允许为空");
            String cameraCtrlProtocol = excelItem.getCameraCtrlProtocol();
            AssertUtil.isTrue(StrUtil.isNotBlank(cameraCtrlProtocol), "通道接入协议不允许为空");
            String deviceType = excelItem.getDeviceType();
            AssertUtil.isTrue(StrUtil.isNotBlank(deviceType), "通道类型不允许为空");
            // String rtsp = excelItem.getRtsp();
            // AssertUtil.isTrue(StrUtil.isNotBlank(rtsp), "通道RTSP地址不允许为空");
            String manufacturer = excelItem.getManufacturer();
            AssertUtil.isTrue(StrUtil.isNotBlank(manufacturer), "通道厂商不允许为空");
        }
    }

    public InspChannelInfo getOneByRtsp(String rtsp) {
        LambdaQueryWrapper<InspChannelInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InspChannelInfo::getRtsp, rtsp);
        List<InspChannelInfo> list = list(wrapper);
        if (CollectionUtil.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    @Transactional
    public Object switchMode(SwitchChannelModeReq reqModel) {
        String channelId = reqModel.getChannelId();
        String channelModeStr = reqModel.getMode();
        if (StrUtil.hasBlank(channelId, channelModeStr)) {
            throw new InspectionException("参数错误");
        }

        if (!EnumUtil.contains(ScheduleTaskMode.class, channelModeStr)) {
            throw new InspectionException("通道模式参数错误");
        }
        // 切换到手动
        String taskModeKey = getChannelModeKey(channelId);
        if (ScheduleTaskMode.MANUAL.name().equals(channelModeStr)) {
            redisHelper.setEx(taskModeKey, DateUtil.now(), TASK_RECOVER_IN_SECONDS, TimeUnit.SECONDS);
        }
        // 切换自动
        else {
            redisHelper.delete(taskModeKey);
        }
        return true;
    }

    /**
     * 获取摄像机手自动状态
     */
    public ScheduleTaskMode getChannelMode(String channelId) {
        String channelModeKey = getChannelModeKey(channelId);
        String modelFromRedis = redisHelper.get(channelModeKey);
        if (StrUtil.isNotBlank(modelFromRedis)) {
            return ScheduleTaskMode.MANUAL;
        }
        return ScheduleTaskMode.AUTOMATIC;
    }

    private String getChannelModeKey(String channelId) {
        return String.format("%s:%s", CHANNEL_MODE_RECOVER, channelId);
    }

    @Transactional
    public List<CatchCreateChannelRsp> batchCreateChannel(CatchCreateChannelReq paramReqModel) {
        List<CatchCreateChannelRsp> result = new ArrayList<>();
        List<RtspStreamModel> rtspStreamModels = paramReqModel.getRtspStreamModels();
        if (CollectionUtil.isEmpty(rtspStreamModels)) {
            return result;
        }
        String address = paramReqModel.getAddress();
        AssertUtil.isTrue(IpUtil.isIpv4(address), "通道地址IPV4格式校验失败");

        String username = paramReqModel.getUsername();
        AssertUtil.isTrue(StrUtil.isNotBlank(username), "用户名不允许为空");

        String password = paramReqModel.getPassword();
        AssertUtil.isTrue(StrUtil.isNotBlank(password), "用户密码不允许为空");

        String cameraCtrlProtocol = paramReqModel.getCameraCtrlProtocol();
        String namePrefix = paramReqModel.getNamePrefix();
        AssertUtil.isTrue(StrUtil.isNotBlank(namePrefix), "通道名称前缀不允许为空");
        AssertUtil.isTrue(namePrefix.length() <= 15, "通道名称前缀不能超过15个字符");

        // 参数校验，判断RTSP地址是否重复
        checkRtspDuplicate(rtspStreamModels);

        // 校验rtsp中的IP和通道连接IP是否一致 直接取rtsp列表的第一个进行IP比较即可
        String hostFromRtspUrl = getHostFromRtspUrl(rtspStreamModels.get(0).getRtspUrl());
        AssertUtil.isTrue(address.equals(hostFromRtspUrl), "rtsp地址中IP地址与通道地址不一致");

        InspChannelInfo channel = new InspChannelInfo();
        channel.setAddress(address);
        channel.setUsername(username);
        channel.setPassword(password);
        channel.setCameraCtrlProtocol(cameraCtrlProtocol);
        // 厂商信息
        String manufacturer = cameraCtrlProxyService.getManufacturer(channel);
        channel.setManufacturer(manufacturer);

        // 名称重复性校验
        for (RtspStreamModel rtspStreamModel : rtspStreamModels) {
            // 生成通道名称
            int channelNum = rtspStreamModel.getChannelNum();
            String channelLabel = namePrefix + channelNum;
            InspProjectTreeNode oneByName = projectTreeNodeService.getOneByName(channelLabel);
            AssertUtil.isTrue(Objects.isNull(oneByName), "当前提交的{}号通道名称[{}]已存在", channelNum, channelLabel);
        }

        for (RtspStreamModel rtspStreamModel : rtspStreamModels) {
            int channelNum = rtspStreamModel.getChannelNum();
            String channelLabel = namePrefix + channelNum;
            // 创建节点和初始化通道信息
            InspProjectTreeNode projectTreeNode = new InspProjectTreeNode();
            projectTreeNode.setType(ProjectNodeType.CHANNEL);
            projectTreeNode.setLabel(channelLabel);
            projectTreeNode.setParentId(paramReqModel.getParentId());
            InspProjectTreeNode projectNode = projectTreeNodeService.createProjectNode(projectTreeNode);

            // 更新通道信息
            CatchCreateChannelRsp channelItem = new CatchCreateChannelRsp();
            BeanUtil.copyProperties(channel, channelItem);

            channelItem.setId(projectNode.getId());
            channelItem.setChannelNum(channelNum);
            channelItem.setRtsp(rtspStreamModel.getRtspUrl());

            updateById(channelItem);

            channelItem.setType(projectNode.getType());
            channelItem.setLabel(projectNode.getLabel());
            result.add(channelItem);
        }
        return result;
    }

    private void checkRtspDuplicate(List<RtspStreamModel> rtspStreamModels) {
        // 查询已入库的全部RTSP
        List<InspChannelInfo> list = list();
        if (CollectionUtil.isEmpty(list)) {
            return;
        }

        for (InspChannelInfo channelInfo : list) {
            String rtsp = channelInfo.getRtsp();
            if (StrUtil.isEmpty(rtsp)) {
                continue;
            }
            // 判断是否重复
            Optional<RtspStreamModel> first = rtspStreamModels.stream()
                    .filter(item -> StrUtil.isNotBlank(item.getRtspUrl()) && item.getRtspUrl().equals(rtsp))
                    .findFirst();
            if (first.isPresent()) {
                InspProjectTreeNode treeNode = projectTreeNodeService.getOneById(channelInfo.getId());
                RtspStreamModel rtspStreamModel = first.get();
                String format = StrUtil.format("当前提交的{}号通道RTSP地址与已存储通道[{}]重复",
                        rtspStreamModel.getChannelNum(), treeNode.getLabel());
                throw new InspectionException(format);

            }
        }
    }
}

