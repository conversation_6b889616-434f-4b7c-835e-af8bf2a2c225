package com.hollysys.inspection.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.csv.CsvWriter;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.config.redis.RedisHelper;
import com.hollysys.inspection.constants.*;
import com.hollysys.inspection.controller.websocket.WebSocketAlarmServer;
import com.hollysys.inspection.controller.websocket.WebSocketTaskServer;
import com.hollysys.inspection.entity.*;
import com.hollysys.inspection.mapper.InspAlarmRecordMapper;
import com.hollysys.inspection.model.alarm.AlarmListItemModel;
import com.hollysys.inspection.model.alarm.config.AdvanceAlarmRuleInfo;
import com.hollysys.inspection.model.alarm.config.AlarmRuleItemModel;
import com.hollysys.inspection.model.algorithm.execute.AlgorithmExecuteRsp;
import com.hollysys.inspection.model.algorithm.execute.OutputValueObj;
import com.hollysys.inspection.model.context.ExecuteAlgorithmCtx;
import com.hollysys.inspection.model.dbbase.DbBaseReadPointRespModel;
import com.hollysys.inspection.model.socket.AlarmWebsocketMsg;
import com.hollysys.inspection.model.socket.TaskSocketMsg;
import com.hollysys.inspection.service.platform.DbBaseApiService;
import com.hollysys.inspection.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * (InspAlarmRecord)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-24 15:56:48
 */
@Service
@Slf4j
public class InspAlarmRecordService extends ServiceImpl<InspAlarmRecordMapper, InspAlarmRecord> {

    @Value("${alarm-restore.max-times}")
    private int alarmRestoreMaxTimes;

    // @Value("${alarm-restore.timeout}")
    // private int alarmRestoreTimeout;

    @Resource
    private WebSocketAlarmServer webSocketAlarmServer;

    @Resource
    private WebSocketTaskServer webSocketTaskServer;

    @Resource
    private DbBaseApiService dbBaseApiService;

    @Resource
    private InspAlarmConfigService alarmConfigService;

    @Resource
    private ProjectTreeNodeService projectTreeNodeService;

    @Resource
    private InspAlgorithmInstanceService algorithmInstanceService;

    @Resource
    private InspScheduleTaskTreeNodeService scheduleTaskTreeNodeService;

    @Resource
    private InspAlgorithmParamService algorithmParamService;

    @Resource
    private RedisHelper redisHelper;

    /**
     * 判断算法实例的结果是否触发报警
     *
     * @return Map  key:出参key value:命中的报警规则(报警)、null(未报警)
     * 返回结果中不包含key的参数，说明当前出参报警判断异常，不属于报警也不属于未报警
     */
    public Map<String, AlarmRuleItemModel> checkOutputAlarm(String algorithmInstanceId, Map<String, OutputValueObj> output) {
        Map<String, AlarmRuleItemModel> alarmResultMap = new HashMap<>();
        if (CollectionUtil.isEmpty(output)) {
            // 算法执行异常或无返回结果
            return alarmResultMap;
        }
        List<InspAlarmConfig> alarmConfigs = alarmConfigService.listByAlgorithmInstId(algorithmInstanceId);
        if (CollectionUtil.isEmpty(alarmConfigs)) {
            // 未配置报警规则
            return alarmResultMap;
        }
        // 一个算法实例下，对于有多个出参的算法来说就会有多个报警规则配置
        for (InspAlarmConfig alarmConfig : alarmConfigs) {
            String alarmType = alarmConfig.getAlarmType();
            AlarmModeType alarmModeType = EnumUtil.fromString(AlarmModeType.class, alarmType);
            AdvanceAlarmRuleInfo ruleModel = null;
            switch (alarmModeType) {
                case OFF_LIMIT_ALARM:
                    ruleModel = alarmConfig.getRuleLimit();
                    break;
                case DEVIATION_ALARM:
                    ruleModel = alarmConfig.getRuleDeviation();
                    break;
                case DEEP_ALARM:
                    ruleModel = alarmConfig.getRuleDeep();
                    break;
                case STR_ALARM:
                    ruleModel = alarmConfig.getRuleStr();
                    break;
                case BOOL_ALARM:
                    ruleModel = alarmConfig.getRuleBool();
                    break;
            }
            if (Objects.isNull(ruleModel) || CollectionUtil.isEmpty(ruleModel.getRuleInfo())) {
                // 规则未配置完成
                continue;
            }
            String outputParamId = alarmConfig.getOutputParamId();
            InspAlgorithmParam oneById = algorithmParamService.getById(outputParamId);
            if (Objects.isNull(oneById)) {
                log.warn("报警规则已配置，但是未找到[{}]参数定义信息", outputParamId);
                continue;
            }
            String paramKey = oneById.getKey();
            OutputValueObj valueObj = output.get(paramKey);
            if (Objects.isNull(valueObj)) {
                log.warn("报警规则已配置，但是算法返回的出参里未找到[{}]相应结果值", paramKey);
                continue;
            }
            // 过略全部开启的报警规则
            TreeSet<AlarmRuleItemModel> ruleInfo = ruleModel.getRuleInfo();
            List<AlarmRuleItemModel> collect = ruleInfo.stream().filter(AlarmRuleItemModel::isEnable).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(collect)) {
                continue;
            }
            Object value = valueObj.getValue();
            String pointName = ruleModel.getPointName();
            doCheckAlarm(alarmResultMap, collect, pointName, value, paramKey);
        }
        return alarmResultMap;
    }

    /**
     * alarmResultMap  key:出参key value:命中的报警规则(报警)、null(未报警)
     * 返回结果中不包含key的参数，说明当前出参报警判断异常，不属于报警也不属于未报警
     */
    private void doCheckAlarm(Map<String, AlarmRuleItemModel> alarmResultMap, List<AlarmRuleItemModel> itemModelList,
                              String pointName, Object value, String paramKey) {
        // 读取平台点项值
        String dataValueV = null;
        if (StrUtil.isNotBlank(pointName)) {
            pointName = PointNameUtil.pointName2NodeId(pointName);
            DbBaseReadPointRespModel.ReadDataValue readDataValue = dbBaseApiService.readPoint(pointName);
            if (Objects.nonNull(readDataValue) && Objects.nonNull(readDataValue.getV())) {
                dataValueV = readDataValue.getV().toString();
            }
        }
        // 遍历报警规则，进行算法结果报警判断
        Map<AlarmRuleItemModel, Boolean> alarmResult = new HashMap<>();
        // 如果同时包含低和低低报警，则需要将低低报警放在低报警之前，优先判断
        List<AlarmRuleItemModel> collect = itemModelList.stream()
                .filter(item -> Arrays.asList(AlarmLevelEnum.L, AlarmLevelEnum.LL).contains(item.getType()))
                .collect(Collectors.toList());
        if (collect.size() == 2) {
            // 交换两个报警规则顺序
            ListUtil.swapElement(itemModelList, collect.get(0), collect.get(1));
        }

        for (AlarmRuleItemModel alarmRuleItemModel : itemModelList) {
            Object setVal = alarmRuleItemModel.getVal();
            AlarmLevelEnum type = alarmRuleItemModel.getType();
            // 报警：true  未报警：false  未判断成功：null
            Boolean process = type.process(dataValueV, value.toString(), setVal);
            // 命中报警，结果为true，未命中报警规则，结果为null或者false
            if (BooleanUtil.isTrue(process)) {
                // 命中的报警规则后直接返回，不需要执行后续逻辑
                alarmResultMap.put(paramKey, alarmRuleItemModel);
                break;
            }
            alarmResult.put(alarmRuleItemModel, process);
        }
        if (!alarmResultMap.containsKey(paramKey)) {
            // 未命中报警，则需要判断是异常还是未命中
            if (alarmResult.containsValue(false)) {
                alarmResultMap.put(paramKey, null);
            }
        }
    }

    /**
     * 算法报警记录入库
     */
    public void insertAlarmRecord(ExecuteAlgorithmCtx algorithmCtx, String taskId, AlgorithmExecuteRsp algorithmExecuteRsp) {
        Map<String, AlarmRuleItemModel> alarmResultMap = algorithmExecuteRsp.getAlarmResultMap();
        if (CollectionUtil.isEmpty(alarmResultMap)) {
            return;
        }
        // 查询算法出参Map，为了查询出参ID
        String algorithmId = algorithmCtx.getAlgorithmInstance().getAlgorithmId();
        Map<String, InspAlgorithmParam> algorithmParamMap = algorithmParamService
                .mapOutputByAlgorithmId(algorithmId);
        if (CollectionUtil.isEmpty(algorithmParamMap)) {
            return;
        }
        // 遍历报警结果
        for (Map.Entry<String, AlarmRuleItemModel> entry : alarmResultMap.entrySet()) {
            String paramKey = entry.getKey();
            InspAlgorithmParam param = algorithmParamMap.get(paramKey);
            if (Objects.isNull(param)) {
                log.error("已经判断出报警结果，但是查询出参定义信息为空");
                continue;
            }
            // 查询当前出参，是否已经有未恢复的报警
            String algorithmInstanceId = algorithmCtx.getAlgorithmInstance().getId();
            String paramId = param.getId();
            InspAlarmRecord oneNotRestored = getOneNotRestored(algorithmInstanceId, paramId);
            LocalDateTime now = LocalDateTime.now();
            AlarmRuleItemModel alarmRuleItemModel = entry.getValue();
            if (Objects.isNull(alarmRuleItemModel)) {
                // 未报警
                if (Objects.nonNull(oneNotRestored) && AlarmCheckUtil.checkCanRestored(oneNotRestored, alarmRestoreMaxTimes)) {
                    // 且有未恢复的历史报警，且达到恢复条件
                    oneNotRestored.setRestoreTime(now);
                    updateById(oneNotRestored);
                }
            } else {
                String resultImgUrl = algorithmExecuteRsp.getResultImgUrl();
                String picUrl = MinioUtil.copyObject(resultImgUrl, FileDirEnum.ALARM, true);
                // 判断出报警
                if (Objects.isNull(oneNotRestored)) {
                    // 无未恢复的历史报警，则新增一条报警记录
                    oneNotRestored = new InspAlarmRecord();
                    oneNotRestored.setChannelId(algorithmCtx.getChannelId());
                    oneNotRestored.setPresetId(algorithmCtx.getPresetId());
                    oneNotRestored.setAlgorithmInstanceId(algorithmInstanceId);
                    oneNotRestored.setOutputParamId(paramId);
                    oneNotRestored.setAlarmLevel(alarmRuleItemModel.getType().getDesc());
                    oneNotRestored.setAlarmDesc(alarmRuleItemModel.getDesc());
                    oneNotRestored.setLevelValue(alarmRuleItemModel.getVal());
                    oneNotRestored.setCreateTime(now);
                    oneNotRestored.setUpdateTime(now);
                    // 设置最新报警图片
                    oneNotRestored.addResultImg(picUrl);

                    save(oneNotRestored);
                } else {
                    // 有未恢复的历史报警，则更新此报警记录
                    // 设置最新报警图片
                    oneNotRestored.addResultImg(picUrl);

                    oneNotRestored.setAlarmLevel(alarmRuleItemModel.getType().getDesc());
                    oneNotRestored.setAlarmDesc(alarmRuleItemModel.getDesc());
                    oneNotRestored.setLevelValue(alarmRuleItemModel.getVal());
                    oneNotRestored.setUpdateTime(now);

                    updateById(oneNotRestored);

                    // 出现报警后，需要删除报警恢复次数缓存
                    AlarmCheckUtil.delRestoredCache(oneNotRestored);
                }
                // 发送实时报警
                sendRealtimeAlarm(taskId, oneNotRestored);
            }
        }
    }

    /**
     * 发送实时报警
     */
    private void sendRealtimeAlarm(String taskId, InspAlarmRecord alarmRecord) {
        AlarmWebsocketMsg alarmWebsocketMsg = null;
        try {
            // 向MES发送实时报警
            alarmWebsocketMsg = webSocketAlarmServer.alarmRecordToMsg(alarmRecord);
            webSocketAlarmServer.sendMessage(alarmWebsocketMsg);

            // 向本系统发送实时报警通道ID，用来视频弹出
            String channelId = alarmRecord.getChannelId();
            InspProjectTreeNode channelNode = projectTreeNodeService.getById(channelId);
            // 查询当前任务所在分组（判断任务所属的父二级目录是否为同一个，是则属于一组）
            List<String> groupTaskIds = scheduleTaskTreeNodeService.getGroupTaskIdsByTaskId(taskId);
            webSocketTaskServer.sendMessageToWeb(groupTaskIds, TaskSocketMsg.buildAlarm(channelId, channelNode.getLabel()));
        } catch (Exception exception) {
            log.error("发送实时报警到前端出现异常，msg = {}", JSONUtil.toJsonStr(alarmWebsocketMsg), exception);
        }
    }

    /**
     * 获取MES报警列表（不区分实时报警和历史报警）
     */
    public IPage<AlarmListItemModel> pageMesAlarms(IPage<InspAlarmRecord> page, String nodeId, String startTime, String endTime,
                                                   Boolean isConfirm, Boolean isClose, String searchStr) {
        Boolean isRealtime;
        if (Objects.isNull(isClose)) {
            isRealtime = null;
        } else {
            // MES中的关闭相当于本系统的历史报警
            isRealtime = !isClose;
        }
        return pageAlarms(isRealtime, page, nodeId, startTime, endTime, isConfirm, searchStr, null, null);
    }

    /**
     * 获取实时报警列表（未恢复的报警）
     */
    public IPage<AlarmListItemModel> pageRealtimeAlarms(IPage<InspAlarmRecord> page, String nodeId, String startTime,
                                                        String endTime, String searchStr) {
        return pageAlarms(true, page, nodeId, startTime, endTime, null, searchStr, null, null);
    }

    /**
     * 获取历史报警列表（已恢复的报警）
     */
    public IPage<AlarmListItemModel> pageHistoryAlarms(IPage<InspAlarmRecord> page, String nodeId, String startTime,
                                                       String endTime, String searchStr, Boolean confirm, Boolean falseAlarm) {
        return pageAlarms(false, page, nodeId, startTime, endTime, null, searchStr, confirm, falseAlarm);
    }

    public Map<String, String> searchIdNameMap(Map<String, String> allIdNameMap, String searchStr) {
        Map<String, String> idNameMapSearch;
        if (StrUtil.isNotBlank(searchStr)) {
            String escapeStr = StrEscapeUtil.escapeChar(searchStr);
            Set<Map.Entry<String, String>> entries = allIdNameMap.entrySet();
            idNameMapSearch = new HashMap<>();
            entries.forEach(entry -> {
                String value = entry.getValue();
                if (StrUtil.containsIgnoreCase(value, escapeStr)) {
                    idNameMapSearch.put(entry.getKey(), value);
                }
            });
        } else {
            idNameMapSearch = null;
        }
        return idNameMapSearch;
    }

    /**
     * 查询全部名称数据（通道、预置点、算法实例、出参名称），整理成ID-Name Map结构
     */
    public Map<String, String> getAllIdNameMap(String nodeId) {
        Map<String, String> idNameMap = new HashMap<>();
        // 查询全部名称数据（通道、预置点、算法实例、出参名称），整理成ID-Name Map结构
        List<InspProjectTreeNode> searchProjectTreeNodes;
        if (StrUtil.isNotBlank(nodeId)) {
            searchProjectTreeNodes = projectTreeNodeService.getAllSubNodesByParentId(nodeId);
            if (CollectionUtil.isEmpty(searchProjectTreeNodes)) {
                // 选中的节点下无子节点，则直接返回
                return idNameMap;
            }
            // 如果当前选中节点为预置点时，则需要将父级节点添加
            InspProjectTreeNode treeNode = projectTreeNodeService.getOneById(nodeId);
            if (ProjectNodeType.PRESET.equals(treeNode.getType())) {
                InspProjectTreeNode parentNode = projectTreeNodeService.getOneById(treeNode.getParentId());
                searchProjectTreeNodes.add(parentNode);
            }
        } else {
            searchProjectTreeNodes = projectTreeNodeService.list();
        }
        if (CollectionUtil.isEmpty(searchProjectTreeNodes)) {
            // 如果节点数据为空，则直接返回
            return idNameMap;
        }

        List<String> treeNodeIds = new ArrayList<>();
        for (InspProjectTreeNode treeNode : searchProjectTreeNodes) {
            idNameMap.put(treeNode.getId(), treeNode.getLabel());
            treeNodeIds.add(treeNode.getId());
        }
        if (CollectionUtil.isEmpty(treeNodeIds)) {
            // 如果数据为空，则直接返回
            return idNameMap;
        }
        List<String> algorithmIds = new ArrayList<>();
        List<InspAlgorithmInstance> algorithmInstances = algorithmInstanceService.listWithInstName(treeNodeIds);
        for (InspAlgorithmInstance algorithmInstance : algorithmInstances) {
            idNameMap.put(algorithmInstance.getId(), algorithmInstance.getName());
            algorithmIds.add(algorithmInstance.getAlgorithmId());
        }
        if (CollectionUtil.isEmpty(algorithmIds)) {
            // 如果数据为空，则直接返回
            return idNameMap;
        }
        List<InspAlgorithmParam> algorithmParams = algorithmParamService.listOutputByAlgorithmIds(algorithmIds);
        for (InspAlgorithmParam algorithmParam : algorithmParams) {
            idNameMap.put(algorithmParam.getId(), algorithmParam.getLabel());
        }
        return idNameMap;
    }

    private IPage<AlarmListItemModel> pageAlarms(Boolean isRealtime, IPage<InspAlarmRecord> page, String nodeId, String startTime,
                                                 String endTime, Boolean isConfirm, String searchStr, Boolean confirm, Boolean falseAlarm) {
        Map<String, String> allIdNameMap = getAllIdNameMap(nodeId);
        if (CollectionUtil.isEmpty(allIdNameMap)) {
            return page.convert(inspAlarmRecord -> new AlarmListItemModel());
        }
        // 按searchStr模糊
        Map<String, String> idNameMapSearch = searchIdNameMap(allIdNameMap, searchStr);

        // 数据库查询
        IPage<InspAlarmRecord> alarmRecordPage;
        LambdaQueryChainWrapper<InspAlarmRecord> lambdaWrapper = lambdaQuery();

        // 增加对 confirm 的查询条件
        if (Objects.nonNull(confirm)) {
            lambdaWrapper.eq(InspAlarmRecord::getConfirm, confirm);
        }

        // 增加对 falseAlarm 的查询条件
        if (Objects.nonNull(falseAlarm)) {
            lambdaWrapper.eq(InspAlarmRecord::getFalseAlarm, falseAlarm);
        }

        if (Objects.nonNull(isConfirm)) {
            lambdaWrapper.eq(InspAlarmRecord::getConfirm, isConfirm);
        }
        if (StrUtil.isNotBlank(startTime)) {
            lambdaWrapper.gt(InspAlarmRecord::getCreateTime, DateUtil.parse(startTime, InspConstants.yyyy_MM_ddHHmmss));
        }
        if (StrUtil.isNotBlank(startTime)) {
            lambdaWrapper.gt(InspAlarmRecord::getCreateTime, DateUtil.parse(startTime, InspConstants.yyyy_MM_ddHHmmss));
        }
        if (StrUtil.isNotBlank(endTime)) {
            lambdaWrapper.lt(InspAlarmRecord::getCreateTime, DateUtil.parse(endTime, InspConstants.yyyy_MM_ddHHmmss));
        }
        if (Objects.nonNull(isRealtime)) {
            if (isRealtime) {
                lambdaWrapper.isNull(InspAlarmRecord::getRestoreTime);
            } else {
                lambdaWrapper.isNotNull(InspAlarmRecord::getRestoreTime);
            }
        }

        // 按更新时间降序排列，新的报警排在前面
        lambdaWrapper.orderByDesc(InspAlarmRecord::getUpdateTime);
        if (Objects.isNull(idNameMapSearch)) {
            // idNameMap == null 说明前端未输入搜索文本
            alarmRecordPage = lambdaWrapper
                    .in(InspAlarmRecord::getChannelId, allIdNameMap.keySet())
                    .in(InspAlarmRecord::getPresetId, allIdNameMap.keySet())
                    .in(InspAlarmRecord::getAlgorithmInstanceId, allIdNameMap.keySet())
                    .page(page);
        } else {
            // idNameMap != null 说明前端输入了搜索文本
            if (CollectionUtil.isEmpty(idNameMapSearch)) {
                // 搜索文本未未命中数据
                alarmRecordPage = page;
                alarmRecordPage.setRecords(new ArrayList<>());
            } else {
                alarmRecordPage = lambdaWrapper
                        .and(wrapper -> wrapper
                                .in(InspAlarmRecord::getChannelId, idNameMapSearch.keySet())
                                .or()
                                .in(InspAlarmRecord::getPresetId, idNameMapSearch.keySet())
                                .or()
                                .in(InspAlarmRecord::getAlgorithmInstanceId, idNameMapSearch.keySet())
                        )
                        .page(page);
            }
        }
        // 对象转化
        return alarmRecordPage.convert(inspAlarmRecord -> {
            AlarmListItemModel alarmListItemModel = new AlarmListItemModel();
            BeanUtil.copyProperties(inspAlarmRecord, alarmListItemModel);
            // 设置通道名
            String channelId = inspAlarmRecord.getChannelId();
            String channelName = allIdNameMap.get(channelId);
            alarmListItemModel.setChannelName(channelName);
            // 设置预置点名
            String presetId = inspAlarmRecord.getPresetId();
            String presetName = allIdNameMap.get(presetId);
            alarmListItemModel.setPresetName(presetName);
            // 设置算法实例名
            String algorithmInstanceId = inspAlarmRecord.getAlgorithmInstanceId();
            String algorithmInstanceName = allIdNameMap.get(algorithmInstanceId);
            alarmListItemModel.setAlgorithmInstanceName(algorithmInstanceName);
            // 设置输出参数名
            String outputParamId = inspAlarmRecord.getOutputParamId();
            String outputParamName = allIdNameMap.get(outputParamId);
            alarmListItemModel.setOutputParamName(outputParamName);
            // 图片路径转化为相对路径
            List<String> resultImgs = alarmListItemModel.getResultImgs();
            alarmListItemModel.setResultImgs(MinioUtil.urlsToRelative(resultImgs));
            return alarmListItemModel;
        });
    }

    /**
     * 获取未恢复的报警信息
     */
    private InspAlarmRecord getOneNotRestored(String algorithmInstanceId, String outputParamId) {
        return lambdaQuery().eq(InspAlarmRecord::getAlgorithmInstanceId, algorithmInstanceId)
                .eq(InspAlarmRecord::getOutputParamId, outputParamId)
                .isNull(InspAlarmRecord::getRestoreTime)
                .one();
    }

    public void removeByChannelIds(Collection<? extends Serializable> channelIdList) {
        LambdaQueryWrapper<InspAlarmRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(InspAlarmRecord::getChannelId, channelIdList);
        deleteFile(wrapper);
        remove(wrapper);
    }

    public void removeByPresetIds(Set<String> presetIds) {
        LambdaQueryWrapper<InspAlarmRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(InspAlarmRecord::getPresetId, presetIds);
        deleteFile(queryWrapper);
        remove(queryWrapper);
    }

    public void removeByAlgorithmInstanceId(String algorithmInstanceId) {
        LambdaQueryWrapper<InspAlarmRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(InspAlarmRecord::getAlgorithmInstanceId, algorithmInstanceId);
        deleteFile(queryWrapper);
        remove(queryWrapper);
    }

    private void deleteFile(LambdaQueryWrapper<InspAlarmRecord> queryWrapper) {
        List<InspAlarmRecord> list = list(queryWrapper);
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        List<String> collect = list.stream()
                .map(InspAlarmRecord::getResultImgs)
                .filter(CollectionUtil::isNotEmpty)
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        MinioUtil.removeObjects(collect);
    }

    /**
     * 备份报警记录
     *
     * @param recordLogs 报警记录
     * @return 备份文件
     */
    public File backup(List<InspAlarmRecord> recordLogs) {
        List<String[]> rows = new ArrayList<>();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

        // 添加 操作记录 CSV 文件头
        String[] headers = InspAlarmRecord.generateCsvHeader();
        rows.add(headers);

        // 添加操作记录
        for (InspAlarmRecord recordLog : recordLogs) {
            List<String> row = new ArrayList<>();
            JSONObject logEntries = JSONUtil.parseObj(recordLog);
            for (String key : headers) {
                Object object = logEntries.get(key);
                if (object != null) {
                    if (object instanceof LocalDateTime) {
                        row.add(((LocalDateTime) object).format(dateTimeFormatter));
                    } else if (object instanceof Boolean) {
                        row.add(object.toString());
                    } else {
                        row.add(JSONUtil.toJsonStr(object));
                    }
                } else {
                    row.add("");
                }
            }
            rows.add(row.toArray(new String[0]));
        }

        // 生成备份文件名 时间
        InspAlarmRecord recordFirst = recordLogs.get(0);
        InspAlarmRecord recordLast = recordLogs.get(recordLogs.size() - 1);
        String fileName = FileNameUtil.getBackFileName(recordFirst.getCreateTime(), recordLast.getCreateTime());

        // 操作日志记录文件
        File tempAlarmLogFile = LocalFileServerUtil.getTempFile(fileName);

        // 写入csv文件
        try (CsvWriter writer = new CsvWriter(tempAlarmLogFile, CharsetUtil.CHARSET_UTF_8)) {
            for (String[] row : rows) {
                writer.write(row);
            }
        }

        // 删除报警日志记录数据
        List<String> ids = recordLogs.stream().map(InspAlarmRecord::getId).collect(Collectors.toList());
        removeByIds(ids);

        return tempAlarmLogFile;
    }

    public void recoverAlarm(String algorithmInstanceId, String outputParamId) {
        Optional<InspAlarmRecord> oneOpt = lambdaQuery().eq(InspAlarmRecord::getAlgorithmInstanceId, algorithmInstanceId)
                .eq(InspAlarmRecord::getOutputParamId, outputParamId)
                .isNull(InspAlarmRecord::getRestoreTime)
                .oneOpt();
        if (oneOpt.isPresent()) {
            InspAlarmRecord inspAlarmRecord = oneOpt.get();
            inspAlarmRecord.setRestoreTime(LocalDateTime.now());
            updateById(inspAlarmRecord);
        }
    }

    /**
     * 误报
     * @param id 记录id
     * @return 结果
     */
    public Object falseAlarm(String id) {
        InspAlarmRecord alarmRecord = getById(id);
        if (Objects.isNull(alarmRecord)) {
            throw new InspectionException("报警记录不存在");
        }
        alarmRecord.setFalseAlarm(true);
        alarmRecord.setRestoreTime(LocalDateTime.now());
        updateById(alarmRecord);
        return alarmRecord;
    }
}

