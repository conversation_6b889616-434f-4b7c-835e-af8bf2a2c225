package com.hollysys.inspection.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import com.hollysys.inspection.config.exceptions.InspectionException;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.util.Objects;

public class LocalFileServerUtil {
    // 临时目录，默认./temp
    private static String fileTempDir = "temp";

    public static void init(String tempDir) {
        fileTempDir = tempDir;
    }

    public static File getTempFile(String fileName) {
        File tempDir = getTempDir();
        return new File(tempDir, fileName);
    }

    public static File getTempDir() {
        String uuid = IdUtil.simpleUUID();
        File fileParent = new File(fileTempDir, uuid);
        FileUtil.mkdir(fileParent);
        return fileParent;
    }

    public static File saveToTemp(MultipartFile uploadFile) {
        File targetFile = getTempFile(Objects.requireNonNull(uploadFile.getOriginalFilename()));
        try (InputStream inputStream = uploadFile.getInputStream()) {
            // 使用 Files.copy 将输入流直接复制到目标文件
            Files.copy(inputStream, targetFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            return targetFile;
        } catch (IOException e) {
            throw new InspectionException("文件保存错误"); // 建议包含原始异常
        }
    }

//    public static File saveToTemp(MultipartFile uploadFile) {
//        File targetFile = getTempFile(Objects.requireNonNull(uploadFile.getOriginalFilename()));
//        try {
//            uploadFile.transferTo(targetFile);
//        } catch (IOException e) {
//            throw new InspectionException("文件保存错误");
//        }
//        return targetFile;
//    }

    public static File saveToTemp(InputStream stream, String fileName) {
        File targetFile = getTempFile(fileName);
        return FileUtil.writeFromStream(stream, targetFile);
    }

    public static File downloadToTemp(String fileHttpUrl) {
        String name = FileUtil.getName(fileHttpUrl);
        File targetFile = getTempFile(name);
        MyHttpUtil.downloadFile(fileHttpUrl, targetFile);
        return targetFile;
    }
}
