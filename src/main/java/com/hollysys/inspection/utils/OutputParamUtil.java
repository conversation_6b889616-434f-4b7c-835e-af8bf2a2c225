package com.hollysys.inspection.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import com.hollysys.inspection.config.redis.RedisHelper;
import com.hollysys.inspection.constants.InspConstants;
import com.hollysys.inspection.entity.InspAlgorithmInstance;
import com.hollysys.inspection.model.algorithm.execute.OutputValueObj;
import com.hollysys.inspection.model.variable.VariableRealtimeValue;

import java.util.concurrent.TimeUnit;

/**
 * 出参实时库工具类
 */
public class OutputParamUtil {
    private static final RedisHelper redisHelper = SpringUtil.getBean(RedisHelper.class);

    private static String buildKey(String algorithmInstanceId, String paramKey) {
        return StrUtil.format(InspConstants.ALGORITHM_OUTPUT_VALUE_KEY + "{}:{}",
                algorithmInstanceId, paramKey);
    }

    /**
     * 拼接算法实例带序号名称
     */
    public static String getAlgorithmInstName(InspAlgorithmInstance algorithmInstance, int index) {
        String algorithmName = algorithmInstance.getName();
        return algorithmName + "#" + (index + 1);
    }

    /**
     * 拼接输出参数名称（预置点名、算法实例名，参数名）
     */
    public static String getOutputParamLabel(String presetName, String algorithmInstName, String paramName) {
        return StrUtil.join("-", presetName, algorithmInstName, paramName);
    }

    /**
     * 将算法输出结果写入Redis
     */
    public static void writeToRedis(String algorithmInstanceId, String paramKey, OutputValueObj valueObj) {
        VariableRealtimeValue variableRealtimeValue = new VariableRealtimeValue();
        BeanUtil.copyProperties(valueObj, variableRealtimeValue);
        variableRealtimeValue.setTimestamp(DateUtil.current());
        String key = buildKey(algorithmInstanceId, paramKey);
        // 不去除末尾零
        JSONConfig jsonConfig = JSONConfig.create().setStripTrailingZeros(false);
        String jsonStr = JSONUtil.toJsonStr(variableRealtimeValue, jsonConfig);
        redisHelper.setEx(key, jsonStr, 2, TimeUnit.HOURS);
    }

    /**
     * 从Redis读取算法输出结果
     */
    public static VariableRealtimeValue readFromRedis(String algorithmInstanceId, String paramKey) {
        String key = buildKey(algorithmInstanceId, paramKey);
        String str = redisHelper.get(key);
        if (StrUtil.isBlank(str)) {
            return null;
        }
        return JSONUtil.toBean(str, VariableRealtimeValue.class);
    }
}
