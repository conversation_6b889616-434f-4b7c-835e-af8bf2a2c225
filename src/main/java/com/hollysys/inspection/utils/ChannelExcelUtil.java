package com.hollysys.inspection.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.hollysys.inspection.model.channel.ChannelExcelItem;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

public class ChannelExcelUtil {

    public static List<ChannelExcelItem> readExcel(String filePath) {
        try (ExcelReader reader = ExcelUtil.getReader(filePath)) {
            List<List<Object>> read = reader.read();
            if (CollectionUtil.isEmpty(read)) {
                return null;
            }
            // 去除表格标题
            read.remove(0);

            List<ChannelExcelItem> result = new ArrayList<>(read.size());
            for (List<Object> objects : read) {
                ChannelExcelItem channelExcelItem = new ChannelExcelItem();
                int startCol = 0;
                // 区域名称（多级结构使用点号隔开）
                String rowValue0 = getValueFromIndex(objects, startCol++);
                if (StrUtil.isNotBlank(rowValue0)) {
                    List<String> split = StrUtil.split(rowValue0, ".");
                    channelExcelItem.setAreaNames(split);
                }

                // 通道名称
                channelExcelItem.setChannelName(getValueFromIndex(objects, startCol++));
                // 通道描述
                channelExcelItem.setChannelDesc(getValueFromIndex(objects, startCol++));
                // // 视频接入协议类型
                // channelExcelItem.setVideoProtocolType(getValueFromIndex(objects, startCol++));
                // 摄像机接入协议类型
                channelExcelItem.setCameraCtrlProtocol(getValueFromIndex(objects, startCol++));
                // 通道类型
                channelExcelItem.setDeviceType(getValueFromIndex(objects, startCol++));
                // 通道IP
                channelExcelItem.setAddress(getValueFromIndex(objects, startCol++));
                // 用户名
                channelExcelItem.setUsername(getValueFromIndex(objects, startCol++));
                // 密码
                channelExcelItem.setPassword(getValueFromIndex(objects, startCol++));
                // 通道RTSP地址
                channelExcelItem.setRtsp(getValueFromIndex(objects, startCol++));
                // // 此字段为GB28181协议下SIP用户名
                // channelExcelItem.setSipName(getValueFromIndex(objects, startCol++));
                // // 此字段为GB28181协议下视频通道ID
                // channelExcelItem.setVideoChannelId(getValueFromIndex(objects, startCol++));
                // 视频传输协议类型TCP、UDP（mediamtx流媒体服务需要）
                channelExcelItem.setSourceProtocol(getValueFromIndex(objects, startCol++));
                // 摄像头厂商
                channelExcelItem.setManufacturer(getValueFromIndex(objects, startCol));

                result.add(channelExcelItem);
            }
            return result;
        }
    }

    public static void writeExcel(List<ChannelExcelItem> channelExcelItems, File excelFile) {
        if (CollectionUtil.isEmpty(channelExcelItems)) {
            return;
        }
        try (ExcelWriter writer = ExcelUtil.getWriter(excelFile)) {
            // 跳过首行
            writer.passCurrentRow();
            List<List<String>> rows = new ArrayList<>();
            for (ChannelExcelItem channelExcelItem : channelExcelItems) {
                List<String> areaNames = channelExcelItem.getAreaNames();
                String areaNamesStr = null;
                if (CollectionUtil.isNotEmpty(areaNames)) {
                    areaNamesStr = StrUtil.join(".", areaNames);
                }
                List<String> row = Arrays.asList(nullToEmpty(areaNamesStr),
                        nullToEmpty(channelExcelItem.getChannelName()),
                        nullToEmpty(channelExcelItem.getChannelDesc()),
                        // nullToEmpty(channelExcelItem.getVideoProtocolType()),
                        nullToEmpty(channelExcelItem.getCameraCtrlProtocol()),
                        nullToEmpty(channelExcelItem.getDeviceType()),
                        nullToEmpty(channelExcelItem.getAddress()),
                        nullToEmpty(channelExcelItem.getUsername()),
                        nullToEmpty(channelExcelItem.getPassword()),
                        nullToEmpty(channelExcelItem.getRtsp()),
                        // nullToEmpty(channelExcelItem.getSipName()),
                        // nullToEmpty(channelExcelItem.getVideoChannelId()),
                        nullToEmpty(channelExcelItem.getSourceProtocol()),
                        nullToEmpty(channelExcelItem.getManufacturer())
                );
                rows.add(row);
            }
            writer.write(rows);
        }
    }

    /**
     * 由于writer.write不能写null（会抛出异常），所以将null转化为空字符串
     */
    private static String nullToEmpty(String value) {
        return StrUtil.nullToEmpty(value);
    }

    private static String getValueFromIndex(List<Object> row, int index) {
        Object object = CollectionUtil.get(row, index);
        if (Objects.isNull(object)) {
            return null;
        }
        String string = object.toString();
        if (StrUtil.isBlank(string)) {
            return null;
        }
        return string;
    }
}
