package com.hollysys.inspection.utils;

import cn.hutool.core.util.EnumUtil;
import com.hollysys.inspection.constants.algorithm.param.DataType;

public class RoiUtil {

    /**
     * 校验ROI的值是否与类型相匹配
     */
    public static void checkType(String roiDrawType, String roi) {
        AssertUtil.isTrue(EnumUtil.contains(DataType.class, roiDrawType), "当前算法的ROI类型错误");
        DataType dataTypeEnum = EnumUtil.fromStringQuietly(DataType.class, roiDrawType);
        AssertUtil.isTrue(dataTypeEnum.checkType(roi), "当前算法的ROI与ROI绘制类型不匹配");
    }
}
