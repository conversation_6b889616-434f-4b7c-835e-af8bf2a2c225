package com.hollysys.inspection.utils;

import cn.hutool.core.util.StrUtil;
import com.hollysys.inspection.entity.InspGlobalVariable;

import java.util.ArrayList;
import java.util.List;

public class PointNameUtil {

    /**
     * 转化 10012.QA_AMI_010.AV 为 10012:QA_AMI_010.AV
     */
    public static String pointName2NodeId(String pointName) {
        if (StrUtil.isBlank(pointName)) {
            return null;
        }
        List<String> split = StrUtil.split(pointName, ".");
        if (split.size() != 3) {
            return null;
        }
        return StrUtil.format("{}:{}.{}", split.get(0), split.get(1), split.get(2));
    }

    /**
     * 批量点项名格式转化
     * 转化 10012.QA_AMI_010.AV 为 10012:QA_AMI_010.AV
     */
    public static List<String> batchPointName2NodeId(List<InspGlobalVariable> collect) {
        List<String> nodeIds = new ArrayList<>();
        for (InspGlobalVariable globalVariable : collect) {
            // 实时值
            String pointName = globalVariable.getPointName();
            String nodeId = pointName2NodeId(pointName);
            if (StrUtil.isNotBlank(nodeId)) {
                nodeIds.add(nodeId);
                // 将新的点项名格式设置回对象中，后面需要用到
                globalVariable.setPointName(nodeId);
            }
            // 质量位
            String qualityPointName = globalVariable.getQualityPointName();
            String qualityNodeId = pointName2NodeId(qualityPointName);
            if (StrUtil.isNotBlank(qualityNodeId)) {
                nodeIds.add(qualityNodeId);
                // 将新的点项名格式设置回对象中，后面需要用到
                globalVariable.setQualityPointName(qualityNodeId);
            }
        }
        return nodeIds;
    }

}
