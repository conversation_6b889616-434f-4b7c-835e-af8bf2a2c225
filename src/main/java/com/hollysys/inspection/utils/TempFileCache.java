package com.hollysys.inspection.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 临时文件缓存管理
 *
 * <AUTHOR>
 */
public class TempFileCache {
    private static final ThreadLocal<List<File>> TEMP_FILE_CACHE = new ThreadLocal<>();

    /**
     * 添加文件缓存
     *
     * @param file 缓存文件
     */
    public static void cache(File file) {
        List<File> files = getLocalCache();
        files.add(file);
        TEMP_FILE_CACHE.set(files);
    }

    /**
     * 清除缓存内的临时文件
     */
    public static void clean() {
        List<File> files = getLocalCache();
        if (CollectionUtil.isNotEmpty(files)) {
            for (File file : files) {
                FileUtil.del(file);
            }
        }
        files.clear();
        TEMP_FILE_CACHE.remove();
    }

    private static List<File> getLocalCache() {
        List<File> files = TEMP_FILE_CACHE.get();
        if (Objects.isNull(files)) {
            files = new ArrayList<>();
        }
        return files;
    }
}
