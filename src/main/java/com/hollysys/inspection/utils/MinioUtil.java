package com.hollysys.inspection.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.constants.FileDirEnum;
import io.minio.*;
import io.minio.messages.*;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.InputStream;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class MinioUtil {

    // 全局minio服务客户端句柄
    private static MinioClient minioClient;

    // 默认文件桶名
    private static String bucketName;

    private static String endpoint;

    // 文件服务器访问的context
    private static String filesMapping;

    // 文件访问HTTP地址模板
    private static String fileHttpUrlTemp;

    public static void init(String endpointUrl, String accessKey, String secretKey, String bucket, String filesMappingDir,
                            int tempExpirationDays, int executeExpirationDays, int operationExpirationDays,
                            int alarmExpirationDays) {
        log.debug("start init minio endpoint url:{}, bucket:{}, filesMapping:{}", endpointUrl, bucket, filesMappingDir);
        minioClient = MinioClient.builder()
                .endpoint(endpointUrl)
                .credentials(accessKey, secretKey)
                .build();
        filesMapping = filesMappingDir;
        fileHttpUrlTemp = endpointUrl.concat("/").concat("{}/{}");
        endpoint = endpointUrl;
        bucketName = bucket;

        try {
            boolean found = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
            log.debug("判断桶[{}]是否存在，结果为：{}", bucketName, found);
            if (!found) {
                // 创建桶
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
            }
        } catch (Exception e) {
            log.error("桶初始化出现异常", e);
            throw new InspectionException("桶初始化出现异常");
        }

        // 设置存储桶为公开可读
        setPublicReadPolicy(bucketName);
        // 设置临时文件生命周期
        addBucketLifecycleByDays(FileDirEnum.TEMP.name(), tempExpirationDays);
        // 设置执行记录备份生命周期
        addBucketLifecycleByDays(FileDirEnum.BACKUP_EXECUTE_RECORD.name(), executeExpirationDays);
        // 设置操作记录备份生命周期
        addBucketLifecycleByDays(FileDirEnum.BACKUP_OPERATION_LOG_RECORD.name(), operationExpirationDays);
        // 设置报警记录备份生命周期
        addBucketLifecycleByDays(FileDirEnum.BACKUP_ALARM_RECORD.name(), alarmExpirationDays);
        log.debug("end init minio endpoint url:{}, bucket:{}, filesMapping:{}", endpointUrl, bucket, filesMappingDir);
    }

    /**
     * 获取存储桶的总大小 (单位：字节)
     */
    public static Double getBucketSize() {
        long totalBytes = 0;
        try {
            Iterable<Result<Item>> results = minioClient.listObjects(
                    ListObjectsArgs.builder()
                            .bucket(bucketName)
                            .recursive(true) // 包含所有子目录
                            .build());
            for (Result<Item> result : results) {
                Item item = result.get();
                totalBytes += item.size();
            }
        } catch (Exception e) {
            log.error("获取存储桶大小失败", e);
            throw new InspectionException("获取存储桶大小失败");
        }
        // 字节转换为GB
        double totalGB = (double) totalBytes / (1024 * 1024 * 1024);

        log.debug("桶大小：{} GB", totalGB);
        return totalGB;
    }

    /**
     * 获取指定前缀列表的文件总大小 (单位：字节)
     *
     * @param prefixes 文件前缀列表，可以指定文件或目录
     * @return 指定文件的总大小，单位为 GB
     */
    public static double getSpecificBucketSize(List<String> prefixes) {
        long totalBytes = 0;
        try {
            for (String prefix : prefixes) {
                Iterable<Result<Item>> results = minioClient.listObjects(
                        ListObjectsArgs.builder()
                                .bucket(bucketName)
                                .prefix(prefix + "/") // 加上斜杠，确保匹配的是目录或该前缀的文件
                                .recursive(true)     // 包含所有子目录下的文件
                                .build());
                for (Result<Item> result : results) {
                    Item item = result.get();
                    totalBytes += item.size();
                }
            }
        } catch (Exception e) {
            log.error("获取指定前缀 {} 的文件大小失败", prefixes, e);
            throw new InspectionException("获取指定文件大小失败");
        }
        // 字节转换为GB
        double totalGB = (double) totalBytes / (1024 * 1024 * 1024);

        log.debug("指定前缀 {} 的文件总大小：{} GB", prefixes, String.format("%.2f", totalGB));
        return totalGB;
    }

    /**
     * 根据指定文件夹列表 列出这些文件夹下所有文件
     *
     * @param prefixes 文件夹
     * @return 文件列表
     */
    public static List<Item> listAllFileObjects(List<String> prefixes) {
        List<Item> allBackupFiles = new ArrayList<>();
        for (String prefix : prefixes) {
            try {
                Iterable<Result<Item>> results = listObjects(prefix);
                for (Result<Item> result : results) {
                    allBackupFiles.add(result.get());
                }
            } catch (Exception e) {
                log.error("获取 {} 目录下文件列表失败", prefix, e);
            }
        }

        return allBackupFiles;
    }

    /**
     * 列出指定前缀的所有对象
     *
     * @param prefix 文件前缀
     * @return Iterable<Result < Item>>
     */
    private static Iterable<Result<Item>> listObjects(String prefix) throws Exception {
        return minioClient.listObjects(
                ListObjectsArgs.builder()
                        .bucket(bucketName)
                        .prefix(prefix + "/") // 确保只列出该目录下的文件
                        .recursive(false) // 不递归子目录，假设备份文件直接在目录下
                        .build());
    }

    /**
     * 添加桶内文件超时时间，超时自动删除配置（增量配置）
     */
    public static void addBucketLifecycleByDays(String prefix, int expirationDays) {
        try {
            LifecycleRule rule = new LifecycleRule(
                    Status.ENABLED,                        // 规则状态
                    null,
                    new Expiration((ResponseDate) null, expirationDays, null), // 过期天数
                    new RuleFilter(prefix),               // 对象前缀过滤
                    prefix,                                 // 规则ID
                    null,
                    null,          // 不设置非当前版本过期
                    null);

            // 查询已经配置的规则列表
            LifecycleConfiguration bucketLifecycle = minioClient.getBucketLifecycle(
                    GetBucketLifecycleArgs.builder()
                            .bucket(bucketName)
                            .build());
            List<LifecycleRule> rules = new ArrayList<>();
            if (Objects.nonNull(bucketLifecycle)) {
                rules.addAll(bucketLifecycle.rules());
            }

            // 如果对于ID的规则已经添加，则使用新的规则覆盖旧的规则，不存在则添加
            List<String> ruleIds = rules.stream().map(LifecycleRule::id).collect(Collectors.toList());
            if (ruleIds.contains(prefix)) {
                rules.set(ruleIds.indexOf(prefix), rule);
            } else {
                rules.add(rule);
            }

            LifecycleConfiguration lifecycleConfig = new LifecycleConfiguration(
                    rules);
            minioClient.setBucketLifecycle(
                    SetBucketLifecycleArgs.builder()
                            .bucket(bucketName)
                            .config(lifecycleConfig)
                            .build());
            log.info("成功设置桶{}的生命周期规则：{}前缀的文件将在{}天后自动删除", bucketName, prefix, expirationDays);
        } catch (Exception e) {
            log.error("设置桶的生命周期失败", e);
            throw new InspectionException("设置桶的生命周期失败");
        }
    }

    /**
     * 通过输入流上传文件，返回成功的文件HTTP地址
     */
    public static String uploadFile(String fileName, InputStream inputStream) {
        return uploadFile(null, fileName, inputStream);
    }

    /**
     * 上传文件，返回成功的文件HTTP地址
     */
    public static String uploadFile(FileDirEnum fileDir, File file) {
        return uploadFile(fileDir, file.getName(), FileUtil.getInputStream(file));
    }

    /**
     * 通过输入流上传文件，返回成功的文件HTTP地址
     * fileDir 可以指定文件存储父级目录
     */
    public static String uploadFile(FileDirEnum fileDir, String fileName, InputStream inputStream) {
        if (Objects.isNull(fileDir)) {
            fileDir = FileDirEnum.TEMP;
        }
        try {
            PutObjectArgs putObjectArgs = PutObjectArgs.builder()
                    .bucket(bucketName)
                    .stream(inputStream, inputStream.available(), -1)
                    .object(fileDir.name() + "/" + fileName)
                    .build();
            minioClient.putObject(putObjectArgs);
            return StrUtil.format(fileHttpUrlTemp, putObjectArgs.bucket(), putObjectArgs.object());
        } catch (Exception e) {
            log.error("文件上传失败，bucketName = {}，fileName = {}", bucketName, fileName, e);
            throw new InspectionException("文件上传失败");
        }
    }

    /**
     * 上传文件到指定对象，返回成功的文件HTTP地址
     * @param objectName 指定路径
     * @param fileName 文件名
     * @param inputStream 文件输入流
     * @return 成功的文件HTTP地址
     */
    public static String uploadFileToSpecificObject(String objectName, String fileName, InputStream inputStream) {
        try {
            PutObjectArgs putObjectArgs = PutObjectArgs.builder()
                    .bucket(bucketName)
                    .stream(inputStream, inputStream.available(), -1)
                    .object(objectName + "/" + fileName)
                    .build();
            minioClient.putObject(putObjectArgs);
            return StrUtil.format(fileHttpUrlTemp, putObjectArgs.bucket(), putObjectArgs.object());
        } catch (Exception e) {
            log.error("文件上传失败，bucketName = {}，fileName = {}", bucketName, fileName, e);
            throw new InspectionException("文件上传失败");
        }
    }

    /**
     * 下载文件到本地路径   (注意： 该方法存在当大量调用时 会导致文件下载失败得错误 目前没发现原因 请谨慎使用)
     */
    public static void downloadFile(String fileName, String filePath) {
        try {
            // 先判断文件是否存在
            boolean fileExists = doesObjectExist(fileName);
            if (!fileExists) {
                log.error("文件不存在于MinIO中，跳过下载操作。fileName = {}", fileName);
                throw new InspectionException("文件不存在于MinIO中, 无法下载");
            }
            // 临时解决因为父级目录不存在，导致文件下载失败问题
            FileUtil.mkParentDirs(filePath);

            DownloadObjectArgs objectArgs = DownloadObjectArgs.builder()
                    .bucket(bucketName)
                    .object(fileName)
                    .filename(filePath)
                    .build();
            minioClient.downloadObject(objectArgs);
        } catch (Exception e) {
            log.error("文件下载失败，bucketName = {}，fileName = {}，filePath = {}", bucketName, fileName, filePath, e);
            throw new InspectionException("文件下载失败");
        }
    }

    /**
     * 判断MinIO中是否存在指定对象 用来判断文件是否存在 防止下载文件报错
     *
     * @param objectName 对象名（文件路径，例如 "TEMP/my_file.jpg"）
     * @return 如果对象存在则返回true，否则返回false
     */
    public static boolean doesObjectExist(String objectName) {
        try {
            minioClient.statObject(
                    StatObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .build());
            return true; // 如果statObject成功，说明对象存在
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 根据文件url进行删除
     *
     * @param fileUrl 文件url
     */
    public static void removeObject(String fileUrl) {
        // 删除对象
        String objKey = getObjKey(fileUrl);
        removeObjectByKey(objKey);
    }

    /**
     * 根据文件名字来进行删除
     *
     * @param objKey 文件名
     */
    public static void removeObjectByKey(String objKey) {
        // 删除对象
        try {
            minioClient.removeObject(
                    RemoveObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objKey)
                            .build());
        } catch (Exception e) {
            log.error("删除失败的对象: {}", objKey, e);
            throw new InspectionException("删除文件对象失败");
        }
        log.debug("成功删除对象: {} 从存储桶: {}", objKey, bucketName);
    }

    /**
     * 根据文件名字集合来删除文件
     *
     * @param objKeyList 名字集合
     */
    public static void removeObjectsByKey(List<String> objKeyList) {
        if (CollectionUtil.isEmpty(objKeyList)) {
            return;
        }
        List<DeleteObject> deleteObjects = new ArrayList<>();
        for (String objKey : objKeyList) {
            if (StrUtil.isBlank(objKey)) {
                continue;
            }
            DeleteObject deleteObject = new DeleteObject(objKey);
            deleteObjects.add(deleteObject);
        }

        RemoveObjectsArgs objectsArgs = RemoveObjectsArgs.builder()
                .bucket(bucketName)
                .objects(deleteObjects)
                .build();
        Iterable<Result<DeleteError>> results = minioClient.removeObjects(objectsArgs);
        // 检查是否有删除失败的对象
        for (Result<DeleteError> result : results) {
            try {
                DeleteError error = result.get();
                log.error("删除失败的对象: {}, 错误: {}", error.objectName(), error.message());
            } catch (Exception e) {
                log.error("获取删除结果异常");
            }
        }
    }

    public static void removeObjects(List<String> httpUrlList) {
        if (CollectionUtil.isEmpty(httpUrlList)) {
            return;
        }
        List<String> keys = httpUrlList.stream().map(MinioUtil::getObjKey).collect(Collectors.toList());
        removeObjectsByKey(keys);
    }

    public static String copyObject(String sourceHttpUrl, FileDirEnum destFileDir, boolean addRandomPrefix) {
        if (StrUtil.isBlank(sourceHttpUrl)) {
            return sourceHttpUrl;
        }
        String sourceObjKey = getObjKey(sourceHttpUrl);
        String randomPrefix = "";
        if (addRandomPrefix) {
            randomPrefix = String.valueOf(System.currentTimeMillis());
        }
        String destObject = destFileDir.name() + "/" + randomPrefix + "_" + FileUtil.getName(sourceHttpUrl);

        copyObject(sourceObjKey, destObject);
        return StrUtil.format(fileHttpUrlTemp, bucketName, destObject);
    }

    public static String copyObject(String sourceHttpUrl, FileDirEnum destFileDir) {
        if (StrUtil.isBlank(sourceHttpUrl)) {
            return sourceHttpUrl;
        }
        String sourceObjKey = getObjKey(sourceHttpUrl);
        String destObject = destFileDir.name() + "/" + FileUtil.getName(sourceHttpUrl);

        copyObject(sourceObjKey, destObject);
        return StrUtil.format(fileHttpUrlTemp, bucketName, destObject);
    }

    /**
     * 桶内之间文件复制
     */
    public static void copyObject(String sourceObject, String destObject) {
        if (!doesObjectExist(sourceObject)) {
            log.error("源文件 '{}' 不存在，无法执行复制操作。", sourceObject);
            throw new InspectionException("源对象不存在，无法执行复制操作");
        }
        try {
            // 如果目标对象名为空，则使用源对象名
            String targetObject = destObject != null ? destObject : sourceObject;

            // 复制对象到目标桶
            minioClient.copyObject(
                    CopyObjectArgs.builder()
                            .source(CopySource.builder()
                                    .bucket(bucketName)
                                    .object(sourceObject)
                                    .build())
                            .bucket(bucketName)
                            .object(targetObject)
                            .build());
        } catch (Exception e) {
            log.error("文件复制操作出现异常，sourceObject = {}，destObject = {}", sourceObject, destObject, e);
            throw new InspectionException("文件复制操作出现异常");
        }
    }

    /**
     * 桶内之间文件移动
     */
    public static void moveObject(String sourceObject, String destObject) {
        if (!doesObjectExist(sourceObject)) {
            log.error("源文件 '{}' 不存在，无法执行移动操作。", sourceObject);
            throw new InspectionException("源对象不存在，无法执行移动操作");
        }
        try {
            // 如果目标对象名为空，则使用源对象名
            String targetObject = destObject != null ? destObject : sourceObject;

            // 1. 复制对象到目标桶
            minioClient.copyObject(
                    CopyObjectArgs.builder()
                            .source(CopySource.builder()
                                    .bucket(bucketName)
                                    .object(sourceObject)
                                    .build())
                            .bucket(bucketName)
                            .object(targetObject)
                            .build());

            // 2. 删除源对象
            minioClient.removeObject(
                    RemoveObjectArgs.builder()
                            .bucket(bucketName)
                            .object(sourceObject)
                            .build());
        } catch (Exception e) {
            log.error("文件移动出现异常，sourceObject = {}，destObject = {}", sourceObject, destObject, e);
            throw new InspectionException("文件移动操作出现异常");
        }
    }

    /**
     * 生成上传凭据，供前端直接上传文件到服务器
     */
    public static Map<String, String> generateUploadCredential(String bucketName, String objectName) {
        // 设置凭证过期时间
        ZonedDateTime expirationDate = ZonedDateTime.now().plusMinutes(10);
        // 创建一个凭证
        PostPolicy policy = new PostPolicy(bucketName, expirationDate);
        policy.addEqualsCondition("key", objectName);
        // 限制文件大小，单位是字节byte，也就是说可以设置如：只允许10M以内的文件上传
        //        policy.setContentRange(1, 10 * 1024);
        // 限制上传文件请求的ContentType
        //        policy.setContentType("image/png");

        // 生成凭证并返回
        try {
            return minioClient.getPresignedPostFormData(policy);
        } catch (Exception e) {
            log.error("上传凭据生成失败，bucketName={}，bucketName={}", bucketName, objectName, e);
            throw new InspectionException("上传凭据生成失败");
        }
    }

    /**
     * 设置存储桶为公开可读
     */
    public static void setPublicReadPolicy(String bucketName) {
        String policyJson = "{\n" +
                "\"Version\": \"2012-10-17\",\n" +
                "\"Statement\": [\n" +
                "\t{\n" +
                "\t\t\"Effect\": \"Allow\",\n" +
                "\t\t\"Principal\": \"*\",\n" +
                "\t\t\"Action\": [\n" +
                "\t\t\t\"s3:GetObject\"\n" +
                "\t\t],\n" +
                "\t\t\"Resource\": [\n" +
                "\t\t\t\"arn:aws:s3:::{}/*\"\n" +
                "\t\t]\n" +
                "\t}\n" +
                "]\n" +
                "}";
        policyJson = StrUtil.format(policyJson, bucketName);
        try {
            minioClient.setBucketPolicy(SetBucketPolicyArgs.builder()
                    .bucket(bucketName)
                    .config(policyJson)
                    .build());
        } catch (Exception e) {
            log.error("设置存储桶为公开可读失败，bucketName={}，policyJson={}", bucketName, policyJson, e);
            throw new InspectionException("设置存储桶为公开可读失败");
        }
    }

    /**
     * 从HTTP连接中解析文件对象的key
     */
    public static String getObjKey(String httpUrl) {
        // 例如：http://***************:18010/inspection/SCENE/corp_202504241955524249.JPEG
        // 使用 /inspection/ 分割后为 ["http://***************:18010","SCENE/corp_202504241955524249.JPEG"]
        String[] split = httpUrl.split("/" + bucketName + "/");
        return split[1];
    }

    /**
     * 相对路径转化为绝对路径
     * /file-server/inspection/TEMP/202505201133221295.JPEG 转化为HTTP路径内部使用
     */
    public static String urlToAbsolute(String relativeUrl) {
        if (StrUtil.isBlank(relativeUrl)) {
            return relativeUrl;
        }
        if (relativeUrl.startsWith("http")) {
            return relativeUrl;
        }
        relativeUrl = relativeUrl.replaceFirst("/" + filesMapping, "");
        return endpoint.concat(relativeUrl);
    }

    public static List<String> urlsToAbsolute(List<String> relativeUrls) {
        if (CollectionUtil.isNotEmpty(relativeUrls)) {
            return relativeUrls.stream().map(MinioUtil::urlToAbsolute).collect(Collectors.toList());
        }
        return relativeUrls;
    }

    /**
     * 绝对路径转化为相对路径
     */
    public static String urlToRelative(String absoluteUrl) {
        if (StrUtil.isBlank(absoluteUrl)) {
            return absoluteUrl;
        }
        return "/" + filesMapping + URLUtil.getPath(absoluteUrl);
    }

    public static List<String> urlsToRelative(List<String> absoluteUrls) {
        if (CollectionUtil.isNotEmpty(absoluteUrls)) {
            return absoluteUrls.stream().map(MinioUtil::urlToRelative).collect(Collectors.toList());
        }
        return absoluteUrls;
    }
}
