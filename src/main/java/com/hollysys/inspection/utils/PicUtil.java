package com.hollysys.inspection.utils;

import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import com.hollysys.inspection.model.algorithm.param.data.Point;
import com.hollysys.inspection.model.algorithm.param.data.Square;
import com.hollysys.inspection.model.channel.ScaleModel;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;

public class PicUtil {

    /**
     * 获取HTTP图片的分辨率
     */
    public static ScaleModel getScaleByImgUrl(String imgUrl) {
        File imagesLocalFile = LocalFileServerUtil.downloadToTemp(imgUrl);
        return getScaleByLocalImg(imagesLocalFile.getAbsolutePath());
    }

    /**
     * 获取本地图片的分辨率
     */
    public static ScaleModel getScaleByLocalImg(String imgPath) {
        BufferedImage read = ImgUtil.read(imgPath);
        ScaleModel scaleModel = new ScaleModel();
        scaleModel.setHeight(read.getHeight());
        scaleModel.setWidth(read.getWidth());
        return scaleModel;
    }

    /**
     * 图片裁剪
     *
     * @param img    待裁剪图片本地文件
     * @param square 裁剪范围
     */
    public static File cropPic(File img, Square square) {
        String name = FileUtil.getName(img);
        File tempFile = LocalFileServerUtil.getTempFile(name);

        // 获取左上角点，获取起点
        Point point0 = square.get(0);
        // 获取右下角点，获取宽高
        Point point2 = square.get(2);
        ImgUtil.cut(
                img,
                tempFile,
                // 裁剪的矩形区域
                new Rectangle(point0.getX(), point0.getY(),
                        point2.getX() - point0.getX(), point2.getY() - point0.getY())
        );
        return tempFile;
    }

    /**
     * 判断图片文件是否刻可读（合法）
     */
    public static boolean isReadable(String imgPath) {
        try {
            ImgUtil.read(imgPath);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
