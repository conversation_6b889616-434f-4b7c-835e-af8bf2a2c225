package com.hollysys.inspection.utils;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.hollysys.inspection.config.exceptions.InspectionException;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.URI;
import java.nio.channels.SocketChannel;
import java.util.List;

/**
 * RTSP相关工具方法
 **/
@Slf4j
public class RtspUtil {

    /**
     * 从RTSP地址中解析通道号
     */
    public static int getChannelNum(String rtspUrl) {
        int channelNum = 0;
        try {
            // 宇视
            // rtsp://************/media/video1
            if (rtspUrl.contains("/media/video")) {
                List<String> split = StrUtil.split(rtspUrl, "/media/video");
                String split1 = split.get(1);
                channelNum = NumberUtil.parseInt(split1);
                return channelNum;
            }
            // 海康
            // rtsp://************:554/Streaming/Channels/101?transportmode=unicast&profile=Profile_1
            if (rtspUrl.contains("/Streaming/Channels")) {
                // /Streaming/Channels/101
                String path = URLUtil.getPath(rtspUrl);
                List<String> split = StrUtil.split(path, "/");
                channelNum = NumberUtil.parseInt(split.get(split.size() - 1));
                channelNum = channelNum / 100;
                return channelNum;
            }
            // 大华
            // rtsp://192.168.1.75:554/cam/realmonitor?channel=1&subtype=0&unicast=true
            if (rtspUrl.contains("/cam/realmonitor")) {
                URI uri = URLUtil.toURI(rtspUrl);
                // channel=1&subtype=0&unicast=true
                String query = uri.getQuery();
                List<String> split = StrUtil.split(query, "&");
                String split0 = split.get(0);
                split = StrUtil.split(split0, "=");
                channelNum = NumberUtil.parseInt(split.get(split.size() - 1)) - 1;
                return channelNum;
            }
        } catch (Exception exception) {
            log.error("从RTSP[{}]地址中获取通道号出现异常", rtspUrl, exception);
        }
        return channelNum;
    }

    public static boolean checkRtspUrl(String rtspUrl) {
        try {
            RtspUrlInfo rtspUrlInfo = searchRtspUrlInfo(rtspUrl);
            return isReachable(rtspUrlInfo.getIpAddress(), rtspUrlInfo.getPort());
        } catch (Exception e) {
            log.error("校验rtspUrl中的IP和端口是否可用时发生异常", e);
            return false;
        }
    }

    public static boolean isRtspUsable(String rtspUrl, int timeoutSeconds) {
        boolean urlAndPortIsRight = checkRtspUrl(rtspUrl);
        return BooleanUtil.isTrue(urlAndPortIsRight);
    }

    private static RtspUrlInfo searchRtspUrlInfo(String rtspUrl) {
        // 1. 去掉 "rtsp://"
        if (!rtspUrl.startsWith("rtsp://")) {
            throw new IllegalArgumentException("Invalid RTSP URL, must start with rtsp://");
        }
        String username = null;
        String password = null;
        String ipAddressAndPort;
        String path = null;
        String ipAddress;
        int port = 554; // 默认端口
        try {
            rtspUrl = rtspUrl.substring(7);
            // 2. 查找最后一个 @，分隔出用户名和密码
            int atIndex = rtspUrl.lastIndexOf('@');
            if (atIndex != -1) {
                // 用户名和密码部分
                String userInfo = rtspUrl.substring(0, atIndex);
                int colonIndex = userInfo.indexOf(':');
                if (colonIndex != -1) {
                    username = userInfo.substring(0, colonIndex);
                    password = userInfo.substring(colonIndex + 1);
                } else {
                    username = userInfo; // 只有用户名，没有密码
                }
                rtspUrl = rtspUrl.substring(atIndex + 1); // 剩下的部分就是IP地址和路径
            }
            // 3. 解析IP地址和端口
            int slashIndex = rtspUrl.indexOf('/');
            if (slashIndex != -1) {
                ipAddressAndPort = rtspUrl.substring(0, slashIndex);
                path = rtspUrl.substring(slashIndex); // 路径部分
            } else {
                ipAddressAndPort = rtspUrl; // 没有路径部分，ip地址和端口就是全部
            }
            // 解析IP地址和端口
            int colonIndex = ipAddressAndPort.indexOf(':');
            if (colonIndex != -1) {
                ipAddress = ipAddressAndPort.substring(0, colonIndex);
                try {
                    port = Integer.parseInt(ipAddressAndPort.substring(colonIndex + 1));
                } catch (NumberFormatException e) {
                    throw new IllegalArgumentException("无法识别rtspUrl[" + rtspUrl + "]中的端口");
                }
            } else {
                ipAddress = ipAddressAndPort; // 没有端口，默认554
            }
        } catch (IllegalArgumentException e) {
            log.warn(e.getMessage(), e);
            throw new InspectionException("rtspUrl[" + rtspUrl + "]地址格式错误");
        }
        // 构建并返回RtspUrlInfo对象
        RtspUrlInfo rtspUrlInfo = new RtspUrlInfo();
        rtspUrlInfo.setUsername(username);
        rtspUrlInfo.setPassword(password);
        rtspUrlInfo.setIpAddress(ipAddress);
        rtspUrlInfo.setPort(port);
        rtspUrlInfo.setPath(path);
        return rtspUrlInfo;
    }

    private static boolean isReachable(String ipAddress, int port) {
        SocketChannel socketChannel = null;
        try {
            InetSocketAddress socketAddress = new InetSocketAddress(ipAddress, port);
            socketChannel = SocketChannel.open();
            socketChannel.configureBlocking(false); // Set non-blocking mode
            if (socketChannel.connect(socketAddress)) {
                return true; // Connection successful
            }
            long startTime = System.currentTimeMillis();
            while (!socketChannel.finishConnect()) {
                if (System.currentTimeMillis() - startTime >= 500) {
                    socketChannel.close();
                    return false; // Connection timed out
                }
            }
            return true; // Connection successful
        } catch (IOException e) {
            return false; // Exception occurred
        } finally {
            if (socketChannel != null && socketChannel.isOpen()) {
                try {
                    socketChannel.close();
                } catch (IOException e) {
                    // Handle exception or log it
                    log.error("socketChannel对象关闭异常", e);
                }
            }
        }
    }

    @Getter
    @Setter
    private static class RtspUrlInfo {
        private String username;
        private String password;
        private String ipAddress;
        private int port;
        private String path;
    }
}
