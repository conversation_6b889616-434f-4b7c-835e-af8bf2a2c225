package com.hollysys.inspection.utils;

import cn.hutool.core.lang.PatternPool;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.net.InetAddress;
import java.util.regex.Matcher;

@Slf4j
public class IpUtil {
    public static boolean isReachableIp(String ipAddress) {
        // 超时时间(毫秒)
        int timeout = 3000;

        try {
            InetAddress inet = InetAddress.getByName(ipAddress);
            return inet.isReachable(timeout);
        } catch (Exception e) {
            return false;
        }
    }

    public static boolean isIpv4(String ipAddress) {
        if (StrUtil.isBlank(ipAddress)) {
            return false;
        }
        Matcher matcher = PatternPool.IPV4.matcher(ipAddress);
        return matcher.matches();
    }
}
