package com.hollysys.inspection.utils.param;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.EnumUtil;
import com.hollysys.inspection.constants.algorithm.param.DataType;
import com.hollysys.inspection.entity.InspAlgorithmParamInstance;
import com.hollysys.inspection.model.algorithm.param.data.Point;
import com.hollysys.inspection.model.algorithm.param.data.Square;
import com.hollysys.inspection.model.channel.ScaleModel;

import java.util.List;
import java.util.Objects;

/**
 * 参数坐标相关逻辑工具类
 */
public class ParamCoordsUtil {

    /**
     * 1.进行算法参数坐标偏差校准（按照模板匹配结果）
     * 2.对于超出图片边界的参数值进行处理
     *
     * @param scaleByImg     图片分辨率
     * @param sceneTmpSquare 原模板位置
     * @param matchingSquare 当前匹配模板位置
     */
    public static void resetCoords(ScaleModel scaleByImg, Square sceneTmpSquare, Square matchingSquare,
                                   List<InspAlgorithmParamInstance> paramList) {
        if (CollectionUtil.isEmpty(paramList)) {
            return;
        }

        // 计算新旧roi区域坐标差值
        Point squareOffset = getSquareOffset(sceneTmpSquare, matchingSquare);

        for (InspAlgorithmParamInstance paramInstance : paramList) {
            String dataType = paramInstance.getDataType();
            if (!DataType.drawableNames().contains(dataType)) {
                continue;
            }
            Object value = paramInstance.getValue();
            if (Objects.isNull(value)) {
                return;
            }
            DataType dataTypeEnum = EnumUtil.fromStringQuietly(DataType.class, dataType);
            if (Objects.isNull(dataType)) {
                continue;
            }
            Object newValue = dataTypeEnum.resetCoords(value, squareOffset.getX(), squareOffset.getY(), scaleByImg);
            paramInstance.setValue(newValue);
        }
    }

    /**
     * 计算新旧roi区域坐标差值
     */
    public static Point getSquareOffset(Square sceneTmpSquare, Square matchingSquare) {
        // 计算新旧roi区域坐标差值
        int xOffset;
        int yOffset;
        if (Objects.isNull(sceneTmpSquare) || Objects.isNull(matchingSquare)) {
            xOffset = 0;
            yOffset = 0;
        } else {
            Point sourcePoint = sceneTmpSquare.get(0);
            Point currentPoint = matchingSquare.get(0);
            xOffset = sourcePoint.getX() - currentPoint.getX();
            yOffset = sourcePoint.getY() - currentPoint.getY();
        }
        return new Point(xOffset, yOffset);
    }
}
