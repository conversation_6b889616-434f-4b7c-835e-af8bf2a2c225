package com.hollysys.inspection.utils;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;

import java.time.LocalDateTime;

public class FileNameUtil {

    public static String createOutPath(String inputPath, String suffix) {
        String fileName = FileUtil.mainName(inputPath);
        String newFileName = fileName + suffix;
        return StrUtil.replace(inputPath, fileName, newFileName);
    }

    /**
     * 获取备份文件名
     * @param startTime1 开始时间
     * @param startTime2 开始时间
     * @return 文件名
     */
    public static String getBackFileName(LocalDateTime startTime1, LocalDateTime startTime2) {
        String startTimeStr1 = DateUtil.format(startTime1, DatePattern.CHINESE_DATE_TIME_PATTERN);
        String startTimeStr2 = DateUtil.format(startTime2, DatePattern.CHINESE_DATE_TIME_PATTERN);
        return startTimeStr1 + "-" + startTimeStr2 + ".csv"; // 文件名主体，不包含后缀
    }
}
