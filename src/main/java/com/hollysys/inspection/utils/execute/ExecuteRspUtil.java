package com.hollysys.inspection.utils.execute;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import com.hollysys.inspection.model.algorithm.execute.AlgorithmExecuteRsp;
import com.hollysys.inspection.model.algorithm.execute.OutputValueObj;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;

public class ExecuteRspUtil {

    /**
     * 根据精度重新设置结果值
     */
    public static void setValueByScale(AlgorithmExecuteRsp algorithmExecuteRsp) {
        if (Objects.isNull(algorithmExecuteRsp)) {
            return;
        }

        if (!BooleanUtil.isTrue(algorithmExecuteRsp.getIsSuccess())) {
            return;
        }

        Map<String, OutputValueObj> output = algorithmExecuteRsp.getOutput();
        if (CollectionUtil.isEmpty(output)) {
            return;
        }

        for (Map.Entry<String, OutputValueObj> valueObjEntry : output.entrySet()) {
            OutputValueObj valueObj = valueObjEntry.getValue();
            Object valueByScale = getValueByScale(valueObj);
            valueObj.setValue(valueByScale);
        }
    }

    /**
     * 返回结果值（按指定精度保留小数位）
     */
    public static Object getValueByScale(OutputValueObj valueObj) {
        Object value = valueObj.getValue();
        if (Objects.isNull(value)) {
            return null;
        }
        String string = value.toString();
        if (!NumberUtil.isNumber(string)) {
            return value;
        }
        Integer resultPrecision = valueObj.getResultPrecision();
        if (Objects.isNull(resultPrecision)) {
            return value;
        }

        float aFloat = Float.parseFloat(string);
        BigDecimal round = NumberUtil.round(aFloat, resultPrecision);
        return round.floatValue();
    }
}
