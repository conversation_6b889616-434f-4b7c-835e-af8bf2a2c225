package com.hollysys.inspection.utils.execute;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.constants.algorithm.param.DataType;
import com.hollysys.inspection.model.algorithm.correct.CorrectReqModel;
import com.hollysys.inspection.model.algorithm.correct.CorrectRespModel;
import com.hollysys.inspection.model.algorithm.correct.PicCorrectParamModel;
import com.hollysys.inspection.model.algorithm.osd.OSDItem;
import com.hollysys.inspection.model.algorithm.param.data.Circle;
import com.hollysys.inspection.model.algorithm.param.data.Ellipse;
import com.hollysys.inspection.model.algorithm.param.data.Point;
import com.hollysys.inspection.model.algorithm.param.data.Square;
import com.hollysys.inspection.model.channel.ScaleModel;
import com.hollysys.inspection.service.PythonServerService;
import com.hollysys.inspection.utils.AssertUtil;
import com.hollysys.inspection.utils.param.ParamCoordsUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 透视变换相关逻辑工具类
 */
public class TransformPicUtil {

    private static final PythonServerService pythonServerService = SpringUtil.getBean(PythonServerService.class);

    /**
     * 根据透视变换参数对图像进行变换
     */
    public static List<String> transformPic(ScaleModel scaleByImg, PicCorrectParamModel correctParam,
                                            Square sceneTmpSquare, Square matchingSquare,
                                            List<String> inputImageUrlList) {

        if (Objects.isNull(correctParam)) {
            throw new InspectionException("透视变换参数不允许为空");
        }

        Float ratio = correctParam.getRatio();
        AssertUtil.isTrue(Objects.nonNull(ratio), "透视变换比例参数不允许为空");

        Square square = correctParam.getFromSquare();
        AssertUtil.isTrue(Objects.nonNull(square), "透视变换原框参数不允许为空");

        // 计算新旧roi区域坐标差值
        Point squareOffset = ParamCoordsUtil.getSquareOffset(sceneTmpSquare, matchingSquare);

        List<String> transformPicResult = new ArrayList<>();
        for (String picUrl : inputImageUrlList) {

            // 对原来变换参数，坐标按照场景匹配结果进行换算，消除偏差
            Object object = DataType.SQUARE.resetCoords(square, squareOffset.getX(), squareOffset.getY(), scaleByImg);
            Square newSquare = Square.buildByObj(object);
            // 矫正结果输出到原文件路径
            CorrectReqModel correctReqModel = new CorrectReqModel();
            correctReqModel.setRatio(ratio);
            correctReqModel.setInputPath(picUrl);
            correctReqModel.setSquare(newSquare);

            // 执行变换
            CorrectRespModel correctRespModel = pythonServerService.transformPic(correctReqModel);

            transformPicResult.add(correctRespModel.getResultImg());
        }
        return transformPicResult;
    }

    /**
     * 对OSD中的坐标进行透视变换逆变换
     */
    public static void retransformOsdCoords(List<OSDItem> osdItems, ScaleModel scaleByImg, PicCorrectParamModel correctParam,
                                            Square sceneTmpSquare, Square matchingSquare) {
        if (CollectionUtil.isEmpty(osdItems)) {
            return;
        }
        AssertUtil.isTrue(Objects.nonNull(correctParam), "透视变换参数不允许为空");
        // 计算新旧场景模板区域坐标差值
        Point squareOffset = ParamCoordsUtil.getSquareOffset(sceneTmpSquare, matchingSquare);
        for (OSDItem osdItem : osdItems) {
            Object coordinate = osdItem.getCoords();
            if (Objects.isNull(coordinate)) {
                continue;
            }
            String dataType = osdItem.getDataType();
            DataType dataTypeEnum = EnumUtil.fromStringQuietly(DataType.class, dataType);
            if (Objects.isNull(dataTypeEnum)) {
                continue;
            }
            // 逆透视变换
            Object newCoordinate = dataTypeEnum.retransformCoords(coordinate, scaleByImg, correctParam);

            if (DataType.CIRCLE == dataTypeEnum) {
                // 圆形经过逆透视变换后，成为了椭圆形
                dataTypeEnum = DataType.ELLIPSE;
                osdItem.setDataType(DataType.ELLIPSE.name());
            }

            // 计算新旧模板坐标差值  坐标偏差补偿
            Object object = dataTypeEnum.resetCoords(newCoordinate, squareOffset.getX(), squareOffset.getY(), scaleByImg);
            osdItem.setCoords(object);
        }
    }

    /**
     * 透视变换核心方法
     * 此方法不区分正变换和逆变换
     */
    public static List<Point> transformPoints(List<Point> points, Square formSquare, Square toSquare) {
        return pythonServerService.transformPoints(points, formSquare, toSquare);
    }

    /**
     * 圆形透视变换
     * 透视变换结果为椭圆形
     */
    public static Ellipse transformCircle(Circle circle, Square formSquare, Square toSquare) {
        return pythonServerService.circleToEllipse(circle, formSquare, toSquare);
    }
}
