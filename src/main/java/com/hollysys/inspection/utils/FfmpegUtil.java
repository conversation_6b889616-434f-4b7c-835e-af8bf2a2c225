package com.hollysys.inspection.utils;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.WeakCache;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ArrayUtil;
import com.hollysys.inspection.config.exceptions.InspectionException;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

@Slf4j
public class FfmpegUtil {

    private FfmpegUtil() {
        throw new InspectionException("can not be instantiated");
    }

    private final static WeakCache<String, Thread> THREAD_CACHE = CacheUtil.newWeakCache(0);

    static {
        THREAD_CACHE.setListener((key, thread) -> {
            log.debug("停止推流：{}", key);
            // 中断推流线程
            ThreadUtil.interrupt(thread, false);
        });
    }

    public static void stopPushing(String srsStreamUrl) {
        THREAD_CACHE.remove(srsStreamUrl);
    }

    public static void pushCpuStream(String streamKey, String rtspUrl, String srsStreamUrl) {
        String[] command = new String[]{"ffmpeg", "-rtsp_transport", "tcp", "-i", rtspUrl, "-vcodec", "copy", "-c:a",
                "aac", "-ac", "1", "-f", "flv", srsStreamUrl};

        String commandStr = ArrayUtil.join(command, " ");
        log.debug("pushCpuStream... commandStr = {}", commandStr);
//		StringBuilder buffer = new StringBuilder();
//		buffer.append("ffmpeg -rtsp_transport tcp -i ");
//		buffer.append(streamUrl);
//		buffer.append(" ");
//		buffer.append("-vcodec ");
//		buffer.append("copy ");
//		buffer.append("-c:a ");
//		buffer.append("aac ");
//		buffer.append("-ac ");
//		buffer.append("1 ");
//		buffer.append("-f ");
//		buffer.append("flv ");
//		buffer.append(srsStreamUrl);
        execute(command, streamKey);
    }

    public static void pushGpuStream(String streamKey, String rtspUrl, String srsStreamUrl) {
//		StringBuilder buffer = new StringBuilder();
//		buffer.append("ffmpeg -hwaccel cuvid -rtsp_transport tcp -vcodec h264_cuvid -i ");
//		buffer.append(streamUrl);
//		buffer.append(" ");
//		buffer.append("-vcodec h264_nvenc ");
//		buffer.append("-c:a ");
//		buffer.append("aac ");
//		buffer.append("-ac ");
//		buffer.append("1 ");
//		buffer.append("-f ");
//		buffer.append("flv ");
//		buffer.append(srsStreamUrl);
        String[] command = new String[]{"ffmpeg", "hwaccel", "cuvid", "-rtsp_transport", "tcp", "-vcodec", "h264_cuvid",
                "-i", rtspUrl, "-vcodec", "h264_nvenc", "-c:a", "aac", "-ac", "1", "-f", "flv", srsStreamUrl};
        String commandStr = ArrayUtil.join(command, " ");
        log.debug("pushGpuStream... commandStr = {}", commandStr);
        execute(command, streamKey);
    }

    private static void execute(String[] command, String streamKey) {
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.redirectErrorStream(true);
        Thread thread = ThreadUtil.newThread(() -> {
            try {
                Process process = processBuilder.start();
                // 读取FFmpeg的输出信息
                InputStreamReader inputStreamReader = new InputStreamReader(process.getInputStream());
                BufferedReader reader = new BufferedReader(inputStreamReader);
                String line;
                while ((line = reader.readLine()) != null) {
                    // log.info("--------ffmpeg推流={}" , line);
                }
                // 等待FFmpeg进程结束
                process.waitFor();
                reader.close();
                inputStreamReader.close();
            } catch (InterruptedException e) {
                log.error("ffmpeg推流中断退出.streamKey = {}", streamKey, e);
            } catch (IOException e) {
                Thread.currentThread().interrupt();
                log.error("ffmpeg推流出现异常.streamKey = {}", streamKey, e);
            }
        }, "");
        thread.start();
        THREAD_CACHE.put(streamKey, thread);
    }
}
