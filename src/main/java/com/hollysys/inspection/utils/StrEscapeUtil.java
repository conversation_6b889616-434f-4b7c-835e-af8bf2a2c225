package com.hollysys.inspection.utils;


import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;

public class StrEscapeUtil {

    /**
     * 特殊字符转移
     *
     * @param str 待转移字符
     * @return 转移结果
     */
    public static String escapeChar(String str) {
        if (!StrUtil.isEmpty(str)) {
            str = URLUtil.decode(str);
            str = str.replace("\\", "\\\\");
            str = str.replace("_", "\\_");
            str = str.replace("%", "\\%");
        }
        return str;
    }
}
