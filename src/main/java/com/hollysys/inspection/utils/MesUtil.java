package com.hollysys.inspection.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;

import java.util.List;
import java.util.stream.Collectors;

public class MesUtil {

    public static String urlToAbsolute(String serverHost, String relativeUrl) {
        if (StrUtil.isBlank(relativeUrl)) {
            return relativeUrl;
        }
        if (relativeUrl.startsWith("http")) {
            return relativeUrl;
        }
        return "http://" + serverHost + relativeUrl;
    }

    public static List<String> urlsToAbsolute(String serverHost, List<String> relativeUrls) {
        if (CollectionUtil.isNotEmpty(relativeUrls)) {
            return relativeUrls.stream().map(item -> urlToAbsolute(serverHost, item)).collect(Collectors.toList());
        }
        return relativeUrls;
    }


}
