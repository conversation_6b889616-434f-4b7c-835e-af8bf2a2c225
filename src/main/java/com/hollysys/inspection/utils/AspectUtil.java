package com.hollysys.inspection.utils;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.lang.reflect.Method;
import java.util.Optional;

/**
 * 切面相关工具类
 */
public class AspectUtil {

    /**
     * 获取切点函数入参属性值
     * 如：#channelId、#channelInfo.id
     *
     * @param spelStr   参数表达式
     * @param joinPoint 切点
     */
    public static Object parseRtParam(String spelStr, JoinPoint joinPoint) {
        if (StrUtil.isBlank(spelStr)) {
            return null;
        }
        // 通过joinPoint获取被注解方法
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        // 创建解析器
        SpelExpressionParser parser = new SpelExpressionParser();
        Expression expression = parser.parseExpression(spelStr);
        EvaluationContext context = new StandardEvaluationContext();
        // 获取参数值
        Object[] args = joinPoint.getArgs();
        // 获取运行时参数的名称
        DefaultParameterNameDiscoverer discoverer = new DefaultParameterNameDiscoverer();
        String[] parameterNames = discoverer.getParameterNames(method);
        if (ArrayUtil.isNotEmpty(parameterNames)) {
            for (int i = 0; i < parameterNames.length; i++) {
                context.setVariable(parameterNames[i], args[i]);
            }
            // 解析,获取替换后的结果
            Optional<Object> result = Optional.ofNullable(expression.getValue(context));
            return result.orElse(null);
        }
        return null;
    }
}
