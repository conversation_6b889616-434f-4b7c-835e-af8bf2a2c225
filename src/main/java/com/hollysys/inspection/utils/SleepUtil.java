package com.hollysys.inspection.utils;

import com.hollysys.inspection.config.exceptions.InspectionException;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;

@Slf4j
public class SleepUtil {

    private SleepUtil() {
        throw new RuntimeException();
    }

    public static void sleepSecond(long second) {
        try {
            TimeUnit.SECONDS.sleep(second);
        } catch (InterruptedException e) {
            log.error("sleepSecond error", e);
            Thread.currentThread().interrupt();
            throw new InspectionException("线程等待错误");
        }
    }

    public static void sleepMillisecond(long millisecond) {
        try {
            TimeUnit.MILLISECONDS.sleep(millisecond);
        } catch (InterruptedException e) {
            log.error("sleepSecond error", e);
            Thread.currentThread().interrupt();
            throw new InspectionException("线程等待错误");
        }
    }

}
