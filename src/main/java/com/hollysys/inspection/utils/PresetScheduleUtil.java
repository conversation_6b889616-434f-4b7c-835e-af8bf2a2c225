package com.hollysys.inspection.utils;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.thread.ThreadUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

@Slf4j
public class PresetScheduleUtil {

    private static final TimedCache<String, CountDownLatch> TIMED_CACHE = CacheUtil.newTimedCache(1000);

    static {
        TIMED_CACHE.setListener((s, s2) -> {
            if (Objects.nonNull(s2)) {
                s2.countDown();
            }
            log.debug("移除缓存：{}，CountDownLatch.size：{} ", s, s2.getCount());
        });
        TIMED_CACHE.schedulePrune(1);
    }

    /**
     * 等待预置点执行结束，或者超时结束
     *
     * @param taskId    任务ID
     * @param millisecond 超时时间（毫秒）
     */
    public static void waitMillisecond(String taskId, long millisecond) {
        CountDownLatch countDownLatch = ThreadUtil.newCountDownLatch(1);
        TIMED_CACHE.put(taskId, countDownLatch, millisecond);
        try {
            if (!countDownLatch.await(millisecond, TimeUnit.MILLISECONDS)) {
                log.debug("waitMillisecond 超时，taskId = {}，millisecond = {} ", taskId, millisecond);
            }
        } catch (InterruptedException e) {
            log.error("waitMillisecond 出现错误，taskId = {}，millisecond = {} ", taskId, millisecond, e);
            // Restore interrupted state...
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 重置缓存（一般在调度间隔修改后，调用此方法）
     *
     * @param taskId 任务ID
     */
    public static void resetCache(String taskId) {
        TIMED_CACHE.remove(taskId);
    }
}
