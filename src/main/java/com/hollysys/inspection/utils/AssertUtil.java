package com.hollysys.inspection.utils;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.hollysys.inspection.config.exceptions.InspectionException;

import java.util.function.Supplier;


public class AssertUtil {
    public static <X extends Throwable> void isTrue(boolean expression, Supplier<? extends X> supplier) throws X {
        Assert.isTrue(expression, supplier);
    }

    public static <X extends Throwable> void isTrue(boolean expression, String errorMsgTemplate, Object... params) throws X {
        Assert.isTrue(expression, () -> new InspectionException(StrUtil.format(errorMsgTemplate, params)));
    }

    public static <X extends Throwable> void isTrue(boolean expression) throws X {
        Assert.isTrue(expression);
    }

    public static <X extends Throwable> void isFalse(boolean expression, String errorMsgTemplate, Object... params) throws X {
        Assert.isFalse(expression, () -> new InspectionException(StrUtil.format(errorMsgTemplate, params)));
    }

    public static <X extends Throwable> void isFalse(boolean expression) throws X {
        Assert.isFalse(expression);
    }

    public static void throwsException(Runnable runnable, Class<? extends Throwable> expectedType) {
        try {
            runnable.run();
        } catch (Throwable actualException) {
            Assert.isTrue(expectedType.isInstance(actualException));
        }
        Assert.isFalse(false);
    }
}
