package com.hollysys.inspection.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.Week;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class TimeSlotUtil {
    /**
     * 判断当前时间是否包含在指定时间段（含头不含尾）
     *
     * @param timeSlot HH:mm时间段列表
     * @param dateNow  当前时间
     * @return 是否包含
     */
    public static boolean isContains(String timeSlot, DateTime dateNow) {
        List<List<Number>> timeSlotList = str2timeSlot(timeSlot);
        String format = "HH:mm";
        String dateNowFormat = DateUtil.format(dateNow, format);
        for (List<Number> numbers : timeSlotList) {
            List<String> strings = timeSlotNum2Str(numbers);
            String start = strings.get(0);
            String end = strings.get(1);
            // 含头不含尾
            int compare1 = StrUtil.compareVersion(dateNowFormat, start);
            int compare2 = StrUtil.compareVersion(dateNowFormat, end);
            boolean isContains = compare1 >= 0 && compare2 < 0;
            if (isContains) {
                return true;
            }
        }
        return false;
    }

    /**
     * 将二进制字符串转化为时间段集合
     *
     * @param timeSlot 二进制字符串
     * @return 时间段集合
     */
    private static List<List<Number>> str2timeSlot(String timeSlot) {
        char[] chars = timeSlot.toCharArray();
        List<List<Number>> result = new ArrayList<>();
        List<Number> slotItemList = new ArrayList<>();
        for (int i = 0; i < chars.length; i++) {
            char aChar = chars[i];
            if (aChar == '1') {
                if (CollectionUtil.isEmpty(slotItemList)) {
                    slotItemList.add((i) * 0.5);
                }
            } else {
                if (CollectionUtil.isNotEmpty(slotItemList)) {
                    slotItemList.add((i) * 0.5);
                    result.add(new ArrayList<>(slotItemList));
                    slotItemList.clear();
                }
            }

            if (i == chars.length - 1 && CollectionUtil.isNotEmpty(slotItemList)) {
                slotItemList.add((i + 1) * 0.5);
                result.add(new ArrayList<>(slotItemList));
            }
        }
        return result;
    }

    private static List<String> timeSlotNum2Str(List<Number> timeSlotNumList) {
        return timeSlotNumList.stream().map(number -> {
            int hour = number.intValue();
            String hourStr = NumberUtil.decimalFormat("00", hour);
            double doubleValue = number.doubleValue();
            int minute = (int) ((doubleValue - hour) * 60);
            if (minute == 0) {
                return hourStr + ":00";
            }
            return hourStr + ":" + minute;
        }).collect(Collectors.toList());
    }

    public static synchronized int getDateWeek() {
        Week week = DateUtil.date().dayOfWeekEnum();
        // 转化成中国星期顺序
        return week.getIso8601Value();
    }

    /**
     * 判断 timeSlot1 和 timeSlot2是否有交集
     *
     * @param timeSlot1 入参一
     * @param timeSlot2 入参二
     * @return 是否有交集
     */
    public static boolean hasIntersection(String timeSlot1, String timeSlot2) {
        char[] chars1 = timeSlot1.toCharArray();
        char[] chars2 = timeSlot2.toCharArray();
        int minLen = Math.min(chars1.length, chars2.length);
        for (int i = 0; i < minLen; i++) {
            Integer decode1 = Integer.decode(String.valueOf(chars1[i]));
            Integer decode2 = Integer.decode(String.valueOf(chars2[i]));
            if ((decode1 & decode2) == 1) {
                return true;
            }
        }
        return false;
    }
}
