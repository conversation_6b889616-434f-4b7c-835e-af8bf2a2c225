package com.hollysys.inspection.utils;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.extra.spring.SpringUtil;
import com.hollysys.inspection.entity.InspChannelInfo;
import com.hollysys.inspection.model.sdk.LoginResp;
import com.hollysys.inspection.service.protocol.impl.CameraCtrlProxyService;
import lombok.extern.slf4j.Slf4j;

/**
 * 摄像机SDK登录对象缓存工具
 */
@Slf4j
public class SdkTokenUtil {

    private static final CameraCtrlProxyService cameraCtrlProxyService = SpringUtil.getBean(CameraCtrlProxyService.class);

    // 创建超时缓存，超时时间为一分钟
    private static final TimedCache<String, LoginResp> TIMED_CACHE = CacheUtil.newTimedCache(1000 * 60);

    static {
        TIMED_CACHE.setListener((key, value) -> {
            cameraCtrlProxyService.logout(value);
            log.debug("摄像机：{}由于长时间未操作，自动退出登录", value.getChannelInfo().getAddress());
        });
        // 100毫秒定时清理一次过期数据
        TIMED_CACHE.schedulePrune(100);
    }

    private static String buildCacheKey(InspChannelInfo channelInfo) {
        return channelInfo.getCameraCtrlProtocol()
                + "_" + channelInfo.getAddress()
                + "_" + channelInfo.getUsername()
                + "_" + channelInfo.getPassword();
    }

    public static LoginResp getLoginCache(InspChannelInfo channelInfo) {
        String key = buildCacheKey(channelInfo);
        return TIMED_CACHE.get(key);
    }

    public static void setLoginCache(LoginResp loginResp) {
        String key = buildCacheKey(loginResp.getChannelInfo());
        TIMED_CACHE.put(key, loginResp);
    }
}
