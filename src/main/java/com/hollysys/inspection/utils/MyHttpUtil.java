package com.hollysys.inspection.utils;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import org.springframework.http.HttpHeaders;

import java.io.File;
import java.util.Map;

/**
 * 主要是对hutool.HttpUtil的封装，增加全局超时时间的配置
 */
public class MyHttpUtil {

    // 全局超时时间为30s
    private static final int TIMEOUT = 30 * 1000;

    public static String post(String url, Object param, String token) {
        try (HttpResponse execute = HttpUtil.createPost(url)
                .header(HttpHeaders.AUTHORIZATION, token)
                .timeout(TIMEOUT)
                .body(JSONUtil.toJsonStr(param)).execute()) {
            return execute.body();
        }
    }

    public static HttpResponse post(String url, Object param) {
        try (HttpResponse execute = HttpUtil.createPost(url)
                .timeout(TIMEOUT)
                .body(JSONUtil.toJsonStr(param)).execute()) {
            return execute;
        }
    }

    public static HttpResponse delete(String url, Object param) {
        try (HttpResponse execute = HttpUtil.createRequest(Method.DELETE, url)
                .timeout(TIMEOUT)
                .body(JSONUtil.toJsonStr(param)).execute()) {
            return execute;
        }
    }

    public static void downloadFile(String url, File destFile) {
        HttpUtil.downloadFile(url, destFile, TIMEOUT);
    }

    public static String get(String urlString, Map<String, Object> paramMap) {
        return HttpUtil.get(urlString, paramMap, TIMEOUT);
    }

    public static String get(String urlString) {
        return get(urlString, null);
    }
}
