package com.hollysys.inspection.utils.tree;

import cn.hutool.core.util.StrUtil;

import java.io.Serializable;
import java.lang.invoke.SerializedLambda;
import java.lang.reflect.Method;
import java.util.function.Function;

/**
 * 自定义方法函数
 *
 * <AUTHOR>
 */
public interface CusFunction<T, R> extends Function<T, R>, Serializable {


    /**
     * 获取属性名称
     *
     * @return 属性名称
     */
    default String getFieldName() {
        SerializedLambda serializedLambda;
        try {
            Method method = getClass().getDeclaredMethod("writeReplace");
            method.setAccessible(true);
            serializedLambda = (SerializedLambda) method.invoke(this);
            method.setAccessible(false);
        } catch (Exception exception) {
            return null;
        }
        String implMethodName = serializedLambda.getImplMethodName();
        final String generalField = StrUtil.getGeneralField(implMethodName);
        return StrUtil.isEmpty(generalField) ? implMethodName : generalField;
    }

}
