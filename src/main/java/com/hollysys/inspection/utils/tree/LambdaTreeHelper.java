package com.hollysys.inspection.utils.tree;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 使用Lambda，动态组装树形结构
 *
 * <AUTHOR>
 */
public class LambdaTreeHelper<P, R, T> {

    private CusFunction<R, T> idKeyFun;
    private CusFunction<P, T> idValueFun;

    private CusFunction<R, T> parentIdKeyFun;
    private CusFunction<P, T> parentIdValueFun;

    private CusFunction<R, String> labelKeyFun;
    private CusFunction<P, String> labelValueFun;

    private CusFunction<R, Long> orderNoKeyFun;
    private CusFunction<P, Long> orderNoValueFun;

    private CusFunction<R, List<R>> childrenKeyFun;

    private final Map<CusFunction<R, Object>, CusFunction<P, Object>> extraMap = new HashMap<>();

    public static <P, R, T> LambdaTreeHelper<P, R, T> newInstance() {
        return new LambdaTreeHelper<>();
    }

    public LambdaTreeHelper<P, R, T> childrenKey(CusFunction<R, List<R>> childrenKeyFun) {
        this.childrenKeyFun = childrenKeyFun;
        return this;
    }

    public LambdaTreeHelper<P, R, T> id(CusFunction<R, T> idKeyFun, CusFunction<P, T> idValueFun) {
        this.idKeyFun = idKeyFun;
        this.idValueFun = idValueFun;
        return this;
    }

    public LambdaTreeHelper<P, R, T> parentId(CusFunction<R, T> parentIdKeyFun, CusFunction<P, T> parentIdValueFun) {
        this.parentIdKeyFun = parentIdKeyFun;
        this.parentIdValueFun = parentIdValueFun;
        return this;
    }

    public LambdaTreeHelper<P, R, T> label(CusFunction<R, String> labelKeyFun, CusFunction<P, String> labelValueFun) {
        this.labelKeyFun = labelKeyFun;
        this.labelValueFun = labelValueFun;
        return this;
    }

//    public LambdaTreeWrapper<P, R, T> orderNo(CusFunction<R, Comparable<?>> orderNoKeyFun, CusFunction<P, Comparable<?>> orderNoValueFun) {
//        this.orderNoKeyFun = orderNoKeyFun;
//        this.orderNoValueFun = orderNoValueFun;
//        return this;
//    }

    public LambdaTreeHelper<P, R, T> orderNo(CusFunction<R, Long> orderNoKeyFun, CusFunction<P, Long> orderNoValueFun) {
        this.orderNoKeyFun = orderNoKeyFun;
        this.orderNoValueFun = orderNoValueFun;
        return this;
    }

    public LambdaTreeHelper<P, R, T> putExtra(CusFunction<R, Object> keyFun, CusFunction<P, Object> valueFun) {
        extraMap.put(keyFun, valueFun);
        return this;
    }

    public List<R> buildTreeList(List<P> entityList, T parentId, Class<R> rClass) {
        Map<T, List<P>> groupByPatentId = entityList.stream().collect(Collectors.groupingBy(parentIdValueFun));
        return buildTreeList(groupByPatentId, parentId, rClass);
    }

    private List<R> buildTreeList(Map<T, List<P>> groupByPatentId, T parentId, Class<R> rClass) {
        List<P> parentList = groupByPatentId.get(parentId);
        // 没有找到根节点
        if (CollUtil.isEmpty(parentList)) {
            return CollUtil.newArrayList();
        }

        List<R> result = CollUtil.newArrayList();
        for (P entity : parentList) {
            LinkedHashMap<Object, Object> nodeModel = new LinkedHashMap<>();
            nodeModel.put(getName(idKeyFun), idValueFun.apply(entity));
            nodeModel.put(getName(parentIdKeyFun), parentIdValueFun.apply(entity));
            nodeModel.put(getName(labelKeyFun), labelValueFun.apply(entity));
            if (Objects.nonNull(orderNoKeyFun)) {
                nodeModel.put(getName(orderNoKeyFun), orderNoValueFun.apply(entity));
            }
            // 设置扩展参数
            if (CollUtil.isNotEmpty(extraMap)) {
                for (Map.Entry<CusFunction<R, Object>, CusFunction<P, Object>> entry : extraMap.entrySet()) {
                    CusFunction<R, Object> key = entry.getKey();
                    CusFunction<P, Object> value = entry.getValue();
                    nodeModel.put(getName(key), value.apply(entity));
                }
            }
            List<R> childNodes = buildTreeList(groupByPatentId, idValueFun.apply(entity), rClass);
            nodeModel.put(getName(childrenKeyFun), childNodes);
            R r = JSON.parseObject(JSON.toJSONString(nodeModel), rClass);
            result.add(r);
        }

        // 节点排序
        if (Objects.nonNull(orderNoKeyFun)) {
            CollUtil.sort(result, Comparator.comparing(orderNoKeyFun));
        }
        return result;
    }

    private String getName(CusFunction<?, ?> function) {
        return function.getFieldName();
    }
}
