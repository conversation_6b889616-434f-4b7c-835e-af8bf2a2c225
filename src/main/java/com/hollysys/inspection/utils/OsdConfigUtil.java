package com.hollysys.inspection.utils;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.hollysys.inspection.model.algorithm.osd.OsdConfig;
import com.hollysys.inspection.service.InspCommonConfigService;

import java.util.Objects;

/**
 * Web端使用RGB格式
 * Python-cv中使用BGR
 */
public class OsdConfigUtil {
    private static final BaseOsdConfigGetter GETTER_CV = new Cv();

    private static final BaseOsdConfigGetter GETTER_WEB = new Web();

    private static OsdConfig OSD_CONFIG_CV;

    private static OsdConfig OSD_CONFIG_WEB;

    private interface BaseOsdConfigGetter {

        /**
         * 获取OSD基本颜色
         *
         * @return OSD基本颜色
         */
        int[] getOsdColor();

        /**
         * 获取OSD文本绘制颜色
         *
         * @return OSD文本绘制颜色
         */
        int[] getTextColor();

        /**
         * 获取OSD文本绘制背景颜色
         *
         * @return OSD文本绘制背景颜色
         */
        int[] getTextBgColor();

        /**
         * 获取OSD报警后图形绘制颜色
         *
         * @return OSD报警后图形绘制颜色
         */
        int[] getAlarmColor();

        /**
         * 获取绘制文本字体大小
         *
         * @return OSD文本字体大小
         */
        int getFontSize();

        /**
         * 获取OSD图形绘制画笔粗细
         *
         * @return OSD图形绘制画笔粗细
         */
        int getFontThickness();
    }

    private static class Web implements BaseOsdConfigGetter {

        private static final InspCommonConfigService commonConfigService = SpringUtil.getBean(InspCommonConfigService.class);

        @Override
        public int[] getOsdColor() {
            return getOsdConfig().getColorNormal();
        }

        @Override
        public int[] getTextColor() {
            return getOsdConfig().getFontColor();
        }

        @Override
        public int[] getTextBgColor() {
            Boolean fontBgEnable = commonConfigService.getCommonConfigCache().getFontBgEnable();
            if (BooleanUtil.isFalse(fontBgEnable)) {
                // 文字背景未开启，则设置背景颜色为透明色
                return null;
            }
            return getOsdConfig().getFontBgColor();
        }

        @Override
        public int[] getAlarmColor() {
            return getOsdConfig().getColorWarn();
        }

        @Override
        public int getFontSize() {
            return getOsdConfig().getFontSize();
        }

        @Override
        public int getFontThickness() {
            int fontThickness = getOsdConfig().getFontThickness();
            if (fontThickness <= 0) {
                fontThickness = 1;
            }
            return fontThickness;
        }

        private OsdConfig getOsdConfig() {
            return commonConfigService.getCommonConfigCache().getOsdConfig();
        }
    }

    private static class Cv extends Web {

        @Override
        public int[] getOsdColor() {
            return rgb2Bgr(super.getOsdColor());
        }

        @Override
        public int[] getTextColor() {
            return rgb2Bgr(super.getTextColor());
        }

        @Override
        public int[] getTextBgColor() {
            return rgb2Bgr(super.getTextBgColor());
        }

        @Override
        public int[] getAlarmColor() {
            return rgb2Bgr(super.getAlarmColor());
        }

        @Override
        public int getFontThickness() {
            int fontThickness = super.getFontThickness();
            fontThickness = (int) (fontThickness * 0.3);
            if (fontThickness <= 0) {
                fontThickness = 1;
            }
            return fontThickness;
        }

        private int[] rgb2Bgr(int[] rgb) {
            if (Objects.isNull(rgb)) {
                return null;
            }
            int[] bgr = new int[3];
            bgr[0] = rgb[2];
            bgr[1] = rgb[1];
            bgr[2] = rgb[0];
            return bgr;
        }
    }

    private static OsdConfig buildOsdConfig(BaseOsdConfigGetter osdConfigGetter, OsdConfig osdConfig) {
        osdConfig.setColorNormal(osdConfigGetter.getOsdColor());
        osdConfig.setColorWarn(osdConfigGetter.getAlarmColor());
        osdConfig.setFontBgColor(osdConfigGetter.getTextBgColor());
        osdConfig.setFontColor(osdConfigGetter.getTextColor());
        osdConfig.setFontThickness(osdConfigGetter.getFontThickness());
        osdConfig.setFontSize(osdConfigGetter.getFontSize());
        return osdConfig;
    }

    public static OsdConfig getWebOsdConfig() {
        if (OSD_CONFIG_WEB == null) {
            OSD_CONFIG_WEB = new OsdConfig();
        }
        return buildOsdConfig(GETTER_WEB, OSD_CONFIG_WEB);
    }

    public static OsdConfig getCvOsdConfig() {
        if (OSD_CONFIG_CV == null) {
            OSD_CONFIG_CV = new OsdConfig();
        }
        return buildOsdConfig(GETTER_CV, OSD_CONFIG_CV);
    }
}
