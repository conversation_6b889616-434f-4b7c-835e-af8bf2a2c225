package com.hollysys.inspection.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.hollysys.inspection.config.redis.RedisHelper;
import com.hollysys.inspection.entity.InspAlarmRecord;

import java.time.LocalDateTime;

/**
 * 报警恢复次数判断工具类
 */
public class AlarmCheckUtil {

    private static final RedisHelper redisHelper = SpringUtil.getBean(RedisHelper.class);

    /**
     * 判断报警是否满足恢复条件
     * 将报警时间存储在redis
     */
    public static boolean checkCanRestored(InspAlarmRecord oneNotRestored, int alarmRestoreMaxTimes) {
        String key = getKey(oneNotRestored);
        String timesStr = redisHelper.get(key);
        JSONArray timesArray;
        int times = 0;
        if (StrUtil.isEmpty(timesStr)) {
            timesArray = new JSONArray();
        } else {
            timesArray = JSONUtil.parseArray(timesStr);
            times = timesArray.size();
        }
        times += 1;
        if (times >= alarmRestoreMaxTimes) {
            // 判断成功后删除缓存
            redisHelper.delete(key);
            return true;
        }

        // 加入缓存
        timesArray.add(0, LocalDateTime.now());
        redisHelper.set(key, timesArray.toString());
        return false;
    }

    /**
     * 判断报警是否满足恢复条件
     * 将报警时间存储在redis
     */
    public static void delRestoredCache(InspAlarmRecord oneNotRestored) {
        String key = getKey(oneNotRestored);
        redisHelper.delete(key);
    }

    private static String getKey(InspAlarmRecord oneNotRestored) {
        return "checkCanRestored:" + oneNotRestored.getId();
    }
}
