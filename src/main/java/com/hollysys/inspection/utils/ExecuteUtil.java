package com.hollysys.inspection.utils;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.hollysys.inspection.service.InspCommonConfigService;
import org.slf4j.Logger;

public class ExecuteUtil {

    private static final InspCommonConfigService commonConfigService = SpringUtil.getBean(InspCommonConfigService.class);

    /**
     * 打印执行过程中日志
     *
     * @param logger 日志对象
     * @param var1   参数一
     * @param var2   参数二
     */
    public static void printExecuteLog(Logger logger, String var1, Object... var2) {
        Boolean executeLogEnable = commonConfigService.getCommonConfigCache().getExecuteLogEnable();
        if (BooleanUtil.isTrue(executeLogEnable)) {
            logger.debug(var1, var2);
        }
    }
}
