package com.hollysys.inspection.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.hollysys.inspection.constants.algorithm.param.DataType;
import com.hollysys.inspection.entity.InspAlgorithmParam;
import com.hollysys.inspection.entity.InspAlgorithmParamInstance;
import com.hollysys.inspection.model.algorithm.execute.AlgorithmExecuteRsp;
import com.hollysys.inspection.model.algorithm.execute.OutputValueObj;
import com.hollysys.inspection.model.algorithm.osd.OSDItem;
import com.hollysys.inspection.model.algorithm.osd.OsdConfig;
import com.hollysys.inspection.model.algorithm.param.data.Point;
import com.hollysys.inspection.model.algorithm.param.data.Square;
import com.hollysys.inspection.model.channel.ScaleModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.keyvalue.DefaultMapEntry;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class OsdUtil {

    /**
     * 构建OSD基础数据集合，不包含颜色等数据
     */
    public static List<OSDItem> buildOsdBaseList(List<InspAlgorithmParamInstance> paramList,
                                                 AlgorithmExecuteRsp algorithmExecuteRsp) {
        List<OSDItem> result = new ArrayList<>();
        // 获取出参中的OSD
        List<OSDItem> osdInfoOutput = algorithmExecuteRsp.getOsdInfo();
        if (CollectionUtil.isNotEmpty(osdInfoOutput)) {
            result.addAll(osdInfoOutput);
        }

        // 获取入参中的OSD
        if (CollUtil.isNotEmpty(paramList)) {
            List<InspAlgorithmParamInstance> drawToOsdParams = paramList.stream().filter(item -> Objects.nonNull(item.getValue())).filter(InspAlgorithmParam::getDrawToOsd).filter(item -> DataType.drawableNames().contains(item.getDataType())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(drawToOsdParams)) {
                for (InspAlgorithmParamInstance drawToOsdParam : drawToOsdParams) {
                    OSDItem osdItem = new OSDItem();
                    osdItem.setDataType(drawToOsdParam.getDataType());
                    osdItem.setCoords(drawToOsdParam.getValue());
                    result.add(osdItem);
                }
            }
        }
        return result;
    }

    /**
     * 需要在透视变换之后进行组织，因为涉及到对坐标边界的判断
     */
    public static List<OSDItem> getTextOsdList(List<OSDItem> osdInfoOutput) {
        List<OSDItem> result = new ArrayList<>();
        if (CollectionUtil.isEmpty(osdInfoOutput)) {
            return result;
        }

        // 拆分输出OSD中文本
        for (OSDItem osdItem : osdInfoOutput) {
            OutputValueObj textObj = osdItem.getTextObj();
            // 只有当图形为SQUARE时，才会绘制文本
            if (DataType.SQUARE.name().equals(osdItem.getDataType()) && Objects.nonNull(textObj)) {
                Object coords = osdItem.getCoords();
                if (!DataType.SQUARE.checkType(coords)) {
                    continue;
                }
                String text = textObj.buildResultOsdText();
                if (StrUtil.isBlank(text)) {
                    continue;
                }

                OSDItem textOsdItemText = new OSDItem();
                textOsdItemText.setDataType(DataType.STRING.name());
                // 置信度拼接
                Float score = osdItem.getScore();
                if (Objects.nonNull(score)) {
                    text += "  " + score;
                }
                textOsdItemText.setText(text);
                textOsdItemText.setScore(score);

                Square points = Square.buildByObj(coords);
                Map.Entry<String, Point> textPosition = getTextPosition(points);
                textOsdItemText.setCoords(textPosition.getValue());
                textOsdItemText.setTextPosition(textPosition.getKey());
                result.add(textOsdItemText);
            }
        }
        return result;
    }

    /**
     * 获取文本OSD起点位置和坐标
     */
    private static Map.Entry<String, Point> getTextPosition(Square points) {
        // 文本在图像上边界（左上点y坐标小于50时，认为处于上边界内）
        Point point0 = points.get(0);
        Integer y = point0.getY();
        boolean isTopBound = y < 50;
        // TODO 文本在图像右边界（右上点x坐标+50>=图像分辨率x轴总长时，认为处于右边界内）
        // Point point1 = points.get(1);
        // Integer x = point1.get(0);
        // boolean isRightBound=y < 50;

        // key:前端绘制文本的起始点
        Map.Entry<String, Point> result;
        if (isTopBound) {
            // 文字绘制以标识框左下角点为
            result = new DefaultMapEntry<>("left:top", points.get(1));
        } else {
            result = new DefaultMapEntry<>("left:bottom", point0);
        }
        return result;
    }

    public static OSDItem getDescOsdItem(String presetDes, ScaleModel displayResolution, List<AlgorithmExecuteRsp> algorithmExecuteRspList) {
        List<String> lines = new ArrayList<>();
        // 拼接预置点描述
        if (StrUtil.isNotBlank(presetDes)) {
            lines.add(presetDes);
        }
        // 拼接算法结果或者报警
        if (CollectionUtil.isNotEmpty(algorithmExecuteRspList)) {
            for (AlgorithmExecuteRsp algorithmExecuteRsp : algorithmExecuteRspList) {
                if (BooleanUtil.isTrue(algorithmExecuteRsp.getIsSuccess())) {
                    List<String> strings = buildResultDes(algorithmExecuteRsp);
                    if (Objects.nonNull(strings)) {
                        lines.addAll(strings);
                    }
                }
            }
        }

        if (CollectionUtil.isEmpty(lines)) {
            return null;
        }

        int height;
        if (Objects.isNull(displayResolution)) {
            height = 1080;
            log.error("方法传入的分辨率为空，返回默认分辨率");
        } else {
            height = displayResolution.getHeight();
        }
        // 固定展示在左下角，边距10像素
        String presetDesc = StrUtil.join(System.lineSeparator(), lines);

        OSDItem osdItem = new OSDItem();
        osdItem.setDataType(DataType.STRING.name());
        osdItem.setCoords(new Point(10, height - 10));
        osdItem.setText(presetDesc);
        return osdItem;
    }

    private static List<String> buildResultDes(AlgorithmExecuteRsp algorithmExecuteRsp) {
        Map<String, OutputValueObj> output = algorithmExecuteRsp.getOutput();
        if (CollectionUtil.isEmpty(output)) {
            return null;
        }

        List<String> result = new ArrayList<>();
        for (OutputValueObj valueObj : output.values()) {
            String resultDes = valueObj.getResultDes();
            Object value = valueObj.getValue();
            if (StrUtil.isBlank(resultDes) || Objects.isNull(value)) {
                continue;
            }
            // 拼接结果描述和结果值、单位  resultDes : value unit
            String resultValue = resultDes + " : " + value;
            String unit = valueObj.getUnit();
            if (StrUtil.isNotBlank(unit)) {
                resultValue += "  " + unit;
            }

            if (StrUtil.isNotBlank(resultDes)) {
                result.add(resultValue);
            }
        }
        return result;
    }

    /**
     * 为OSD集合设置绘制配置信息（坏颜色、粗细等、报警状态）
     */
    public static void setOsdConfig(List<OSDItem> baseOsdItems, OsdConfig cvOsdConfig, Boolean isAlarm) {
        if (CollUtil.isNotEmpty(baseOsdItems)) {
            for (OSDItem osdItem : baseOsdItems) {
                osdItem.setOsdConfig(cvOsdConfig);
                if (Objects.nonNull(isAlarm)) {
                    osdItem.setIsAlarm(isAlarm);
                }
            }
        }
    }
}
