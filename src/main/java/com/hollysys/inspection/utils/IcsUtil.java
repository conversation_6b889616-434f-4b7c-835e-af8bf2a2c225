package com.hollysys.inspection.utils;

import cn.hutool.core.util.StrUtil;
import com.hollysys.inspection.config.exceptions.InspectionException;

public class IcsUtil {

    public static final int NAMESPACE_MAX_LEN = 50;
    public static final int TAG_MAX_LEN = 50;
    public static final int ITEM_MAX_LEN = 10;
    public static final String REGEX = "^[A-Za-z_0-9]+$";

    /**
     * 判断符合规则（名空间、点名、点项不能为空）
     *
     * @param namespace 名空间
     * @param tag       点名
     * @param item      点项
     */
    public static void checkIcsPointNotBlank(String namespace, String tag, String item) {
        if (StrUtil.hasBlank(namespace, tag, item)) {
            throw new InspectionException("点域、点名、点项不允许为空，检查后再试");
        }

        checkIcsPoint(namespace, tag, item);
    }

    /**
     * 判断符合规则（名空间、点名、点项可以为空）
     *
     * @param namespace 名空间
     * @param tag       点名
     * @param item      点项
     */
    public static void checkIcsPoint(String namespace, String tag, String item) {
        if (StrUtil.isNotBlank(namespace)) {
            checkNamespace(namespace);
        }
        if (StrUtil.isNotBlank(tag)) {
            checkTag(tag);
        }
        if (StrUtil.isNotBlank(item)) {
            checkItem(item);
        }
    }

    private static void checkNamespace(String namespace) {
        if (namespace.length() > NAMESPACE_MAX_LEN) {
            throw new InspectionException(StrUtil.format("点域长度超限，最多{}个字符，检查后再试", NAMESPACE_MAX_LEN));
        }
        if (!namespace.matches(REGEX)) {
            throw new InspectionException("点域只允许包含字母、下划线和数字，检查后再试");
        }
    }

    private static void checkTag(String tag) {
        if (tag.length() > TAG_MAX_LEN) {
            throw new InspectionException(StrUtil.format("点名长度超限，最多{}个字符，检查后再试", TAG_MAX_LEN));
        }
        if (!tag.matches(REGEX)) {
            throw new InspectionException("点名只允许包含字母、下划线和数字，检查后再试");
        }
    }

    private static void checkItem(String item) {
        if (item.length() > ITEM_MAX_LEN) {
            throw new InspectionException(StrUtil.format("项名长度超限，最多{}个字符，检查后再试", ITEM_MAX_LEN));
        }
        if (!item.matches(REGEX)) {
            throw new InspectionException("项名只允许包含字母、下划线和数字，检查后再试");
        }
    }

    public static String buildRedisKey(String namespace, String tag, String pointItem) {
        return StrUtil.format("{}:{}:{}", namespace, tag, pointItem);
    }
}
