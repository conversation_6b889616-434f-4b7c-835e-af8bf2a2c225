package com.hollysys.inspection.model.preset;

import com.hollysys.inspection.entity.InspAlgorithm;
import com.hollysys.inspection.entity.InspAlgorithmInstance;
import com.hollysys.inspection.entity.InspSceneDefinition;
import com.hollysys.inspection.model.algorithm.configure.SaveScheduleConfigReqModel;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class PresetConfigModel {

    private PresetInfoDetail presetDetailInfo;

    private List<InspSceneDefinition> sceneList;

    private List<InspAlgorithm> algorithmAllList;

    private List<InspAlgorithmInstance> algorithmSelectedList;

    private SaveScheduleConfigReqModel scheduleConfigInfo;
}
