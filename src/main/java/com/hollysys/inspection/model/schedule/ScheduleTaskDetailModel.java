package com.hollysys.inspection.model.schedule;

import com.hollysys.inspection.constants.ScheduleTaskMode;
import com.hollysys.inspection.entity.InspScheduleTask;
import com.hollysys.inspection.model.period.InspSchedulePeriodModel;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class ScheduleTaskDetailModel extends InspScheduleTask {

    private List<ScheduleTaskNodeModel> taskNodes;

    private InspSchedulePeriodModel schedulePeriods;

    private ScheduleTaskMode taskMode;
}
