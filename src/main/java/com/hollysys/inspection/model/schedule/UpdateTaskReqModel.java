package com.hollysys.inspection.model.schedule;

import com.hollysys.inspection.entity.InspPresetInfo;
import com.hollysys.inspection.entity.InspScheduleTask;
import com.hollysys.inspection.model.period.InspSchedulePeriodModel;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class UpdateTaskReqModel extends InspScheduleTask {

    private List<InspPresetInfo> taskNodes;

    private InspSchedulePeriodModel schedulePeriods;
}
