package com.hollysys.inspection.model.schedule;

import com.hollysys.inspection.entity.InspPresetInfo;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ScheduleTaskNodeModel extends InspPresetInfo {
    // 任务点ID
    private String taskNodeId;
    // 任务点名称
    private String label;

    // 排序字段
    private Integer sortNo;

    // 任务点运行状态
    private String status;

    // 任务点实时画面预览URL
    private String previewUrl;

    // 相机类型
    private String cameraType;

    // 任务点所属通道模式
    private String mode;

    // 当前预置点所属通道的实时图片
    private String channelPic;
}
