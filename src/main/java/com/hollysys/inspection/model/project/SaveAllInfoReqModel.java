package com.hollysys.inspection.model.project;

import com.hollysys.inspection.entity.InspChannelInfo;
import com.hollysys.inspection.entity.InspProject;
import com.hollysys.inspection.entity.InspProjectTreeNode;
import com.hollysys.inspection.model.algorithm.configure.SaveScheduleConfigReqModel;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class SaveAllInfoReqModel {
    /**
     * 工程节点基础信息（工程名称、描述）
     */
    private InspProject projectBaseInfo;

    /**
     * 通道节点基础信息（节点名称、描述）
     */
    private List<InspProjectTreeNode> channelBaseInfoList;

    /**
     * 通道配置信息（IP、端口...）
     */
    private List<InspChannelInfo> channelConfigInfoList;

    /**
     * 预置点节点基础信息（节点名称、描述）
     */
    private List<InspProjectTreeNode> presetBaseInfoList;

    /**
     * 预置点执行调度配置信息（停留时间、验证次数、失败策略...）
     */
    private List<SaveScheduleConfigReqModel> scheduleConfigModelList;
}
