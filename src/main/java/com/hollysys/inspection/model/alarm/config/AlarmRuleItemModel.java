package com.hollysys.inspection.model.alarm.config;

import cn.hutool.core.util.NumberUtil;
import com.hollysys.inspection.constants.AlarmLevelEnum;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;
import java.util.TreeSet;
import java.util.stream.Collectors;

@Getter
@Setter
public class AlarmRuleItemModel implements Comparable<AlarmRuleItemModel> {
    // 是否启用
    private boolean enable;
    // 报警级别
    private AlarmLevelEnum type;
    // 报警判断值
    private Object val;
    // 报警文本描述
    private String desc;
    // 报警提示文本
    private String placeHolder;

    @Override
    public int compareTo(@NotNull AlarmRuleItemModel obj) {
        // 按照AlarmLevelEnum枚举定义顺序进行排序
        AlarmLevelEnum[] values = AlarmLevelEnum.values();
        List<AlarmLevelEnum> alarmLevelEnumList = Arrays.stream(values).collect(Collectors.toList());
        AlarmLevelEnum type = obj.getType();

        int objIndex = alarmLevelEnumList.indexOf(type);
        int thisIndex = alarmLevelEnumList.indexOf(this.getType());

        return NumberUtil.compare(thisIndex, objIndex);
    }

    private static AlarmRuleItemModel createInstance(AlarmLevelEnum type, String placeHolder) {
        AlarmRuleItemModel infoModel = new AlarmRuleItemModel();
        infoModel.setType(type);
        infoModel.setPlaceHolder(placeHolder);
        return infoModel;
    }

    public static AdvanceAlarmRuleInfo newLimitAlarmRule() {
        TreeSet<AlarmRuleItemModel> ruleInfo = new TreeSet<>();
        ruleInfo.add(createInstance(AlarmLevelEnum.HH, "超过高高限"));
        ruleInfo.add(createInstance(AlarmLevelEnum.H, "超过高限"));
        ruleInfo.add(createInstance(AlarmLevelEnum.L, "低于低限"));
        ruleInfo.add(createInstance(AlarmLevelEnum.LL, "低于低低限"));

        AdvanceAlarmRuleInfo advanceAlarmInfo = new AdvanceAlarmRuleInfo();
        advanceAlarmInfo.setRuleInfo(ruleInfo);
        return advanceAlarmInfo;
    }

    public static AdvanceAlarmRuleInfo newDeviationAlarmRule() {
        TreeSet<AlarmRuleItemModel> ruleInfo = new TreeSet<>();
        ruleInfo.add(createInstance(AlarmLevelEnum.BIG_DEVIATION, "算法和DCS点项值的差值绝对值"));
        ruleInfo.add(createInstance(AlarmLevelEnum.SMALL_DEVIATION, "算法和DCS点项值的差值绝对值"));

        AdvanceAlarmRuleInfo advanceAlarmInfo = new AdvanceAlarmRuleInfo();
        advanceAlarmInfo.setRuleInfo(ruleInfo);
        return advanceAlarmInfo;
    }

    public static AdvanceAlarmRuleInfo newDeepAlarmRule() {
        TreeSet<AlarmRuleItemModel> ruleInfo = new TreeSet<>();
        ruleInfo.add(createInstance(AlarmLevelEnum.BOTH_GT, "设置值"));
        ruleInfo.add(createInstance(AlarmLevelEnum.BOTH_LT, "设置值"));

        AdvanceAlarmRuleInfo advanceAlarmInfo = new AdvanceAlarmRuleInfo();
        advanceAlarmInfo.setRuleInfo(ruleInfo);
        return advanceAlarmInfo;
    }

    public static AdvanceAlarmRuleInfo newStrAlarmRule() {
        TreeSet<AlarmRuleItemModel> ruleInfo = new TreeSet<>();
        ruleInfo.add(createInstance(AlarmLevelEnum.IS_EMPTY_STR, "值为空"));
        ruleInfo.add(createInstance(AlarmLevelEnum.NOT_EMPTY_STR, "值不为空"));
        ruleInfo.add(createInstance(AlarmLevelEnum.EQUALS, "值等于"));
        ruleInfo.add(createInstance(AlarmLevelEnum.NOT_EQUALS, "值不等于"));
        ruleInfo.add(createInstance(AlarmLevelEnum.REGEX_MATCHES, "正则匹配"));

        AdvanceAlarmRuleInfo advanceAlarmInfo = new AdvanceAlarmRuleInfo();
        advanceAlarmInfo.setRuleInfo(ruleInfo);
        return advanceAlarmInfo;
    }

    public static AdvanceAlarmRuleInfo newBooleanAlarmRule() {
        TreeSet<AlarmRuleItemModel> ruleInfo = new TreeSet<>();
        ruleInfo.add(createInstance(AlarmLevelEnum.IS_EMPTY_BOOL, "值为空"));
        ruleInfo.add(createInstance(AlarmLevelEnum.TRUE, "值为是（true）"));
        ruleInfo.add(createInstance(AlarmLevelEnum.FALSE, "值为否（false）"));

        AdvanceAlarmRuleInfo advanceAlarmInfo = new AdvanceAlarmRuleInfo();
        advanceAlarmInfo.setRuleInfo(ruleInfo);
        return advanceAlarmInfo;
    }
}
