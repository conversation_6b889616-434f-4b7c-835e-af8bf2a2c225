package com.hollysys.inspection.model.alarm;

import com.hollysys.inspection.entity.InspAlarmRecord;
import lombok.Getter;
import lombok.Setter;

/**
 * 报警列表单条对象
 */
@Setter
@Getter
public class AlarmListItemModel extends InspAlarmRecord {
    // 通道名称
    private String channelName;
    // 预置点名称
    private String presetName;
    // 算法实例名称（包含算法实例序号）
    private String algorithmInstanceName;
    // 算法出参名称
    private String outputParamName;
}
