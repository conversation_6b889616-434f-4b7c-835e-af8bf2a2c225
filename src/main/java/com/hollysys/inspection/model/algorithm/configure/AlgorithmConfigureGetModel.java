package com.hollysys.inspection.model.algorithm.configure;

import com.hollysys.inspection.model.algorithm.correct.CorrectParamSaveModel;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class AlgorithmConfigureGetModel extends AlgorithmConfigureSaveModel {
    // 算法是否需要ROI
    private Boolean roiRequired;

    private String roiDrawType;

    // 算法是否需要批量图片输入
    private Boolean isBatchProcessing;

    // 算法描述
    private String algorithmDes;

    private List<SceneForParamConfigModel> sceneList;

    // 是否进行模板匹配
    private Boolean matchEnable;

    // 是否进行透视变换
    private Boolean correctEnable;

    // 透视变换参数
    private CorrectParamSaveModel correctParam;
}
