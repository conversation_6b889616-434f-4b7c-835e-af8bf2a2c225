package com.hollysys.inspection.model.algorithm.py;

import com.alibaba.fastjson.annotation.JSONField;

public class AlgorithmParam extends Base {

    @JSONField(name = "cn_name")
    private String cnName;

    private Boolean nullable;

    @J<PERSON><PERSON>ield(name = "val_type")
    private String valType;

    @JSONField(name = "val_range")
    private Object valRange;

    private String value;

    public String getCnName() {
        return cnName;
    }

    public void setCnName(String cnName) {
        this.cnName = cnName;
    }

    public Boolean getNullable() {
        return nullable;
    }

    public void setNullable(Boolean nullable) {
        this.nullable = nullable;
    }

    public String getValType() {
        return valType;
    }

    public void setValType(String valType) {
        this.valType = valType;
    }

    public Object getValRange() {
        return valRange;
    }

    public void setValRange(Object valRange) {
        this.valRange = valRange;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
