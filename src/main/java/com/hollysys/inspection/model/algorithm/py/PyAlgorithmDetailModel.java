package com.hollysys.inspection.model.algorithm.py;


import com.alibaba.fastjson.annotation.JSONField;

import java.util.LinkedHashMap;
import java.util.List;

public class PyAlgorithmDetailModel {
    private List<Classifies> classifies;

    private AlgorithmInfo info;

    private LinkedHashMap<String, AlgorithmParam> params;

    private List<Strategies> strategies;

    @JSONField(name = "output_define")
    private OutputDefineModel outputDefine;

    @JSONField(name = "sub_list")
    private List<SubAlgorithmInfo> subList;

    public List<Classifies> getClassifies() {
        return classifies;
    }

    public void setClassifies(List<Classifies> classifies) {
        this.classifies = classifies;
    }

    public AlgorithmInfo getInfo() {
        return info;
    }

    public void setInfo(AlgorithmInfo info) {
        this.info = info;
    }

    public LinkedHashMap<String, AlgorithmParam> getParams() {
        return params;
    }

    public void setParams(LinkedHashMap<String, AlgorithmParam> params) {
        this.params = params;
    }

    public List<Strategies> getStrategies() {
        return strategies;
    }

    public void setStrategies(List<Strategies> strategies) {
        this.strategies = strategies;
    }

    public List<SubAlgorithmInfo> getSubList() {
        return subList;
    }

    public void setSubList(List<SubAlgorithmInfo> subList) {
        this.subList = subList;
    }

    public OutputDefineModel getOutputDefine() {
        return outputDefine;
    }

    public void setOutputDefine(OutputDefineModel outputDefine) {
        this.outputDefine = outputDefine;
    }
}
