package com.hollysys.inspection.model.algorithm.execute;

import com.hollysys.inspection.entity.InspAlgorithmParamInstance;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 算法识别入参模型
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class AlgorithmExecutePyReq {
    /**
     * 算法CODE
     */
    private String code;

    /**
     * 实时图片路径
     */
    private List<String> images;

    /**
     * 算法绑定ROI
     */
    private RoiRqePyModel roi;

    /**
     * 场景基准图路径
     */
    private String sceneImage;

    /**
     * 用户上传或预置算法
     */
    private String userOrSystem;

    /**
     * 算法输入参数
     */
    private List<InspAlgorithmParamInstance> inputParam;
}
