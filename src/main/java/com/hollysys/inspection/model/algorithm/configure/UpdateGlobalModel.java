package com.hollysys.inspection.model.algorithm.configure;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Data
@Valid
public class UpdateGlobalModel {
    @NotBlank
    @Length(max = 32, min = 32)
    private String id;
    @NotBlank
    @Length(max = 32, min = 32)
    private String algorithmInstanceId;
    @Pattern(regexp = "boolean|double|string")
    @NotBlank
    private String valType;
    @Length(max = 100, min = 5)
    private String valName;
    @Length(max = 100)
    private String nickname;
    @Length(max = 255)
    private String description;
    @NotNull
    private Boolean isBind;
    @Length(max = 255)
    private String outputValTemp;
}
