package com.hollysys.inspection.model.algorithm.execute;

import com.hollysys.inspection.model.algorithm.param.AlgorithmParamConst;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class RoiRqePyModel {
    // ROI坐标值
    private Object value;
    // ROI类型
    private String dataType;
    // 参数值约束
    private AlgorithmParamConst constraints;

    public static RoiRqePyModel newInst(Object value, String dataType, Boolean required) {
        RoiRqePyModel roiRqePyModel = new RoiRqePyModel();
        roiRqePyModel.setValue(value);
        roiRqePyModel.setDataType(dataType);

        AlgorithmParamConst algorithmParamConst = new AlgorithmParamConst();
        algorithmParamConst.setRequired(required);
        roiRqePyModel.setConstraints(algorithmParamConst);

        return roiRqePyModel;
    }
}
