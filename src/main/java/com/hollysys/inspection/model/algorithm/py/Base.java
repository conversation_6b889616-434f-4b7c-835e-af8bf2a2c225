package com.hollysys.inspection.model.algorithm.py;

import com.alibaba.fastjson.annotation.JSONField;

public class Base {
    @JSONField(name = "_id")
    private Integer id;

    @JSONField(name = "_name")
    private String name;

    @JSONField(name = "_description")
    private String description;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
