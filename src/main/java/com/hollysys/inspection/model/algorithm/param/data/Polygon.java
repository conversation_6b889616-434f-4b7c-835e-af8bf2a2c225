package com.hollysys.inspection.model.algorithm.param.data;

import cn.hutool.json.JSONUtil;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.constants.algorithm.param.DataType;

import java.util.ArrayList;
import java.util.List;

/**
 * 存储多点坐标
 */
public class Polygon extends ArrayList<Point> {

    public static Polygon buildByObj(Object obj) {
        if (!DataType.POLYGON.checkType(obj)) {
            throw new InspectionException("矩形参数值格式不正确");
        }

        List<Point> list = JSONUtil.toList(JSONUtil.toJsonStr(obj), Point.class);
        Polygon polygon = new Polygon();
        polygon.addAll(list);
        return polygon;
    }
}
