package com.hollysys.inspection.model.algorithm.configure;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Data
public class UpdateOutputModel {
    @NotBlank
    @Length(max = 32, min = 32)
    private String id;
    @NotBlank
    @Length(max = 32, min = 32)
    private String algorithmInstanceId;
    @NotBlank
    @Pattern(regexp = "ONLY_QUALITY|VAL|ALARM")
    private String outputValType;
    @NotNull
    private Boolean isWriteHistory;

}
