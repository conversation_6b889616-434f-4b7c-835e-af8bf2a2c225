package com.hollysys.inspection.model.algorithm.param.data;

import cn.hutool.json.JSONUtil;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.constants.algorithm.param.DataType;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class Circle {
    private Point center;

    private Integer radius;

    public static Circle buildByObj(Object obj) {
        if (!DataType.CIRCLE.checkType(obj)) {
            throw new InspectionException("圆形参数值格式不正确");
        }
        return JSONUtil.toBean(JSONUtil.toJsonStr(obj), Circle.class);
    }
}
