package com.hollysys.inspection.model.algorithm.param.data;

import cn.hutool.json.JSONUtil;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.constants.algorithm.param.DataType;
import lombok.Getter;
import lombok.Setter;

/**
 * 存储点坐标 [x, y]
 */
@Getter
@Setter
public class Point {

    private Integer x;
    private Integer y;

    public Point() {
    }

    public Point(Integer x, Integer y) {
        this.x = x;
        this.y = y;
    }

    public static Point buildByObj(Object obj) {
        if (!DataType.POINT.checkType(obj)) {
            throw new InspectionException("点形参数值格式不正确");
        }
        return JSONUtil.toBean(JSONUtil.toJsonStr(obj), Point.class);
    }
}
