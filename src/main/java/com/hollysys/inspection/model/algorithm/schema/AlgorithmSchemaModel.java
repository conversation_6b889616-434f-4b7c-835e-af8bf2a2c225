package com.hollysys.inspection.model.algorithm.schema;

import com.hollysys.inspection.entity.InspAlgorithm;
import com.hollysys.inspection.entity.InspAlgorithmParam;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class AlgorithmSchemaModel {
    private InspAlgorithm algorithmMetadata;
    private List<InspAlgorithmParam> input;
    private List<InspAlgorithmParam> output;
}
