package com.hollysys.inspection.model.algorithm.execute.record;

import com.hollysys.inspection.entity.InspRecordAlgorithm;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
public class AlgorithmRecordItemModel extends InspRecordAlgorithm {
    // 通道名称
    private String channelName;
    // 预置点名称
    private String presetName;
    // 算法实例名称（包含算法实例序号）
    private String algorithmInstanceName;
    // 将算法结果转化为map
    private Map<String, Object> algorithmValue;
}
