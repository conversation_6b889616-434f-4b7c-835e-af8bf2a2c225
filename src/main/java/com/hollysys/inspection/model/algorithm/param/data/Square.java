package com.hollysys.inspection.model.algorithm.param.data;

import cn.hutool.json.JSONUtil;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.constants.algorithm.param.DataType;

import java.util.ArrayList;
import java.util.List;

/**
 * 存储四点坐标 [左上、左下、右下、右上] 从左上点开始逆时针顺序
 */
public class Square extends ArrayList<Point> {
    public static final int MAX_ARRAY_SIZE = 4;

    public Square() {
    }

    public Square(int width, int height) {
        this.clear();
        add(new Point(0, 0));
        add(new Point(0, height));
        add(new Point(width, height));
        add(new Point(width, 0));
    }

    public static Square buildByObj(Object obj) {
        if (!DataType.SQUARE.checkType(obj)) {
            throw new InspectionException("矩形参数值格式不正确");
        }
        List<Point> list = JSONUtil.toList(JSONUtil.toJsonStr(obj), Point.class);
        Square square = new Square();
        square.addAll(list);
        return square;
    }

    @Override
    public boolean add(Point point) {
        if (hugeCapacity()) {
            throw new InspectionException("对象元素数量超限");
        }
        return super.add(point);
    }

    private boolean hugeCapacity() {
        int size = this.size();
        return size == MAX_ARRAY_SIZE;
    }
}
