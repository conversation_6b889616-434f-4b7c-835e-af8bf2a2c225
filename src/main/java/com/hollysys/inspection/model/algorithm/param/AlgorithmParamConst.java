package com.hollysys.inspection.model.algorithm.param;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 算法参数值约束对象
 */
@Getter
@Setter
public class AlgorithmParamConst {
    // 是否必填
    private Boolean required;
    // 值校验正则
    private String regexPattern;
    // 最大长度（字符串类型参数有效）
    private Integer maxLength;
    // 最小长度（字符串类型参数有效）
    private Integer minLength;
    // 最大值，包含（数字类型参数有效）
    private Float max;
    // 最小值，包含（数字类型参数有效）
    private Float min;
    // 步长（数字类型参数有效）
    private Float precision;
    // 下拉选项（下拉框类型有效）
    private List<SelectorOption> options;
}
