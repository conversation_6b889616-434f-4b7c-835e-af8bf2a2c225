package com.hollysys.inspection.model.algorithm.param.data;

import cn.hutool.json.JSONUtil;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.constants.algorithm.param.DataType;
import lombok.Getter;
import lombok.Setter;

/**
 * 椭圆
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class Ellipse {

    /**
     * 圆心坐标 [x, y]
     */
    private Point center;

    /**
     * 椭圆 [短轴, 长轴]
     */
    private Integer[] axes;

    /**
     * 旋转角度，垂直方形为0度，顺时针计算
     */
    private Float angle;

    public static Ellipse buildByObj(Object obj) {
        if (!DataType.ELLIPSE.checkType(obj)) {
            throw new InspectionException("椭圆形参数值格式不正确");
        }
        return JSONUtil.toBean(JSONUtil.toJsonStr(obj), Ellipse.class);
    }
}
