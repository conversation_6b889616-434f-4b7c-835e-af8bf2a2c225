package com.hollysys.inspection.model.algorithm.py;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class AlgorithmInfo extends Base {
    private String code;

    @JSONField(name = "cn_name")
    private String cnName;

    @JSONField(name = "alarm_support")
    private Boolean alarmSupport;

    @JSONField(name = "type_")
    private String type;

    @JSONField(name = "is_override_alarm")
    private Boolean isOverrideAlarm;

}
