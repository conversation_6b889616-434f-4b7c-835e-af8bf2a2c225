package com.hollysys.inspection.model.algorithm.param.data;

import cn.hutool.json.JSONUtil;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.constants.algorithm.param.DataType;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class Range {
    private Float start;

    private Float end;

    public static Range buildByObj(Object obj) {
        if (!DataType.RANGE.checkType(obj)) {
            throw new InspectionException("量程类型参数值格式不正确");
        }
        return JSONUtil.toBean(JSONUtil.toJsonStr(obj), Range.class);
    }
}
