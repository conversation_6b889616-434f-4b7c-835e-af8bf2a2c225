package com.hollysys.inspection.model.algorithm.configure;

import com.hollysys.inspection.entity.InspAlgorithmParamInstance;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
public class AlgorithmConfigureSaveModel {
    private String presetId;

    private String algorithmId;

    private String algorithmInstanceId;

    private Object roi;

    // 批量图片输入,截图间隔(单位秒)
    private Integer cutPicInterval;

    // 批量图片输入,图片张数
    private Integer cutPicSize;

    // 算法实例参数map
    private Map<String, List<InspAlgorithmParamInstance>> paramMap;

    // 是否进行模板匹配
    private Boolean matchEnable;
}
