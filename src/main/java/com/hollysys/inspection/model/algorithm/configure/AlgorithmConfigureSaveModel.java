package com.hollysys.inspection.model.algorithm.configure;

import com.hollysys.inspection.entity.InspAlgorithmParamInstance;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class AlgorithmConfigureSaveModel {
    private String presetId;

    private String sceneId;

    private String algorithmId;

    private String algorithmInstanceId;

    private Object roi;

    // 批量图片输入,截图间隔(单位秒)
    private Integer cutPicInterval;

    // 批量图片输入,图片张数
    private Integer cutPicSize;

    private List<InspAlgorithmParamInstance> paramList;

    // 是否进行模板匹配
    private Boolean matchEnable;
}
