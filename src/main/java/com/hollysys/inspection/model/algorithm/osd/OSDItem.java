package com.hollysys.inspection.model.algorithm.osd;

import com.hollysys.inspection.model.algorithm.execute.OutputValueObj;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class OSDItem implements Serializable {
    // square(矩形)、circle(圆形)、ellipse(椭圆)、line(线)、point(点)、text(文本)
    private String dataType;
    // 置信度
    private Float score;
    // 绘制文本（只有当dataType=SQUARE有效，将此内容绘制到矩形周围）
    private String text;
    // 绘制文本对象（由算法返回，包含绘制文本、单位、精度），算法返回后需要组织成text绘制在OSD
    private OutputValueObj textObj;
    // 文本绘制坐标位置（类型为文本时，此值有含义，值为left,right和top,bottom互相组合，例如：left:top，表示当前文本以coordinate为左上角为起点绘制）
    private String textPosition = "left:bottom";
    // 图形绘制坐标
    private Object coords;
    // 是否报警
    private Boolean isAlarm;
    // OSD绘制颜色相关配置
    private OsdConfig osdConfig;
}
