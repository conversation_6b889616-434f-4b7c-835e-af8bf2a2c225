package com.hollysys.inspection.model.algorithm.osd;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class OsdConfig implements Serializable {
    // 报警颜色
    private int[] colorWarn = {0, 0, 255};

    // 正常颜色
    private int[] colorNormal = {255, 0, 0};

    // 文字绘制颜色
    private int[] fontColor = {0, 0, 0};

    // 文字背景绘制颜色
    private int[] fontBgColor = {255, 255, 255};

    // 文字大小
    private int fontSize = 30;

    // 画笔粗细
    private int fontThickness = 3;

    // 前端OSD显示时间（秒）
    private int webOsdTimeout = 10;
}
