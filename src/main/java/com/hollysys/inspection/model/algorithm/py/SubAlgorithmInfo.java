package com.hollysys.inspection.model.algorithm.py;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

public class SubAlgorithmInfo extends Base {

    private List<Classifies> classifies;

    private List<Strategies> strategies;

    @JSONField(name = "privacy_params")
    private List<AlgorithmParam> privacyParams;
    public List<Classifies> getClassifies() {
        return classifies;
    }

    public void setClassifies(List<Classifies> classifies) {
        this.classifies = classifies;
    }

    public List<Strategies> getStrategies() {
        return strategies;
    }

    public void setStrategies(List<Strategies> strategies) {
        this.strategies = strategies;
    }

    public List<AlgorithmParam> getPrivacyParams() {
        return privacyParams;
    }

    public void setPrivacyParams(List<AlgorithmParam> privacyParams) {
        this.privacyParams = privacyParams;
    }
}
