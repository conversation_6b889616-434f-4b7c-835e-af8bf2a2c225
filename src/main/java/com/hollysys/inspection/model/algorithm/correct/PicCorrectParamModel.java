package com.hollysys.inspection.model.algorithm.correct;

import cn.hutool.json.JSONUtil;
import com.hollysys.inspection.model.algorithm.param.data.Square;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Objects;

@Setter
@Getter
public class PicCorrectParamModel implements Serializable {

    private Boolean available;

    private Square fromSquare;

    private Square toSquare;

    private Float ratio;

    public static PicCorrectParamModel buildFrom(CorrectParamSaveModel reqModel) {
        PicCorrectParamModel picCorrectParamModel = new PicCorrectParamModel();
        picCorrectParamModel.setRatio(reqModel.getRatio());
        picCorrectParamModel.setFromSquare(reqModel.getFromSquare());

        Integer toSquareWidthF = reqModel.getToSquareWidth();
        Integer toSquareHeightF = reqModel.getToSquareHeight();
        if (Objects.nonNull(toSquareWidthF) && Objects.nonNull(toSquareHeightF)) {
            int toSquareWidth = toSquareWidthF;
            int toSquareHeight = toSquareHeightF;
            Square toSquare = new Square(toSquareWidth, toSquareHeight);
            picCorrectParamModel.setToSquare(toSquare);
        }
        return picCorrectParamModel;
    }

    public static boolean equals(PicCorrectParamModel model1, PicCorrectParamModel model2) {
        if (model1 == null && model2 == null) {
            return true;
        }
        if (model1 == null || model2 == null) {
            return false;
        }
        Float ratio1 = model1.getRatio();
        Float ratio2 = model2.getRatio();
        if (!Objects.equals(ratio1, ratio2)) {
            return false;
        }

        Square fromSquare1 = model1.getFromSquare();
        Square fromSquare2 = model2.getFromSquare();
        if (!Objects.equals(JSONUtil.toJsonStr(fromSquare1), JSONUtil.toJsonStr(fromSquare2))) {
            return false;
        }

        Square toSquare1 = model1.getToSquare();
        Square toSquare2 = model2.getToSquare();
        return Objects.equals(JSONUtil.toJsonStr(toSquare1), JSONUtil.toJsonStr(toSquare2));
    }

}
