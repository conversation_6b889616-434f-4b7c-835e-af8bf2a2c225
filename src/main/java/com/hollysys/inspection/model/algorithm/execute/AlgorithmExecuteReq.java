package com.hollysys.inspection.model.algorithm.execute;

import com.hollysys.inspection.entity.InspAlgorithmParamInstance;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 算法识别入参模型
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class AlgorithmExecuteReq {
    /**
     * 请求客户端唯一标识，用来后端发送执行日志到前端的识别KEY
     */
    private String clientKey;

    /**
     * 算法实例ID
     */
    private String algorithmInstanceId;

    /**
     * 场景ID
     */
    private String sceneId;

    /**
     * 实时图片路径
     */
    private List<String> images;

    /**
     * 算法绑定ROI
     */
    private Object roi;

    /**
     * 算法输入参数
     */
    private List<InspAlgorithmParamInstance> paramList;
}
