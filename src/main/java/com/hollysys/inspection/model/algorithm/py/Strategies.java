package com.hollysys.inspection.model.algorithm.py;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.Map;

public class Strategies extends Base {
    private String code;

    @JSONField(name = "cn_name")
    private String cnName;

    @JSONField(name = "kv_params")
    private Map<String, String> kvParams;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCnName() {
        return cnName;
    }

    public void setCnName(String cnName) {
        this.cnName = cnName;
    }

    public Map<String, String> getKvParams() {
        return kvParams;
    }

    public void setKvParams(Map<String, String> kvParams) {
        this.kvParams = kvParams;
    }
}
