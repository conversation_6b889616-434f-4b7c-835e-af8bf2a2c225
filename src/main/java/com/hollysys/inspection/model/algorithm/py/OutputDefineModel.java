package com.hollysys.inspection.model.algorithm.py;

import com.alibaba.fastjson.annotation.JSONField;
import com.hollysys.inspection.model.algorithm.configure.InspOutputAlarmInfoModel;
import lombok.Data;

import java.util.List;

@Data
public class OutputDefineModel {
    private String _description;
    private int _id;
    private String _name;
    @JSONField(name = "type_")
    private String type;
    private List<InspOutputAlarmInfoModel> ruleInfo;
}
