package com.hollysys.inspection.model.algorithm.execute;

import cn.hutool.core.util.StrUtil;
import com.hollysys.inspection.utils.execute.ExecuteRspUtil;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

@Getter
@Setter
public class OutputValueObj {
    // 算法结果值
    private Object value;
    // 算法结果单位
    private String unit;
    // 算法结果描述
    private String resultDes;
    // 算法结果保留精度（当结果类型为数字时，按此字段进行位数保留）
    private Integer resultPrecision;

    /**
     * 拼接结果和单位
     */
    public String buildResultOsdText() {
        Object valueByScale = ExecuteRspUtil.getValueByScale(this);
        if (Objects.isNull(valueByScale)) {
            return null;
        }

        String text = valueByScale.toString();
        if (StrUtil.isNotBlank(unit)) {
            text += unit;
        }
        return text;
    }
}
