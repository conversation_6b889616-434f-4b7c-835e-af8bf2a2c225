package com.hollysys.inspection.model.algorithm.execute;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.hollysys.inspection.model.alarm.config.AlarmRuleItemModel;
import com.hollysys.inspection.model.algorithm.osd.OSDItem;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 算法识别响应模型
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class AlgorithmExecuteRsp {
    /**
     * 算法出参结果（由Python算法服务返回）
     */
    private Map<String, OutputValueObj> output;

    /**
     * 最终返回的是需要绘制的OSD条目（包含算法服务返回的和入参中需要绘制的），由java测会整合入参、出参全部OSD
     */
    private List<OSDItem> osdInfo;

    /**
     * 算法执行成功与否，由java测设置
     */
    private Boolean isSuccess;

    /**
     * 算法执行失败原因
     */
    private String msg;

    /**
     * 算法执行结果图片地址，如果算法未返回，则取第一张输入图片
     */
    private String resultImgUrl;

    /**
     * 算法结果值报警判断，由java测设置
     */
    private Map<String, AlarmRuleItemModel> alarmResultMap;

    /**
     * 将算法结果转化为简单map结构
     */
    public Map<String, Object> toValueMap() {
        Map<String, Object> map = new HashMap<>();
        if (CollectionUtil.isNotEmpty(output)) {
            for (Map.Entry<String, OutputValueObj> objEntry : output.entrySet()) {
                map.put(objEntry.getKey(), objEntry.getValue().getValue());
            }
        }
        return MapUtil.sort(map);
    }
}
