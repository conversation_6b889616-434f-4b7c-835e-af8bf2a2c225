package com.hollysys.inspection.model.algorithm.param.data;

import cn.hutool.json.JSONUtil;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.constants.algorithm.param.DataType;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class Line {

    private Point start;

    private Point end;

    public static Line buildByObj(Object obj) {
        if (!DataType.LINE.checkType(obj)) {
            throw new InspectionException("直线形参数值格式不正确");
        }
        return JSONUtil.toBean(JSONUtil.toJsonStr(obj), Line.class);
    }
}
