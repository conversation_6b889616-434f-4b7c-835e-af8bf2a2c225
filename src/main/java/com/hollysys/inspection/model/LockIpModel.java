package com.hollysys.inspection.model;

import com.hollysys.inspection.constants.CommonCache;

public class LockIpModel {
    private String ip;

    private LockIpModel(String ip) {
        this.ip = ip;
    }

    public static LockIpModel getInstance(String ip) {
        LockIpModel lockIpModel = CommonCache.IP_PIC.get(ip);
        if (lockIpModel == null) {
            lockIpModel = new LockIpModel(ip);
            CommonCache.IP_PIC.put(ip, lockIpModel);
            return lockIpModel;
        }
        return lockIpModel;
    }

    public String ip() {
        return ip;
    }

    public LockIpModel setIp(String ip) {
        this.ip = ip;
        return this;
    }
}
