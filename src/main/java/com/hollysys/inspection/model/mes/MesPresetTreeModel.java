package com.hollysys.inspection.model.mes;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: zhangjianqing
 * @Create: 2024/12/25 14:11
 **/
@Data
@ApiModel("摄像头预置点树型结构传输对象")
public class MesPresetTreeModel implements Serializable {

	@ApiModelProperty("唯一标识")
	private String id;

	@ApiModelProperty("父级ID")
	private String parentId;

	@ApiModelProperty("名称")
	private String nodeName;

	@ApiModelProperty("类型")
	private String nodeType;

	@ApiModelProperty("描述")
	private String description;
}
