package com.hollysys.inspection.model.mes;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class ListAlarmItem {
    // 报警ID
    private String id;
    // 通道id
    private String channelId;
    // 通道名称
    private String channelName;
    // 预置点id
    private String presetId;
    // 预置点名称
    private String presetName;
    // 告警图片地址
    private List<String> imgPath;
    // 开始时间
    private String time;
    // 识别类型
    private String type;
    // 是否确认
    private Boolean confirm;
    // 确认时间
    private String confirmTime;
    // 恢复时间
    private String restoreTime;
    // 报警内容
    private String msg;
}
