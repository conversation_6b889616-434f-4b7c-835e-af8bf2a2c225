package com.hollysys.inspection.model.dbbase;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Objects;

/**
 * 数据底座接口统一返回对象
 **/
@Getter
@Setter
public class DbBaseRespModel implements Serializable {

    // 返回码，0成功，其他失败
    private int code;

    // 返回信息描述
    private String message;

    public static boolean isSuccess(DbBaseRespModel respModel) {
        if (Objects.isNull(respModel)) {
            return false;
        }
        return 0 == respModel.getCode();
    }
}
