package com.hollysys.inspection.model.dbbase;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @Author: zhangjianqing
 * @Create: 2024/8/29 15:55
 **/
@Getter
@Setter
public class ReadPointDataRespModel implements Serializable {

		//点域
		private String nameSpace;

		//点名
		private String tag;

		//点项
		private String item;

		//原始测点值变化时间，Unix时间戳，精确到毫秒
		private long st;

		//实时服务返回的时间，Unix时间戳，精确到毫秒
		private long t;

		//测点值质量位
		private int q;

		//值,Json基本数据类型，例如：12.1、 false
		private Object v;
}
