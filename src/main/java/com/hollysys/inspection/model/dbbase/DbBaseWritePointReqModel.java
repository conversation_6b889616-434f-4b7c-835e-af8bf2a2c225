package com.hollysys.inspection.model.dbbase;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: zhangjianqing
 * @Create: 2024/8/29 15:55
 **/
@Getter
@Setter
public class DbBaseWritePointReqModel implements Serializable {

	// 写值对象列表，单次最大写值点数为2000。
	private List<WriteDataValue> data;

	@Getter
	@Setter
	public static class WriteDataValue implements Serializable {

		//测点名，格式为“命名空间:测点名”，如“100:AI001.PV”。
		private String nodeId;

		//值,Json基本数据类型，例如：12.1、 false
		private Object value;
	}
}
