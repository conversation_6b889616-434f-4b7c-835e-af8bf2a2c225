package com.hollysys.inspection.model.tree;

import com.hollysys.inspection.entity.InspAlgorithm;
import com.hollysys.inspection.entity.InspProjectTreeNode;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 算法树节点信息模型
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class AlgorithmTreeNodeModel extends InspAlgorithm {
    // 算法分类
    private String classification;

    // 算法授权列表
    private List<String> authList;

    // 算法列表
    private List<InspAlgorithm> algorithmList;

}
