package com.hollysys.inspection.model.tree;

import com.hollysys.inspection.entity.InspProjectTreeNode;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 工程树节点信息模型
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class InspProjectTreeNodeModel extends InspProjectTreeNode {

    // 子节点
    private List<InspProjectTreeNodeModel> children;

    // 通道相机类型
    private String deviceType;
}
