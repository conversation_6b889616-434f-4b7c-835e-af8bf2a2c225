package com.hollysys.inspection.model.tree;

import com.hollysys.inspection.entity.InspChannelInfo;
import com.hollysys.inspection.entity.InspPresetInfo;
import com.hollysys.inspection.entity.InspProjectTreeNode;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class InspThirdAppProjectTreeNodeModal extends InspProjectTreeNode {
    // 子节点
    private List<InspThirdAppProjectTreeNodeModal> children;

    // 预置点信息
    private InspPresetInfo presetInfo;
    // 通道信息
    private InspChannelInfo channelInfo;

    // 通道相机类型
    private String deviceType;
}
