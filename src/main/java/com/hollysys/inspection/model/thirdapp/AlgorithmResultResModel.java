package com.hollysys.inspection.model.thirdapp;

import com.hollysys.inspection.model.algorithm.execute.OutputValueObj;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AlgorithmResultResModel {
    private String presetId;      // 预置点ID
    private String algorithmInstanceId;   // 算法实例ID
    private String algorithmId;   // 算法ID
    private Date timestamp; // 结果时间戳
    private boolean isAlarm; // 是否为报警结果
    private Map<String, OutputValueObj> output; // 算法结果数据
}
