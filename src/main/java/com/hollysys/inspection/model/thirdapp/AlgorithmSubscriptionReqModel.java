package com.hollysys.inspection.model.thirdapp;

import lombok.Data;
import org.hibernate.validator.constraints.URL;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;
@Data
public class AlgorithmSubscriptionReqModel {

    // 发送方式
    @NotBlank(message = "订阅类型不能为空")
    @Pattern(regexp = "[01]", message = "订阅类型必须是0或1")
    private String subscriptionType;

    // 预置点
    @NotNull(message = "预置点不能为空")
    private String presetId;

    // 订阅回调地址
    @NotBlank(message = "回调地址不能为空")
    @URL(message = "回调地址必须是有效的URL格式")
    private String callbackUrl;

    // 接口请求方式
    @NotBlank(message = "请求方式不能为空")
    @Pattern(regexp = "GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS",
            flags = Pattern.Flag.CASE_INSENSITIVE,
            message = "请求方式必须是有效的HTTP方法")
    private String httpMethod;

    // 算法id
    private String algorithmId;

    // 算法实例id集合
    private List<String> algorithmInstanceIds;
}
