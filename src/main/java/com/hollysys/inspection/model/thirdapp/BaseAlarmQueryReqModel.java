package com.hollysys.inspection.model.thirdapp;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.constants.InspConstants;
import com.hollysys.inspection.entity.InspAlarmRecord;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
public class BaseAlarmQueryReqModel {
    protected static final int MAX_PAGE_SIZE = 1000; // 最大页大小
    protected static final int MIN_PAGE_SIZE = 1;    // 最小页大小
    protected static final int DEFAULT_PAGE_SIZE = 10; // 默认页大小
    protected static final int DEFAULT_CURRENT_PAGE = 1; // 默认当前页

    private String nodeId;
    private String startTime;
    private String endTime;
    private String searchStr;
    private String confirm;
    private String falseAlarm;

    @NotNull(message = "current 不能为空")
    @Min(value = DEFAULT_CURRENT_PAGE, message = "页码必须大于等于" + DEFAULT_CURRENT_PAGE)
    private Integer current = DEFAULT_CURRENT_PAGE;

    @NotNull(message = "size 不能为空")
    @Min(value = MIN_PAGE_SIZE, message = "每页数量必须大于等于" + MIN_PAGE_SIZE)
    @Max(value = MAX_PAGE_SIZE, message = "每页数量不能超过" + MAX_PAGE_SIZE)
    private Integer size = DEFAULT_PAGE_SIZE;

    // 通用的校验方法
    public void validate() {
        // 校验 confirm
        if (StrUtil.isNotBlank(confirm) && !("true".equalsIgnoreCase(confirm) || "false".equalsIgnoreCase(confirm))) {
            throw new InspectionException("confirm 参数格式错误，应为布尔值或其字符串表示");
        }
        // 校验 falseAlarm
        if (StrUtil.isNotBlank(falseAlarm) && !("true".equalsIgnoreCase(falseAlarm) || "false".equalsIgnoreCase(falseAlarm))) {
            throw new InspectionException("falseAlarm 参数格式错误，应为布尔值或其字符串表示");
        }
        // 校验 current 和 size
        if (this.current == null) {
            throw new InspectionException("current 不能为空");
        }
        if (this.current < DEFAULT_CURRENT_PAGE) {
            throw new InspectionException("页码必须大于等于" + DEFAULT_CURRENT_PAGE);
        }
        if (this.size == null) {
            throw new InspectionException("size 不能为空");
        }
        if (this.size < MIN_PAGE_SIZE) {
            throw new InspectionException("每页数量必须大于等于" + MIN_PAGE_SIZE);
        }
        if (this.size > MAX_PAGE_SIZE) {
            throw new InspectionException("每页数量不能超过" + MAX_PAGE_SIZE);
        }
        // 校验时间格式
        if (StrUtil.isNotBlank(startTime)) {
            try {
                DateUtil.parse(startTime, InspConstants.yyyy_MM_ddHHmmss);
            } catch (Exception e) {
                throw new InspectionException("startTime 格式错误，应为 " + InspConstants.yyyy_MM_ddHHmmss);
            }
        }
        if (StrUtil.isNotBlank(endTime)) {
            try {
                DateUtil.parse(endTime, InspConstants.yyyy_MM_ddHHmmss);
            } catch (Exception e) {
                throw new InspectionException("endTime 格式错误，应为 " + InspConstants.yyyy_MM_ddHHmmss);
            }
        }
        // 时间区间校验
        if (StrUtil.isNotBlank(startTime) && StrUtil.isNotBlank(endTime)) {
            if (DateUtil.parse(startTime).after(DateUtil.parse(endTime))) {
                throw new InspectionException("endTime 不能早于 startTime");
            }
        }
    }

    public Page<InspAlarmRecord> toPage() {
        return new Page<>(this.current, this.size);
    }
}
