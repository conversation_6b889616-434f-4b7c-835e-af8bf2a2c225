package com.hollysys.inspection.model.thirdapp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class PresentInfoResModel {
    private String id;
    private String label;
    private String channelId;
    private String channelLabel;
    private List<AlgorithmInstanceResModel> algorithmInstances = Collections.emptyList();
}
