package com.hollysys.inspection.model.channel;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class CatchCreateChannelReq {
    // 通道名称前缀
    private String namePrefix;

    // 父级id
    private String parentId;

    // 通道连接IP
    private String address;

    // 用户名
    private String username;

    // 密码
    private String password;

    // 摄像机接入协议类型
    private String cameraCtrlProtocol;

    // 选中的通道流集合
    private List<RtspStreamModel> rtspStreamModels;
}
