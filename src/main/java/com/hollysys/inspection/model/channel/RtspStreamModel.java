package com.hollysys.inspection.model.channel;

import lombok.Getter;
import lombok.Setter;

/**
 *
 */
@Getter
@Setter
public class RtspStreamModel {
    // 通道号
    private int channelNum;
    // RTSP地址
    private String rtspUrl;

    public static RtspStreamModel createRtspStreamModel(int channelNum, String rtspUrl) {
        RtspStreamModel rtspStreamModel = new RtspStreamModel();
        rtspStreamModel.channelNum = channelNum;
        rtspStreamModel.rtspUrl = rtspUrl;
        return rtspStreamModel;
    }
}
