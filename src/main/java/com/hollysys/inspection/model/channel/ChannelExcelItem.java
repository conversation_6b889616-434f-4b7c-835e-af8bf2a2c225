package com.hollysys.inspection.model.channel;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class ChannelExcelItem {
    // 区域名称
    private List<String> areaNames;
    // 通道名称
    private String channelName;
    // 通道描述
    private String channelDesc;
    // // 视频接入协议类型
    // private String videoProtocolType;
    // 摄像机接入协议类型
    private String cameraCtrlProtocol;
    // // 此字段为GB28181协议下SIP用户名
    // private String sipName;
    // // 此字段为GB28181协议下视频通道ID
    // private String videoChannelId;
    // 通道类型
    private String deviceType;
    // 通道连接IP
    private String address;
    // 用户名
    private String username;
    // 密码
    private String password;
    // 通道RTSP地址
    private String rtsp;
    // 视频传输协议类型TCP、UDP（mediamtx流媒体服务需要）
    private String sourceProtocol;
    // 摄像头厂商
    private String manufacturer;
}
