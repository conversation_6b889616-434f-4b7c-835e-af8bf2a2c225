package com.hollysys.inspection.model.socket;

import com.hollysys.inspection.constants.ScheduleTaskMode;
import com.hollysys.inspection.model.algorithm.osd.OSDItem;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class WebsocketMessage {

    // 消息类型  clean(清除OSD)、draw(绘制OSD)、switch_mode(切换手自动状态)
    private String eventType;

    // 通道ID
    private String channelId;

    // 通道最新手、自状态
    private ScheduleTaskMode channelMode;

    private List<OSDItem> osdItemList;
}
