package com.hollysys.inspection.model.socket;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.hollysys.inspection.constants.MsgLevel;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class ExecuteMsg {
    private String msg;

    private MsgLevel level;

    private String timestamp;

    public static ExecuteMsg create(MsgLevel level, String msg) {
        ExecuteMsg logMsg = new ExecuteMsg();
        logMsg.setMsg(msg);
        logMsg.setLevel(level);
        String dateNow = DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_MS_PATTERN);
        logMsg.setTimestamp(dateNow);
        return logMsg;
    }
}
