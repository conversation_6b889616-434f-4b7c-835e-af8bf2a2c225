package com.hollysys.inspection.model.socket;

import cn.hutool.core.date.DateUtil;
import com.hollysys.inspection.constants.MsgLevel;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class ImportExcelMsg {
    private String msg;

    // 进度（小数）
    private Float progress;

    private MsgLevel level;

    private String timestamp;

    public static ImportExcelMsg create(MsgLevel level, String msg, Float progress) {
        ImportExcelMsg logMsg = new ImportExcelMsg();
        logMsg.setMsg(msg);
        logMsg.setProgress(progress);
        logMsg.setLevel(level);
        String dateNow = DateUtil.formatDateTime(DateUtil.date());
        logMsg.setTimestamp(dateNow);
        return logMsg;
    }
}
