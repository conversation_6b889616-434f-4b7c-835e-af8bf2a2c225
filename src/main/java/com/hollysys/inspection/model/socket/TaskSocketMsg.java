package com.hollysys.inspection.model.socket;

import com.hollysys.inspection.constants.ScheduleTaskMode;
import com.hollysys.inspection.model.schedule.ScheduleTaskNodeModel;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


/**
 * 操作员调度任务实时长连接消息对象
 */
@Setter
@Getter
public class TaskSocketMsg {
    // 消息类型  ALARM（报警）、PROGRESS（执行进度）、SWITCH_MODE
    private String msgType;
    // 通道ID
    private String channelId;
    // 通道名称
    private String channelName;
    // 任务最新手、自状态
    private ScheduleTaskMode taskMode;

    private List<ScheduleTaskNodeModel> scheduleTaskNodeModels;

    public static TaskSocketMsg buildProgress(List<ScheduleTaskNodeModel> scheduleTaskNodeModels) {
        TaskSocketMsg taskSocketMsg = new TaskSocketMsg();
        taskSocketMsg.setMsgType("PROGRESS");
        taskSocketMsg.setScheduleTaskNodeModels(scheduleTaskNodeModels);
        return taskSocketMsg;
    }

    public static TaskSocketMsg buildAlarm(String channelId, String channelName) {
        TaskSocketMsg taskSocketMsg = new TaskSocketMsg();
        taskSocketMsg.setMsgType("ALARM");
        taskSocketMsg.setChannelId(channelId);
        taskSocketMsg.setChannelName(channelName);
        return taskSocketMsg;
    }

    public static TaskSocketMsg buildSwitchModel(ScheduleTaskMode mode) {
        TaskSocketMsg taskSocketMsg = new TaskSocketMsg();
        taskSocketMsg.setMsgType("SWITCH_MODE");
        taskSocketMsg.setTaskMode(mode);
        return taskSocketMsg;
    }
}
