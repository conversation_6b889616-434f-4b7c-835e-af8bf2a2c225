package com.hollysys.inspection.model.modelinstance;

import com.hollysys.inspection.entity.InspAlgorithm;
import com.hollysys.inspection.entity.InspAlgorithmInstance;
import com.hollysys.inspection.entity.InspSceneDefinition;
import com.hollysys.inspection.model.AlarmNoticeModel;
import com.hollysys.inspection.model.algorithm.configure.SaveScheduleConfigReqModel;
import com.hollysys.inspection.model.algorithm.configure.AlgorithmOutputModel;
import com.hollysys.inspection.model.algorithm.correct.CorrectParamSaveModel;

import java.util.List;

public class ModelInstanceConfigModel {

    private ModelInstanceDetail modelInstance;

    private List<InspSceneDefinition> sceneList;

    private List<InspAlgorithm> algorithmAllList;

    private List<InspAlgorithmInstance> algorithmSelectedList;

    private CorrectParamSaveModel correctParam;

    private List<AlgorithmOutputModel> algorithmInstanceOutputList;

    private SaveScheduleConfigReqModel scheduleConfigInfo;

    private List<AlarmNoticeModel> notice;

    public ModelInstanceDetail getModelInstance() {
        return modelInstance;
    }

    public void setModelInstance(ModelInstanceDetail modelInstance) {
        this.modelInstance = modelInstance;
    }

    public List<InspSceneDefinition> getSceneList() {
        return sceneList;
    }

    public void setSceneList(List<InspSceneDefinition> sceneList) {
        this.sceneList = sceneList;
    }

    public List<InspAlgorithm> getAlgorithmAllList() {
        return algorithmAllList;
    }

    public void setAlgorithmAllList(List<InspAlgorithm> algorithmAllList) {
        this.algorithmAllList = algorithmAllList;
    }

    public List<InspAlgorithmInstance> getAlgorithmSelectedList() {
        return algorithmSelectedList;
    }

    public void setAlgorithmSelectedList(List<InspAlgorithmInstance> algorithmSelectedList) {
        this.algorithmSelectedList = algorithmSelectedList;
    }

    public CorrectParamSaveModel getCorrectParam() {
        return correctParam;
    }

    public void setCorrectParam(CorrectParamSaveModel correctParam) {
        this.correctParam = correctParam;
    }

    public List<AlgorithmOutputModel> getAlgorithmInstanceOutputList() {
        return algorithmInstanceOutputList;
    }

    public SaveScheduleConfigReqModel getScheduleConfigInfo() {
        return scheduleConfigInfo;
    }

    public void setAlgorithmInstanceOutputList(List<AlgorithmOutputModel> algorithmInstanceOutputList) {
        this.algorithmInstanceOutputList = algorithmInstanceOutputList;
    }

    public void setScheduleConfigInfo(SaveScheduleConfigReqModel scheduleConfigInfo) {
        this.scheduleConfigInfo = scheduleConfigInfo;
    }

    public List<AlarmNoticeModel> getNotice() {
        return notice;
    }

    public void setNotice(List<AlarmNoticeModel> notice) {
        this.notice = notice;
    }
}
