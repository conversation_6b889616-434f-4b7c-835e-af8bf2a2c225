package com.hollysys.inspection.model.modelinstance;

import lombok.AllArgsConstructor;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

@Data
@AllArgsConstructor
public class InspModelChannelPTZModel {
    @NotBlank
    @Length(min = 32, max = 32)
    private String id;
    @Max(1)
    @Min(-1)
    private Double pan;
    //垂直参数
    @Max(1)
    @Min(-1)
    private Double tilt;
    //焦距倍数
    @Max(1)
    @Min(0)
    private Double zoom;
}
