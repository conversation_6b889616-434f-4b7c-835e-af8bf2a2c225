package com.hollysys.inspection.model.draw.shape;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 存储多点坐标
 */
public class Polygon extends ArrayList<Point> {

    public static Polygon buildByStr(String jsonStr) {
        List<JSONArray> arrList = JSON.parseArray(jsonStr, JSONArray.class);
        List<Point> points = new ArrayList<>();
        for (JSONArray jsonArray : arrList) {
            List<Double> doubles = jsonArray.toJavaList(Double.class);
            Point point = new Point();
            for (Double aDouble : doubles) {
                BigDecimal round = NumberUtil.round(aDouble, 0);
                point.add(round.intValue());
            }
            points.add(point);
        }

        Polygon polygon = new Polygon();
        polygon.addAll(points);

        return polygon;
    }
}
