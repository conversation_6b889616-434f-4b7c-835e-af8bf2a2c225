package com.hollysys.inspection.model.draw.shape;

import com.alibaba.fastjson.JSON;
import com.hollysys.inspection.config.exceptions.InspectionException;

import java.util.ArrayList;
import java.util.List;

/**
 * 存储点坐标 [x, y]
 */
public class Point extends ArrayList<Integer> {
    private static final int MAX_ARRAY_SIZE = 2;

    public Point() {
    }

    public Point(Integer pointX, Integer pointY) {
        this.add(pointX);
        this.add(pointY);
    }

    public static Point buildByStr(String str) {
        List<Integer> integers = JSON.parseArray(str, Integer.class);
        Point point = new Point();
        if (integers.size() == 1) {
            point.add(integers.get(0));
            return point;
        }
        if (integers.size() > 1) {
            point.add(integers.get(0));
            point.add(integers.get(1));
            return point;
        }
        return point;
    }

    @Override
    public boolean add(Integer integer) {
        if (hugeCapacity()) {
            throw new InspectionException("对象元素数量超限");
        }
        return super.add(integer);
    }

    private boolean hugeCapacity() {
        int size = this.size();
        return size == MAX_ARRAY_SIZE;
    }
}
