package com.hollysys.inspection.model.draw.shape;

public class Circle {
    private Point center;

    private Integer radius;

    public Circle() {
    }

    public Circle(Point center, Integer radius) {
        this.center = center;
        this.radius = radius;
    }

    public Point getCenter() {
        return center;
    }

    public void setCenter(Point center) {
        this.center = center;
    }

    public Integer getRadius() {

        return radius;
    }

    public void setRadius(Integer radius) {
        this.radius = radius;
    }
}
