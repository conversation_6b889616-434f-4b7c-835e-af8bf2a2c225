package com.hollysys.inspection.model.draw.shape;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.hollysys.inspection.config.exceptions.InspectionException;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 存储四点坐标 [左上、右上、右下、左下]
 */
public class Square extends ArrayList<Point> {
    private static final int MAX_ARRAY_SIZE = 4;

    public static Square buildByStr(String jsonStr) {
        List<JSONArray> arrList = JSON.parseArray(jsonStr, JSONArray.class);
        List<Point> points = new ArrayList<>();
        for (JSONArray jsonArray : arrList) {
            List<Double> doubles = jsonArray.toJavaList(Double.class);
            Point point = new Point();
            for (Double aDouble : doubles) {
                BigDecimal round = NumberUtil.round(aDouble, 0);
                point.add(round.intValue());
            }
            points.add(point);
        }
        List<Integer> point0 = JSON.parseArray(JSON.toJSONString(points.get(0)), Integer.class);
        List<Integer> point1 = JSON.parseArray(JSON.toJSONString(points.get(1)), Integer.class);
        List<Integer> point2 = JSON.parseArray(JSON.toJSONString(points.get(2)), Integer.class);
        List<Integer> point3 = JSON.parseArray(JSON.toJSONString(points.get(3)), Integer.class);
        return new Square(point0, point1, point2, point3);
    }

    public Square() {
    }

    public Square(List<Integer> point0, List<Integer> point1, List<Integer> point2, List<Integer> point3) {
        this.add(listToPoint(point0));
        this.add(listToPoint(point1));
        this.add(listToPoint(point2));
        this.add(listToPoint(point3));
    }

    public Square(Point point0, Point point1, Point point2, Point point3) {
        this.add(point0);
        this.add(point1);
        this.add(point2);
        this.add(point3);
    }

    public Square(Integer[] point0, Integer[] point1, Integer[] point2, Integer[] point3) {
        this(Arrays.asList(point0), Arrays.asList(point1), Arrays.asList(point2), Arrays.asList(point3));
    }

    @Override
    public boolean add(Point point) {
        if (hugeCapacity()) {
            throw new InspectionException("对象元素数量超限");
        }
        return super.add(point);
    }

    private boolean hugeCapacity() {
        int size = this.size();
        return size == MAX_ARRAY_SIZE;
    }

    private Point listToPoint(List<Integer> point) {
        return new Point(point.get(0), point.get(1));
    }
}
