package com.hollysys.inspection.model.draw.shape;

/**
 * 椭圆
 *
 * <AUTHOR>
 */
public class Ellipse {

    /**
     * 圆心坐标 [x, y]
     */
    private Point center;

    /**
     * 椭圆 [短轴, 长轴]
     */
    private Integer[] axes;

    /**
     * 旋转角度，垂直方形为0度，顺时针计算
     */
    private Float angle;

    public Point getCenter() {
        return center;
    }

    public void setCenter(Point center) {
        this.center = center;
    }

    public Integer[] getAxes() {
        return axes;
    }

    public void setAxes(Integer[] axes) {
        this.axes = axes;
    }

    public Float getAngle() {
        return angle;
    }

    public void setAngle(Float angle) {
        this.angle = angle;
    }
}
