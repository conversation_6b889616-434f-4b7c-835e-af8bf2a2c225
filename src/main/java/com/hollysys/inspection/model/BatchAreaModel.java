package com.hollysys.inspection.model;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Data
public class BatchAreaModel {
    @NotBlank
    @Pattern(regexp = "^[\u4E00-\u9FA5A-Za-z0-9_#]+$", message = "区域名称只能是中文、英文字母和数字，且长度不能超过8位！")
    @Length(max = 8)
    //节点prefix
    private String areaPre;
    //节点范围range
    @NotNull
    private String[] areaRange;
    //节点后缀areaFix
    @NotBlank
    @Length(max = 8)
    @Pattern(regexp = "^[\u4E00-\u9FA5A-Za-z0-9]+$", message = "区域名称只能是中文、英文字母和数字，且长度不能超过8位！")
    private String areaFix;
    //通道类型
    @NotBlank
    private String type;
    //父级id
    @NotBlank
    private String parentId;

}
