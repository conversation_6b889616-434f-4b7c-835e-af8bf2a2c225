package com.hollysys.inspection.model.variable;

import com.hollysys.inspection.entity.InspGlobalVariable;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GlobalVariableListItem extends InspGlobalVariable {
    // 绑定的输出名称
    private String bindOutputName;

    // 变量实时值
    private String value;

    // 变量质量位
    private String quality;

    // 变量值更新时间
    private String variableTime;
}
