package com.hollysys.inspection.model.variable;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class OutputParamTreeItem {
    // 主键ID
    private String id;
    // 节点名称
    private String label;
    // 通道类型
    private String type;
    // 父级id
    private String parentId;
    // 排序字段
    private Long sortNo;
    // 输出参数是否被绑定
    private Boolean isBind;
    // 输出参数值得数据类型
    private String dataType;
    // 子节点
    private List<OutputParamTreeItem> children;
}
