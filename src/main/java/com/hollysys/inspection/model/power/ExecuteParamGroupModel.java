package com.hollysys.inspection.model.power;

import com.hollysys.inspection.entity.InspAlgorithmParamInstance;

import java.util.List;

public class ExecuteParamGroupModel {
    private String groupLabel;

    private List<InspAlgorithmParamInstance> paramList;

    public String getGroupLabel() {
        return groupLabel;
    }

    public void setGroupLabel(String groupLabel) {
        this.groupLabel = groupLabel;
    }

    public List<InspAlgorithmParamInstance> getParamList() {
        return paramList;
    }

    public void setParamList(List<InspAlgorithmParamInstance> paramList) {
        this.paramList = paramList;
    }
}
