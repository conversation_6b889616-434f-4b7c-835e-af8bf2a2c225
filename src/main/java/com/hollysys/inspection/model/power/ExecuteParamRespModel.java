package com.hollysys.inspection.model.power;

import java.util.List;

public class ExecuteParamRespModel {

    private List<ExecuteParamGroupModel> executeConfig;

    // 基准图
    private String benchPic;

    // 参数配置基准图（透视变换后的图或者基准图原图）
    private String configBenchPic;

    public List<ExecuteParamGroupModel> getExecuteConfig() {
        return executeConfig;
    }

    public void setExecuteConfig(List<ExecuteParamGroupModel> executeConfig) {
        this.executeConfig = executeConfig;
    }

    public String getBenchPic() {
        return benchPic;
    }

    public void setBenchPic(String benchPic) {
        this.benchPic = benchPic;
    }

    public String getConfigBenchPic() {
        return configBenchPic;
    }

    public void setConfigBenchPic(String configBenchPic) {
        this.configBenchPic = configBenchPic;
    }
}
