package com.hollysys.inspection.model.power;

import java.util.List;

public class ExecuteParamSaveModel {

    private String algorithmInstanceId;

    private List<ExecuteParamGroupModel> paramInfoList;

    public String getAlgorithmInstanceId() {
        return algorithmInstanceId;
    }

    public void setAlgorithmInstanceId(String algorithmInstanceId) {
        this.algorithmInstanceId = algorithmInstanceId;
    }

    public List<ExecuteParamGroupModel> getParamInfoList() {
        return paramInfoList;
    }

    public void setParamInfoList(List<ExecuteParamGroupModel> paramInfoList) {
        this.paramInfoList = paramInfoList;
    }
}
