package com.hollysys.inspection.model.power;

import com.hollysys.inspection.model.draw.shape.Square;

import java.util.List;

public class RoiInfoModel {

    private List<Float> range;

    private List<Integer> thicknessThreshold;

    private Square roi;

    private TopicObjModel topicMap;

    public List<Float> getRange() {
        return range;
    }

    public void setRange(List<Float> range) {
        this.range = range;
    }

    public List<Integer> getThicknessThreshold() {
        return thicknessThreshold;
    }

    public void setThicknessThreshold(List<Integer> thicknessThreshold) {
        this.thicknessThreshold = thicknessThreshold;
    }

    public Square getRoi() {
        return roi;
    }

    public void setRoi(Square roi) {
        this.roi = roi;
    }

    public TopicObjModel getTopicMap() {
        return topicMap;
    }

    public void setTopicMap(TopicObjModel topicMap) {
        this.topicMap = topicMap;
    }
}
