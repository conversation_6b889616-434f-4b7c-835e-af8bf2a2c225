package com.hollysys.inspection.model.backup;

import lombok.Data;

/**
 * 用于备份配置的类
 */
@Data
public class SystemBackupConfig {
    // 最大备份存储 GB
    private double maxBackupSize;
    // 执行记录备份配置
    private RecordConfig executeRecord;
    // 操作日志备份配置
    private RecordConfig operationLogRecord;
    // 报警记录备份配置
    private RecordConfig alarmRecord;

    @Data
    public static class RecordConfig {
        // 最大备份条数 超出则备份超出的部分
        private int outOfSize;
        // 备份单个csv文件的最大条数
        private int backupItemSize;
    }
}
