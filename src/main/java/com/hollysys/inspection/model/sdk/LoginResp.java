package com.hollysys.inspection.model.sdk;

import com.hollysys.inspection.entity.InspChannelInfo;
import com.hollysys.inspection.sdk.dahua.NetSDKLib;
import com.sun.jna.Pointer;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class LoginResp {
    private InspChannelInfo channelInfo;
    // 通道号集合
    private List<Integer> channelNums;
    // 大华登录返回类型
    private NetSDKLib.LLong dhToken;
    // 海康登录返回类型
    private Integer hikToken;
    // 宇视登录返回类型
    private Pointer ysToken;

    public static LoginResp buildDhToken(InspChannelInfo channelInfo, NetSDKLib.LLong dhToken) {
        LoginResp loginResp = new LoginResp();
        loginResp.setDhToken(dhToken);
        loginResp.setChannelInfo(channelInfo);
        return loginResp;
    }

    public static LoginResp buildHikToken(InspChannelInfo channelInfo, Integer hikToken) {
        LoginResp loginResp = new LoginResp();
        loginResp.setHikToken(hikToken);
        loginResp.setChannelInfo(channelInfo);
        return loginResp;
    }

    public static LoginResp buildYsToken(InspChannelInfo channelInfo, Pointer ysToken) {
        LoginResp loginResp = new LoginResp();
        loginResp.setYsToken(ysToken);
        loginResp.setChannelInfo(channelInfo);
        return loginResp;
    }
}
