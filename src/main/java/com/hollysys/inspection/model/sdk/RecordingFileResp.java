package com.hollysys.inspection.model.sdk;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class RecordingFileResp {
    /**
     * 文件名
     */
    private String fileName;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    /**
     * 文件大小（字节）
     */
    private int fileSize;

    /**
     * 码流类型 0- 主码流，1- 子码流，2- 码流三
     */
    private int streamType;

    /**
     * 通道号
     */
    private int channelNum;

    /**
     * 完整文件名
     */
    private String fullFileName;
}
