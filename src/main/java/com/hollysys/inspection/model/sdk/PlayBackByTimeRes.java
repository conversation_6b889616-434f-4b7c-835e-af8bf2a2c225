package com.hollysys.inspection.model.sdk;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hollysys.inspection.entity.InspChannelInfo;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class PlayBackByTimeRes extends InspChannelInfo {
    // 回放开始时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    // 回放结束时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    // 码流类型，0为主码流，1为辅码流
    private int streamType;

    // 前端唯一标识  标识一个独立播放客户端
    private String clientKey;
}
