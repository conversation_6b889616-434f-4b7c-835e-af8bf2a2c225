package com.hollysys.inspection.model.sdk;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hollysys.inspection.entity.InspChannelInfo;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class DownloadRecordingFileRes extends InspChannelInfo {
    // 要下载的录像文件名
    private String fileName;

    // 下载录像文件大小
    private int fileSize;

    // 码流类型
    private int streamType;

    // 录像开始时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    // 录像结束时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
}
