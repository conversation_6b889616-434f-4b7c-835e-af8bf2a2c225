package com.hollysys.inspection.model.sdk;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hollysys.inspection.entity.InspChannelInfo;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
@Getter
@Setter
public class RecordingFileRes extends InspChannelInfo {
    // 开始时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    // 结束时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;
}
