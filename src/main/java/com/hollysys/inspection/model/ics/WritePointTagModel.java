package com.hollysys.inspection.model.ics;

import java.util.List;

public class WritePointTagModel {

    private List<WriteTagItemModel> items;

    private String namespace;

    private String tag;

    public List<WriteTagItemModel> getItems() {
        return items;
    }

    public void setItems(List<WriteTagItemModel> items) {
        this.items = items;
    }

    public String getNamespace() {
        return namespace;
    }

    public void setNamespace(String namespace) {
        this.namespace = namespace;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }
}
