package com.hollysys.inspection.model.ics;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.Map;

public class ReadPointsRespModel {
    @J<PERSON><PERSON>ield(name = "v")
    private Map<String, Object> value;

    @J<PERSON><PERSON>ield(name = "ns")
    private String namespace;

    @JSO<PERSON>ield(name = "n")
    private String tag;

    @JSONField(name = "t")
    private long timestamp;

    public Map<String, Object> getValue() {
        return value;
    }

    public void setValue(Map<String, Object> value) {
        this.value = value;
    }

    public String getNamespace() {
        return namespace;
    }

    public void setNamespace(String namespace) {
        this.namespace = namespace;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
}
