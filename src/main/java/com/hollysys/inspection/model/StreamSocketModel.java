package com.hollysys.inspection.model;

import lombok.Data;

import javax.websocket.Session;
import java.util.Objects;

@Data
public class StreamSocketModel {
    private Session session;
    private String clientId;


    @Override
    public int hashCode() {
        return session.hashCode();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        StreamSocketModel that = (StreamSocketModel) o;
        return Objects.equals(session, that.session) && Objects.equals(clientId, that.clientId);
    }

}
