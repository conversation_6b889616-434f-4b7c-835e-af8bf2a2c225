package com.hollysys.inspection.model.context;

import com.hollysys.inspection.entity.InspAlgorithmInstance;
import com.hollysys.inspection.entity.InspAlgorithmParamInstance;
import com.hollysys.inspection.entity.InspRecordAlgorithm;
import com.hollysys.inspection.model.algorithm.execute.AlgorithmExecuteRsp;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 算法执行上下文
 */
@Getter
@Setter
public class ExecuteAlgorithmCtx {
    /**
     * 请求客户端唯一标识，用来后端发送执行日志到前端的识别KEY，调试时此字段有意义且不为空
     */
    private String clientKey;

    // private String algorithmId;

    private String presetId;

    private String channelId;

    // private String algorithmId;

    private String algorithmInstanceId;

    private InspAlgorithmInstance algorithmInstance;

    private String sceneId;

    // 执行记录
    private InspRecordAlgorithm recordAlgorithm;

    // 算法执行输入图片集合，当为空时，执行前会进行截图
    private List<String> inputImages;

    // 算法执行输入参数
    private List<InspAlgorithmParamInstance> inputParams;

    // 算法执行输出参数定义
    // private List<InspAlgorithmParam> outputParamList;

    // 算法执行结果
    private AlgorithmExecuteRsp algorithmExecuteRsp;

    // 算法执行开始时间
    private LocalDateTime startTime;

    // 算法执行结束时间
    private LocalDateTime endTime;
}
