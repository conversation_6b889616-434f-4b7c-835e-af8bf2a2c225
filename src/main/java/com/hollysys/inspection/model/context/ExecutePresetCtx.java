package com.hollysys.inspection.model.context;

import com.hollysys.inspection.entity.InspPresetInfo;
import com.hollysys.inspection.entity.InspProject;
import com.hollysys.inspection.entity.InspProjectTreeNode;
import com.hollysys.inspection.entity.InspRecordPreset;
import com.hollysys.inspection.model.algorithm.osd.OSDItem;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 算法执行上下文
 */
@Getter
@Setter
public class ExecutePresetCtx {
    /**
     * 请求客户端唯一标识，用来后端发送执行日志到前端的识别KEY，调试时此字段有意义且不为空
     */
    private String clientKey;

    private InspProject project;

    // 调度任务ID，非调度执行，此字段值为空
    private String taskId;

    private String channelId;

    private String presetId;

    private List<OSDItem> allOsdInfo;

    private InspProjectTreeNode presetTreeNodeInfo;

    private InspPresetInfo presetInfo;

    // 整体执行记录
    private InspRecordPreset recordPreset;

    // 单次执行记录
    private InspRecordPreset recordTimesPreset;

    // 预置点执行开始时间
    private LocalDateTime startTime;

    // 预置点执行结束时间
    private LocalDateTime endTime;

}
