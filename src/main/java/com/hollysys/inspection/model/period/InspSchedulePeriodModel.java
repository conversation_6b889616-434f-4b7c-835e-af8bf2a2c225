package com.hollysys.inspection.model.period;


import lombok.Data;

import java.util.List;

/**
 * 通道运行周期调度信息表(InspSchedulePeriod)表实体类
 *
 * <AUTHOR>
 * @since 2023-02-02 17:37:41
 */
@Data
public class InspSchedulePeriodModel {
    //所属任务id
    private String taskId;

    private List<Periods> periods;

    @Data
    public static class Periods {
        //所属星期几
        private int week;
        //时间段集合
        private String timeSlot;
    }
}

