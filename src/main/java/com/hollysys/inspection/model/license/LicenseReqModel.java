package com.hollysys.inspection.model.license;

import cn.darkjrong.license.core.common.pojo.params.LicenseExtraParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LicenseReqModel extends LicenseExtraParam {

    // 通道数量
    private int channelNodeCount;

    // 算法数量
    private int algorithmCount;

    // 算法名称列表
    private List<String> algorithmNameList;

    // 机器码
    private String appCode;

    // 证书过期时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expireTime;

    // 证书授权时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date authTime;

    // 系统版本
    private String systemVersion;

    // 系统类型
    private String productVersion;

}
