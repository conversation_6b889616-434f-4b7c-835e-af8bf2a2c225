package com.hollysys.inspection.config.mybaits;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.hollysys.inspection.model.alarm.config.AdvanceAlarmRuleInfo;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class AdvanceAlarmToStringHandle extends BaseTypeHandler<AdvanceAlarmRuleInfo> {
    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, AdvanceAlarmRuleInfo obj, JdbcType jdbcType) throws SQLException {
        if (ObjectUtil.isNotEmpty(obj)) {
            preparedStatement.setString(i, JSON.toJSONString(obj));
        } else {
            preparedStatement.setString(i, null);
        }
    }

    @Override
    public AdvanceAlarmRuleInfo getNullableResult(ResultSet resultSet, String s) throws SQLException {
        String result = resultSet.getString(s);
        return newObject(result);
    }

    @Override
    public AdvanceAlarmRuleInfo getNullableResult(ResultSet resultSet, int i) throws SQLException {
        String result = resultSet.getString(i);
        return newObject(result);
    }

    @Override
    public AdvanceAlarmRuleInfo getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        String result = callableStatement.getString(i);
        return newObject(result);
    }

    private AdvanceAlarmRuleInfo newObject(String result) {
        if (StrUtil.isNotBlank(result)) {
            return JSON.parseObject(result, AdvanceAlarmRuleInfo.class);
        }
        return null;
    }
}
