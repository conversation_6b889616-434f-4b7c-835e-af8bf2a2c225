package com.hollysys.inspection.config.mybaits;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class JSONObjectToStringHandle extends BaseTypeHandler<JSONObject> {
    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, JSONObject obj, JdbcType jdbcType) throws SQLException {
        if (ObjectUtil.isNull(obj)) {
            preparedStatement.setString(i, null);
        }
        preparedStatement.setString(i, JSONUtil.toJsonStr(obj));
    }

    @Override
    public JSONObject getNullableResult(ResultSet resultSet, String s) throws SQLException {
        String result = resultSet.getString(s);
        return newObject(result);
    }

    @Override
    public JSONObject getNullableResult(ResultSet resultSet, int i) throws SQLException {
        String result = resultSet.getString(i);
        return newObject(result);
    }

    @Override
    public JSONObject getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        String result = callableStatement.getString(i);
        return newObject(result);
    }

    private JSONObject newObject(String result) {
        JSONObject obj = null;
        if (JSONUtil.isTypeJSONObject(result)) {
            obj = JSONUtil.parseObj(result);
        }
        return obj;
    }
}
