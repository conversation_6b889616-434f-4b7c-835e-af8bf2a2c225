package com.hollysys.inspection.config.mybaits;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.hollysys.inspection.model.algorithm.param.data.Square;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class SquareToStringHandle extends BaseTypeHandler<Square> {

    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, Square obj, JdbcType jdbcType) throws SQLException {
        if (ObjectUtil.isNotEmpty(obj)) {
            preparedStatement.setString(i, JSON.toJSONString(obj));
        } else {
            preparedStatement.setString(i, null);
        }
    }

    @Override
    public Square getNullableResult(ResultSet resultSet, String s) throws SQLException {
        String result = resultSet.getString(s);
        return newSquare(result);
    }

    @Override
    public Square getNullableResult(ResultSet resultSet, int i) throws SQLException {
        String result = resultSet.getString(i);
        return newSquare(result);
    }

    @Override
    public Square getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        String result = callableStatement.getString(i);
        return newSquare(result);
    }

    private Square newSquare(String result) {
        if (StrUtil.isNotBlank(result)) {
            return Square.buildByObj(result);
        }
        return null;
    }
}
