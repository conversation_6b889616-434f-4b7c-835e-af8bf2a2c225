package com.hollysys.inspection.config.mybaits;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.hollysys.inspection.model.algorithm.correct.PicCorrectParamModel;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class PicCorrectToStringHandle extends BaseTypeHandler<PicCorrectParamModel> {
    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, PicCorrectParamModel obj, JdbcType jdbcType) throws SQLException {
        if (ObjectUtil.isNotEmpty(obj)) {
            preparedStatement.setString(i, JSON.toJSONString(obj));
        } else {
            preparedStatement.setString(i, null);
        }
    }

    @Override
    public PicCorrectParamModel getNullableResult(ResultSet resultSet, String s) throws SQLException {
        String result = resultSet.getString(s);
        return newPicCorrectParamModel(result);
    }

    @Override
    public PicCorrectParamModel getNullableResult(ResultSet resultSet, int i) throws SQLException {
        String result = resultSet.getString(i);
        return newPicCorrectParamModel(result);
    }

    @Override
    public PicCorrectParamModel getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        String result = callableStatement.getString(i);
        return newPicCorrectParamModel(result);
    }

    private PicCorrectParamModel newPicCorrectParamModel(String result) {
        if (StrUtil.isNotBlank(result)) {
            return JSONUtil.toBean(result, PicCorrectParamModel.class);
        }
        return null;
    }
}
