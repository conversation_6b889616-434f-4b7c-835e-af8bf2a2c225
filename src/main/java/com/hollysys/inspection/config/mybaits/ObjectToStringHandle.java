package com.hollysys.inspection.config.mybaits;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.math.BigDecimal;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class ObjectToStringHandle extends BaseTypeHandler<Object> {
    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, Object obj, JdbcType jdbcType) throws SQLException {
        if (ObjectUtil.isNull(obj)) {
            preparedStatement.setString(i, null);
        } else if (obj instanceof String) {
            preparedStatement.setString(i, obj.toString());
        } else if (obj instanceof Integer) {
            preparedStatement.setInt(i, Integer.parseInt(obj.toString()));
        } else if (obj instanceof Long) {
            preparedStatement.setLong(i, Long.parseLong(obj.toString()));
        } else if (obj instanceof Double) {
            preparedStatement.setDouble(i, Double.parseDouble(obj.toString()));
        } else if (obj instanceof Float) {
            preparedStatement.setFloat(i, Float.parseFloat(obj.toString()));
        }  else if (obj instanceof BigDecimal) {
            preparedStatement.setDouble(i, Double.parseDouble(obj.toString()));
        } else if (obj instanceof Boolean) {
            preparedStatement.setBoolean(i, Boolean.parseBoolean(obj.toString()));
        } else {
            preparedStatement.setString(i, JSONUtil.toJsonStr(obj));
        }
    }

    @Override
    public Object getNullableResult(ResultSet resultSet, String s) throws SQLException {
        String result = resultSet.getString(s);
        return newObject(result);
    }

    @Override
    public Object getNullableResult(ResultSet resultSet, int i) throws SQLException {
        String result = resultSet.getString(i);
        return newObject(result);
    }

    @Override
    public Object getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        String result = callableStatement.getString(i);
        return newObject(result);
    }

    private Object newObject(String result) {
        Object obj;
        if (StrUtil.isNotBlank(result)) {
            if (JSONUtil.isTypeJSONArray(result)) {
                obj = JSONUtil.toList(result, Object.class);
            } else if (JSONUtil.isTypeJSONObject(result)) {
                obj = JSONUtil.toBean(result, Object.class);
            } else {
                obj = result;
            }
            return obj;
        }
        return null;
    }
}
