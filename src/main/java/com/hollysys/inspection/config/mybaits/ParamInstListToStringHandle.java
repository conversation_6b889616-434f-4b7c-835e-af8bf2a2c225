package com.hollysys.inspection.config.mybaits;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.hollysys.inspection.entity.InspAlgorithmParamInstance;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class ParamInstListToStringHandle extends BaseTypeHandler<List<InspAlgorithmParamInstance>> {
    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, List<InspAlgorithmParamInstance> obj, JdbcType jdbcType) throws SQLException {
        if (ObjectUtil.isNotEmpty(obj)) {
            preparedStatement.setString(i, JSON.toJSONString(obj));
        } else {
            preparedStatement.setString(i, null);
        }
    }

    @Override
    public List<InspAlgorithmParamInstance> getNullableResult(ResultSet resultSet, String s) throws SQLException {
        String result = resultSet.getString(s);
        return newObject(result);
    }

    @Override
    public List<InspAlgorithmParamInstance> getNullableResult(ResultSet resultSet, int i) throws SQLException {
        String result = resultSet.getString(i);
        return newObject(result);
    }

    @Override
    public List<InspAlgorithmParamInstance> getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        String result = callableStatement.getString(i);
        return newObject(result);
    }

    private List<InspAlgorithmParamInstance> newObject(String result) {
        List<InspAlgorithmParamInstance> obj;
        if (StrUtil.isNotBlank(result)) {
            if (JSONUtil.isTypeJSONArray(result)) {
                obj = JSONUtil.toList(result, InspAlgorithmParamInstance.class);
            } else {
                obj = new ArrayList<>();
            }
            return obj;
        }
        return null;
    }
}
