package com.hollysys.inspection.config.mybaits;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.hollysys.inspection.model.algorithm.osd.OsdConfig;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class OsdConfigToStringHandle extends BaseTypeHandler<OsdConfig> {
    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, OsdConfig obj, JdbcType jdbcType) throws SQLException {
        if (ObjectUtil.isNotEmpty(obj)) {
            preparedStatement.setString(i, JSON.toJSONString(obj));
        } else {
            preparedStatement.setString(i, null);
        }
    }

    @Override
    public OsdConfig getNullableResult(ResultSet resultSet, String s) throws SQLException {
        String result = resultSet.getString(s);
        return newObject(result);
    }

    @Override
    public OsdConfig getNullableResult(ResultSet resultSet, int i) throws SQLException {
        String result = resultSet.getString(i);
        return newObject(result);
    }

    @Override
    public OsdConfig getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        String result = callableStatement.getString(i);
        return newObject(result);
    }

    private OsdConfig newObject(String result) {
        OsdConfig obj;
        if (StrUtil.isNotBlank(result)) {
            if (JSONUtil.isTypeJSONObject(result)) {
                obj = JSONUtil.toBean(result, OsdConfig.class);
            } else {
                obj = new OsdConfig();
            }
            return obj;
        }
        return null;
    }
}
