package com.hollysys.inspection.config.mybaits;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.hollysys.inspection.model.algorithm.param.SelectorOption;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class OptionToStringHandle extends BaseTypeHandler<SelectorOption> {

    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, SelectorOption obj, JdbcType jdbcType) throws SQLException {
        if (ObjectUtil.isNotEmpty(obj)) {
            preparedStatement.setString(i, JSON.toJSONString(obj));
        } else {
            preparedStatement.setString(i, null);
        }
    }

    @Override
    public SelectorOption getNullableResult(ResultSet resultSet, String s) throws SQLException {
        String result = resultSet.getString(s);
        return newObject(result);
    }

    @Override
    public SelectorOption getNullableResult(ResultSet resultSet, int i) throws SQLException {
        String result = resultSet.getString(i);
        return newObject(result);
    }

    @Override
    public SelectorOption getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        String result = callableStatement.getString(i);
        return newObject(result);
    }

    private SelectorOption newObject(String result) {
        SelectorOption obj;
        if (StrUtil.isNotBlank(result)) {
            if (JSONUtil.isTypeJSONObject(result)) {
                obj = JSONUtil.toBean(result, SelectorOption.class);
            } else {
                obj = new SelectorOption();
            }
            return obj;
        }
        return null;
    }
}
