package com.hollysys.inspection.config.mybaits;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.hollysys.inspection.model.backup.SystemBackupConfig;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class SystemBackupConfigToStringHandle extends BaseTypeHandler<SystemBackupConfig> {
    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, SystemBackupConfig obj, JdbcType jdbcType) throws SQLException {
        if (ObjectUtil.isNotEmpty(obj)) {
            preparedStatement.setString(i, JSON.toJSONString(obj));
        } else {
            preparedStatement.setString(i, null);
        }
    }

    @Override
    public SystemBackupConfig getNullableResult(ResultSet resultSet, String s) throws SQLException {
        String result = resultSet.getString(s);
        return newObject(result);
    }

    @Override
    public SystemBackupConfig getNullableResult(ResultSet resultSet, int i) throws SQLException {
        String result = resultSet.getString(i);
        return newObject(result);
    }

    @Override
    public SystemBackupConfig getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        String result = callableStatement.getString(i);
        return newObject(result);
    }

    private SystemBackupConfig newObject(String result) {
        SystemBackupConfig obj;
        if (StrUtil.isNotBlank(result)) {
            if (JSONUtil.isTypeJSONObject(result)) {
                obj = JSONUtil.toBean(result, SystemBackupConfig.class);
            } else {
                obj = new SystemBackupConfig();
            }
            return obj;
        }
        return null;
    }
}
