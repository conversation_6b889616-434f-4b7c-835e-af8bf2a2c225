package com.hollysys.inspection.config;

import cn.hutool.http.server.filter.SimpleFilter;
import com.sun.net.httpserver.Headers;
import com.sun.net.httpserver.HttpExchange;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

@Configuration
public class FileServerHttpFilter extends SimpleFilter {
    @Override
    public void doFilter(HttpExchange httpExchange, Chain chain) throws IOException {
        Headers responseHeaders = httpExchange.getResponseHeaders();
        responseHeaders.set("Access-Control-Allow-Origin", "*");
        responseHeaders.set("Access-Control-Allow-Methods", "POST, GET, OPTIONS, DELETE, PUT, GET");
        responseHeaders.set("Access-Control-Max-Age", "3600");
        responseHeaders.set("Access-Control-Allow-Headers", "x-requested-with");
        chain.doFilter(httpExchange);
    }
}
