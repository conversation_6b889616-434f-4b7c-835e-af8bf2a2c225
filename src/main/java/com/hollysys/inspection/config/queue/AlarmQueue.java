package com.hollysys.inspection.config.queue;

import com.hollysys.inspection.model.AlarmQueueFailedModel;
import com.hollysys.inspection.model.AlarmQueueModel;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;

@Configuration
public class AlarmQueue {

    @Bean
    public ArrayBlockingQueue<AlarmQueueModel> dingTalkQueue() {
        return new ArrayBlockingQueue<>(1000, true);
    }

    @Bean
    public ArrayBlockingQueue<AlarmQueueFailedModel> dingTalkFailedQueue() {
        return new ArrayBlockingQueue<>(1000, true);
    }

    @Bean
    public ArrayBlockingQueue<AlarmQueueModel> weChatQueue() {
        return new ArrayBlockingQueue<>(1000, true);
    }

    @Bean
    public ArrayBlockingQueue<AlarmQueueModel> voiceQueue() {
        return new ArrayBlockingQueue<>(1000, true);
    }

    @Bean
    public Map<String, LocalDateTime> alarmIntervalTime() {
        return new HashMap<>();
    }

}
