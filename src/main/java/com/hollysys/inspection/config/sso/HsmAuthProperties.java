package com.hollysys.inspection.config.sso;

import cn.hutool.core.util.StrUtil;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.config.redis.RedisHelper;
import com.hollysys.inspection.model.DataBaseConfig;
import com.hollysys.inspection.service.InspCommonConfigService;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * @Description:
 * @Title: AuthProperties
 * @Package: com.hollysys.smartfactory.holliics.manager.filter
 * @Author: wuyuanbing
 * @CreateTime: 2025/3/3 15:31
 */
@Slf4j
@Data
@Component
public class HsmAuthProperties {

    private static final List<String> AUTH_SRV_FIXED_FILTERS = Collections.unmodifiableList(Arrays.asList(
            "/**/icon/**/**#anon",
            "/**/api/v1/reference/refStatus#anon",
            "/**/api/hsm/auth/tokenCallback#anon"
    ));

    private final Sso sso = new Sso();

    @Value("${server.host}")
    private String serverHost;

    @Value("${db-base.real-srv.api.get-token}")
    private String getTokenApiUrl;

    @Value("${db-base.real-srv.api.write-points}")
    private String writePointsApiUrl;

    @Value("${db-base.real-srv.api.find-points}")
    private String findPointsApiUrl;

    @Value("${db-base.real-srv.api-base}")
    private String apiBasePort;

    private static final String CONFIG_FINISH = "config";

    @Resource
    private RedisHelper redisUtils;

    @Resource
    private InspCommonConfigService inspCommonConfigService;

    @Getter
    @Setter
    public static class Sso {
        /** 校验token有效性graphQl接口-path*/
        private String path;
        /** 校验token有效性graphQl接口-query指定*/
        private String query;
        /** 单点登录服务的端口号 */
        private Integer port;
        /** 获取token的接口path */
        private String tokenPath;
        /** 刷新token的接口path */
        private String refreshTokenPath;
        /** 登出graphQl接口-query指定 */
        private String signOutQuery;
        /** 用户信息查询 */
        private String userInfoQuery;

        // 设置默认值
        public Sso() {
            this.path = "/api/graphql";
            this.query = "mutation { validToken { data } }";
            this.port = 6200;
            this.tokenPath = "/oauth2/token";
            this.refreshTokenPath = "/oauth2/refresh";
            this.signOutQuery = "mutation { signOut { data } }";
            this.userInfoQuery = "{me { id name} }";
        }
    }

    private DataBaseConfig getSystemConfigFromCache() {
        return inspCommonConfigService.getCommonConfigCache().getDataBaseConfig();
    }

    public String getServerIp() {
        DataBaseConfig dataBaseConfig = getSystemConfigFromCache();
        String dbBaseServerIp = dataBaseConfig.getDbBaseServerIp();
        if (StrUtil.isEmpty(dbBaseServerIp)) {
            throw new InspectionException("请配置数据底座服务器IP");
        }
        return dbBaseServerIp;
    }

    public String getClientId() {
        DataBaseConfig dataBaseConfig = getSystemConfigFromCache();
        String dbBaseClientId = dataBaseConfig.getDbBaseClientId();
        if (StrUtil.isEmpty(dbBaseClientId)) {
            throw new InspectionException("请配置数据底座服务器ID");
        }
        return dbBaseClientId;

    }

    public String getSecret() {
        DataBaseConfig dataBaseConfig = getSystemConfigFromCache();
        String dbBaseSecret = dataBaseConfig.getDbBaseSecret();
        if (StrUtil.isEmpty(dbBaseSecret)) {
            throw new InspectionException("请配置数据底座服务器密钥");
        }
        return dbBaseSecret;
    }

    public String getRedirects() {
        String page = "/design";
        // 判断是否是第一次登录 如果是则定向到系统预设置页面 否则就是主页
        if (redisUtils.get(CONFIG_FINISH) == null) {
            page = "/systemPreset";
        }
        return "http://" + serverHost + page;
    }

    public String getRealSrvApiBaseUrl() {
        String currentServerIp = getServerIp();
        return "http://" + currentServerIp + ":" + apiBasePort;
    }

    public String getRealSrvGetTokenUrl() {
        String apiBase = getRealSrvApiBaseUrl();
        return apiBase + getTokenApiUrl;
    }

    public String getRealSrvWritePointsUrl() {
        String apiBase = getRealSrvApiBaseUrl();
        return apiBase + writePointsApiUrl;
    }

    public String getRealSrvFindPointsUrl() {
        String apiBase = getRealSrvApiBaseUrl();
        return apiBase + findPointsApiUrl;
    }

    public String getSsoUrl() {
        String currentServerIp = getServerIp();
        return "http://" + currentServerIp;
    }
}
