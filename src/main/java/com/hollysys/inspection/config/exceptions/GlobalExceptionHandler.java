package com.hollysys.inspection.config.exceptions;


import cn.darkjrong.license.core.common.exceptions.LicenseException;
import cn.dev33.satoken.exception.NotLoginException;
import com.hollysys.inspection.controller.base.BaseApiController;
import com.hollysys.inspection.controller.base.Resp;
import de.schlichtherle.license.LicenseContentException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import java.time.DateTimeException;

@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler extends BaseApiController {

    @Value("${spring.servlet.multipart.max-file-size}")
    private String maxFileSize;

    @ExceptionHandler(LicenseException.class)
    public Object licenseException(LicenseException e) {
        log.error("throw LicenseException", e);
        Resp<String> failed = failed(e.getMessage());
        // HTTP_PAYMENT_REQUIRED
        failed.setCode(402);
        return failed;
    }

    @ExceptionHandler(LicenseContentException.class)
    public Object licenseContentException(LicenseContentException e) {
        log.error("throw LicenseContentException", e);
        Resp<String> failed = failed(e.getMessage());
        // HTTP_PAYMENT_REQUIRED
        failed.setCode(402);
        return failed;
    }

    @ExceptionHandler(Exception.class)
    public Object exception(Exception e) {
        log.error("throw Exception", e);
        // 未知异常消息不暴露给前端
        return failed("系统内部错误,请联系管理员处理.");
    }

    @ExceptionHandler(InspectionException.class)
    public Object exception(InspectionException e) {
        log.error("throw InspectionException", e);
        Resp<String> failed = failed(e.getMessage());
        int inspStatusCode = e.getInspStatusCode();
        failed.setCode(inspStatusCode);
        return failed;
    }

    @ExceptionHandler(NotSupportOnvifException.class)
    public Object exception(NotSupportOnvifException e) {
        log.error("throw NotSupportOnvifException", e);
        return success("NotSupportOnvif");
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Object handleValidationException(MethodArgumentNotValidException e) {
        log.error("参数校验失败，请求参数：{}", e.getBindingResult().getTarget(), e);
        // 获取第一条错误的默认消息（不包含字段名）
        String errorMessage = e.getBindingResult()
                .getFieldErrors()
                .stream()
                .map(FieldError::getDefaultMessage)  // 只取错误消息
                .findFirst()                         // 只取第一个错误
                .orElse("参数校验失败");

        return failed(errorMessage);
    }

    @ExceptionHandler(NotLoginException.class)
    public Object notLoginException(NotLoginException e) {
        log.error("throw notLoginException", e);
        Resp<String> failed = failed("会话已过期，请重新登录.");
        failed.setCode(109);
        return failed;
    }

    @ExceptionHandler(DateTimeException.class)
    public Object dateTimeException(DateTimeException e) {
        log.error("throw DateTimeException", e);
        Resp<String> failed = failed("参数格式错误，请检查.");
        failed.setCode(400);
        return failed;
    }

    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public Object uploadException(MaxUploadSizeExceededException e) {
        log.error("throw MaxUploadSizeExceededException", e);
        Resp<String> failed = failed(String.format("文件大小超过限制%s,请核对后再试.", maxFileSize));
        failed.setCode(406);
        return failed;
    }
}
