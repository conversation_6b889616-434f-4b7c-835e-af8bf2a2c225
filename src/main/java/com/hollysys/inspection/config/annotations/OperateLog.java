package com.hollysys.inspection.config.annotations;

import com.hollysys.inspection.constants.operatelog.BusinessClassify;
import com.hollysys.inspection.constants.operatelog.OperateType;

import java.lang.annotation.*;

/**
 * 操作记录切点注解
 * 注解使用必须遵守以下规则
 * {@link OperateType#CREATE}:方法返回值必须是新增后的数据对象或对象集合
 * {@link OperateType#UPDATE}:方法返回值必须是更新后的数据对象或对象集合
 * {@link OperateType#DELETE}:方法返回值必须是删除的数据对象或对象集合
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OperateLog {
    OperateType operateType();

    BusinessClassify businessClassify();

    String message() default "";
}
