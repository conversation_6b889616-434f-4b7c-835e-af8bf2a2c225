package com.hollysys.inspection.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

@Slf4j
@Configuration
public class ThreadPoolConfig {

    @Value("${executor.realtime.core_pool_size:10}")
    private Integer realtimeCorePoolSize;

    @Value("${executor.realtime.max_pool_size:10}")
    private Integer realtimeMaxPoolSize;

    @Value("${executor.realtime.queue_capacity:500}")
    private Integer realtimeQueueCapacity;

    @Value("${executor.realtime.keep_alive_seconds:0}")
    private Integer realtimeKeepAliveSeconds;

    @Value("${executor.realtime.await_termination_seconds:5}")
    private Integer realtimeAwaitTerminationSeconds;

    @Bean("realtimeExecutor")
    public Executor customThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(realtimeCorePoolSize);  // 核心线程数
        executor.setMaxPoolSize(realtimeMaxPoolSize);   // 最大线程数
        executor.setQueueCapacity(realtimeQueueCapacity); // 队列容量
        executor.setKeepAliveSeconds(realtimeKeepAliveSeconds); // 空闲线程最大保留时间（秒）
        executor.setThreadNamePrefix("realtimeExecutor-");
        // 配置线程超时处理
        executor.setAwaitTerminationSeconds(realtimeAwaitTerminationSeconds);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 配置异常处理策略
        executor.setRejectedExecutionHandler((r, exec) -> log.warn("Task rejected due to realtimeExecutor overload."));
        executor.initialize();
        return executor;
    }
}
