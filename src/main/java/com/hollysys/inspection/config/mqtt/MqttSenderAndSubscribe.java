package com.hollysys.inspection.config.mqtt;

import com.hollysys.inspection.utils.CacheUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
@ConditionalOnProperty(name = "spring.mqtt.enable", havingValue = "true")
public class MqttSenderAndSubscribe {
    @Resource
    private MqttGateway myGateway;

    @Resource
    private MqttPahoMessageDrivenChannelAdapter adapter;

    public void publish(String topic, String message) {
        myGateway.sendToMqtt(topic, message);
    }

    public void subscribe(String name) {
        Boolean exist = CacheUtils.CACHE_MAP.get(name);
        if (exist == null) {
            adapter.addTopic(name, 1);
            CacheUtils.CACHE_MAP.put(name, true);
        }
    }

    public void removeTopic(String topic) {
        if (StringUtils.isNotBlank(topic)) {
            adapter.removeTopic(topic);
        }
    }
}
