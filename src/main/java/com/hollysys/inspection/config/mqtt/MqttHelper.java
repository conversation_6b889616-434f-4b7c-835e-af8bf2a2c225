package com.hollysys.inspection.config.mqtt;

import com.hollysys.inspection.config.rabbitmq.RabbitHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class MqttHelper {

    private static final Logger logger = LoggerFactory.getLogger(RabbitHelper.class);

    @Autowired(required = false)
    private MqttSenderAndSubscribe mqttSenderAndSubscribe;

    public void removeTopic(String valName) {
        if (Objects.nonNull(mqttSenderAndSubscribe)) {
            mqttSenderAndSubscribe.removeTopic(valName);
        } else {
            logger.error("Mqtt未初始化，removeTopic执行失败");
        }
    }

    public void subscribe(String valName) {
        if (Objects.nonNull(mqttSenderAndSubscribe)) {
            mqttSenderAndSubscribe.subscribe(valName);
        } else {
            logger.error("Mqtt未初始化，subscribe执行失败");
        }
    }

    public void publish(String topic, String msg) {
        if (Objects.nonNull(mqttSenderAndSubscribe)) {
            mqttSenderAndSubscribe.publish(topic, msg);
        } else {
            logger.error("Mqtt未初始化，publish执行失败");
        }
    }
}
