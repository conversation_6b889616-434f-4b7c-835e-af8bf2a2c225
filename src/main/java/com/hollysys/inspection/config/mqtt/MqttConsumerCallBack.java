package com.hollysys.inspection.config.mqtt;

import com.hollysys.inspection.utils.CacheUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHandler;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.MessagingException;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MqttConsumerCallBack implements MessageHandler {

    @Value("${power.publish-topic.img_python_path1}")
    private String powerImgPythonTopic1;
    @Value("${power.publish-topic.img_python_path2}")
    private String powerImgPythonTopic2;
    @Value("${power.publish-topic.img_python_path3}")
    private String powerImgPythonTopic3;
    @Value("${power.publish-topic.img_python_path4}")
    private String powerImgPythonTopic4;


    @Override
    public void handleMessage(Message<?> message) throws MessagingException {
        Object payload = message.getPayload();
        MessageHeaders headers = message.getHeaders();
        String mqttReceivedTopic = headers.get("mqtt_receivedTopic", String.class);
        // log.info("接收到的消息={}，topic={}", payload, mqttReceivedTopic);
        if (powerImgPythonTopic1.equals(mqttReceivedTopic) || powerImgPythonTopic2.equals(mqttReceivedTopic) ||
                powerImgPythonTopic3.equals(mqttReceivedTopic) || powerImgPythonTopic4.equals(mqttReceivedTopic)) {
            // 切换调度状态
            CacheUtils.IMG_MAP.put(mqttReceivedTopic, (String) payload);
        }
    }
}
