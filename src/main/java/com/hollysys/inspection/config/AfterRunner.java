package com.hollysys.inspection.config;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.cron.CronUtil;
import com.hollysys.inspection.service.*;
import com.hollysys.inspection.service.protocol.impl.CameraProtocolDh;
import com.hollysys.inspection.service.protocol.impl.CameraProtocolHik;
import com.hollysys.inspection.service.protocol.impl.CameraProtocolYs;
import com.hollysys.inspection.task.FreshLicTimeTask;
import com.hollysys.inspection.task.ProjectScheduleTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

import static com.hollysys.inspection.listener.InspLicenseVerifyListener.cipher;

@Slf4j
@Component
public class AfterRunner implements ApplicationRunner {

    @Resource
    private NtpManageService ntpManageService;

    @Value("${schedule.refresh-node-status-enable}")
    private boolean refreshNodeStatusEnable;

    @Value("${schedule.execute-task-interval}")
    private int executeTaskInterval;

    @Value("${schedule.execute-task-enable}")
    private boolean executeTaskEnable;

    @Value("${logging.dh-log-open}")
    private boolean openDhLog;

    @Value("${logging.logPath}")
    private String logDirPath;

    @Resource
    private ProjectTreeNodeService projectTreeNodeService;

    @Resource
    private FileServeService fileServeService;

    @Resource
    private CameraProtocolHik cameraProtocolHik;

    @Resource
    private CameraProtocolYs cameraProtocolYs;

    @Resource
    private CameraProtocolDh cameraProtocolDh;

    @Resource
    private SystemBackupService systemBackupService;

    @Resource
    private InspCommonConfigService inspCommonConfigService;

    @Resource
    private MediamtxService mediamtxService;

    @Override
    public void run(ApplicationArguments args) throws IOException {
        // 初始化大华SDK
        cameraProtocolDh.init(null, null, openDhLog, logDirPath);
        // 初始化海康SDK
        cameraProtocolHik.init();
        // 初始化宇视SDK
        cameraProtocolYs.init();
        if (executeTaskEnable) {
            // 巡检任务定时调度启动
            CronUtil.schedule("*/" + executeTaskInterval + " * * * * *", new ProjectScheduleTask());
        } else {
            log.warn("巡检任务定时调度开关关闭，调度任务未启动");
        }

        // 文件服务器相关逻辑初始化
        fileServeService.init();

        // 系统配置初始化
        inspCommonConfigService.freshCache();

        // 获取物理机IP列表 然后将第一个IP插入到多网口字段中
        mediamtxService.setIpForMtx();

        // 支持秒级别定时任务
        CronUtil.setMatchSecond(true);
        CronUtil.start();

        // 从FTP服务同步时间调度任务启动
        ntpManageService.scheduleSyncDateFromNtp();

        // 开启定时备份任务
        systemBackupService.initBackupSchedule();
        // not a BUG
        CronUtil.schedule("0 0/1 * * * ?", new FreshLicTimeTask(cipher));
    }

    @Scheduled(fixedDelayString = "${schedule.refresh-node-status-interval}", timeUnit = TimeUnit.SECONDS)
    public void freshChannelNodeState() {
        if (!refreshNodeStatusEnable) {
            return;
        }
        TimeInterval timer = DateUtil.timer();
        try {
            projectTreeNodeService.freshChannelNodeState();
        } catch (Exception exception) {
            log.error("FreshNodeState error... ", exception);
        } finally {
            log.debug("刷新通道节点状态，用时{}毫秒", timer.interval());
        }
    }
}
