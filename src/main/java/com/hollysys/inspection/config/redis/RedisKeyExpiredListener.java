package com.hollysys.inspection.config.redis;

import cn.hutool.core.thread.ThreadUtil;
import com.hollysys.inspection.constants.ScheduleTaskMode;
import com.hollysys.inspection.constants.WebsocketEventType;
import com.hollysys.inspection.controller.websocket.WebSocketOsdServer;
import com.hollysys.inspection.model.socket.WebsocketMessage;
import com.hollysys.inspection.service.InspGlobalVariableService;
import com.hollysys.inspection.service.InspScheduleTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

import static cn.hutool.core.text.CharSequenceUtil.startWith;
import static com.hollysys.inspection.constants.InspConstants.CHANNEL_MODE_RECOVER;
import static com.hollysys.inspection.constants.InspConstants.TASK_MODE_RECOVER;

/**
 * <AUTHOR> Thou
 */
@Slf4j
@Component
public class RedisKeyExpiredListener implements MessageListener {

    @Resource
    private InspGlobalVariableService globalVariableService;

    @Resource
    private InspScheduleTaskService scheduleTaskService;

    @Resource
    private WebSocketOsdServer webSocketOsdServer;

    @Override
    public void onMessage(Message message, @Nullable byte[] pattern) {
        String key = new String(message.getBody(), StandardCharsets.UTF_8);
        log.debug("redis client on message::{} ", key);

        // 质量位超时删除
        ThreadUtil.execute(() ->
                globalVariableService.onQualityBitTimeout(key));

        // if (startWith(key, TASK_MODE_RECOVER)) {
        //     String channelId = key.split(":")[1];
        //
        //     InspChannelInfo channelInfo = new InspChannelInfo();
        //     channelInfo.setId(channelId);
        //     channelInfo.setMode(ChannelMode.AUTOMATIC.name());
        //     inspChannelInfoService.switchMode(channelInfo);
        //
        //     // 发送消息，通知前端通道模式发生变化
        //     WebsocketMessage websocketMessage = new WebsocketMessage();
        //     websocketMessage.setChannelId(channelId);
        //     websocketMessage.setEventType(WebsocketEventType.SWITCH_CHANNEL_MODE);
        //     webSocketOsdServer.sendMessageToChannel(websocketMessage);
        // }

        if (startWith(key, TASK_MODE_RECOVER)) {
            String taskId = key.split(":")[1];
            // 切换任务为自动状态
            scheduleTaskService.resumeTask(taskId);
        }

        if (startWith(key, CHANNEL_MODE_RECOVER)) {
            String channelId = key.split(":")[1];
            // 发送消息到前端，刷新页面
            WebsocketMessage websocketMessage = new WebsocketMessage();
            websocketMessage.setEventType(WebsocketEventType.SWITCH_MODE);
            websocketMessage.setChannelId(channelId);
            websocketMessage.setChannelMode(ScheduleTaskMode.AUTOMATIC);
            webSocketOsdServer.sendMessageToChannel(websocketMessage);
        }
    }
}
