package com.hollysys.inspection.config.redis;

import cn.hutool.core.util.StrUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.stream.MapRecord;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.concurrent.TimeUnit;

@Component
public class RedisHelper extends CachingConfigurerSupport {
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private RedisKeyExpiredListener redisMessageListener;

    @Value("${spring.redis.database:0}")
    private int db;

    @Bean
    public ChannelTopic expiredTopic() {
        return new ChannelTopic(String.format("__keyevent@%s__:expired", db));
    }

    @Bean
    public RedisMessageListenerContainer redisMessageListenerContainer(
            ChannelTopic expiredTopic,
            RedisConnectionFactory redisConnectionFactory) {
        RedisMessageListenerContainer redisMessageListenerContainer = new RedisMessageListenerContainer();
        redisMessageListenerContainer.setConnectionFactory(redisConnectionFactory);
        redisMessageListenerContainer.addMessageListener(redisMessageListener, expiredTopic);
        return redisMessageListenerContainer;
    }


    /**
     * 删除key
     *
     * @param key 键
     */
    public void delete(String key) {
        stringRedisTemplate.delete(key);
    }

    /**
     * 批量删除key
     *
     * @param keys 键
     */
    public void delete(Collection<String> keys) {
        stringRedisTemplate.delete(keys);
    }

    /**
     * 设置指定 key 的值
     *
     * @param key   键
     * @param value 值
     */
    public void set(String key, String value) {
        stringRedisTemplate.opsForValue().set(key, value);
    }

    /**
     * 获取指定 key 的值
     *
     * @param key 键
     * @return value值
     */
    public String get(String key) {
        return stringRedisTemplate.opsForValue().get(key);
    }

    /**
     * 将给定 key 的值设为 value ，并返回 key 的旧值(old value)
     *
     * @param key   键
     * @param value 值
     * @return value值
     */
    public String getAndSet(String key, String value) {
        return stringRedisTemplate.opsForValue().getAndSet(key, value);
    }

    /**
     * 将值 value 关联到 key ，并将 key 的过期时间设为 timeout
     *
     * @param key     键
     * @param value   值
     * @param timeout 延迟时间
     *                过期时间
     * @param unit    时间单位, 天:TimeUnit.DAYS 小时:TimeUnit.HOURS 分钟:TimeUnit.MINUTES
     *                秒:TimeUnit.SECONDS 毫秒:TimeUnit.MILLISECONDS
     */
    public void setEx(String key, String value, long timeout, TimeUnit unit) {
        stringRedisTemplate.opsForValue().set(key, value, timeout, unit);
    }

    public Long incrementCounter(String key) {
        if (StrUtil.isBlank(key)) {
            throw new RuntimeException("incrementCounter 错误， key 为空");
        }
        return stringRedisTemplate.opsForValue().increment(key);
    }

    /**
     * 添加数据到stream
     * @param mapRecord 数据键值对
     */
    public void xAdd(MapRecord<String, String, byte[]> mapRecord){
        stringRedisTemplate.opsForStream().add(mapRecord);
    }
    /**
     * 删除stream数据，保持stream中的数据总数为 maxLen
     * @param streamKey stream
     * @param maxLen stream中的数据总数
     */
    public void xTrim(String streamKey, long maxLen){
        stringRedisTemplate.opsForStream().trim(streamKey, maxLen);
    }

}
