package com.hollysys.inspection.config.rabbitmq;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ReturnedMessage;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

@Slf4j
public class RabbitReturnsCallback implements RabbitTemplate.ReturnsCallback {

    @Override
    public void returnedMessage(ReturnedMessage returnedMessage) {
        log.info("消息主体: {}", returnedMessage.getMessage());
        log.info("回复编码: {}", returnedMessage.getReplyCode());
        log.info("回复内容: {}", returnedMessage.getReplyText());
        log.info("交换器: {}", returnedMessage.getExchange());
        log.info("路由键: {}", returnedMessage.getRoutingKey());
    }
}
