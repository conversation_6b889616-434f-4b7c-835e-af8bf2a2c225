package com.hollysys.inspection.config.rabbitmq;

import cn.hutool.extra.spring.SpringUtil;
import com.hollysys.inspection.constants.InspRabbitConstans;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class RabbitHelper {

    private static final Logger logger = LoggerFactory.getLogger(RabbitHelper.class);

    @Autowired(required = false)
    private DirectExchange alarmExchange;

    // @Autowired
    // // @Resource
    // private RabbitTemplate rabbitTemplate;

    public void initCallback(RabbitTemplate.ConfirmCallback confirmCallback, RabbitTemplate.ReturnsCallback returnsCallback) {
        RabbitTemplate rabbitTemplate = getSpringBean(RabbitTemplate.class);
        if (Objects.nonNull(rabbitTemplate)) {
            rabbitTemplate.setConfirmCallback(confirmCallback);
            rabbitTemplate.setReturnsCallback(returnsCallback);
        } else {
            logger.error("Rabbit未初始化，Callback函数设置失败");
        }
    }

    public void convertAndSend(String routingKey, Object object) {
        RabbitTemplate rabbitTemplate = getSpringBean(RabbitTemplate.class);
        if (Objects.nonNull(rabbitTemplate)) {
            rabbitTemplate.convertAndSend(routingKey, object);
        } else {
            logger.error("Rabbit未初始化，消息发送失败");
        }
    }


    public void declareBinding(String valName) {
        AmqpAdmin amqpAdmin = getSpringBean(AmqpAdmin.class);
        if (Objects.nonNull(amqpAdmin)) {
            Queue queue = new Queue(valName);
            amqpAdmin.declareQueue(queue);
            amqpAdmin.declareBinding(BindingBuilder.bind(queue).to(alarmExchange).with(InspRabbitConstans.ALARM_ROUTE_KET));
        } else {
            logger.error("Rabbit未初始化，declareBinding失败");
        }
    }

    public void deleteQueue(String valName) {
        AmqpAdmin amqpAdmin = getSpringBean(AmqpAdmin.class);
        if (Objects.nonNull(amqpAdmin)) {
            amqpAdmin.deleteQueue(valName);
        } else {
            logger.error("Rabbit未初始化，deleteQueue失败");
        }
    }

    private <T> T getSpringBean(Class<T> clazz) {
        try {
            return SpringUtil.getBean(clazz);
        } catch (Exception ex) {
            logger.error("getSpringBean失败:{}", ex.getMessage());
            return null;
        }
    }
}
