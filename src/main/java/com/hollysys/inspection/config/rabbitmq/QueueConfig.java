package com.hollysys.inspection.config.rabbitmq;

import com.hollysys.inspection.constants.InspRabbitConstans;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Slf4j
@Configuration
public class QueueConfig {
    @Value("${spring.rabbitmq.enable}")
    private boolean enable;

    @Resource
    private RabbitHelper rabbitHelper;

    @PostConstruct
    public void initCallback() {
        rabbitHelper.initCallback(new RabbitConfirmCallBack(), new RabbitReturnsCallback());
    }

    @Bean
    public DirectExchange alarmExchange() {
        return new DirectExchange(InspRabbitConstans.ALARM_EXCHANGE);
    }

    @Bean
    public Consumer initRabbitMessageQueueReceiver() {
        if (enable) {
            Consumer rabbitMessageQueueReceiver = new Consumer();
            log.info("【------已启用------】Consumer");
            return rabbitMessageQueueReceiver;
        } else {
            log.info("【------不启用------】Consumer");
            return null;
        }
    }
}
