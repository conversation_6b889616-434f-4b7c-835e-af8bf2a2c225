package com.hollysys.inspection.config.rabbitmq;

import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Headers;

import java.io.IOException;
import java.util.Map;

@Slf4j
public class Consumer {

    @RabbitHandler
    @RabbitListener(queuesToDeclare = @Queue("QueueNames.Test"))
    public void consumer1(String message, @Headers Map<String, Object> headers, Channel channel) throws IOException {
        Long deliveryTag = (Long) headers.get(AmqpHeaders.DELIVERY_TAG);
        try {
            log.info("收到通知：" + message);
            channel.basicAck(deliveryTag, false);
        } catch (Exception e) {
            // ack返回false，requeue-true并重新回到队列
            channel.basicNack(deliveryTag, false, true);
        }
    }
}
