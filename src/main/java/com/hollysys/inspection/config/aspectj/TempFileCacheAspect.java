package com.hollysys.inspection.config.aspectj;

import com.hollysys.inspection.utils.TempFileCache;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class TempFileCacheAspect {

    @Pointcut("@annotation(com.hollysys.inspection.config.annotations.TempFileCleanable)")
    public void pointcut() {
    }

    @After("pointcut()")
    public void doAfter()  {
        // TODO 判断当前方法名称，否则会有两个地方的缓存，被同时删除
        TempFileCache.clean();
    }
}
