package com.hollysys.inspection.config.aspectj;

import cn.hutool.core.util.StrUtil;
import com.hollysys.inspection.config.annotations.AutoClearOsd;
import com.hollysys.inspection.controller.websocket.WebSocketOsdServer;
import com.hollysys.inspection.utils.AspectUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Aspect
@Component
public class AutoClearOsdAspect {

    @Resource
    private WebSocketOsdServer webSocketOsdServer;

    @Pointcut("@annotation(com.hollysys.inspection.config.annotations.AutoClearOsd)")
    public void pointcut() {
    }

    @Before("pointcut()")
    public void doBefore(JoinPoint joinPoint) {
        // 获取当前方法的注解对象
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        AutoClearOsd annotation = signature.getMethod().getAnnotation(AutoClearOsd.class);
        String spelStr = annotation.channelId();
        String channelId = StrUtil.toString(AspectUtil.parseRtParam(spelStr, joinPoint));
        // PTZ开始变化后，尝试清除web端OSD
        if (StrUtil.isNotBlank(channelId)) {
            log.debug("通道[{}]下预置点发生修改，发送OSD清除消息", channelId);
            webSocketOsdServer.cleanWebOSD(channelId);
        }
    }
}
