package com.hollysys.inspection.config.aspectj;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import com.hollysys.inspection.config.annotations.OperateLog;
import com.hollysys.inspection.constants.operatelog.BusinessClassify;
import com.hollysys.inspection.constants.operatelog.OperateType;
import com.hollysys.inspection.entity.InspLogOperate;
import com.hollysys.inspection.entity.InspSystemUser;
import com.hollysys.inspection.model.sso.UserInfoModel;
import com.hollysys.inspection.service.InspLogOperateService;
import com.hollysys.inspection.service.InspSystemUserService;
import com.hollysys.inspection.service.sso.HsmAuthTokenService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

@Slf4j
@Aspect
@Component
public class OperateLogAspect {


    @Value("${db-base.auth-srv.enabled}")
    private Boolean authSrvEnabled;

    @Resource
    private InspSystemUserService systemUserService;

    @Resource
    private HsmAuthTokenService hsmAuthService;

    @Resource
    private InspLogOperateService logOperateService;

    @Pointcut("@annotation(com.hollysys.inspection.config.annotations.OperateLog)")
    public void pointcut() {
    }

    @AfterReturning(value = "pointcut()", returning = "obj")
    public void doAfter(JoinPoint joinPoint, Object obj) {
        String userName = null;
        try {
            if (authSrvEnabled) {
                // 获取当前登录用户信息
                UserInfoModel userInfo = hsmAuthService.getUserInfo();
                if (Objects.isNull(userInfo)) {
                    log.error("操作日志记录失败，无法获取登录用户信息");
                    return;
                }
                userName = userInfo.getUserName();
            } else {
                // 如果开启saToken，则优先使用saToken
                // 判断登录状态
                if (!StpUtil.isLogin()) {
                    return;
                }
                // 获取当前登录用户ID
                Object loginId = StpUtil.getLoginId();
                // 操作人用户名称
                InspSystemUser systemUser = systemUserService.getById(loginId.toString());
                if (Objects.nonNull(systemUser)) {
                    userName = systemUser.getName();
                }
            }
        } catch (Exception e) {
            return;
        }

        if (StrUtil.isEmpty(userName)) {
            log.error("操作日志记录失败，获取登录用户名为空");
            return;
        }

        // 获取当前方法的注解对象
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        OperateLog annotation = signature.getMethod().getAnnotation(OperateLog.class);
        OperateType operateType = annotation.operateType();
        BusinessClassify businessClassify = annotation.businessClassify();
        String message = annotation.message();
        InspLogOperate operateObj = logOperateService.buildMessageAndOperateIds(message, operateType, businessClassify, obj);

        logOperateService.log(userName, operateType, businessClassify, operateObj);
    }
}
