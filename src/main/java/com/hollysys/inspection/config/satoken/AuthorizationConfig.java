package com.hollysys.inspection.config.satoken;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;

@Configuration
public class AuthorizationConfig implements WebMvcConfigurer {

    @Value("${sa-token.include-path}")
    private String[] includePathList;

    @Value("${sa-token.exclude-path}")
    private String[] excludePathList;

    @Resource
    private MySaInterceptor saInterceptor;

    /**
     * 注册sa-token拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册 Sa-Token 拦截器，校验规则为 StpUtil.checkLogin() 登录校验。
        saInterceptor.setAuth(handle -> StpUtil.checkLogin());
        registry.addInterceptor(saInterceptor)
                .addPathPatterns(CollectionUtil.newArrayList(includePathList))
                .excludePathPatterns(CollectionUtil.newArrayList(excludePathList));
    }
}
