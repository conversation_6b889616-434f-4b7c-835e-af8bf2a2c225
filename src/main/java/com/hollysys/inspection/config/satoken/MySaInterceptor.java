package com.hollysys.inspection.config.satoken;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.config.sso.HsmAuthProperties;
import com.hollysys.inspection.service.InspThirdPermissionService;
import com.hollysys.inspection.service.sso.HsmAuthTokenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;

@Slf4j
@Component
public class MySaInterceptor extends SaInterceptor {

    @Value("${db-base.auth-srv.enabled}")
    private Boolean authSrvEnabled;


    @Resource
    private InspThirdPermissionService thirdPermissionService;

    @Resource
    private HsmAuthTokenService hsmAuthService;

    @Resource
    private HsmAuthProperties hsmAuthProperties;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String requestURI = request.getRequestURI();
        if (StrUtil.isNotBlank(requestURI) && requestURI.startsWith("/open-api/")) {
            // 南向接口鉴权
            String licence = ServletUtil.getHeader(request, HttpHeaders.AUTHORIZATION, StandardCharsets.UTF_8);
            String method = request.getMethod();
            thirdPermissionService.checkPermission(licence, method, requestURI);
            return true;
        }

        // SSO鉴权
        if (authSrvEnabled) {
            String token = ServletUtil.getHeader(request, HttpHeaders.AUTHORIZATION, StandardCharsets.UTF_8);
            if (StrUtil.isEmpty(token)) {
                throw new InspectionException("请求token为空");
            }
            hsmAuthService.validSsoToken(token);
            return true;
        } else {
            // SaToken授权
            // 操作员账户永不过期
            String tokenValue = StpUtil.getTokenValue();
            String loginId = StpUtil.stpLogic.getLoginIdNotHandle(tokenValue);
            if ("2500050680240cbd9760609f474f03b5".equals(loginId)) {
                // 无限续期
                StpUtil.updateLastActivityToNow();
            }
            boolean result;
            try {
                result = super.preHandle(request, response, handler);
            } catch (Exception exception) {
                log.error("preHandle occur exception, request url = {}", requestURI, exception);
                throw exception;
            }
            return result;
        }
    }
}
