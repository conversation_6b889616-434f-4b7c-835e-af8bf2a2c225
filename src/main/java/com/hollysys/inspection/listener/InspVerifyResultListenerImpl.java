package com.hollysys.inspection.listener;

import cn.darkjrong.license.core.common.listener.VerifyResultListener;
import com.hollysys.inspection.utils.LicenseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class InspVerifyResultListenerImpl implements VerifyResultListener {

    @Override
    public void verifyFailed() {
        LicenseUtil.IS_VERIFY_SUCCESS.set(false);
    }

    @Override
    public void verifySuccess() {
        LicenseUtil.IS_VERIFY_SUCCESS.set(true);
    }
}
