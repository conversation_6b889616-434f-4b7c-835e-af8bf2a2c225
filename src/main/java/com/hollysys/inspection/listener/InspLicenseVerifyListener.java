package com.hollysys.inspection.listener;


import cn.darkjrong.license.core.common.exceptions.LicenseException;
import cn.darkjrong.license.core.common.listener.VerifyListener;
import cn.darkjrong.license.core.common.pojo.params.LicenseExtraParam;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.hollysys.inspection.service.LicenseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.File;

@Slf4j
@Component
public class InspLicenseVerifyListener implements VerifyListener {

    @Value("${file-server.root-dir}")
    private String fileRootDir;

    @Resource
    private LicenseService licenseService;

    public static File cipher;

    @PostConstruct
    public void init() {
        cipher = FileUtil.file(fileRootDir, ".cipher");
        if (!FileUtil.exist(cipher)) {
            FileUtil.writeBytes(RandomUtil.randomBytes(8), cipher);
        }
    }

    @Override
    public boolean verify(LicenseExtraParam param) throws LicenseException {
        // 保存license信息到redis中
        String map = JSON.toJSONString(param);
        licenseService.saveLicenseExtraParam(map);

        if (log.isDebugEnabled()) {
            log.debug("cpuSerial {}", param.getCpuSerial());
            log.debug("mainBoardSerial {}", param.getMainBoardSerial());
            log.debug("ipAddress {}", param.getIpAddress());
            log.debug("macAddress {}", param.getMacAddress());
        }

        // 如果文件不存在则创建
        if (!FileUtil.exist(cipher)) {
            FileUtil.writeBytes(RandomUtil.randomBytes(8), cipher);
            return true;
        }
        // 锚点文件的时间大于当前时间，说明系统时间被修改为了过去时间，则认为授权异常
        if (cipher.lastModified() > System.currentTimeMillis()) {
            log.error("由于系统时间发生异常变化，授权校验失败");
            return false;
        }
        return true;
    }
}
