package com.hollysys.inspection.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.hollysys.inspection.config.exceptions.ChannelGetPicException;
import com.hollysys.inspection.config.exceptions.execute.ExecuteBeforeException;
import com.hollysys.inspection.constants.FileDirEnum;
import com.hollysys.inspection.constants.MsgLevel;
import com.hollysys.inspection.constants.WebsocketEventType;
import com.hollysys.inspection.constants.algorithm.ExecuteResultStatus;
import com.hollysys.inspection.constants.algorithm.QualityBitEnum;
import com.hollysys.inspection.constants.project.OnlineState;
import com.hollysys.inspection.controller.websocket.ExecutePresetServer;
import com.hollysys.inspection.controller.websocket.WebSocketOsdServer;
import com.hollysys.inspection.entity.*;
import com.hollysys.inspection.model.algorithm.execute.AlgorithmExecuteRsp;
import com.hollysys.inspection.model.algorithm.execute.OutputValueObj;
import com.hollysys.inspection.model.algorithm.osd.OSDItem;
import com.hollysys.inspection.model.context.ExecuteAlgorithmCtx;
import com.hollysys.inspection.model.context.ExecutePresetCtx;
import com.hollysys.inspection.model.socket.ExecuteMsg;
import com.hollysys.inspection.model.socket.WebsocketMessage;
import com.hollysys.inspection.service.*;
import com.hollysys.inspection.utils.MinioUtil;
import com.hollysys.inspection.utils.OsdConfigUtil;
import com.hollysys.inspection.utils.OsdUtil;
import com.hollysys.inspection.utils.OutputParamUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 预置点执行过程监听器
 */
@Slf4j
@Component
public class ExecutePresetListener {
    @Resource
    private InspRecordPresetService recordPresetService;

    @Resource
    private InspRecordAlgorithmService recordAlgorithmService;

    /**
     * 算法调试长连接
     */
    @Resource
    private ExecutePresetServer executePresetServer;

    /**
     * Web的OSD长连接
     */
    @Resource
    private WebSocketOsdServer webSocketOsdServer;

    @Resource
    private InspGlobalVariableService globalVariableService;

    @Resource
    private InspAlarmRecordService alarmRecordService;

    @Resource
    private InspThirdAlgorithmSubscriptionService inspThirdAlgorithmSubscriptionService;

    /**
     * 预置点开始执行时调用
     */
    public void onStart(ExecutePresetCtx presetCtx) {
        InspProjectTreeNode presetTreeNodeInfo = presetCtx.getPresetTreeNodeInfo();
        presetCtx.setStartTime(LocalDateTime.now());
        // 插入预置点执行记录
        // InspRecordPreset executeRecord = InspRecordPreset.buildByPresetCtx(presetCtx);
        // recordPresetService.save(executeRecord);
        // presetCtx.setRecordPreset(executeRecord);

        String msg = StrUtil.format("开始执行预置点：{}", presetTreeNodeInfo.getLabel());
        log.info(msg);
        sendLogToWeb(presetCtx, ExecuteMsg.create(MsgLevel.INFO, msg));
    }

    /**
     * 多次循环执行预置点开始时调用
     */
    public void onTimesStart(ExecutePresetCtx presetCtx, int time) {
        // 插入预置点单次执行记录
        InspRecordPreset executeRecord = InspRecordPreset.buildByPresetCtx(presetCtx);
        executeRecord.setTimes(time);
        executeRecord.setStartTime(LocalDateTime.now());
        recordPresetService.save(executeRecord);
        presetCtx.setRecordTimesPreset(executeRecord);

        InspProjectTreeNode presetTreeNodeInfo = presetCtx.getPresetTreeNodeInfo();
        String msg = StrUtil.format("预置点[{}]第[{}]次开始执行 ", presetTreeNodeInfo.getLabel(), time);
        sendLogToWeb(presetCtx, ExecuteMsg.create(MsgLevel.INFO, msg));
    }

    /**
     * 多次循环执行预置点时抛出异常时调用
     */
    public void onTimesException(ExecutePresetCtx presetCtx, int time, Exception exception) {
        // 更新预置点单次执行记录
        InspRecordPreset executeRecord = presetCtx.getRecordTimesPreset();
        executeRecord.setResultStatus(ExecuteResultStatus.FAILED.name());
        executeRecord.setFailureReason(exception.getMessage());
        executeRecord.setEndTime(LocalDateTime.now());
        recordPresetService.updateById(executeRecord);
        presetCtx.setRecordTimesPreset(executeRecord);

        // 质量位设置
        if (exception instanceof ChannelGetPicException || exception instanceof ExecuteBeforeException) {
            // PTZ跳转失败、截图失败、场景匹配失败、透视变换失败，质量位设置为Uncertain
            globalVariableService.saveQualityBitByPresetId(presetCtx.getPresetId(), QualityBitEnum.Uncertain);
        } else {
            // 其余情况设置为bad
            globalVariableService.saveQualityBitByPresetId(presetCtx.getPresetId(), QualityBitEnum.Bad);
        }
        InspProjectTreeNode presetTreeNodeInfo = presetCtx.getPresetTreeNodeInfo();
        String msg = StrUtil.format("预置点[{}]执行第[{}]次出现异常，异常信息为：{}", presetTreeNodeInfo.getLabel(), time, exception.getMessage());
        log.error(msg);
        sendLogToWeb(presetCtx, ExecuteMsg.create(MsgLevel.ERROR, msg));
    }

    /**
     * 算法开始执行时调用
     */
    public void onAlgorithmStart(ExecutePresetCtx presetCtx, ExecuteAlgorithmCtx algorithmCtx) {
        InspAlgorithmInstance algorithmInstance = algorithmCtx.getAlgorithmInstance();
        String msg = StrUtil.format("开始执行算法实例：{}", algorithmInstance.getName());
        log.info(msg);
        algorithmCtx.setStartTime(LocalDateTime.now());
        // 插入算法执行记录
        InspRecordAlgorithm executeRecord = InspRecordAlgorithm.buildByAlgorithmCtx(algorithmCtx);
        executeRecord.setRecordPresetId(presetCtx.getRecordTimesPreset().getId());
        recordAlgorithmService.save(executeRecord);
        algorithmCtx.setRecordAlgorithm(executeRecord);

        // 发送执行消息到前端
        sendLogToWeb(presetCtx, ExecuteMsg.create(MsgLevel.INFO, msg));
    }

    /**
     * 算法执行抛出异常时调用,此方法和onAlgorithmFinish只会触发一个
     * executeAlgorithm未调用之前出现异常
     */
    public void onAlgorithmException(ExecutePresetCtx presetCtx, ExecuteAlgorithmCtx algorithmCtx, Exception exception) {
        InspAlgorithmInstance algorithmInstance = algorithmCtx.getAlgorithmInstance();
        LocalDateTime now = LocalDateTime.now();
        algorithmCtx.setEndTime(now);
        // 更新执行记录
        InspRecordAlgorithm executeRecord = algorithmCtx.getRecordAlgorithm();
        executeRecord.setFailureReason(exception.getMessage());
        executeRecord.setResultStatus(ExecuteResultStatus.FAILED.name());
        executeRecord.setEndTime(now);

        // 移动输入图片到EXECUTE目录
        List<String> inputImages = algorithmCtx.getInputImages();
        if (CollectionUtil.isNotEmpty(inputImages)) {
            inputImages = inputImages.parallelStream().map(item -> MinioUtil.copyObject(item, FileDirEnum.EXECUTE)).collect(Collectors.toList());
            executeRecord.setInputImages(inputImages);
        }

        recordAlgorithmService.updateById(executeRecord);
        algorithmCtx.setRecordAlgorithm(executeRecord);

        // 质量位设置
        if (exception instanceof ChannelGetPicException || exception instanceof ExecuteBeforeException) {
            // PTZ跳转失败、截图失败、场景匹配失败、透视变换失败，质量位设置为Uncertain
            globalVariableService.saveQualityBitByAlgorithmInstId(algorithmCtx.getAlgorithmInstanceId(), QualityBitEnum.Uncertain);
        } else {
            // 其余情况设置为bad
            globalVariableService.saveQualityBitByAlgorithmInstId(algorithmCtx.getAlgorithmInstanceId(), QualityBitEnum.Bad);
        }

        String msg = StrUtil.format("算法实例[{}]执行出现异常，异常信息为：{}", algorithmInstance.getName(),
                exception.getMessage());
        log.error(msg);
        sendLogToWeb(presetCtx, ExecuteMsg.create(MsgLevel.ERROR, msg));
    }

    /**
     * 算法执行结束时调用,此方法和onAlgorithmException只会触发一个
     */
    public void onAlgorithmFinish(ExecutePresetCtx presetCtx, ExecuteAlgorithmCtx algorithmCtx) {
        AlgorithmExecuteRsp algorithmExecuteRsp = algorithmCtx.getAlgorithmExecuteRsp();
        LocalDateTime now = LocalDateTime.now();
        algorithmCtx.setEndTime(now);

        // 更新算法执行记录
        InspRecordAlgorithm executeRecord = algorithmCtx.getRecordAlgorithm();
        executeRecord.setExecuteResult(algorithmExecuteRsp);
        executeRecord.setResultStatus(algorithmExecuteRsp.getIsSuccess() ?
                ExecuteResultStatus.SUCCESSFUL.name() : ExecuteResultStatus.FAILED.name());
        executeRecord.setFailureReason(algorithmExecuteRsp.getMsg());
        // 移动文件到EXECUTE目录
        String resultImgUrl = algorithmExecuteRsp.getResultImgUrl();
        String moveResult = MinioUtil.copyObject(resultImgUrl, FileDirEnum.EXECUTE, true);
        executeRecord.setOutputImage(moveResult);
        executeRecord.setEndTime(now);

        // 移动输入图片到EXECUTE目录
        List<String> inputImages = algorithmCtx.getInputImages();
        if (CollectionUtil.isNotEmpty(inputImages)) {
            inputImages = inputImages.parallelStream().map(item -> MinioUtil.copyObject(item, FileDirEnum.EXECUTE)).collect(Collectors.toList());
            executeRecord.setInputImages(inputImages);
        }

        recordAlgorithmService.updateById(executeRecord);
        algorithmCtx.setRecordAlgorithm(executeRecord);

        InspAlgorithmInstance algorithmInstance = algorithmCtx.getAlgorithmInstance();
        ExecuteMsg executeMsg;
        if (BooleanUtil.isTrue(algorithmExecuteRsp.getIsSuccess())) {
            String msg = StrUtil.format("算法实例[{}]执行成功", algorithmInstance.getName());
            log.info(msg);
            executeMsg = ExecuteMsg.create(MsgLevel.INFO, msg);

            Map<String, OutputValueObj> output = algorithmExecuteRsp.getOutput();

            ThreadUtil.execute(() -> {
                // 变量值写入实时库
                globalVariableService.saveRealtimeValue(algorithmInstance, output);
            });
            ThreadUtil.execute(() -> {
                // 将算法结果写入实时库
                for (Map.Entry<String, OutputValueObj> objEntry : output.entrySet()) {
                    OutputParamUtil.writeToRedis(algorithmInstance.getId(), objEntry.getKey(), objEntry.getValue());
                }
            });

            ThreadUtil.execute(() -> {
                // 报警记录入库
                alarmRecordService.insertAlarmRecord(algorithmCtx, presetCtx.getTaskId(), algorithmExecuteRsp);
            });
            if (StrUtil.isBlank(presetCtx.getClientKey())) {


                // 输出参数不为空 且 执行由自动调度触发
                if (CollectionUtil.isNotEmpty(output)) {

                    ThreadUtil.execute(() -> {
                        // 发送订阅算法
                        inspThirdAlgorithmSubscriptionService.sendSubscriptionAlgorithmResult(algorithmInstance, output, algorithmExecuteRsp.getAlarmResultMap());
                    });
                }
            }
        } else {
            // 质量位设置
            globalVariableService.saveQualityBitByAlgorithmInstId(algorithmInstance.getId(), QualityBitEnum.Bad);

            String msg = StrUtil.format("算法实例[{}]执行失败，失败原因：{}", algorithmInstance.getName(),
                    algorithmExecuteRsp.getMsg());
            log.error(msg);
            executeMsg = ExecuteMsg.create(MsgLevel.ERROR, msg);
        }
        sendLogToWeb(presetCtx, executeMsg);
    }


    /**
     * 多次循环执行预置点结束时调用
     */
    public void onTimesFinish(ExecutePresetCtx presetCtx, int time) {
        // 更新预置点单次执行记录
        InspRecordPreset executeRecord = presetCtx.getRecordTimesPreset();
        executeRecord.setResultStatus(ExecuteResultStatus.SUCCESSFUL.name());
        executeRecord.setEndTime(LocalDateTime.now());
        recordPresetService.updateById(executeRecord);
        presetCtx.setRecordTimesPreset(executeRecord);

        InspProjectTreeNode presetTreeNodeInfo = presetCtx.getPresetTreeNodeInfo();
        String msg = StrUtil.format("预置点[{}]第[{}]次执行结束 ", presetTreeNodeInfo.getLabel(), time);
        log.info(msg);
        sendLogToWeb(presetCtx, ExecuteMsg.create(MsgLevel.INFO, msg));
    }

    /**
     * 预置点执行抛出异常时调用，此方法与onFinish互斥，只会执行一个
     */
    public void onException(ExecutePresetCtx presetCtx, Exception exception) {
        if (exception instanceof ChannelGetPicException || exception instanceof ExecuteBeforeException) {
            // PTZ跳转失败、截图失败、场景匹配失败、透视变换失败，质量位设置为Uncertain
            globalVariableService.saveQualityBitByPresetId(presetCtx.getPresetId(), QualityBitEnum.Uncertain);
        } else {
            // 其余情况设置为bad
            globalVariableService.saveQualityBitByPresetId(presetCtx.getPresetId(), QualityBitEnum.Bad);
        }

        // 更新预置点执行记录
        // InspRecordPreset executeRecord = presetCtx.getRecordPreset();
        // executeRecord.setResultStatus(ExecuteResultStatus.FAILED.name());
        // executeRecord.setFailureReason(exception.getMessage());
        LocalDateTime now = LocalDateTime.now();
        // executeRecord.setEndTime(now);
        // recordPresetService.updateById(executeRecord);
        // presetCtx.setRecordPreset(executeRecord);

        InspProjectTreeNode presetTreeNodeInfo = presetCtx.getPresetTreeNodeInfo();
        presetCtx.setEndTime(now);
        String msg = StrUtil.format("预置点[{}]执行出现异常，异常信息为：{}", presetTreeNodeInfo.getLabel(), exception.getMessage());
        log.error(msg);
        sendLogToWeb(presetCtx, ExecuteMsg.create(MsgLevel.ERROR, msg));
    }

    /**
     * 后置延时时间等待之前执行
     */
    public void beforeWaitPostDelay(ExecutePresetCtx presetCtx) {
        // 绘制OSD 只有当前工程为在线状态时，前端展示OSD
        InspProject project = presetCtx.getProject();
        List<OSDItem> allOsdInfo = presetCtx.getAllOsdInfo();
        if (OnlineState.ONLINE.name().equals(project.getOnlineState()) && CollectionUtil.isNotEmpty(allOsdInfo)) {
            // websocket发送消息到客户端，实时显示OSD
            OsdUtil.setOsdConfig(allOsdInfo, OsdConfigUtil.getWebOsdConfig(), null);
            WebsocketMessage websocketMessage = new WebsocketMessage();
            websocketMessage.setChannelId(presetCtx.getChannelId());
            websocketMessage.setEventType(WebsocketEventType.DRAW_OSD);
            websocketMessage.setOsdItemList(allOsdInfo);
            webSocketOsdServer.sendMessageToChannel(websocketMessage);
        }
        int postExecuteDelay = presetCtx.getPresetInfo().getPostExecuteDelay();
        String msg = StrUtil.format("开始执行后等待，时间为{}秒...", postExecuteDelay);
        sendLogToWeb(presetCtx, ExecuteMsg.create(MsgLevel.INFO, msg));
    }

    /**
     * 预置点执行结束时调用，此方法与onException互斥，只会执行一个
     */
    public void onFinish(ExecutePresetCtx presetCtx) {
        // 更新预置点执行记录
        // InspRecordPreset executeRecord = presetCtx.getRecordPreset();
        // executeRecord.setResultStatus(ExecuteResultStatus.SUCCESSFUL.name());
        LocalDateTime now = LocalDateTime.now();
        // executeRecord.setEndTime(now);
        // recordPresetService.updateById(executeRecord);
        // presetCtx.setRecordPreset(executeRecord);

        InspProjectTreeNode presetTreeNodeInfo = presetCtx.getPresetTreeNodeInfo();
        presetCtx.setEndTime(now);
        String msg = StrUtil.format("预置点[{}]执行结束 ", presetTreeNodeInfo.getLabel());
        log.info(msg);
        sendLogToWeb(presetCtx, ExecuteMsg.create(MsgLevel.INFO, msg));
    }

    private void sendLogToWeb(ExecutePresetCtx presetCtx, ExecuteMsg msg) {
        executePresetServer.sendLogToWeb(presetCtx.getClientKey(), presetCtx.getPresetId(), msg);
    }
}
