package com.hollysys.inspection.listener;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.hollysys.inspection.constants.MsgLevel;
import com.hollysys.inspection.controller.websocket.ExecuteAlgorithmServer;
import com.hollysys.inspection.entity.InspAlgorithmInstance;
import com.hollysys.inspection.model.algorithm.execute.AlgorithmExecuteRsp;
import com.hollysys.inspection.model.context.ExecuteAlgorithmCtx;
import com.hollysys.inspection.model.socket.ExecuteMsg;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 算法执行过程监听器
 */
@Component
public class TestAlgorithmListener {

    /**
     * 算法调试长连接
     */
    @Resource
    private ExecuteAlgorithmServer executeAlgorithmServer;

    /**
     * 算法开始执行时调用
     */
    public void onStart(ExecuteAlgorithmCtx context) {
        InspAlgorithmInstance algorithmInstance = context.getAlgorithmInstance();
        String msg = StrUtil.format("开始执行算法实例：{}", algorithmInstance.getName());

        executeAlgorithmServer.sendLogToWeb(context.getClientKey(), ExecuteMsg.create(MsgLevel.INFO, msg));
    }

    /**
     * 算法执行抛出异常时调用
     */
    public void onException(ExecuteAlgorithmCtx context, Exception exception) {
        InspAlgorithmInstance algorithmInstance = context.getAlgorithmInstance();
        String msg = StrUtil.format("算法实例[{}]执行出现异常，异常信息为：{}", algorithmInstance.getName(),
                exception.getMessage());
        executeAlgorithmServer.sendLogToWeb(context.getClientKey(), ExecuteMsg.create(MsgLevel.ERROR, msg));
    }

    /**
     * 算法执行结束时调用,此方法和onException只会触发一个
     */
    public void onFinish(ExecuteAlgorithmCtx context) {
        AlgorithmExecuteRsp algorithmExecuteRsp = context.getAlgorithmExecuteRsp();
        InspAlgorithmInstance algorithmInstance = context.getAlgorithmInstance();
        if (Objects.nonNull(algorithmExecuteRsp)) {
            ExecuteMsg executeMsg;
            if (BooleanUtil.isTrue(algorithmExecuteRsp.getIsSuccess())) {
                String msg = StrUtil.format("算法实例[{}]执行成功", algorithmInstance.getName());
                executeMsg = ExecuteMsg.create(MsgLevel.SUCCESS, msg);
            } else {
                String msg = StrUtil.format("算法实例[{}]执行失败，失败原因：{}", algorithmInstance.getName(),
                        algorithmExecuteRsp.getMsg());
                executeMsg = ExecuteMsg.create(MsgLevel.ERROR, msg);
            }
            executeAlgorithmServer.sendLogToWeb(context.getClientKey(), executeMsg);
        } else {
            String msg = StrUtil.format("算法实例[{}]执行失败，算法返回结果对象为空", algorithmInstance.getName());
            executeAlgorithmServer.sendLogToWeb(context.getClientKey(), ExecuteMsg.create(MsgLevel.ERROR, msg));
        }
    }
}
