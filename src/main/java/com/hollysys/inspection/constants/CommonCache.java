package com.hollysys.inspection.constants;

import com.hollysys.inspection.model.LockIpModel;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public class CommonCache {
    private CommonCache() {
    }

    // 巡检任务运行状态缓存（控制启停）
    public static final ConcurrentHashMap<String, LockIpModel> IP_PIC = new ConcurrentHashMap<>();

    public static final List<String> ZERO_TASK_ID = new ArrayList<>();
}
