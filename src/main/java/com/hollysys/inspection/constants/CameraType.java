package com.hollysys.inspection.constants;

import lombok.Getter;

/**
 * 摄像机类型
 *
 * <AUTHOR>
 */
@Getter
public enum CameraType {
    PTZ_CAMERA("球机"),
    TUBE_CAMERA("筒机"),
    GUN_CAMERA("枪机");

    private final String label;

    CameraType(String label) {
        this.label = label;
    }

    public static CameraType fromLabel(String label) {
        for (CameraType cameraType : CameraType.values()) {
            if (cameraType.label.equals(label)) {
                return cameraType;
            }
        }
        return null;
    }
}
