package com.hollysys.inspection.constants.channel;

import com.hollysys.inspection.service.protocol.ICameraCtrlProtocol;
import com.hollysys.inspection.service.protocol.impl.*;
import lombok.Getter;

/**
 * 摄像机接入协议类型
 */
@Getter
public enum CameraCtrlProtocol {
    ONVIF("ONVIF", CameraProtocolOnvif.class),
    SDK_HIK("海康SDK", CameraProtocolHik.class),
    SDK_DH("大华SDK", CameraProtocolDh.class),
    SDK_YS("宇视SDK", CameraProtocolYs.class)
    // ,VIDEO_RTSP("RTSP视频流", CameraProtocolRtsp.class)
    ;

    private final String label;

    private final Class<? extends ICameraCtrlProtocol> protocolClass;

    CameraCtrlProtocol(String label, Class<? extends ICameraCtrlProtocol> protocolClass) {
        this.label = label;
        this.protocolClass = protocolClass;
    }

    public static CameraCtrlProtocol fromLabel(String label) {
        for (CameraCtrlProtocol cameraCtrlProtocol : CameraCtrlProtocol.values()) {
            if (cameraCtrlProtocol.label.equals(label)) {
                return cameraCtrlProtocol;
            }
        }
        return null;
    }
}
