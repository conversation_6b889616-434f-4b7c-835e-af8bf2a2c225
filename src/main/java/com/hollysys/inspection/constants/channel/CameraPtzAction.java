package com.hollysys.inspection.constants.channel;

import com.hollysys.inspection.sdk.ys.NetDEVSDKLib;
import lombok.Getter;

/**
 * 相机PTZ控制行为枚举，本系统自定义（与第三方协议需要进行转化）
 */
@Getter
public enum CameraPtzAction {
    UP(3, 21, 0, 0x0402), // 上
    DOWN(4, 22, 1, 0x0404), // 下
    LEFT(1, 23, 2, 0x0504), // 左
    RIGHT(2, 24, 3, 0x0502), // 右
    ZOOM_ADD(5, 11, 4, 0x0302), // 变倍+
    ZOOM_DEC(6, 12, 5, 0x0304), // 变倍-
    FOCUS_ADD(0, 13, 6, 0x0202), // 调焦+
    FOCUS_DEC(0, 14, 7, 0x0204), // 调焦-
    APERTURE_ADD(0, 0, 8, 0x0104), // 光圈+
    APERTURE_DEC(0, 0, 9, 0x0102), // 光圈-
    POINT_MOVE(0, 0, 0, 0), // 转至预置点
    POINT_SET(0, 0, 0, 0), // 设置
    POINT_DEL(0, 0, 0, 0), // 删除
    POINT_LOOP(0, 0, 0, 0), // 点间巡航
    LAMP_CONTROL(0, 0, 0, 0); // 灯光雨刷

    private final int onvif;
    private final int hik;
    private final int dh;
    private final int ys;

    CameraPtzAction(int onvif, int hik, int dh, int ys) {
        this.onvif = onvif;
        this.hik = hik;
        this.dh = dh;
        this.ys = ys;
    }
}
