package com.hollysys.inspection.constants.algorithm;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import lombok.Getter;

/**
 * 质量位状态枚举
 */
@Getter
public enum QualityBitEnum {
    // 好
    Good(0x00000000L),
    // 坏
    Bad(0x80000000L),
    // 超时置坏
    Bad_Timeout(0x8000A000L),
    // 服务质量位坏
    Bad_OutOfService(0x80100000L),
    // 未知，除上述情况外
    Uncertain(0x40000000L);

    private final long code;

    QualityBitEnum(long code) {
        this.code = code;
    }

    /**
     * 转化为平台质量位值
     * 平台质量位：0为好点 非0为坏点
     */
    public static int toPlatformQualityBit(QualityBitEnum qualityBitEnum) {
        if (QualityBitEnum.Good == qualityBitEnum) {
            return 0;
        }
        return 1;
    }

    /**
     * 转化平台质量位值为本系统内质量位值
     * 平台质量位：0为好点 非0为坏点
     */
    public static QualityBitEnum fromPlatformQualityBit(Object datumV) {
        boolean resultBoolean = false;
        if (datumV instanceof Number) {
            Number datum = (Number) datumV;
            resultBoolean = !NumberUtil.equals(datum.doubleValue(),0);
        } else if (datumV instanceof Boolean) {
            Boolean datum = (Boolean) datumV;
            resultBoolean = BooleanUtil.isTrue(datum);
        }

        // 平台质量位：0为好点 非0为坏点
        if (resultBoolean) {
            return Bad;
        }
        return Good;
    }
}
