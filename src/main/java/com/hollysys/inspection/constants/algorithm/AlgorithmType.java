package com.hollysys.inspection.constants.algorithm;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 算法类型
 *
 * <AUTHOR>
 */
@Getter
public enum AlgorithmType {
    // openCV
    OPENCV("openCV"),
    // 深度学习
    DEEP_LEARNING("DeepLearning");

    private final String key;

    AlgorithmType(String key) {
        this.key = key;
    }

    public static List<String> keys() {
        AlgorithmType[] values = AlgorithmType.values();
        return Arrays.stream(values).map(AlgorithmType::getKey).collect(Collectors.toList());
    }
}
