package com.hollysys.inspection.constants.algorithm.param;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.model.algorithm.correct.PicCorrectParamModel;
import com.hollysys.inspection.model.algorithm.param.AlgorithmParamConst;
import com.hollysys.inspection.model.algorithm.param.SelectorOption;
import com.hollysys.inspection.model.algorithm.param.data.*;
import com.hollysys.inspection.model.channel.ScaleModel;
import com.hollysys.inspection.utils.AssertUtil;
import com.hollysys.inspection.utils.execute.TransformPicUtil;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/***
 * 算法参数定义中数据类型枚举
 */
@Getter
public enum DataType {
    // 矩形参数
    SQUARE(true) {
        @Override
        public boolean checkType(Object value) {
            if (!POLYGON.checkType(value)) {
                return false;
            }

            String jsonStr = JSONUtil.toJsonStr(value);
            List<Point> list = JSONUtil.toList(jsonStr, Point.class);
            return list.size() == Square.MAX_ARRAY_SIZE;
        }

        @Override
        public Object valueStrToObj(Object value) {
            return Square.buildByObj(value);
        }

        @Override
        boolean checkByConstraints(Object value, AlgorithmParamConst constraints) {
            return true;
        }

        @Override
        public Object resetCoords(Object value, int xOffset, int yOffset, ScaleModel scaleByImg) {
            Square square = Square.buildByObj(value);
            for (Point point : square) {
                resetPointCoords(point, xOffset, yOffset, scaleByImg);
            }
            return square;
        }

        @Override
        public Object retransformCoords(Object coords, ScaleModel scaleByImg, PicCorrectParamModel correctParam) {
            return POLYGON.retransformCoords(coords, scaleByImg, correctParam);
        }
    },

    // 圆形参数
    CIRCLE(true) {
        @Override
        public boolean checkType(Object value) {
            if (Objects.isNull(value)) {
                return false;
            }
            String jsonStr = JSONUtil.toJsonStr(value);
            if (!JSONUtil.isTypeJSONObject(jsonStr)) {
                return false;
            }
            Circle circle = JSONUtil.toBean(jsonStr, Circle.class);
            Point center = circle.getCenter();
            if (!checkPoint(center)) {
                return false;
            }
            return Objects.nonNull(circle.getRadius());
        }

        @Override
        public Object valueStrToObj(Object value) {
            return Circle.buildByObj(value);
        }

        @Override
        boolean checkByConstraints(Object value, AlgorithmParamConst constraints) {
            return true;
        }

        @Override
        public Object resetCoords(Object value, int xOffset, int yOffset, ScaleModel scaleByImg) {
            Circle circle = Circle.buildByObj(value);
            Point center = circle.getCenter();
            resetPointCoords(center, xOffset, yOffset, scaleByImg);

            // 判断边缘是否超出图像边界
            Integer radius = circle.getRadius();
            Integer centerX = center.getX();
            Integer centerY = center.getY();

            int outLeft = centerX - radius;
            if (outLeft < 0) {
                // 左边界溢出，圆心向右移动
                center.setX(centerX + Math.abs(outLeft));
            }

            int outTop = centerY - radius;
            if (outTop < 0) {
                // 上边界溢出，圆心向下移动
                center.setY(centerY + Math.abs(outTop));
            }
            int maxW = scaleByImg.getWidth();
            int outRight = centerX + radius - maxW;
            if (outRight > 0) {
                // 右边界溢出，圆心向左移动
                center.setX(centerX - Math.abs(outRight));
            }

            int maxH = scaleByImg.getHeight();
            int outBottom = centerY + radius - maxH;
            if (outBottom > 0) {
                // 右边界溢出，圆心向左移动
                center.setY(centerY - Math.abs(outBottom));
            }

            circle.setRadius(radius);
            circle.setCenter(center);
            return circle;
        }

        @Override
        public Object retransformCoords(Object coords, ScaleModel scaleByImg, PicCorrectParamModel correctParam) {
            Circle circle = Circle.buildByObj(coords);
            return TransformPicUtil.transformCircle(circle, correctParam.getFromSquare(), correctParam.getToSquare());
        }
    },

    // 椭圆
    ELLIPSE(true) {
        @Override
        public boolean checkType(Object value) {
            if (Objects.isNull(value)) {
                return false;
            }
            String jsonStr = JSONUtil.toJsonStr(value);
            if (!JSONUtil.isTypeJSONObject(jsonStr)) {
                return false;
            }
            Ellipse ellipse = JSONUtil.toBean(jsonStr, Ellipse.class);
            Point center = ellipse.getCenter();
            if (!checkPoint(center)) {
                return false;
            }
            Integer[] axes = ellipse.getAxes();
            if (axes.length != 2) {
                return false;
            }
            if (Objects.isNull(axes[0]) || Objects.isNull(axes[1])) {
                return false;
            }
            return Objects.nonNull(ellipse.getAngle());
        }

        @Override
        public Object valueStrToObj(Object value) {
            return Ellipse.buildByObj(value);
        }

        @Override
        boolean checkByConstraints(Object value, AlgorithmParamConst constraints) {
            return true;
        }

        @Override
        public Object resetCoords(Object value, int xOffset, int yOffset, ScaleModel scaleByImg) {
            // TODO 实际应该参考圆形的透视变换逻辑，但是椭圆透视变换后不一定是规则图形
            Ellipse ellipse = Ellipse.buildByObj(value);
            Point center = ellipse.getCenter();
            resetPointCoords(center, xOffset, yOffset, scaleByImg);
            ellipse.setCenter(center);
            return ellipse;
        }

        @Override
        public Object retransformCoords(Object coords, ScaleModel scaleByImg, PicCorrectParamModel correctParam) {
            Ellipse ellipse = Ellipse.buildByObj(coords);
            Point center = ellipse.getCenter();
            List<Point> points = TransformPicUtil.transformPoints(ListUtil.of(center), correctParam.getFromSquare(), correctParam.getToSquare());
            ellipse.setCenter(points.get(0));
            return ellipse;
        }
    },

    // 多边形
    POLYGON(true) {
        @Override
        public boolean checkType(Object value) {
            if (Objects.isNull(value)) {
                return false;
            }
            String jsonStr = JSONUtil.toJsonStr(value);
            if (!JSONUtil.isTypeJSONArray(jsonStr)) {
                return false;
            }
            List<Point> list = JSONUtil.toList(jsonStr, Point.class);
            for (Point point : list) {
                if (!checkPoint(point)) {
                    return false;
                }
            }
            return true;
        }

        @Override
        public Object valueStrToObj(Object value) {
            return Polygon.buildByObj(value);
        }

        @Override
        boolean checkByConstraints(Object value, AlgorithmParamConst constraints) {
            return true;
        }

        @Override
        public Object resetCoords(Object value, int xOffset, int yOffset, ScaleModel scaleByImg) {
            Polygon polygon = Polygon.buildByObj(value);
            for (Point point : polygon) {
                resetPointCoords(point, xOffset, yOffset, scaleByImg);
            }
            return polygon;
        }

        @Override
        public Object retransformCoords(Object coords, ScaleModel scaleByImg, PicCorrectParamModel correctParam) {
            Polygon polygon = Polygon.buildByObj(coords);
            return TransformPicUtil.transformPoints(polygon, correctParam.getFromSquare(), correctParam.getToSquare());
        }
    },

    // 线参数
    LINE(true) {
        @Override
        public boolean checkType(Object value) {
            if (Objects.isNull(value)) {
                return false;
            }
            String jsonStr = JSONUtil.toJsonStr(value);
            if (!JSONUtil.isTypeJSONObject(jsonStr)) {
                return false;
            }
            Line bean = JSONUtil.toBean(jsonStr, Line.class);
            return checkPoint(bean.getStart()) && checkPoint(bean.getEnd());
        }

        @Override
        public Object valueStrToObj(Object value) {
            return Line.buildByObj(value);
        }

        @Override
        boolean checkByConstraints(Object value, AlgorithmParamConst constraints) {
            return true;
        }

        @Override
        public Object resetCoords(Object value, int xOffset, int yOffset, ScaleModel scaleByImg) {
            Line line = Line.buildByObj(value);
            resetPointCoords(line.getStart(), xOffset, yOffset, scaleByImg);
            resetPointCoords(line.getEnd(), xOffset, yOffset, scaleByImg);
            return line;
        }

        @Override
        public Object retransformCoords(Object coords, ScaleModel scaleByImg, PicCorrectParamModel correctParam) {
            Line line = Line.buildByObj(coords);
            List<Point> points = TransformPicUtil.transformPoints(ListUtil.of(line.getStart(), line.getEnd()), correctParam.getFromSquare(), correctParam.getToSquare());
            line.setStart(points.get(0));
            line.setEnd(points.get(1));
            return line;
        }
    },

    // 线（带箭头）参数
    LINE_ARROW(true) {
        @Override
        public boolean checkType(Object value) {
            return LINE.checkType(value);
        }

        @Override
        public Object valueStrToObj(Object value) {
            return Line.buildByObj(value);
        }

        @Override
        boolean checkByConstraints(Object value, AlgorithmParamConst constraints) {
            return true;
        }

        @Override
        public Object resetCoords(Object value, int xOffset, int yOffset, ScaleModel scaleByImg) {
            return LINE.resetCoords(value, xOffset, yOffset, scaleByImg);
        }

        @Override
        public Object retransformCoords(Object coords, ScaleModel scaleByImg, PicCorrectParamModel correctParam) {
            return LINE.retransformCoords(coords, scaleByImg, correctParam);
        }
    },

    // 点参数
    POINT(true) {
        @Override
        public boolean checkType(Object value) {
            if (Objects.isNull(value)) {
                return false;
            }
            String jsonStr = JSONUtil.toJsonStr(value);
            if (!JSONUtil.isTypeJSONObject(jsonStr)) {
                return false;
            }
            Point bean = JSONUtil.toBean(jsonStr, Point.class);
            return checkPoint(bean);
        }

        @Override
        public Object valueStrToObj(Object value) {
            return Point.buildByObj(value);
        }

        @Override
        boolean checkByConstraints(Object value, AlgorithmParamConst constraints) {
            return true;
        }

        @Override
        public Object resetCoords(Object value, int xOffset, int yOffset, ScaleModel scaleByImg) {
            Point point = Point.buildByObj(value);
            resetPointCoords(point, xOffset, yOffset, scaleByImg);
            return point;
        }

        @Override
        public Object retransformCoords(Object coords, ScaleModel scaleByImg, PicCorrectParamModel correctParam) {
            Point point = Point.buildByObj(coords);
            List<Point> points = TransformPicUtil.transformPoints(ListUtil.of(point), correctParam.getFromSquare(), correctParam.getToSquare());
            return points.get(0);
        }
    },

    // 字符串参数
    STRING {
        @Override
        public boolean checkType(Object value) {
            if (Objects.isNull(value)) {
                return false;
            }
            return value instanceof String;
        }

        @Override
        public Object valueStrToObj(Object value) {
            return value;
        }

        @Override
        public boolean checkConstraints(AlgorithmParamConst constraints) {
            if (Objects.isNull(constraints)) {
                return false;
            }

            Integer maxLength = constraints.getMaxLength();
            if (Objects.isNull(maxLength)) {
                return false;
            }

            Integer minLength = constraints.getMinLength();
            if (Objects.isNull(minLength)) {
                return false;
            }

            return minLength <= maxLength;
        }

        @Override
        boolean checkByConstraints(Object value, AlgorithmParamConst constraints) {
            String valueStr = value.toString();

            String regexPattern = constraints.getRegexPattern();
            if (StrUtil.isNotBlank(regexPattern)) {
                if (!valueStr.matches(regexPattern)) {
                    return false;
                }
            }
            Integer maxLength = constraints.getMaxLength();
            if (Objects.nonNull(maxLength) && valueStr.length() > maxLength) {
                return false;
            }
            Integer minLength = constraints.getMinLength();
            return !Objects.nonNull(minLength) || valueStr.length() >= minLength;
        }

        @Override
        public Object retransformCoords(Object coords, ScaleModel scaleByImg, PicCorrectParamModel correctParam) {
            return POINT.retransformCoords(coords, scaleByImg, correctParam);
        }
    },

    // 量程类型参数
    RANGE {
        @Override
        public boolean checkType(Object value) {
            if (Objects.isNull(value)) {
                return false;
            }
            String jsonStr = JSONUtil.toJsonStr(value);
            return JSONUtil.isTypeJSONObject(jsonStr);
        }

        @Override
        public Object valueStrToObj(Object value) {
            return Range.buildByObj(value);
        }

        @Override
        public boolean checkConstraints(AlgorithmParamConst constraints) {
            return INTEGER.checkConstraints(constraints);
        }

        @Override
        boolean checkByConstraints(Object value, AlgorithmParamConst constraints) {
            Range range = Range.buildByObj(value);
            Float start = range.getStart();
            Float end = range.getEnd();
            if (Objects.isNull(start) || Objects.isNull(end)) {
                return false;
            }
            Float min = constraints.getMin();
            Float max = constraints.getMax();
            return checkMaxAndMin(start, min, max) && checkMaxAndMin(end, min, max);
        }
    },

    // 浮点型数字参数
    FLOAT {
        @Override
        public boolean checkType(Object value) {
            if (Objects.isNull(value)) {
                return false;
            }
            return NumberUtil.isNumber(value.toString());
        }

        @Override
        public Object valueStrToObj(Object value) {
            return Float.parseFloat(value.toString());
        }

        @Override
        public boolean checkConstraints(AlgorithmParamConst constraints) {
            if (Objects.isNull(constraints)) {
                return false;
            }

            Float precision = constraints.getPrecision();
            if (Objects.isNull(precision)) {
                return false;
            }

            if (precision <= 0) {
                return false;
            }

            Float max = constraints.getMax();
            if (Objects.isNull(max)) {
                return false;
            }

            Float min = constraints.getMin();
            if (Objects.isNull(min)) {
                return false;
            }

            return min < max;
        }

        @Override
        boolean checkByConstraints(Object value, AlgorithmParamConst constraints) {
            float aFloat = NumberUtil.parseFloat(value.toString());
            Float max = constraints.getMax();
            if (Objects.nonNull(max) && NumberUtil.isGreater(BigDecimal.valueOf(aFloat), BigDecimal.valueOf(max))) {
                return false;
            }
            Float min = constraints.getMin();
            return !Objects.nonNull(min) || !NumberUtil.isLess(BigDecimal.valueOf(aFloat), BigDecimal.valueOf(min));
        }
    },

    // 整数类型参数
    INTEGER {
        @Override
        public boolean checkType(Object value) {
            if (Objects.isNull(value)) {
                return false;
            }
            return NumberUtil.isInteger(value.toString());
        }

        @Override
        public Object valueStrToObj(Object value) {
            return Integer.parseInt(value.toString());
        }

        @Override
        public boolean checkConstraints(AlgorithmParamConst constraints) {
            if (!FLOAT.checkConstraints(constraints)) {
                return false;
            }
            Float precision = constraints.getPrecision();
            int parseInt = NumberUtil.parseInt(precision.toString());
            return precision == parseInt;
        }

        @Override
        boolean checkByConstraints(Object value, AlgorithmParamConst constraints) {
            return FLOAT.checkByConstraints(value, constraints);
        }
    },

    // 布尔类型参数
    BOOLEAN {
        @Override
        public boolean checkType(Object value) {
            if (Objects.isNull(value)) {
                return false;
            }
            return value instanceof Boolean;
        }

        @Override
        public Object valueStrToObj(Object value) {
            return BooleanUtil.toBooleanObject(value.toString());
        }

        @Override
        boolean checkByConstraints(Object value, AlgorithmParamConst constraints) {
            return true;
        }
    },

    // 参数数据类型,选择器,可多选
    SELECTOR {
        @Override
        public boolean checkType(Object value) {
            // ["car","person"]
            if (Objects.isNull(value)) {
                return false;
            }
            String jsonStr = JSONUtil.toJsonStr(value);
            return JSONUtil.isTypeJSONArray(jsonStr);
        }

        @Override
        public List<String> valueStrToObj(Object value) {
            if (!checkType(value)) {
                throw new InspectionException("选择器类型参数值格式不正确");
            }
            return JSONUtil.toList(JSONUtil.toJsonStr(value), String.class);
        }

        @Override
        public boolean checkConstraints(AlgorithmParamConst constraints) {
            if (Objects.isNull(constraints)) {
                return false;
            }
            Integer maxLength = constraints.getMaxLength();
            if (Objects.isNull(maxLength)) {
                return false;
            }
            if (maxLength <= 0) {
                return false;
            }

            List<SelectorOption> options = constraints.getOptions();
            if (CollectionUtil.isEmpty(options)) {
                return false;
            }

            if (CollectionUtil.hasNull(options)) {
                return false;
            }

            List<String> keys = options.stream().map(SelectorOption::getKey).filter(StrUtil::isNotBlank).distinct()
                    .collect(Collectors.toList());
            if (keys.size() != options.size()) {
                return false;
            }
            List<String> labels = options.stream().map(SelectorOption::getLabel).filter(StrUtil::isNotBlank).distinct()
                    .collect(Collectors.toList());
            return labels.size() == options.size();
        }

        @Override
        boolean checkByConstraints(Object value, AlgorithmParamConst constraints) {
            List<String> strings = valueStrToObj(value);
            List<SelectorOption> options = constraints.getOptions();
            if (CollectionUtil.isEmpty(options)) {
                return true;
            }

            Integer maxLength = constraints.getMaxLength();
            if (Objects.isNull(maxLength)) {
                // 单选
                if (strings.size() > 1) {
                    return false;
                }
            } else {
                // 多选
                if (strings.size() > maxLength) {
                    return false;
                }
            }
            if (CollectionUtil.hasNull(strings)) {
                return false;
            }

            long count = strings.stream().distinct().count();
            if (count != strings.size()) {
                return false;
            }

            List<String> keys = options.stream().map(SelectorOption::getKey).collect(Collectors.toList());
            // 找出strings中有 keys中没有的元素 不为空，则说明有不合法参数
            Collection<String> subtract = CollectionUtil.subtract(strings, keys);
            return !CollectionUtil.isNotEmpty(subtract);
        }
    },

    // 颜色RGB类型参数
    RGB {
        @Override
        public boolean checkType(Object value) {
            // [255,255,255]
            if (Objects.isNull(value)) {
                return false;
            }
            String jsonStr = JSONUtil.toJsonStr(value);
            return JSONUtil.isTypeJSONArray(jsonStr);
        }

        @Override
        public List<Integer> valueStrToObj(Object value) {
            if (!checkType(value)) {
                throw new InspectionException("颜色RGB类型参数值格式不正确");
            }
            return JSONUtil.toList(JSONUtil.toJsonStr(value), Integer.class);
        }

        @Override
        boolean checkByConstraints(Object value, AlgorithmParamConst constraints) {
            List<Integer> integers = valueStrToObj(value);
            if (integers.size() != 3) {
                return false;
            }
            for (Integer integer : integers) {
                if (Objects.isNull(integer)) {
                    return false;
                }
                if (!checkMaxAndMin(Float.valueOf(integer), 0f, 255f)) {
                    return false;
                }
            }
            return true;
        }
    },

    // 通道图片参数
    CHANNEL_PIC {
        @Override
        public boolean checkType(Object value) {
            return value instanceof String || JSONUtil.isTypeJSONObject(value.toString());
        }

        @Override
        public Object valueStrToObj(Object value) {
            return STRING.valueStrToObj(value);
        }

        @Override
        boolean checkByConstraints(Object value, AlgorithmParamConst constraints) {
            return true;
        }
    };

    DataType(boolean drawable) {
        this.drawable = drawable;
    }

    DataType() {
        this(false);
    }

    private final boolean drawable;

    /**
     * 检查值是否与类型匹配
     */
    public abstract boolean checkType(Object value);

    /**
     * 将字符串结果转化为相应类型Object
     */
    public abstract Object valueStrToObj(Object value);

    /**
     * 检查值是否符合Constraints，禁止外部调用
     */
    abstract boolean checkByConstraints(Object value, AlgorithmParamConst constraints);

    /**
     * 检查Point对象的坐标是否为空
     */
    private static boolean checkPoint(Point point) {
        if (Objects.isNull(point)) {
            return false;
        }
        return Objects.nonNull(point.getX()) && Objects.nonNull(point.getY());
    }

    /**
     * 对Point类型坐标校准
     */
    private static void resetPointCoords(Point point, int xOffset, int yOffset, ScaleModel scaleByImg) {
        Integer x = point.getX();
        Integer y = point.getY();
        x += (-1 * xOffset);
        y += (-1 * yOffset);
        // 对于校准后超出边界的坐标处理
        if (x < 0) {
            x = 0;
        }
        int maxW = scaleByImg.getWidth();
        int maxH = scaleByImg.getHeight();
        if (x > maxW) {
            x = maxW;
        }
        if (y < 0) {
            y = 0;
        }
        if (y > maxH) {
            y = maxH;
        }
        point.setX(x);
        point.setY(y);
    }

    /**
     * 算法参数值约束对象属性校验
     */
    public boolean checkConstraints(AlgorithmParamConst constraints) {
        return true;
    }

    /**
     * 坐标校准：
     * 1.根据模板匹配结果，修正参数坐标的偏差
     * 2.根据图像的分辨率，处理超出分辨率的坐标
     */
    public Object resetCoords(Object value, int xOffset, int yOffset, ScaleModel scaleByImg) {
        return value;
    }

    /**
     * 对坐标进行逆透视变换
     */
    public Object retransformCoords(Object coords, ScaleModel scaleByImg, PicCorrectParamModel correctParam) {
        return coords;
    }

    /**
     * 检查值
     */
    public void checkValue(Object value, AlgorithmParamConst constraints) {
        AssertUtil.isTrue(Objects.nonNull(constraints), "参数定义规则对象不允许为空");
        Boolean required = constraints.getRequired();
        if (BooleanUtil.isTrue(required)) {
            AssertUtil.isTrue(Objects.nonNull(value), "算法参数有必填值未配置");
            AssertUtil.isTrue(checkType(value), "参数值与定义类型不匹配");
            AssertUtil.isTrue(checkByConstraints(value, constraints), "参数值与定义规则不匹配");
        }
    }

    /**
     * 返回可绘制类型列表
     */
    public static List<String> drawableNames() {
        DataType[] values = DataType.values();
        return Arrays.stream(values).filter(DataType::isDrawable).map(DataType::name).collect(Collectors.toList());
    }

    /**
     * 返回可绘制ROI类型列表
     */
    public static List<String> roiTypeNames() {
        return ListUtil.of(POLYGON.name(), SQUARE.name());
    }

    /**
     * 返回出参数据类型
     */
    private static List<DataType> outputDataTypes() {
        return Arrays.asList(STRING, FLOAT, INTEGER, BOOLEAN);
    }

    /**
     * 返回出参数据类型
     */
    public static List<String> outputDataTypeNames() {
        List<DataType> dataTypes = outputDataTypes();
        return dataTypes.stream().map(DataType::name).collect(Collectors.toList());
    }

    /**
     * 校验值是否在[min,max]
     */
    private static boolean checkMaxAndMin(Float value, Float min, Float max) {
        if (Objects.nonNull(max) && NumberUtil.isGreater(BigDecimal.valueOf(value), BigDecimal.valueOf(max))) {
            return false;
        }
        return !Objects.nonNull(min) || !NumberUtil.isLess(BigDecimal.valueOf(value), BigDecimal.valueOf(min));
    }
}
