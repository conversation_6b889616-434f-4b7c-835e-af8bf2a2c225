package com.hollysys.inspection.constants.algorithm;

import lombok.Getter;

/**
 * 算法分类
 *
 * <AUTHOR>
 */
@Getter
public enum AlgorithmClassification {
    DEVICE_METER("设备仪表"),
    PERSONNEL_DRESS("人员着装"),
    PERSONNEL_BEHAVIOR("人员行为"),
    SAFETY_PROTECT("安全防护"),
    ENVIRONMENTAL_SAFETY("环境安全"),
    OTHERS("其他");

    private final String label;

    AlgorithmClassification(String label) {
        this.label = label;
    }
}
