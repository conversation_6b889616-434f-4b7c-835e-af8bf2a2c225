package com.hollysys.inspection.constants;

import cn.hutool.core.map.MapUtil;

import java.util.*;

/**
 * 工程树节点类型
 *
 * <AUTHOR>
 */
public class ProjectNodeType {

    /**
     * 业务根节点类型
     */
    public static final String BUSINESS_ROOT = "business-root";

    /**
     * 目录类型
     */
    public static final String DIRECTORY = "directory";

    /**
     * 通道类型
     */
    public static final String CHANNEL = "channel";

    /**
     * 预置点类型
     */
    public static final String PRESET = "preset";

    private static final Map<String, List<String>> SUB_TYPE_MAP = MapUtil.<String, List<String>>builder()
            .put(BUSINESS_ROOT, Arrays.asList(CHANNEL, DIRECTORY))
            .put(DIRECTORY, Arrays.asList(CHANNEL, DIRECTORY))
            .put(CHANNEL, Collections.singletonList(PRESET))
            .build();

    public static List<String> getSubTypes(String parentType) {
        List<String> strings = SUB_TYPE_MAP.get(parentType);
        if (Objects.isNull(strings)) {
            strings = new ArrayList<>();
        }
        return strings;
    }
}
