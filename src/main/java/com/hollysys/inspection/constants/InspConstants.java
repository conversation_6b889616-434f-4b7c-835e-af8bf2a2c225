package com.hollysys.inspection.constants;


import java.util.Arrays;
import java.util.List;

public class InspConstants {

    private InspConstants() {
    }

    /**
     * 根节点父ID
     */
    public static final String BOOT_NODE_PARENT_ID = "";

    /**
     * 1M
     */
    public static final int PIC_MAX_BYTE = 3145728;
    public static final String PIC_JPEG = "JPEG";
    public static final String PIC_PNG = "PNG";
    public static final String PIC_JPG = "JPG";

    public static final String yyyy_MM_ddHHmmss = "yyyy-MM-dd HH:mm:ss";

    public static final String yyyyMMddHHmmss = "yyyyMMddHHmmss";

    public static volatile int index = 0;

    public static final int SCENE_PIC_COUNT = 24;

    public static final String TASK_MODE_RECOVER = "TASK_MODE_RECOVER";

    public static final String CHANNEL_MODE_RECOVER = "CHANNEL_MODE_RECOVER";

    public static final int TASK_RECOVER_IN_SECONDS = 60;

    public static final String VARIABLE_VALUE_KEY = "GLOBAL:VARIABLE_VALUE:";

    public static final String VARIABLE_QUALITY_KEY = "GLOBAL:VARIABLE_QUALITY:";

    public static final String ALGORITHM_OUTPUT_VALUE_KEY = "ALGORITHM:OUTPUT_VALUE:";

    public static final int MAX_LIST_DIR_LIMIT = 500;

    public static final List<String> YISHI = Arrays.asList("一", "二", "三", "四", "五", "六", "七", "八", "九", "十");

    public static final List<String> CHAR = Arrays.asList("A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z");
}

