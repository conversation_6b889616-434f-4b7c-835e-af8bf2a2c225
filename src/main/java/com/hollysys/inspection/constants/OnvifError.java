package com.hollysys.inspection.constants;

import cn.hutool.core.util.EnumUtil;

public enum OnvifError {

    NOT_SUPPORT_PTZ_OPERATE(920, "当前通道不支持PTZ控制"),

    NOT_SUPPORT_ONVIF(921, "当前通道不支持ONVIF协议");

    private final int code;

    private final String msg;

    OnvifError(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static OnvifError getByCode(int code) {
        return EnumUtil.getBy(OnvifError.class, onvifError -> onvifError.code==code);
    }
}
