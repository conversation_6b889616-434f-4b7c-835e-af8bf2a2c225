package com.hollysys.inspection.constants;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 报警等级
 *
 * <AUTHOR>
 */
@Getter
public enum AlarmLevelEnum {

    HH("高高") {
        @Override
        public Boolean process(String dcsValueStr, String algorithmValueStr, Object setValue) {
            return processOffLimit(true, algorithmValueStr, setValue);
        }
    },

    H("高") {
        @Override
        public Boolean process(String dcsValueStr, String algorithmValueStr, Object setValue) {
            return processOffLimit(true, algorithmValueStr, setValue);
        }
    },

    L("低") {
        @Override
        public Boolean process(String dcsValueStr, String algorithmValueStr, Object setValue) {
            return processOffLimit(false, algorithmValueStr, setValue);
        }
    },

    LL("低低") {
        @Override
        public Boolean process(String dcsValueStr, String algorithmValueStr, Object setValue) {
            return processOffLimit(false, algorithmValueStr, setValue);
        }
    },

    BIG_DEVIATION("大偏差") {
        @Override
        public Boolean process(String dcsValueStr, String algorithmValueStr, Object setValue) {
            return processDeviation(dcsValueStr, algorithmValueStr, setValue);
        }
    },

    SMALL_DEVIATION("小偏差") {
        @Override
        public Boolean process(String dcsValueStr, String algorithmValueStr, Object setValue) {
            return processDeviation(dcsValueStr, algorithmValueStr, setValue);
        }
    },

    BOTH_GT("同时高于") {
        @Override
        public Boolean process(String dcsValueStr, String algorithmValueStr, Object setValue) {
            return processDeep(dcsValueStr, algorithmValueStr, setValue, true);
        }
    },

    BOTH_LT("同时低于") {
        @Override
        public Boolean process(String dcsValueStr, String algorithmValueStr, Object setValue) {
            return processDeep(dcsValueStr, algorithmValueStr, setValue, false);
        }
    },

    // 字符串类型报警
    IS_EMPTY_STR("为空") {
        @Override
        public Boolean process(String dcsValueStr, String algorithmValueStr, Object setValue) {
            return StrUtil.isBlank(algorithmValueStr);
        }
    },

    NOT_EMPTY_STR("不为空") {
        @Override
        public Boolean process(String dcsValueStr, String algorithmValueStr, Object setValue) {
            return StrUtil.isNotBlank(algorithmValueStr);
        }
    },

    EQUALS("等于") {
        @Override
        public Boolean process(String dcsValueStr, String algorithmValueStr, Object setValue) {
            if (Objects.isNull(setValue) || StrUtil.isEmpty(algorithmValueStr)) {
                return null;
            }
            return setValue.toString().equals(algorithmValueStr);
        }
    },

    NOT_EQUALS("不等于") {
        @Override
        public Boolean process(String dcsValueStr, String algorithmValueStr, Object setValue) {
            if (Objects.isNull(setValue) || StrUtil.isEmpty(algorithmValueStr)) {
                return null;
            }
            return !setValue.toString().equals(algorithmValueStr);
        }
    },

    REGEX_MATCHES("正则匹配") {
        @Override
        public Boolean process(String dcsValueStr, String algorithmValueStr, Object setValue) {
            if (Objects.isNull(setValue) || StrUtil.isEmpty(algorithmValueStr)) {
                return null;
            }
            return algorithmValueStr.matches(setValue.toString());
        }
    },

    // 布尔类型报警
    IS_EMPTY_BOOL("为空") {
        @Override
        public Boolean process(String dcsValueStr, String algorithmValueStr, Object setValue) {
            return StrUtil.isBlank(algorithmValueStr);
        }
    },

    TRUE("是") {
        @Override
        public Boolean process(String dcsValueStr, String algorithmValueStr, Object setValue) {
            return BooleanUtil.isTrue(BooleanUtil.toBooleanObject(algorithmValueStr));
        }
    },

    FALSE("否") {
        @Override
        public Boolean process(String dcsValueStr, String algorithmValueStr, Object setValue) {
            return BooleanUtil.isFalse(BooleanUtil.toBooleanObject(algorithmValueStr));
        }
    };

    private final String desc;

    AlarmLevelEnum(String desc) {
        this.desc = desc;
    }

    /**
     * 报警判断核心逻辑
     * 对于数值判断时，大小判断逻辑统一为大于等于或小于等于
     * 如果判断失败，返回null
     */
    public abstract Boolean process(String dcsValueStr, String algorithmValueStr, Object setValue);

    /**
     * 处理越限报警
     *
     * @param isUpperLimitCheck 是否上限校验
     * @param algorithmValueStr 输出值
     * @param setValue          配置值
     */
    private static Boolean processOffLimit(boolean isUpperLimitCheck, String algorithmValueStr, Object setValue) {
        if (!NumberUtil.isNumber(algorithmValueStr)) {
            return null;
        }
        if (Objects.isNull(setValue)) {
            return null;
        }
        String valueString = setValue.toString();
        if (!NumberUtil.isNumber(valueString)) {
            return null;
        }
        BigDecimal algorithmValueBigDecimal = NumberUtil.toBigDecimal(algorithmValueStr);
        BigDecimal setValueBigDecimal = NumberUtil.toBigDecimal(valueString);
        return isUpperLimitCheck ? NumberUtil.isGreaterOrEqual(algorithmValueBigDecimal, setValueBigDecimal)
                : NumberUtil.isLessOrEqual(algorithmValueBigDecimal, setValueBigDecimal);
    }

    private static Boolean processDeviation(String dcsValueStr, String algorithmValueStr, Object setValue) {
        boolean checkValue = checkValue(dcsValueStr, algorithmValueStr, setValue);
        if (!checkValue) {
            return null;
        }
        String valueString = setValue.toString();
        if (!NumberUtil.isNumber(valueString)) {
            return null;
        }
        Number setNumber = NumberUtil.parseNumber(valueString);
        Number dcsValue = NumberUtil.parseNumber(dcsValueStr);
        Number algorithmValue = NumberUtil.parseNumber(algorithmValueStr);
        // 计算差值绝对值
        BigDecimal sub = NumberUtil.sub(dcsValue, algorithmValue);
        double absSub = Math.abs(sub.doubleValue());
        return absSub > setNumber.doubleValue();
    }

    private static Boolean processDeep(String dcsValueStr, String algorithmValueStr, Object setValue, boolean isGreater) {
        boolean checkValue = checkValue(dcsValueStr, algorithmValueStr, setValue);
        if (!checkValue) {
            return null;
        }
        String valueString = setValue.toString();
        if (!NumberUtil.isNumber(valueString)) {
            return null;
        }
        // 格式转化
        Number dcsValue = NumberUtil.parseNumber(dcsValueStr);
        Number algorithmValue = NumberUtil.parseNumber(algorithmValueStr);

        BigDecimal bigDecimalDcs = NumberUtil.toBigDecimal(dcsValue);
        BigDecimal bigDecimalAlgorithm = NumberUtil.toBigDecimal(algorithmValue);
        BigDecimal bigDecimalSet = NumberUtil.toBigDecimal(valueString);

        if (isGreater) {
            return NumberUtil.isGreaterOrEqual(bigDecimalDcs, bigDecimalSet) && NumberUtil.isGreaterOrEqual(bigDecimalAlgorithm, bigDecimalSet);
        } else {
            return NumberUtil.isLessOrEqual(bigDecimalDcs, bigDecimalSet) && NumberUtil.isLessOrEqual(bigDecimalAlgorithm, bigDecimalSet);
        }
    }

    /**
     * 检查参数值合法性
     */
    private static boolean checkValue(String dcsValueStr, String algorithmValueStr, Object setValue) {
        return NumberUtil.isNumber(dcsValueStr) && NumberUtil.isNumber(algorithmValueStr) && Objects.nonNull(setValue);
    }
}
