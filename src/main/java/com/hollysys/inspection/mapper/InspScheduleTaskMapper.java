package com.hollysys.inspection.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import com.hollysys.inspection.entity.InspScheduleTask;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 通道运行周期调度信息表(InspScheduleTask)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-15 16:28:55
 */
@Mapper
public interface InspScheduleTaskMapper extends BaseMapper<InspScheduleTask> {
       @Select("select * from insp_schedule_task where  LOWER(name) ILIKE CONCAT('%', #{taskName}, '%')")
       List<InspScheduleTask> likeByTaskName(String taskName);
}

