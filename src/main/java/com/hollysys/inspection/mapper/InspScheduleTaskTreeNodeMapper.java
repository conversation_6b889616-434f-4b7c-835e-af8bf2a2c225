package com.hollysys.inspection.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hollysys.inspection.entity.InspProjectTreeNode;
import com.hollysys.inspection.entity.InspScheduleTaskTreeNode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * (InspScheduleTaskTreeNode)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-02-01 15:36:00
 */
@Mapper
public interface InspScheduleTaskTreeNodeMapper extends BaseMapper<InspScheduleTaskTreeNode> {


    String SELECT_DEPTH = "with RECURSIVE r as\n" +
            "( select t1.* from insp_schedule_task_tree_node t1 where t1.id = #{id} " +
            " union all\n" +
            " select t2.* FROM insp_schedule_task_tree_node t2, r WHERE t2.id = r.parent_id and t2.type = 'DIRECTORY')\n" +
            "select id, label, parent_id, type from r";


    /**
     * 递归查询区域（目录）节点深度
     */
    @Select(SELECT_DEPTH)
    List<InspProjectTreeNode> selectDirectoryTypeNodeDepth(@Param("id") String id);

}

