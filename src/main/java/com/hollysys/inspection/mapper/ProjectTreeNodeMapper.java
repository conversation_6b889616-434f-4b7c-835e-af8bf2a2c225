package com.hollysys.inspection.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hollysys.inspection.entity.InspProjectTreeNode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * (ProjectTreeNode)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-02-01 15:30:44
 */
@Mapper
public interface ProjectTreeNodeMapper extends BaseMapper<InspProjectTreeNode> {


    String SELECT_DEPTH = "with RECURSIVE r as\n" +
            "( select t1.* from insp_project_tree_node t1 where t1.id = #{id} " +
            " union all\n" +
            " select t2.* FROM insp_project_tree_node t2, r WHERE t2.id = r.parent_id and t2.type = 'directory')\n" +
            "select id, label, parent_id, type from r order by sort_no ";

    /**
     * 递归查询区域（目录）节点深度
     */
    @Select(SELECT_DEPTH)
    List<InspProjectTreeNode> selectDirectoryTypeNodeDepth(@Param("id") String id);
}

