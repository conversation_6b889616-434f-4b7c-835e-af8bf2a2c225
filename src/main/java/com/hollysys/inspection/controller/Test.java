package com.hollysys.inspection.controller;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hollysys.inspection.controller.base.BaseApiController;
import com.hollysys.inspection.entity.*;
import com.hollysys.inspection.model.algorithm.param.data.Circle;
import com.hollysys.inspection.model.algorithm.param.data.Square;
import com.hollysys.inspection.model.algorithm.schema.AlgorithmSchemaModel;
import com.hollysys.inspection.model.sdk.DownloadRecordingFileRes;
import com.hollysys.inspection.model.thirdapp.AlgorithmResultResModel;
import com.hollysys.inspection.service.*;
import com.hollysys.inspection.service.protocol.impl.CameraCtrlProxyService;
import com.hollysys.inspection.task.ScheduleNodesExeTask;
import com.hollysys.inspection.utils.*;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

@RestController
@RequestMapping("test")
@Slf4j
public class Test extends BaseApiController {

    @Resource
    private PythonServerService pythonServerService;

    @Resource
    private FileServeService fileServeService;

    @Resource
    private MediamtxService mediamtxService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private InspAlgorithmService inspAlgorithmService;

    @Resource
    private CameraCtrlProxyService cameraCtrlProxyService;

    @Resource
    private InspChannelInfoService inspChannelInfoService;

    @Resource
    private InspCommonConfigService inspCommonConfigService;

    @GetMapping("/SystemBackup")
    public Object systemBackup() {
        return success(mediamtxService.getNetworkInterfaces());
    }

    @PostMapping("/mtx")
    public Object mtx(@RequestBody List<String> ips) {
        InspCommonConfig commonConfig = new InspCommonConfig();
//        List<String> mtxIps = new ArrayList<>();
//        mtxIps.add("************");
        commonConfig.setMtxIps(ips);
        inspCommonConfigService.updateCommonConfig(commonConfig);
        return success("ok");
    }

//    public Object systemBackup() {
//        DownloadRecordingFileRes channelInfo = new DownloadRecordingFileRes();
//        channelInfo.setAddress("************");
//        channelInfo.setUsername("admin");
//        channelInfo.setPassword("Qqwe1234");
//        channelInfo.setCameraCtrlProtocol("SDK_HIK");
//        channelInfo.setChannelNum(6);
//        channelInfo.setStreamType(0);
//        channelInfo.setFileName("00000002609000000");
//        channelInfo.setFileSize(1063914650);
//        // 设置开始时间
//        LocalDateTime startTime = LocalDateTime.of(2025, 8, 7, 0, 32, 37);
//        channelInfo.setStartTime(startTime);
//
//        // 设置结束时间
//        LocalDateTime endTime = LocalDateTime.of(2025, 8, 7, 4, 18, 56); // 假设结束时间为开始后一分钟
//        channelInfo.setEndTime(endTime);
//        return success(inspChannelInfoService.downloadRecordingFile(channelInfo));
//    }

    @PostMapping("/test-results")
    public Object test(@RequestBody AlgorithmResultResModel algorithmSchema) {
        System.out.println(algorithmSchema);
        log.debug("收到订阅结果{}", algorithmSchema);
        return success(algorithmSchema);
    }

    @GetMapping("/test-mtx")
    public Object testMtx() {
        List<String> list = new ArrayList<>();
        list.add("************");
        mediamtxService.updateWebrtcAdditionalHosts(list);
        return success("ok");
    }


    @PostMapping("/newAlgorithm")
    public Object newAlgorithm(@RequestBody AlgorithmSchemaModel algorithmSchema) {
        inspAlgorithmService.checkAlgorithmSchema(algorithmSchema);
        String name = algorithmSchema.getAlgorithmMetadata().getName();
        InspAlgorithm algorithm = inspAlgorithmService.saveAlgorithmSchemaModel(name, name, algorithmSchema);
        return success(algorithm);
    }

    @GetMapping("/TimeInterval")
    public Object testTimeInterval(int interval) {
        SleepUtil.sleepMillisecond(interval);
        return success("ok");
    }

    @GetMapping("/getTokenActivityTimeout")
    public Object getTokenActivityTimeout() {
        return success(StpUtil.getTokenActivityTimeout());
    }

    @GetMapping("/incrementCounter")
    public Object incrementCounter() {
        Long increment = stringRedisTemplate.opsForValue().increment("111111111");
        return success(increment);
    }

    @PostMapping("/position")
    public Object position(String originImg, String tmpImg, String benchMarkPath) {
        return success(pythonServerService.matchingPosition(originImg, Collections.singletonList(tmpImg), Collections.singletonList(benchMarkPath)));
    }

    @PostMapping("socket/circleToEllipse")
    public Object circleToEllipse(@RequestBody Map<String, Object> param) {
        Circle circle = JSON.parseObject(JSON.toJSONString(param.get("circle")), Circle.class);
        Square formSquare = JSON.parseObject(JSON.toJSONString(param.get("formSquare")), Square.class);
        Square toSquare = JSON.parseObject(JSON.toJSONString(param.get("toSquare")), Square.class);
        return success(pythonServerService.circleToEllipse(circle, formSquare, toSquare));
    }

    @Resource
    private InspAlarmRecordService alarmRecordService;

    @GetMapping("first5")
    public Object sendSocketTaskMsg() {
        List<InspAlarmRecord> first5 = alarmRecordService.lambdaQuery()
                .orderByDesc(InspAlarmRecord::getCreateTime).last("LIMIT 5").list();
        return success(first5);
    }

    @GetMapping("/clearTemp")
    public Object clearTemp() {
        fileServeService.clearTemp();
        return success(true);
    }

    @Resource
    private InspRecordAlgorithmService recordAlgorithmService;


    @GetMapping("/listByRecordPresetId")
    public Object listByRecordPresetId(String presetId) {
        List<InspRecordAlgorithm> inspRecordAlgorithms = recordAlgorithmService
                .listByRecordPresetId(presetId);
        return success(inspRecordAlgorithms);
    }

    @Resource
    private InspAlgorithmParamInstanceService algorithmParamInstanceService;

    @GetMapping("/listParamList")
    public Object listParamList() {
        return success(this.algorithmParamInstanceService.list());
    }

    private HashMap<String, String> param = new HashMap<>();

    @PostMapping("set-value")
    public Object setRedisValue(@RequestBody HashMap<String, String> param) {
        this.param = param;
        return success(true);
    }

    /**
     * 获取全部变量，包括实时值
     *
     * @return 全部变量
     */
    @ApiOperation("获取全部变量，包括实时值")
    @GetMapping("list-value")
    public Object getPreviewUrl() {
        return success(this.param);
    }

//    @Resource
//    private MqttHelper mqttHelper;
//
//    @GetMapping("/mqtt")
//    public void sendMsg(String msg, String topic) {
//        mqttHelper.publish(topic, msg);
//    }

    @GetMapping("/testTimeout")
    public Object testTimeout(int timeout) {
        SleepUtil.sleepSecond(timeout);
        return success("true");
    }

    @Resource
    private ProjectTreeNodeService treeNodeService;

    @Resource
    private InspAlgorithmInstanceService algorithmInstanceService;

    @Resource
    private InspPresetInfoService presetInfoService;

    @Resource
    private InspSceneDefinitionService sceneDefinitionService;

    @Resource
    private ProjectTreeNodeService projectTreeNodeService;

    @GetMapping("freshChannelNodeState")
    public Object freshChannelNodeState() {
        projectTreeNodeService.freshChannelNodeState();
        return success(true);
    }

    // @Resource
    // private IcsPlatformApiService icsPlatformApiService;
    //
    // @PostMapping("ics/findPoints")
    // public Object findPoints(@RequestBody ReadPointsReqModel readPointsReqModel) {
    //     IcsApiResponse icsApiResponse = icsPlatformApiService.readPoints(readPointsReqModel);
    //     if (icsApiResponse.isSuccess()) {
    //         return success(icsApiResponse.getData());
    //     }
    //     R<Object> failed = failed(icsApiResponse.getMessage());
    //     failed.setCode(icsApiResponse.getStatus());
    //     return failed;
    // }
    //
    // @PostMapping("ics/writePoints")
    // public Object writePoints(@RequestBody WritePointsReqModel writePointsReqModel) {
    //     IcsApiResponse icsApiResponse = icsPlatformApiService.writePoints(writePointsReqModel);
    //     if (icsApiResponse.isSuccess()) {
    //         return success(icsApiResponse.getData());
    //     }
    //     R<Object> failed = failed(icsApiResponse.getMessage());
    //     failed.setCode(icsApiResponse.getStatus());
    //     return failed;
    // }

    @Transactional
    @GetMapping("copy-channel")
    public Object copyChannel(String channelId) {
        // 复制通道
        InspProjectTreeNode channelNode = treeNodeService.getOneById(channelId);
        channelNode.setLabel(channelNode.getLabel() + System.currentTimeMillis());
        channelNode.setId(null);
        treeNodeService.save(channelNode);
        String newChannelNodeId = channelNode.getId();
        InspChannelInfo channelInfo = inspChannelInfoService.getOneById(channelId);
        channelInfo.setId(newChannelNodeId);
        inspChannelInfoService.save(channelInfo);

        LambdaQueryWrapper<InspProjectTreeNode> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(InspProjectTreeNode::getParentId, channelId);
        List<InspProjectTreeNode> presetNodeList = treeNodeService.list(wrapper1);

        if (CollectionUtil.isEmpty(presetNodeList)) {
            return success(false);
        }

        copyNode(newChannelNodeId, presetNodeList);

        return success(channelInfo);
    }

    private void copyNode(String newChannelNodeId, List<InspProjectTreeNode> presetNodeList) {
        for (InspProjectTreeNode presetNode : presetNodeList) {
            String presetNodeId = presetNode.getId();

            // 复制通道下的预置点
            presetNode.setParentId(newChannelNodeId);
            presetNode.setLabel(presetNode.getLabel() + UUIDUtil.simpleUUID());
            presetNode.setId(null);
            treeNodeService.save(presetNode);

            InspPresetInfo inspPresetInfo = presetInfoService.getById(presetNodeId);
            inspPresetInfo.setChannelId(newChannelNodeId);
            inspPresetInfo.setId(presetNode.getId());
            presetInfoService.save(inspPresetInfo);

            String newPresetId = presetNode.getId();
            // 复制预置点下的场景
            List<InspSceneDefinition> inspSceneDefinitions = sceneDefinitionService.listByPresetId(presetNodeId);
            if (CollectionUtil.isEmpty(inspSceneDefinitions)) {
                continue;
            }

            Map<String, String> sceneMap = new HashMap<>();
            for (InspSceneDefinition inspSceneDefinition : inspSceneDefinitions) {
                String sceneId = inspSceneDefinition.getId();
                inspSceneDefinition.setId(null);
                inspSceneDefinition.setPresetId(newPresetId);
                sceneDefinitionService.newScene(inspSceneDefinition);
                sceneMap.put(sceneId, inspSceneDefinition.getId());
            }

            List<InspAlgorithmInstance> inspAlgorithmInstances = algorithmInstanceService.listByPresetId(presetNodeId);
            if (CollectionUtil.isEmpty(inspAlgorithmInstances)) {
                continue;
            }


            for (InspAlgorithmInstance algorithmInstance : inspAlgorithmInstances) {
                String algorithmInstanceId = algorithmInstance.getId();
                // 复制预置点下的算法实例
                algorithmInstance.setPresetId(newPresetId);
                algorithmInstance.setId(null);
                algorithmInstanceService.save(algorithmInstance);

                String newAlgorithmInstanceId = algorithmInstance.getId();

                // 复制算法实例下的参数
                LambdaQueryWrapper<InspAlgorithmParamInstance> wrapper2 = new LambdaQueryWrapper<>();
                wrapper2.eq(InspAlgorithmParamInstance::getAlgorithmInstanceId, algorithmInstanceId);
                List<InspAlgorithmParamInstance> algorithmParamInstances = algorithmParamInstanceService.list(wrapper2);

                // List<InspAlgorithmParamInstance> algorithmParamInstances =
                //         algorithmParamInstanceService.listByInstanceIdAndSceneId(algorithmInstanceId, sceneId);
                if (CollectionUtil.isEmpty(algorithmParamInstances)) {
                    continue;
                }
                for (InspAlgorithmParamInstance algorithmParamInstance : algorithmParamInstances) {
                    String newSceneId = sceneMap.get(algorithmParamInstance.getSceneId());
                    algorithmParamInstance.setSceneId(newSceneId);
                    algorithmParamInstance.setAlgorithmInstanceId(newAlgorithmInstanceId);
                    algorithmParamInstance.setId(null);
                    algorithmParamInstanceService.save(algorithmParamInstance);
                }
            }
        }
    }

    @PostMapping("/checkRtspUrl")
    public Object checkRtspUrl(String rtspUri, String username, String password, Integer timeoutSeconds, Boolean onlyCheckIpAndPort) {
        String rtspUrl = InspStringUtils.replaceRTSP(rtspUri, username, password);
        if (BooleanUtil.isTrue(onlyCheckIpAndPort)) {
            return RtspUtil.checkRtspUrl(rtspUrl);
        }
        return RtspUtil.isRtspUsable(rtspUrl, timeoutSeconds);
    }

    // @PostMapping("/createProjectData")
    // public Object createProjectData() {
    //     // 查询全部通道
    //     List<InspChannelInfo> channelInfos = new ArrayList<>();
    //     for (InspChannelInfo channelInfo : channelInfos) {
    //         InspProjectTreeNode projectTreeNode = new InspProjectTreeNode();
    //
    //         InspProjectTreeNode projectNode = projectTreeNodeService.createProjectNode(projectTreeNode);
    //     }
    // }

    @GetMapping("delNoPlayStream")
    public void delNoPlayStream() {
        mediamtxService.delNoPlayStream();
    }

    @GetMapping("getAllScheduleTask")
    public Object getAllScheduleTask() {
        List<Object> list = new ArrayList<>(ScheduleNodesExeTask.CACHE_TASK_ID);
        return success(list);
    }
}
