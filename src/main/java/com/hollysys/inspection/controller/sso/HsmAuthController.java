package com.hollysys.inspection.controller.sso;

import cn.dev33.satoken.exception.NotLoginException;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.controller.base.BaseApiController;
import com.hollysys.inspection.model.sso.UserInfoModel;
import com.hollysys.inspection.service.sso.HsmAuthTokenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/api/hsm/auth/")
public class HsmAuthController extends BaseApiController {
    @Resource
    private HsmAuthTokenService hsmAuthService;

    @GetMapping("tokenCallback")
    public Object codeCallback(@RequestParam("code") String code, HttpServletResponse response) {
        return success(hsmAuthService.exchangeToken(code, response));
    }

    @GetMapping("refresh")
    public Object refresh() {
        return success(hsmAuthService.refreshToken());
    }

    /**
     * 验证是否
     */
    @GetMapping("validate")
    public Object validate(HttpServletRequest request) {
        String token = ServletUtil.getHeader(request, HttpHeaders.AUTHORIZATION, StandardCharsets.UTF_8);
        if (StrUtil.isEmpty(token)) {
            log.error("请求token为空");
            throw new NotLoginException("请求token为空", null, null);
        }
        hsmAuthService.validSsoToken(token);
        return success("已登录");
    }

    /**
     * 获取用户信息
     */
    @GetMapping("user-info")
    public Object getUserInfo(HttpServletRequest request) {
        String token = ServletUtil.getHeader(request, HttpHeaders.AUTHORIZATION, StandardCharsets.UTF_8);
        if (StrUtil.isEmpty(token)) {
            throw new InspectionException("请求token为空");
        }
        hsmAuthService.validSsoToken(token);
        UserInfoModel userInfo = hsmAuthService.getUserInfo();
        if (Objects.nonNull(userInfo)) {
            return success(userInfo.getMe());
        }
        return success(null);
    }

    @GetMapping("logout")
    public Object logout(HttpServletRequest request) {
        return success(hsmAuthService.logOut(request));
    }

    @GetMapping("sso-config")
    public Object getSsoConfig() {
        return success(hsmAuthService.getSsoConfig());
    }
}
