package com.hollysys.inspection.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hollysys.inspection.constants.variable.VariableType;
import com.hollysys.inspection.controller.base.BaseApiController;
import com.hollysys.inspection.entity.InspGlobalVariable;
import com.hollysys.inspection.service.InspGlobalVariableService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 全局变量配置表(InspGlobalVariable)表控制层
 *
 * <AUTHOR>
 * @since 2023-05-11 16:05:10
 */
@Api(description = "[InspGlobalVariable]表控制层")
@RestController
@RequestMapping("global-variable")
public class InspGlobalVariableController extends BaseApiController {
    /**
     * 服务对象
     */
    @Resource
    private InspGlobalVariableService inspGlobalVariableService;

    @ApiOperation("分页查询所有数据")
    @GetMapping("page-all")
    public Object selectAll(Page<InspGlobalVariable> page, String searchStr, VariableType variableType) {
        return success(inspGlobalVariableService.pageAll(page, searchStr, variableType));
    }

    @ApiOperation("新增变量")
    @PostMapping
    public Object insertGlobalVariable(@RequestBody InspGlobalVariable globalVariable) {
        return success(inspGlobalVariableService.createVariable(globalVariable));
    }

    @ApiOperation("更新变量")
    @PutMapping
    public Object updateGlobalVariable(@RequestBody InspGlobalVariable globalVariable) {
        return success(inspGlobalVariableService.updateGlobalVariable(globalVariable));
    }

    @ApiOperation("删除变量")
    @DeleteMapping
    public Object deleteGlobalVariable(String globalVariableId) {
        return success(inspGlobalVariableService.deleteGlobalVariable(globalVariableId));
    }

    @ApiOperation("变量绑定输出参数结果")
    @PostMapping("bind-output-param")
    public Object bindOutputParam(@RequestBody InspGlobalVariable globalVariable) {
        return success(this.inspGlobalVariableService.bindOutputParam(globalVariable));
    }

    @ApiOperation("变量与输出参数值解绑")
    @PostMapping("unbind-output-param")
    public Object unbindOutputParam(@RequestBody InspGlobalVariable globalVariable) {
        return success(this.inspGlobalVariableService.unbindOutputParam(globalVariable));
    }

    @ApiOperation("树结构形式获取全部出参实例")
    @GetMapping("output-param-tree")
    public Object treeOutputParams() {
        return success(inspGlobalVariableService.getOutputParamTree());
    }
}

