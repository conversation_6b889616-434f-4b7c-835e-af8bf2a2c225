package com.hollysys.inspection.controller;


import com.hollysys.inspection.controller.base.BaseApiController;
import com.hollysys.inspection.model.period.InspScheduleCopyModel;
import com.hollysys.inspection.service.InspSchedulePeriodService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 通道运行周期调度信息表(InspSchedulePeriod)表控制层
 *
 * <AUTHOR>
 * @since 2023-04-04 09:02:09
 */
@Api(description = "[InspSchedulePeriod]表控制层")
@RestController
@RequestMapping("schedule-period")
public class InspSchedulePeriodController extends BaseApiController {
    /**
     * 服务对象
     */
    @Resource
    private InspSchedulePeriodService inspSchedulePeriodService;


    /**
     * copy数据
     *
     * @param inspScheduleCopyModel 实体对象
     * @return 新增结果
     */
    @ApiOperation("copy数据")
    @PostMapping("copy/channel-period")
    public Object copyToOther(@RequestBody InspScheduleCopyModel inspScheduleCopyModel) {
        return success(this.inspSchedulePeriodService.copyToOther(inspScheduleCopyModel));
    }
}

