package com.hollysys.inspection.controller;


import com.hollysys.inspection.controller.base.BaseApiController;
import com.hollysys.inspection.entity.InspCommonConfig;
import com.hollysys.inspection.service.InspCommonConfigService;
import com.hollysys.inspection.service.MediamtxService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * (InspCommonConfig)表控制层
 *
 * <AUTHOR>
 * @since 2024-01-17 10:33:18
 */
@Api(description = "[InspCommonConfig]表控制层")
@RestController
@RequestMapping("common-config")
public class InspCommonConfigController extends BaseApiController {
    /**
     * 服务对象
     */
    @Resource
    private InspCommonConfigService inspCommonConfigService;

    @Resource
    private MediamtxService mediamtxService;

    /**
     * 刷新缓存
     *
     * @return 刷新结果
     */
    @ApiOperation("刷新缓存")
    @GetMapping("refresh-cache")
    public Object freshCache() {
        return success(this.inspCommonConfigService.freshCache());
    }

    /**
     * 查看配置信息
     *
     * @return 结果
     */
    @ApiOperation("查看配置信息")
    @GetMapping
    public Object getCommonConfig() {
        return success(this.inspCommonConfigService.getCommonConfigFromDb());
    }

    /**
     * 查看配置单项信息
     *
     * @param itemName 配置单项名称
     * @return 结果
     */
    @ApiOperation("查看配置单项信息")
    @GetMapping("item")
    public Object getCommonConfigItem(String itemName) {
        return success(this.inspCommonConfigService.getCommonConfigItemCache(itemName));
    }


    /**
     * 设置缓存信息
     *
     * @return 结果
     */
    @ApiOperation("设置缓存信息")
    @PostMapping
    public Object setCommonConfig(@RequestBody InspCommonConfig param) {
        this.inspCommonConfigService.updateCommonConfig(param);
        // 刷新缓存
        InspCommonConfig commonConfig = this.inspCommonConfigService.freshCache();
        return success(commonConfig);
    }

    /**
     * 查看主机可配的网卡信息和IP地址
     * @return 结果
     */
    @ApiOperation("查看主机可配的网卡信息和IP地址")
    @GetMapping("available-ips")
    public Object getIp() {
        return success(mediamtxService.getNetworkInterfaces());
    }

    @ApiOperation("判断单点登录接口是否可用")
    @GetMapping("dbase")
    public Object dbaseApi() {
        return success(inspCommonConfigService.needDbaseConfig());
    }

    @ApiOperation("登录模式")
    @GetMapping("loginMode")
    public Object loginMode() {
        return success(inspCommonConfigService.dbBaseEnabled());
    }

    @ApiOperation("判断是否是第一次配置")
    @GetMapping("first-config")
    public Object isFirstConfig() {
        return success(inspCommonConfigService.isFirstConfig());
    }

    @ApiOperation("完成第一次配置后存入redis标识")
    @GetMapping("config-finish")
    public Object finishConfig() {
        return success(inspCommonConfigService.finishConfig());
    }
}

