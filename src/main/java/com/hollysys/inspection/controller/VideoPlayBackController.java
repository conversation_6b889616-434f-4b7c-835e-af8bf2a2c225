package com.hollysys.inspection.controller;

import com.hollysys.inspection.controller.base.BaseApiController;
import com.hollysys.inspection.model.sdk.DownloadOptRes;
import com.hollysys.inspection.model.sdk.DownloadRecordingFileRes;
import com.hollysys.inspection.model.sdk.PlayBackByTimeRes;
import com.hollysys.inspection.model.sdk.RecordingFileRes;
import com.hollysys.inspection.service.MediamtxService;
import com.hollysys.inspection.service.VideoPlayBackService;
import com.hollysys.inspection.service.protocol.impl.CameraProtocolHik;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 为视频回放提供接口，相关接口（跳过认证登录, 授权校验）
 */
@Api(description = "为视频回放提供接口")
@RestController
@RequestMapping("video-play-back")
public class VideoPlayBackController extends BaseApiController {

    @Resource
    private CameraProtocolHik cameraProtocolHik;

    @Resource
    private VideoPlayBackService videoPlayBackService;

    @Resource
    private MediamtxService mediamtxService;

    @ApiOperation("获取摄像头录像文件列表")
    @PostMapping("camera-recording-files-list")
    public Object getCameraRecordingFileList(@RequestBody RecordingFileRes recordingFileRes) {
        return success(videoPlayBackService.getRecordingFileList(recordingFileRes));
    }

    @ApiOperation("通道SDK下载录像文件到服务端")
    @PostMapping("download-recording-file-by-sdk")
    public Object downloadRecordingFileBySDK(@RequestBody DownloadRecordingFileRes downloadRecordingFileRes) {
        return success(videoPlayBackService.downloadRecordingFileByName(downloadRecordingFileRes));
    }

    /**
     * 将下载的录像文件导出
     */
    @ApiOperation("将下载的录像文件导出")
    @GetMapping("export-recording-file")
    public Object exportRecordingFile(@RequestParam String fileName) {
        return this.videoPlayBackService.exportRecordingFile(fileName);
    }

    /**
     * 查询下载进度
     */
    @ApiOperation("查询下载进度")
    @GetMapping("get-download-progress")
    public Object getDownloadProgress(@RequestParam String taskId) {
        return success(this.cameraProtocolHik.getDownloadProgress(taskId));
    }

    /**
     * 查询下载进度
     */
    @ApiOperation("停止下载")
    @PostMapping("stop-download-progress")
    public Object stopDownloadProgress(@RequestBody DownloadOptRes downloadOptRes) {
        this.cameraProtocolHik.stopDownload(downloadOptRes.getLFileHandle(), downloadOptRes.getTaskId());
        return success("ok");
    }


    /**
     * 回放录像
     */
    @ApiOperation("回放录像")
    @PostMapping("play-back-recording")
    public Object playRecordingByFile(@RequestBody PlayBackByTimeRes playBackByTimeRes) {
        return success(this.videoPlayBackService.playBackByTime(playBackByTimeRes));
    }

    /**
     * 踢流
     */
    @ApiOperation("根据流名字踢流")
    @GetMapping("delete-stream")
    public Object deleteStreamByName(@RequestParam String streamName) {
        this.mediamtxService.deleteStreamByName(streamName);
        return success("ok");
    }
}
