package com.hollysys.inspection.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hollysys.inspection.controller.base.BaseApiController;
import com.hollysys.inspection.entity.InspAlgorithmInstance;
import com.hollysys.inspection.entity.InspPresetInfo;
import com.hollysys.inspection.model.ModelInfoModel;
import com.hollysys.inspection.model.algorithm.configure.AlgorithmConfigureSaveModel;
import com.hollysys.inspection.model.algorithm.configure.SaveScheduleConfigReqModel;
import com.hollysys.inspection.model.execute.OutputListItem;
import com.hollysys.inspection.service.InspPresetInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * (InspPresetInfo)表控制层
 *
 * <AUTHOR>
 * @since 2023-02-02 15:50:34
 */
@Api(description = "[InspPresetInfo]表控制层")
@RestController
@RequestMapping("preset-info")
public class InspPresetInfoController extends BaseApiController {
    /**
     * 服务对象
     */
    @Resource
    private InspPresetInfoService inspPresetInfoService;

    /**
     * 通过主键查询预置点全部配置数据
     *
     * @param presetId 主键
     * @return 单条数据
     */
    @ApiOperation("通过主键查询预置点全部配置数据")
    @GetMapping("for-config")
    public Object selectOneForConfig(String presetId) {
        return success(this.inspPresetInfoService.getForConfig(presetId));
    }

    @GetMapping("/list")
    public Object list() {
        return success(this.inspPresetInfoService.getPresetInfoList());
    }

    @PutMapping("/update-status")
    public Object updatePTZStatus(@RequestBody List<ModelInfoModel> modelInfoModels) {
        return success(this.inspPresetInfoService.updatePTZStatus(modelInfoModels));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation("通过主键查询单条数据")
    @GetMapping("{id}")
    public Object selectOne(@PathVariable String id) {
        return success(this.inspPresetInfoService.getDetailById(id));
    }

    /**
     * 新增数据
     *
     * @param inspPresetInfo 实体对象
     * @return 新增结果
     */
    @ApiOperation("新增数据")
    @PostMapping
    public Object insert(@RequestBody InspPresetInfo inspPresetInfo) {
        return success(this.inspPresetInfoService.save(inspPresetInfo));
    }

    /**
     * 修改数据
     *
     * @param inspPresetInfo 实体对象
     * @return 修改结果
     */
    @ApiOperation("修改数据")
    @PutMapping
    public Object update(@RequestBody InspPresetInfo inspPresetInfo) {
        return success(this.inspPresetInfoService.updateById(inspPresetInfo));
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @ApiOperation("删除数据")
    @DeleteMapping
    public Object delete(@RequestParam("idList") List<Long> idList) {
        return success(this.inspPresetInfoService.removeByIds(idList));
    }

    /**
     * 预置点新增算法实例
     *
     * @param algorithmInstance 请求参数
     * @return 结果
     */
    @ApiOperation("预置点新增算法实例")
    @PostMapping("add-algorithm")
    public Object addAlgorithm(@RequestBody InspAlgorithmInstance algorithmInstance) {
        return success(this.inspPresetInfoService.addAlgorithm(algorithmInstance));
    }

    /**
     * 预置点删除算法实例
     *
     * @param algorithmInstanceIds 请求参数
     * @return 结果
     */
    @ApiOperation("预置点删除算法")
    @DeleteMapping("delete-algorithm")
    public Object deleteAlgorithm(@RequestParam List<String> algorithmInstanceIds) {
        return success(this.inspPresetInfoService.deleteAlgorithm(algorithmInstanceIds));
    }

    /**
     * 保存算法配置信息
     *
     * @param reqModel 请求参数
     * @return 结果
     */
    @ApiOperation("保存算法配置信息")
    @PostMapping("algorithm-configure")
    public Object saveAlgorithmConfigure(@RequestBody AlgorithmConfigureSaveModel reqModel) {
        return success(this.inspPresetInfoService.saveAlgorithmConfigure(reqModel));
    }

    /**
     * 获取算法配置信息
     *
     * @param algorithmInstanceId 请求参数，预置点ID
     * @return 结果
     */
    @ApiOperation("获取算法配置信息")
    @GetMapping("algorithm-configure")
    public Object getAlgorithmConfigure(String algorithmInstanceId) {
        return success(this.inspPresetInfoService.getAlgorithmConfigure(algorithmInstanceId));
    }

    /**
     * 保存预置点调度配置
     *
     * @param reqModel 请求参数
     * @return 请求结果
     */
    @ApiOperation("保存预置点调度配置")
    @PostMapping("schedule-config")
    public Object saveScheduleConfig(@RequestBody SaveScheduleConfigReqModel reqModel) {
        return success(this.inspPresetInfoService.saveScheduleConfig(reqModel));
    }

    /**
     * 保存预置点PTZ
     *
     * @param presetInfo 请求参数
     * @return 请求结果
     */
    @ApiOperation("保存预置点PTZ")
    @PostMapping("save-ptz")
    public Object savePtz(@RequestBody InspPresetInfo presetInfo) {
        return success(this.inspPresetInfoService.savePtz(presetInfo));
    }

    @ApiOperation("跳转到预置点位置")
    @GetMapping("jump")
    public Object jumpToPreset(String presetId) {
        return success(inspPresetInfoService.jumpToPreset(presetId));
    }

    /**
     * 查询输出参数和实时值
     */
    @ApiOperation("查询输出参数和实时值")
    @GetMapping("list-all-output")
    public Object savePtz(Page<OutputListItem> page, String nodeId, String searchName) {
        return success(this.inspPresetInfoService.listOutputParam(page, nodeId, searchName));
    }
}
