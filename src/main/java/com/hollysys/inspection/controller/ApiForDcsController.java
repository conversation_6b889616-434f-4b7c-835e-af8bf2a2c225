package com.hollysys.inspection.controller;


import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hollysys.inspection.constants.ProjectNodeType;
import com.hollysys.inspection.constants.algorithm.ExecuteResultStatus;
import com.hollysys.inspection.constants.channel.CameraPtzAction;
import com.hollysys.inspection.controller.base.BaseApiController;
import com.hollysys.inspection.entity.*;
import com.hollysys.inspection.model.alarm.AlarmListItemModel;
import com.hollysys.inspection.model.channel.ScaleModel;
import com.hollysys.inspection.model.channel.SwitchChannelModeReq;
import com.hollysys.inspection.service.*;
import com.hollysys.inspection.service.protocol.impl.CameraCtrlProxyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * 为DCS提供功能，相关接口（跳过认证登录）
 *
 * <AUTHOR>
 * @since 2023-02-01 15:36:00
 */
@Api(description = "为DCS提供功能，相关接口（跳过认证登录）")
@RestController
@RequestMapping("api-for-dcs")
public class ApiForDcsController extends BaseApiController {

    @Resource
    private InspChannelInfoService inspChannelInfoService;

    @Resource
    private InspCommonConfigService commonConfigService;

    @Resource
    private CameraCtrlProxyService cameraCtrlProxyService;

    @Resource
    private InspAlarmRecordService inspAlarmRecordService;

    @Resource
    private InspRecordAlgorithmService recordAlgorithmService;

    @Resource
    private InspRecordPresetService recordPresetService;

    @Resource
    private InspProjectService inspProjectService;

    @Resource
    private InspPresetInfoService inspPresetInfoService;

    @Resource
    private InspScheduleTaskService scheduleTaskService;

    /**
     * 切换通道手动、自动状态
     *
     * @param reqModel 实体对象
     * @return 修改结果
     */
    @ApiOperation("切换通道手动、自动状态")
    @PutMapping("channel/switch-mode")
    public Object switchMode(@RequestBody SwitchChannelModeReq reqModel) {
        return success(this.inspChannelInfoService.switchMode(reqModel));
    }

    /**
     * 获取通道视频预览信息
     *
     * @param channelId 通道id
     * @return 视频预览链接
     */
    @ApiOperation("获取通道视频预览信息")
    @GetMapping("preview-info")
    public Object getPreviewInfo(String channelId) {
        return success(this.inspChannelInfoService.getPreviewInfo(channelId));
    }

    /**
     * @param channelId 通道id
     * @param action    操作动作 1 上 2 下 3左 4右 5 放大 6 缩小
     */
    @ApiOperation("开启控制")
    @GetMapping("start-control-operate")
    public Object startControlOperate(@RequestParam String channelId, @RequestParam CameraPtzAction action) {
        InspChannelInfo channelInfo = inspChannelInfoService.getOneById(channelId);
        cameraCtrlProxyService.startCameraPtz(channelInfo, action);
        return success("ok");
    }

    /**
     * @param channelId 通道id
     */
    @ApiOperation("结束控制")
    @GetMapping("stop-control-operate")
    public Object stopControlOperate(@RequestParam String channelId, @RequestParam CameraPtzAction action) {
        InspChannelInfo channelInfo = inspChannelInfoService.getOneById(channelId);
        cameraCtrlProxyService.stopCameraPtz(channelInfo, action);
        return success("ok");
    }

    @ApiOperation("分页查询实时报警数据")
    @GetMapping("alarm/page-realtime")
    public Object pageAlarmRealtime(Page<InspAlarmRecord> page, String nodeId, String startTime, String endTime, String searchStr) {
        IPage<?> iPage = this.inspAlarmRecordService.pageRealtimeAlarms(page, nodeId, startTime, endTime, searchStr);
        // 设置报警状态颜色配置
        InspCommonConfig commonConfigCache = commonConfigService.getCommonConfigCache();
        Object dcsAlarmStateColor = commonConfigCache.getDcsAlarmStateColor();
        JSONObject entries = JSONUtil.parseObj(iPage);
        entries.set("dcsAlarmStateColor", dcsAlarmStateColor);
        // 设置执行记录展示开关
        entries.set("executeRecordShowable", commonConfigCache.getExecuteRecordShowable());
        return success(entries);
    }

    @ApiOperation("分页查询历史报警数据")
    @GetMapping("alarm/page-history")
    public Object pageAlarmHistory(Page<InspAlarmRecord> page, String nodeId, String startTime, String endTime, String searchStr, Boolean confirm, Boolean falseAlarm) {
        IPage<AlarmListItemModel> iPage = this.inspAlarmRecordService.pageHistoryAlarms(page, nodeId, startTime, endTime, searchStr, confirm, falseAlarm);
        JSONObject entries = JSONUtil.parseObj(iPage);
        // 设置执行记录展示开关
        InspCommonConfig commonConfigCache = commonConfigService.getCommonConfigCache();
        entries.set("executeRecordShowable", commonConfigCache.getExecuteRecordShowable());
        return success(entries);
    }

    @ApiOperation("分页查询算法执行记录数据")
    @GetMapping("algorithm-record/page-all")
    public Object pageAllAlgorithmRecords(Page<InspRecordAlgorithm> page, String nodeId, String startTime, String endTime,
                                          String searchStr, ExecuteResultStatus executeResultStatus) {
        return success(this.recordAlgorithmService.pageAllAlgorithmRecords(page, nodeId, startTime, endTime, searchStr, executeResultStatus));
    }

    @ApiOperation("分页查询预置点执行记录数据")
    @GetMapping("preset-record/page-all")
    public Object pageAllPresetRecords(Page<InspRecordPreset> page, String nodeId, String startTime, String endTime,
                                       String searchStr, ExecuteResultStatus executeResultStatus) {
        return success(this.recordPresetService.pageAllPresetRecords(page, nodeId, startTime, endTime, searchStr, executeResultStatus));
    }

    /**
     * 确认报警接口
     *
     * @param inspAlarmRecord 实体对象
     * @return 修改结果
     */
    @ApiOperation("确认报警接口")
    @PutMapping("/alarm/update")
    public Object update(@RequestBody InspAlarmRecord inspAlarmRecord) {
        if (inspAlarmRecord.getConfirm()) {
            String alarmId = inspAlarmRecord.getId();
            inspAlarmRecordService.lambdaUpdate()
                    .set(InspAlarmRecord::getConfirm, true)
                    .set(InspAlarmRecord::getConfirmTime, LocalDateTime.now())
                    .eq(InspAlarmRecord::getId, alarmId)
                    .update();
            return success(true);
        }
        return success(false);
    }

    /**
     * 分页查询所有工程数据
     *
     * @param page 分页对象
     * @return 所有数据
     */
    @ApiOperation("分页查询所有工程数据")
    @GetMapping("project/list-page")
    public Object selectAllProject(Page<InspProject> page) {
        return success(this.inspProjectService.page(page, new QueryWrapper<>()));
    }

    /**
     * 查询工程树节点、状态信息
     *
     * @return 单条数据
     */
    @ApiOperation("查询工程树节点、状态信息")
    @GetMapping("project/tree-and-state")
    public Object selectOne() {
        return success(this.inspProjectService.getProjectTreeAndState());
    }

    /**
     * 获取视频预览分辨率
     *
     * @param id   id
     * @param type type
     * @return 视频预览链接
     */
    @ApiOperation("获取视频分辨率")
    @GetMapping("scale")
    public Object getScale(@RequestParam String id, @RequestParam String type) {
        ScaleModel result;
        if (type.equals(ProjectNodeType.CHANNEL)) {
            result = inspChannelInfoService.getScale(id);
        } else {
            InspPresetInfo byId = inspPresetInfoService.getOneById(id);
            result = inspChannelInfoService.getScale(byId.getChannelId());
        }
        return success(result);
    }

    /**
     * 查询任务下全部巡检点，并查询巡检点运行状态
     *
     * @param taskId 任务ID
     * @return 巡检任务详情
     */
    @ApiOperation("查询任务下全部巡检点，并查询巡检点运行状态")
    @GetMapping("task/detail")
    public Object listByTaskId(String taskId) {
        return success(scheduleTaskService.listTaskNodesWithStatusByTaskId(taskId));
    }

    @ApiOperation("误报")
    @GetMapping("alarm/false-alarm")
    public Object falseAlarm(@RequestParam String id) {
        return success(inspAlarmRecordService.falseAlarm(id));
    }
}

