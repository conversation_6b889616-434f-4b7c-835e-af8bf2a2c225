package com.hollysys.inspection.controller;


import com.hollysys.inspection.controller.base.BaseApiController;
import com.hollysys.inspection.model.algorithm.configure.CopyParamReqModel;
import com.hollysys.inspection.model.algorithm.configure.SwitchMatchEnableReqModel;
import com.hollysys.inspection.model.algorithm.configure.PerspectiveReqModel;
import com.hollysys.inspection.model.algorithm.correct.CorrectReqModel;
import com.hollysys.inspection.service.ProcessConfigureService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 算法流程配置控制层
 *
 * <AUTHOR>
 * @since 2023-02-01 15:36:00
 */
@Api(description = "算法流程配置控制层")
@RestController
@RequestMapping("process-configure")
public class ProcessConfigureController extends BaseApiController {

    @Resource
    private ProcessConfigureService processConfigureService;

    /**
     * 透视变换（梯形校正）
     *
     * @param reqModel 请求参数
     * @return 实时图片
     */
    @ApiOperation("透视变换（梯形校正）")
    @PostMapping("correct")
    public Object correct(@RequestBody CorrectReqModel reqModel) {
        return success(this.processConfigureService.correct(reqModel));
    }

    /**
     * copy场景配置到其他场景
     *
     * @param reqModel 请求参数
     * @return 请求结果
     */
    @ApiOperation("copy场景配置到其他场景")
    @PostMapping("copy-params")
    public Object correct(@RequestBody CopyParamReqModel reqModel) {
        this.processConfigureService.copyToOtherScenes(reqModel);
        return success(true);
    }

    /**
     * 切换模板匹配启用状态
     *
     * @param reqModel 请求参数
     * @return 请求结果
     */
    @ApiOperation("切换模板匹配启用状态")
    @PostMapping("switch-match-enable")
    public Object switchMatchEnable(@RequestBody SwitchMatchEnableReqModel reqModel) {
        this.processConfigureService.switchMatchEnable(reqModel);
        return success(true);
    }

    /**
     * 切换透视变换状态
     *
     * @param reqModel 请求参数
     * @return 请求结果
     */
    @ApiOperation("切换模板匹配启用状态")
    @PostMapping("perspective-switch")
    public Object switchPerspectiveEnable(@RequestBody PerspectiveReqModel reqModel) {
        this.processConfigureService.switchPerspectiveEnable(reqModel);
        return success(true);
    }

    /**
     *
     *
     * @param reqModel 请求参数
     * @return 请求结果
     */
    @ApiOperation("编辑透视变换参数")
    @PostMapping("perspective-configure")
    public Object perspectiveConfigure(@RequestBody PerspectiveReqModel reqModel) {
        this.processConfigureService.savePerspectiveConfigure(reqModel);
        return success(true);
    }
}

