package com.hollysys.inspection.controller;

import com.hollysys.inspection.controller.base.BaseApiController;
import com.hollysys.inspection.service.DataBaseNetworkService;
import com.hollysys.inspection.service.platform.DbBaseApiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(description = "测试网络连接")
@RestController
@RequestMapping("test-network")
public class DataBaseNetWorkController extends BaseApiController {
    @Resource
    private DataBaseNetworkService netWorkServiceDataBase;

    @ApiOperation("测试平台登录接口")
    @GetMapping("dbase-login")
    public Object testHost(@RequestParam("host") String host) {
        return success(netWorkServiceDataBase.testDataBaseLogin(host));
    }

    @ApiOperation("测试平台用户名密钥是否正确")
    @GetMapping("dbase-clientInfo")
    public Object testClientInfo(@RequestParam("dbBaseIp") String dbBaseIp, @RequestParam("dbBaseClientId") String dbBaseClientId, @RequestParam("dbBaseSecret") String dbBaseSecret) {
        return success(netWorkServiceDataBase.validateDbBaseInfo(dbBaseClientId, dbBaseSecret, dbBaseIp));
    }
}
