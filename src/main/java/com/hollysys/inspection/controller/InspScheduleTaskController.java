package com.hollysys.inspection.controller;


import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.hollysys.inspection.controller.base.BaseApiController;
import com.hollysys.inspection.entity.InspScheduleTask;
import com.hollysys.inspection.entity.InspScheduleTaskTreeNode;
import com.hollysys.inspection.model.schedule.UpdateTaskReqModel;
import com.hollysys.inspection.service.InspScheduleTaskService;
import com.hollysys.inspection.service.InspScheduleTaskTreeNodeService;
import com.hollysys.inspection.utils.PresetScheduleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 通道运行周期调度信息表(InspScheduleTask)表控制层
 *
 * <AUTHOR>
 * @since 2023-06-15 16:28:55
 */
@Api(description = "[InspScheduleTask]表控制层")
@RestController
@RequestMapping("schedule-task")
public class InspScheduleTaskController extends BaseApiController {
    /**
     * 服务对象
     */
    @Resource
    private InspScheduleTaskService inspScheduleTaskService;

    @Resource
    private InspScheduleTaskTreeNodeService scheduleTaskTreeNodeService;

    /**
     * 通过任务ID查询巡检任务详情
     *
     * @param taskId 任务ID
     * @return 巡检任务详情
     */
    @ApiOperation("通过任务ID查询巡检任务详情")
    @GetMapping("task-detail")
    public Object listByTaskId(String taskId) {
        return success(this.inspScheduleTaskService.getTaskDetail(taskId));
    }

    /**
     * 查询全部巡检点（待选择列表）
     *
     * @return 所有数据
     */
    @ApiOperation("查询全部巡检点")
    @GetMapping("task-node/all")
    public Object listAllTaskNodes() {
        return success(this.inspScheduleTaskService.listAllTaskNodes());
    }

    /**
     * 查询任务下全部巡检点，并查询巡检点运行状态
     *
     * @return 所有数据
     */
    @ApiOperation("查询任务下全部巡检点，并查询巡检点运行状态")
    @GetMapping("task-node/by-task-id")
    public Object listTaskNodesByTaskId(String taskId) {
        return success(this.inspScheduleTaskService.listTaskNodesWithStatusByTaskId(taskId));
    }

    /**
     * 新增任务数据
     *
     * @param inspScheduleTask 实体对象
     * @return 新增结果
     */
    @ApiOperation("新增任务数据")
    @PostMapping
    public Object insert(@RequestBody InspScheduleTask inspScheduleTask) {
        return success(this.inspScheduleTaskService.createTask(inspScheduleTask));
    }

    /**
     * 修改任务数据
     *
     * @param reqModel 实体对象
     * @return 修改结果
     */
    @ApiOperation("修改任务数据")
    @PutMapping
    public Object updateTask(@RequestBody InspScheduleTask reqModel) {
        return success(this.inspScheduleTaskService.updateTask(reqModel));
    }

    /**
     * 修改任务详情数据
     *
     * @param reqModel 实体对象
     * @return 修改结果
     */
    @ApiOperation("修改任务详情数据")
    @PutMapping("detail")
    public Object updateTaskDetail(@RequestBody UpdateTaskReqModel reqModel) {
        String taskId = reqModel.getId();
        InspScheduleTask scheduleTask = this.inspScheduleTaskService.getByIdNotNull(taskId);
        Integer intervalOld = scheduleTask.getInterval();
        String intervalUnitOld = scheduleTask.getIntervalUnit();

        Object result = this.inspScheduleTaskService.updateTaskDetail(reqModel);

        // 新建任务后第一次编辑，不需要判断
        if (Objects.nonNull(intervalOld) && Objects.nonNull(intervalUnitOld)) {
            // 判断是否修改了调度间隔
            Integer interval = reqModel.getInterval();
            String intervalUnit = reqModel.getIntervalUnit();
            if (!NumberUtil.equals(interval, intervalOld)
                    || !StrUtil.equals(intervalUnit, intervalUnitOld)) {
                // 间隔和间隔单位任意一个发生变化，认为是间隔修改，间隔修改后，需要立即生效在调度任务中
                PresetScheduleUtil.resetCache(taskId);
            }
        }

        return success(result);
    }

    @ApiOperation("查询任务树结构")
    @GetMapping("tree")
    public Object getTaskTree() {
        return success(this.scheduleTaskTreeNodeService.getTaskTree());
    }

    @ApiOperation("添加任务树节点")
    @PostMapping("tree/node")
    public Object getTaskTree(@RequestBody InspScheduleTaskTreeNode reqModel) {
        return success(this.scheduleTaskTreeNodeService.createNode(reqModel));
    }

    @ApiOperation("修改任务树节点名称")
    @PutMapping("tree/node")
    public Object updateNode(@RequestBody InspScheduleTaskTreeNode reqModel) {
        return success(this.scheduleTaskTreeNodeService.updateNode(reqModel));
    }

    @ApiOperation("删除任务树节点数据")
    @DeleteMapping("tree/node")
    public Object delete(String nodeId) {
        return success(this.scheduleTaskTreeNodeService.removeTaskNode(nodeId));
    }
}

