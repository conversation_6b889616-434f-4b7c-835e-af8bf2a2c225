package com.hollysys.inspection.controller;


import com.hollysys.inspection.controller.base.BaseApiController;
import com.hollysys.inspection.entity.InspProject;
import com.hollysys.inspection.model.project.DeployOrOnlineReqModel;
import com.hollysys.inspection.model.project.SaveAllInfoReqModel;
import com.hollysys.inspection.service.InspProjectService;
import com.hollysys.inspection.service.ProjectTreeNodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 算法信息表(InspProject)表控制层
 *
 * <AUTHOR>
 * @since 2023-05-05 11:17:04
 */
@Api(description = "[InspProject]表控制层")
@RestController
@RequestMapping("project")
public class InspProjectController extends BaseApiController {
    /**
     * 服务对象
     */
    @Resource
    private InspProjectService inspProjectService;


    @Resource
    private ProjectTreeNodeService projectTreeNodeService;

    /**
     * 查询工程树节点、状态信息
     *
     * @return 单条数据
     */
    @ApiOperation("查询工程树节点、状态信息")
    @GetMapping("tree-and-state")
    public Object getProjectTreeAndState() {
        return success(this.inspProjectService.getProjectTreeAndState());
    }

    /**
     * 获取通道数树结构
     */
    @ApiOperation("获取通道数树结构")
    @GetMapping("channel-tree")
    public Object getChannelTree() {
        return success(this.projectTreeNodeService.getChannelTree());
    }

    /**
     * 一键保存全部工程配置数据
     *
     * @param reqModel 实体对象
     * @return 修改结果
     */
    @ApiOperation("一键保存全部工程配置数据")
    @PutMapping("save-all")
    public Object saveAllInfo(@RequestBody SaveAllInfoReqModel reqModel) {
        this.inspProjectService.saveAllInfo(reqModel);
        return success(true);
    }


    /**
     * 部署或停止工程
     *
     * @param reqModel 请求对象
     * @return 操作结果
     */
    @ApiOperation("部署或停止工程")
    @PutMapping("deploy-or-stop")
    public Object deployOrStop(@RequestBody DeployOrOnlineReqModel reqModel) {
        this.inspProjectService.deployOrStop(reqModel);
        return success(true);
    }

    /**
     * 上线或离线工程
     *
     * @param reqModel 请求对象
     * @return 操作结果
     */
    @ApiOperation("上线或离线工程")
    @PutMapping("online-or-offline")
    public Object onlineOrOffline(@RequestBody DeployOrOnlineReqModel reqModel) {
        this.inspProjectService.onlineOrOffline(reqModel);
        return success(true);
    }

    /**
     * 自动截图状态
     *
     * @return 状态
     */
    @ApiOperation("自动截图状态")
    @GetMapping()
    public Object getScreenShotState() {
        return success(inspProjectService.getFirstOne());
    }

    /**
     * 修改数据
     *
     * @param inspProject 实体对象
     * @return 修改结果
     */
    @ApiOperation("修改数据")
    @PutMapping
    public Object update(@RequestBody InspProject inspProject) {
        return success(this.inspProjectService.updateById(inspProject));
    }
}

