package com.hollysys.inspection.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hollysys.inspection.controller.base.BaseApiController;
import com.hollysys.inspection.entity.InspLogOperate;
import com.hollysys.inspection.model.operatelog.OperateLogPageReqModel;
import com.hollysys.inspection.service.InspLogOperateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 操作日志表控制层
 *
 * <AUTHOR>
 * @since 2023-05-19 09:39:35
 */
@Api(description = "[InspLogOperate]表控制层")
@RestController
@RequestMapping("operate-log")
public class InspLogOperateController extends BaseApiController {
    /**
     * 服务对象
     */
    @Resource
    private InspLogOperateService inspLogOperateService;

    /**
     * 分页查询所有数据
     *
     * @param page     分页对象
     * @param reqModel 查询实体
     * @return 所有数据
     */
    @ApiOperation("分页查询所有数据")
    @GetMapping("list-page")
    public Object selectAll(Page<InspLogOperate> page, OperateLogPageReqModel reqModel) {
        return success(this.inspLogOperateService.listPage(page, reqModel));
    }
}

