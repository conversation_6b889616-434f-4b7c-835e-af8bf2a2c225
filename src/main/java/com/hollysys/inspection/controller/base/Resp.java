package com.hollysys.inspection.controller.base;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class Resp<T> implements Serializable {

    private static final int SUCCESS = 0;
    private static final int FAILED = -1;

    private static final long serialVersionUID = 1L;
    private long code;
    private T data;
    private String msg;

    public static <T> Resp<T> ok(T data) {
        return restResult(data, SUCCESS, "请求成功");
    }

    public static <T> Resp<T> failed(String msg) {
        return restResult(null, FAILED, msg);
    }

    private static <T> Resp<T> restResult(T data, int code, String msg) {
        Resp<T> apiResult = new Resp<>();
        apiResult.setCode(code);
        apiResult.setData(data);
        apiResult.setMsg(msg);
        return apiResult;
    }

    public boolean isSuccess() {
        return code == SUCCESS;
    }
}
