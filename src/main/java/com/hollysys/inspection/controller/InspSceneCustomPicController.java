package com.hollysys.inspection.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hollysys.inspection.controller.base.BaseApiController;
import com.hollysys.inspection.entity.InspSceneCustomPic;
import com.hollysys.inspection.model.FailedUploadModel;
import com.hollysys.inspection.service.InspSceneCustomPicService;
import com.hollysys.inspection.utils.MinioUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * (InspSceneCustomPic)表控制层
 *
 * <AUTHOR>
 * @since 2023-03-10 13:54:00
 */
@Api(description = "[InspSceneCustomPic]表控制层")
@RestController
@RequestMapping("scene-custom-pic")
public class InspSceneCustomPicController extends BaseApiController {
    /**
     * 服务对象
     */
    @Resource
    private InspSceneCustomPicService inspSceneCustomPicService;

    /**
     * 分页查询所有数据
     *
     * @param page               分页对象
     * @param inspSceneCustomPic 查询实体
     * @return 所有数据
     */
    @ApiOperation("分页查询所有数据")
    @GetMapping
    public Object selectAll(Page<InspSceneCustomPic> page, InspSceneCustomPic inspSceneCustomPic) {
        return success(this.inspSceneCustomPicService.page(page, new QueryWrapper<>(inspSceneCustomPic)));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param presetId 模型id
     * @return 单条数据
     */
    @ApiOperation("通过主键查询单条数据")
    @GetMapping("{presetId}")
    public Object selectOne(@PathVariable String presetId, String type) {
        return success(this.inspSceneCustomPicService.getCustomPic(presetId, type));
    }

    /**
     * 本地上传
     *
     * @param presetId   实体对象
     * @param sceneFiles 上传图片
     * @return 新增结果
     */
    @ApiOperation("新增数据")
    @PostMapping
    public Object insert(@RequestParam String presetId, @RequestParam MultipartFile[] sceneFiles) {
        return success(this.inspSceneCustomPicService.uploadScenePic(presetId, sceneFiles));
    }

    /**
     * 快捷键上传
     *
     * @param failedUploadModel 实体对象
     * @return 新增结果
     */
    @ApiOperation("新增数据")
    @PostMapping("/failed-upload")
    public Object uploadPic(@RequestBody FailedUploadModel failedUploadModel) {
        return success(this.inspSceneCustomPicService.uploadScenePic(failedUploadModel));
    }

    /**
     * 修改数据
     *
     * @param inspSceneCustomPic 实体对象
     * @return 修改结果
     */
    @ApiOperation("修改数据")
    @PutMapping
    public Object update(@RequestBody InspSceneCustomPic inspSceneCustomPic) {
        List<String> pics = inspSceneCustomPic.getPics();
        inspSceneCustomPic.setPics(MinioUtil.urlsToAbsolute(pics));
        return success(this.inspSceneCustomPicService.updateById(inspSceneCustomPic));
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @ApiOperation("删除数据")
    @DeleteMapping
    public Object delete(@RequestParam("idList") List<Long> idList) {
        return success(this.inspSceneCustomPicService.removeByIds(idList));
    }
}

