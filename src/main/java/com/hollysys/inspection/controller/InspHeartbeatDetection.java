package com.hollysys.inspection.controller;

import com.alibaba.fastjson.JSONObject;
import com.hollysys.inspection.controller.base.BaseApiController;
import com.hollysys.inspection.utils.FfmpegUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
public class InspHeartbeatDetection extends BaseApiController {

    @GetMapping("/ping")
    public Object heartBeat() {
        return success("pong");
    }

    @PostMapping("/api/v1/streams/un-publish")
    public Object unPublish(@RequestBody(required = false) JSONObject jsonObject) {
        log.debug("stream退出，{}", jsonObject);
        // String url = jsonObject.getString("stream_url");
        // String execPath = "/opt/deploy/stop.sh " + url;
        // log.info("stream执行命令，{}", execPath);
        String streamKey = jsonObject.getString("stream");
        FfmpegUtil.stopPushing(streamKey);
        return success(null);
    }

    @PostMapping("/api/v1/streams/publish")
    public Object publish(@RequestBody(required = false) JSONObject jsonObject) {
        return success(null);
    }

    @PostMapping("/api/v1/streams/stop")
    public Object onStop(@RequestBody(required = false) JSONObject jsonObject) {
        return success(null);
    }

    @PostMapping("/api/v1/streams/play")
    public Object onStart(@RequestBody(required = false) JSONObject jsonObject) {
        return success(null);
    }
}
