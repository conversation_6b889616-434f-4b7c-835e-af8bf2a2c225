package com.hollysys.inspection.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hollysys.inspection.controller.base.BaseApiController;
import com.hollysys.inspection.entity.InspThirdApp;
import com.hollysys.inspection.model.BindInterfacesReqModel;
import com.hollysys.inspection.service.InspThirdAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 第三方应用管理(InspThirdApp)表控制层
 *
 * <AUTHOR>
 * @since 2023-07-05 14:28:35
 */
@Api(description = "第三方应用管理控制层")
@RestController
@RequestMapping("third-app")
public class InspThirdAppController extends BaseApiController {
    /**
     * 服务对象
     */
    @Resource
    private InspThirdAppService inspThirdAppService;

    /**
     * 分页查询所有数据
     *
     * @param page    分页对象
     * @param appName 应用名称
     * @param appCode 应用编码
     * @return 所有数据
     */
    @ApiOperation("分页查询所有数据")
    @GetMapping("list-page")
    public Object selectAll(Page<InspThirdApp> page, String appName, String appCode) {
        return success(this.inspThirdAppService.listPage(page, appName, appCode));
    }

    /**
     * 查询应用所绑定的接口权限
     *
     * @return 应用所绑定的接口权限
     */
    @ApiOperation("查询应用所绑定的接口权限")
    @GetMapping("list-bind-interface")
    public Object listBindInterface(String appId) {
        return success(this.inspThirdAppService.listBindInterface(appId));
    }

    /**
     * 查询所有三方接口数据
     *
     * @return 所有三方接口数据
     */
    @ApiOperation("查询所有三方接口数据")
    @GetMapping("list-interface")
    public Object selectAllInterface() {
        return success(this.inspThirdAppService.listAllInterface());
    }

    /**
     * 通过主键查询单条数据
     *
     * @param appId 主键
     * @return 单条数据
     */
    @ApiOperation("通过主键查询单条数据")
    @GetMapping
    public Object selectOne(String appId) {
        return success(this.inspThirdAppService.getAppDetail(appId));
    }

    /**
     * 新增数据
     *
     * @param inspThirdApp 实体对象
     * @return 新增结果
     */
    @ApiOperation("新增数据")
    @PostMapping
    public Object insert(@RequestBody InspThirdApp inspThirdApp) {
        return success(this.inspThirdAppService.newApp(inspThirdApp));
    }

    /**
     * 修改数据
     *
     * @param inspThirdApp 实体对象
     * @return 修改结果
     */
    @ApiOperation("修改数据")
    @PutMapping
    public Object update(@RequestBody InspThirdApp inspThirdApp) {
        return success(this.inspThirdAppService.updateApp(inspThirdApp));
    }

    /**
     * 切换应用可用状态
     *
     * @param inspThirdApp 实体对象
     * @return 修改结果
     */
    @ApiOperation("切换应用可用状态")
    @PutMapping("switch-available")
    public Object switchAvailable(@RequestBody InspThirdApp inspThirdApp) {
        InspThirdApp thirdAppEntity = new InspThirdApp();
        thirdAppEntity.setId(inspThirdApp.getId());
        thirdAppEntity.setAvailable(inspThirdApp.getAvailable());
        return success(this.inspThirdAppService.updateApp(thirdAppEntity));
    }

    /**
     * 应用绑定接口权限
     *
     * @param reqModel 请求参数
     * @return 结果
     */
    @ApiOperation("应用绑定接口权限")
    @PutMapping("bind-interfaces")
    public Object bindInterfaces(@RequestBody BindInterfacesReqModel reqModel) {
        this.inspThirdAppService.bindInterfaces(reqModel);
        return success(true);
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @ApiOperation("删除数据")
    @DeleteMapping
    public Object delete(@RequestParam("idList") List<String> idList) {
        return success(this.inspThirdAppService.removeApps(idList));
    }
}

