package com.hollysys.inspection.controller;


import com.hollysys.inspection.controller.base.BaseApiController;
import com.hollysys.inspection.model.ChangePasswordReq;
import com.hollysys.inspection.service.InspSystemUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 系统设置接口层
 *
 * <AUTHOR>
 * @since 2023-02-01 15:36:00
 */
@Api(description = "系统设置接口层")
@RestController
@RequestMapping("system-setting")
public class SystemSettingController extends BaseApiController {

    @Resource
    private InspSystemUserService userService;

    /**
     * 切换通道手动、自动状态
     *
     * @param reqModel 实体对象
     * @return 修改结果
     */
    @ApiOperation("修改用户密码")
    @PostMapping("change-password")
    public Object changePassword(@RequestBody ChangePasswordReq reqModel) {
        return success(this.userService.changePassword(reqModel));
    }
}

