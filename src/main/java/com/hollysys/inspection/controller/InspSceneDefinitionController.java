package com.hollysys.inspection.controller;


import cn.hutool.core.util.StrUtil;
import com.hollysys.inspection.controller.base.BaseApiController;
import com.hollysys.inspection.entity.InspSceneDefinition;
import com.hollysys.inspection.service.InspSceneDefinitionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;

/**
 * (InspSceneDefinition)表控制层
 *
 * <AUTHOR>
 * @since 2023-02-02 16:29:38
 */
@Api(description = "[InspSceneDefinition]表控制层")
@RestController
@RequestMapping("scene-definition")
public class InspSceneDefinitionController extends BaseApiController {

    /**
     * 服务对象
     */
    @Resource
    private InspSceneDefinitionService inspSceneDefinitionService;

    /**
     * 通过预置点ID查询全部场景数据
     *
     * @param instanceId 预置点主键
     * @return 单条数据
     */
    @ApiOperation("通过预置点ID查询全部场景数据")
    @GetMapping("by-instance-id")
    public Object getByChannelId(String instanceId) {
        return success(this.inspSceneDefinitionService.listByPresetId(instanceId));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation("通过主键查询单条数据")
    @GetMapping("{id}")
    public Object selectOne(@PathVariable Serializable id) {
        InspSceneDefinition inspSceneDefinition = this.inspSceneDefinitionService.getOneById(id);
        return success(inspSceneDefinition);
    }

    /**
     * 新增数据
     *
     * @param inspSceneDefinition 实体对象
     * @return 新增结果
     */
    @ApiOperation("新增数据")
    @PostMapping
    public Object insert(@RequestBody InspSceneDefinition inspSceneDefinition) {
        this.inspSceneDefinitionService.newScene(inspSceneDefinition);
        return success(inspSceneDefinition);
    }

    /**
     * 修改数据
     *
     * @param inspSceneDefinition 实体对象
     * @return 修改结果
     */
    @ApiOperation("修改数据")
    @PutMapping
    public Object update(@RequestBody InspSceneDefinition inspSceneDefinition) {
        inspSceneDefinitionService.validResolutionRatio(inspSceneDefinition);
        String id = inspSceneDefinition.getId();
        String presetId = inspSceneDefinition.getPresetId();
        String name = inspSceneDefinition.getName();
        this.inspSceneDefinitionService.validSceneName(id, presetId, name);
        // this.inspSceneDefinitionService.updateById(inspSceneDefinition);
        inspSceneDefinitionService.updateScene(inspSceneDefinition);
        return success(inspSceneDefinition);
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @ApiOperation("删除数据")
    @DeleteMapping
    public Object delete(@RequestParam("idList") List<String> idList) {
        this.inspSceneDefinitionService.deleteByIds(idList);
        return success("删除成功");
    }


    /**
     * 修改场景名称数据
     *
     * @param inspSceneDefinition 实体对象
     * @return 修改结果
     */
    @ApiOperation("修改场景名称数据")
    @PutMapping("/sceneName")
    public Object updateSceneName(@RequestBody InspSceneDefinition inspSceneDefinition) {
        // 校验该实例场景名称是否与其它场景名称同名
        String id = inspSceneDefinition.getId();
        String name = inspSceneDefinition.getName();
        String presetId = inspSceneDefinition.getPresetId();
        if (StrUtil.isNotBlank(id)) {
            // 修改场景名称
            inspSceneDefinitionService.updateSceneName(id, presetId, name);
        }
        return success(inspSceneDefinition);
    }
}

