package com.hollysys.inspection.controller.websocket;

import ch.qos.logback.core.util.CloseUtil;
import cn.hutool.json.JSONUtil;
import com.hollysys.inspection.model.socket.ImportExcelMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.websocket.OnClose;
import javax.websocket.OnError;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.ServerEndpoint;
import java.util.Objects;

/**
 * 通道excel上传进度监听长链接
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@ServerEndpoint("/websocket/import-excel")
public class ImportFromExcelServer {

    private static Session session;

    /**
     * 连接建立成
     * 功调用的方法
     */
    @OnOpen
    public void onOpen(Session session) {
        ImportFromExcelServer.session = session;
    }

    /**
     * 连接关闭
     * 调用的方法
     */
    @OnClose
    public void onClose() {
        CloseUtil.closeQuietly(session);
    }

    /**
     * 出错后回调
     *
     * @param error 错误信息
     */
    @OnError
    public void onError(Throwable error) {
        log.error("websocket错误:{}", error.getMessage());
    }

    public void sendLogToWeb(ImportExcelMsg msg) {
        if (Objects.isNull(session)) {
            return;
        }
        try {
            session.getBasicRemote().sendText(JSONUtil.toJsonStr(msg));
        } catch (Exception e) {
            log.error("sendMessage出现错误", e);
        }
    }
}

