package com.hollysys.inspection.controller.websocket;

import ch.qos.logback.core.util.CloseUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.hollysys.inspection.constants.MsgLevel;
import com.hollysys.inspection.model.socket.ExecuteMsg;
import com.hollysys.inspection.service.execute.ExecutePresetService;
import com.hollysys.inspection.utils.LicenseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 预置点实时执行、过程日志发送长连接
 * clientKey 为了判断同一个前端重复调用执行
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@ServerEndpoint("/websocket/execute/preset/{clientKey}/{presetId}")
public class ExecutePresetServer {

    /**
     * Session连接缓存 clientKey+presetId:Session
     */
    private static final Map<String, Session> SESSION_CACHE = new ConcurrentHashMap<>();

    private final ExecutePresetService executePresetService = SpringUtil.getBean(ExecutePresetService.class);

    /**
     * 连接建立成
     * 功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("clientKey") String clientKey, @PathParam("presetId") String presetId) {
        // 判断授权信息
        if (!BooleanUtil.isTrue(LicenseUtil.IS_VERIFY_SUCCESS.get())) {
            sendLogToWeb(session, ExecuteMsg.create(MsgLevel.ERROR, "预置点执行结束，因为授权校验失败"));
            return;
        }
        if (StrUtil.hasBlank(clientKey, presetId)) {
            sendLogToWeb(session, ExecuteMsg.create(MsgLevel.ERROR, "预置点执行结束，clientKey和presetId不能为空"));
            return;
        }

        // 判断当前客户端和预置点ID是否存在缓存
        if (SESSION_CACHE.containsKey(buildCacheKey(clientKey, presetId))) {
            sendLogToWeb(session, ExecuteMsg.create(MsgLevel.ERROR, "预置点在执行中，请勿重复执行"));
            CloseUtil.closeQuietly(session);
            return;
        }
        // 连接加入缓存
        putCache(clientKey, presetId, session);
        try {
            executePresetService.executePreset(null, clientKey, presetId);
        } catch (Exception exception) {
            String format = StrUtil.format("预置点执行异常,{}", exception.getMessage());
            log.error(format, exception);
            ExecuteMsg executeMsg = ExecuteMsg.create(MsgLevel.ERROR, format);
            sendLogToWeb(session, executeMsg);
        } finally {
            CloseUtil.closeQuietly(session);
            // 连接缓存移除
            deleteCache(clientKey, presetId);
        }
    }

    /**
     * 连接关闭
     * 调用的方法
     */
    @OnClose
    public void onClose(@PathParam("clientKey") String clientKey, @PathParam("presetId") String presetId) {
        // 连接缓存移除
        deleteCache(clientKey, presetId);
    }

    /**
     * 收到客户端消
     * 息后调用的方法
     *
     * @param message 客户端发送过来的消息
     **/
    @OnMessage
    public void onMessage(String message) {
        log.debug("用户消息,报文: {}", message);
    }


    /**
     * 出错后回调
     *
     * @param error 错误信息
     */
    @OnError
    public void onError(Throwable error) {
        log.error("websocket错误:{}", error.getMessage());
    }

    public void sendLogToWeb(String clientKey, String presetId, ExecuteMsg executeMsg) {
        String cacheKey = buildCacheKey(clientKey, presetId);
        if (!SESSION_CACHE.containsKey(cacheKey)) {
            return;
        }
        Session session = SESSION_CACHE.get(cacheKey);
        sendLogToWeb(session, executeMsg);
    }

    private void sendLogToWeb(Session session, ExecuteMsg executeMsg) {
        try {
            session.getBasicRemote().sendText(JSONUtil.toJsonStr(executeMsg));
        } catch (Exception e) {
            log.error("sendMessage出现错误", e);
        }
    }

    private synchronized void putCache(String clientKey, String presetId, Session session) {
        SESSION_CACHE.put(buildCacheKey(clientKey, presetId), session);
    }

    private synchronized void deleteCache(String clientKey, String presetId) {
        SESSION_CACHE.remove(buildCacheKey(clientKey, presetId));
    }

    private String buildCacheKey(String clientKey, String presetId) {
        return clientKey + presetId;
    }
}

