package com.hollysys.inspection.controller.websocket;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.hollysys.inspection.model.socket.StreamWebsocketMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 实时OSD通知
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@ServerEndpoint("/websocket/stream/{channelId}")
public class WebStreamAlarmServer {

    /**
     * Session连接缓存
     */
    private static final Map<String, Set<Session>> SESSION_CACHE = MapUtil.newConcurrentHashMap(20);

    /**
     * 消息缓存（缓存每个通道最后一个消息）
     */
    private static final Map<String, String> LAST_MESSAGE_CACHE = MapUtil.newConcurrentHashMap(20);

    private String channelId;

    /**
     * 连接建立成
     * 功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam(value = "channelId") String channelId) {
        this.channelId = channelId;
        putCache(channelId, session);
    }

    /**
     * 连接关闭
     * 调用的方法
     */
    @OnClose
    public void onClose(Session session) {
        // 连接缓存移除
        deleteCache(this.channelId, session);
    }

    /**
     * 收到客户端消
     * 息后调用的方法
     *
     * @param message 客户端发送过来的消息
     **/
    @OnMessage
    public void onMessage(String message) {
        LAST_MESSAGE_CACHE.forEach((channelId, messageJson) -> {
            Set<Session> sessions = getCache(channelId);
            for (Session session : sessions) {
                doSendMessage(session, messageJson);
            }
        });
    }


    /**
     * 出错后回调
     *
     * @param error 错误信息
     */
    @OnError
    public void onError(Throwable error) {
        log.error("websocket错误:{}", error.getMessage());
    }

    /**
     * 向通道下全部连接发送消息
     */
    public void sendMessage(StreamWebsocketMsg streamWebsocketMsg) {
        log.debug("向客户端发送Stream通道信息：{}", streamWebsocketMsg);
        Set<Session> sessions = getCache(streamWebsocketMsg.getChannelId());
        for (Session session : sessions) {
            doSendMessage(session, JSONUtil.toJsonStr(streamWebsocketMsg));
        }
    }

    private void doSendMessage(Session session, String messageJson) {
        try {
            session.getBasicRemote().sendText(messageJson);
        } catch (Exception e) {
            log.error("sendMessage出现IO错误", e);
        }
    }


    private synchronized void putCache(String channelId, Session session) {
        Set<Session> sessions = getCache(channelId);
        sessions.add(session);
        SESSION_CACHE.put(channelId, sessions);
    }

    private synchronized Set<Session> getCache(String channelId) {
        Set<Session> sessions = SESSION_CACHE.get(channelId);
        if (CollectionUtil.isEmpty(sessions)) {
            sessions = new HashSet<>();
        }
        return sessions;
    }

    private synchronized void deleteCache(String channelId, Session session) {
        Set<Session> sessions = getCache(channelId);
        sessions.remove(session);
        if (CollectionUtil.isEmpty(sessions)) {
            SESSION_CACHE.remove(channelId);
        }
    }
}

