package com.hollysys.inspection.controller.websocket;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.hollysys.inspection.constants.InspConstants;
import com.hollysys.inspection.entity.InspAlarmRecord;
import com.hollysys.inspection.entity.InspAlgorithmInstance;
import com.hollysys.inspection.entity.InspProjectTreeNode;
import com.hollysys.inspection.model.socket.AlarmWebsocketMsg;
import com.hollysys.inspection.service.InspAlarmRecordService;
import com.hollysys.inspection.service.InspAlgorithmInstanceService;
import com.hollysys.inspection.service.ProjectTreeNodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 实时OSD通知
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@ServerEndpoint("/websocket/alarm")
public class WebSocketAlarmServer {

    /**
     * Session连接缓存
     */
    private static final List<Session> SESSION_CACHE = new ArrayList<>();

    /**
     * 消息缓存（缓存每个通道最后一个消息）
     */
    private static final Map<String, String> LAST_MESSAGE_CACHE = MapUtil.newConcurrentHashMap(20);

    private final ProjectTreeNodeService projectTreeNodeService = SpringUtil.getBean(ProjectTreeNodeService.class);

    private final InspAlgorithmInstanceService algorithmInstanceService = SpringUtil.getBean(InspAlgorithmInstanceService.class);

    private final InspAlarmRecordService alarmRecordService = SpringUtil.getBean(InspAlarmRecordService.class);

    /**
     * 连接建立成
     * 功调用的方法
     */
    @OnOpen
    public void onOpen(Session session) {
        SESSION_CACHE.add(session);
        // 连续发送五条最新实时报警
        List<InspAlarmRecord> first5 = alarmRecordService.lambdaQuery()
                .orderByDesc(InspAlarmRecord::getUpdateTime).last("LIMIT 5").list();
        if (CollectionUtil.isNotEmpty(first5)) {
            for (InspAlarmRecord alarmRecord : first5) {
                AlarmWebsocketMsg alarmWebsocketMsg = alarmRecordToMsg(alarmRecord);
                // 不需要语音播报，所以设置语音内容为空
                alarmWebsocketMsg.setAlarmVoice(null);
                doSendMessage(session, JSONUtil.toJsonStr(alarmWebsocketMsg));
            }
        }
    }

    /**
     * 连接关闭
     * 调用的方法
     */
    @OnClose
    public void onClose(Session session) {
        // 连接缓存移除
        deleteCache(session);
    }

    /**
     * 收到客户端消
     * 息后调用的方法
     *
     * @param message 客户端发送过来的消息
     **/
    @OnMessage
    public void onMessage(String message) {
        log.debug("用户消息,报文:" + message);

        LAST_MESSAGE_CACHE.forEach((channelId, messageJson) -> {
            List<Session> sessions = getCache();
            for (Session session : sessions) {
                doSendMessage(session, messageJson);
            }
        });
    }


    /**
     * 出错后回调
     *
     * @param error 错误信息
     */
    @OnError
    public void onError(Throwable error) {
        log.error("websocket错误:{}", error.getMessage());
    }

    /**
     * 向通道下全部连接发送消息
     */
    public void sendMessage(AlarmWebsocketMsg message) {
        log.debug("向客户端发送语音报警消息：{}", message);
        List<Session> sessions = getCache();
        for (Session session : sessions) {
            doSendMessage(session, JSONUtil.toJsonStr(message));
        }
    }

    private void doSendMessage(Session session, String messageJson) {
        if (!session.isOpen()) {
            deleteCache(session);
            return;
        }
        try {
            session.getBasicRemote().sendText(messageJson);
        } catch (Exception e) {
            log.error("sendMessage出现IO错误", e);
        }
    }

    /**
     * 获得此时的
     * 在线连接数量
     *
     * @return 在线连接数量
     */
    private synchronized int getOnlineCount() {
        return SESSION_CACHE.size();
    }

    /**
     * 获得此时的
     * 在线通道数量
     *
     * @return 在线通道数量
     */
    private synchronized int getOnlineChannelCount() {
        return SESSION_CACHE.size();
    }

    private synchronized void putCache(Session session) {
        SESSION_CACHE.add(session);
    }

    private synchronized List<Session> getCache() {
        return SESSION_CACHE;
    }

    private synchronized void deleteCache(Session session) {
        SESSION_CACHE.remove(session);
    }

    public AlarmWebsocketMsg alarmRecordToMsg(InspAlarmRecord alarmRecord) {
        AlarmWebsocketMsg alarmWebsocketMsg = new AlarmWebsocketMsg();
        alarmWebsocketMsg.setAlarmId(alarmRecord.getId());
        alarmWebsocketMsg.setAlarmLevel(alarmRecord.getAlarmLevel());
        Object levelValue = alarmRecord.getLevelValue();
        if (Objects.nonNull(levelValue)) {
            alarmWebsocketMsg.setAlarmVal(levelValue.toString());
        }
        alarmWebsocketMsg.setAlarmVoice(alarmRecord.getAlarmDesc());

        String algorithmInstanceId = alarmRecord.getAlgorithmInstanceId();
        alarmWebsocketMsg.setAlgorithmInstanceId(algorithmInstanceId);

        String channelId = alarmRecord.getChannelId();
        alarmWebsocketMsg.setChannelId(channelId);

        String presetId = alarmRecord.getPresetId();
        InspProjectTreeNode treeNode = projectTreeNodeService.getOneById(presetId);
        alarmWebsocketMsg.setPresetName(treeNode.getLabel());

        InspAlgorithmInstance algorithmInstance = algorithmInstanceService.getOneById(algorithmInstanceId);
        alarmWebsocketMsg.setAlgorithmName(algorithmInstance.getName());

        // 推送的实时报警的时间，应该显示的是报警的更新时间
        alarmWebsocketMsg.setStartTime(DateUtil.format(alarmRecord.getUpdateTime(), InspConstants.yyyy_MM_ddHHmmss));
        alarmWebsocketMsg.setStats("未确认");
        return alarmWebsocketMsg;
    }
}

