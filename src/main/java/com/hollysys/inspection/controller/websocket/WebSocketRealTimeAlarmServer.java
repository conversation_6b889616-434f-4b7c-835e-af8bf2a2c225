package com.hollysys.inspection.controller.websocket;

import cn.hutool.core.map.MapUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.entity.InspAlarmRecord;
import com.hollysys.inspection.model.alarm.AlarmListItemModel;
import com.hollysys.inspection.model.thirdapp.BaseAlarmQueryReqModel;
import com.hollysys.inspection.service.InspAlarmRecordService;
import com.hollysys.inspection.service.InspThirdPermissionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 提供给第三方的查询实时报警的websocket服务
 */
@Slf4j
@Component
@ServerEndpoint("/open-api/websocket/real-time-alarm-infos/{token}")
public class WebSocketRealTimeAlarmServer {
    private static final Map<String, Session> SESSION_CACHE = MapUtil.newConcurrentHashMap(20);

    private static final Map<String, BaseAlarmQueryReqModel> SUBSCRIPTION_CACHE = MapUtil.newConcurrentHashMap(20);

    private static final String urlPath = "/open-api/websocket/real-time-alarm-infos";

    private static final InspAlarmRecordService inspAlarmRecordService = SpringUtil.getBean(InspAlarmRecordService.class);
    private static final InspThirdPermissionService inspThirdPermissionService = SpringUtil.getBean(InspThirdPermissionService.class);


    // 用于定时刷新变量值的定时任务
    private static final int REFRESH_INTERVAL_MS = 5; // 秒

    // 使用单线程调度器定时发送数据
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();

    static {
        // 初始化定时任务
        scheduler.scheduleAtFixedRate(WebSocketRealTimeAlarmServer::sendRealtimeAlarmToAll,
                0, REFRESH_INTERVAL_MS, TimeUnit.SECONDS);
    }

    /**
     * 连接建立时调用
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("token") String token) {
        // 进行鉴权
        try {
            inspThirdPermissionService.checkPermission(token, "-", urlPath);
        } catch (InspectionException e) {
            log.error("实时报警WebSocket鉴权失败: {}", e.getMessage(), e);
            sendErrorMessage(session, "认证失败: " + e.getMessage());
            throw new InspectionException(e.getMessage());
        }

        String sessionId = session.getId();
        SESSION_CACHE.put(sessionId, session);
    }

    /**
     * 连接关闭调用的方法
     * @param session 当前关闭的 Session
     */
    @OnClose
    public void onClose(Session session) {
        String sessionId = session.getId();
        SESSION_CACHE.remove(sessionId);
        SUBSCRIPTION_CACHE.remove(sessionId);
    }

    /**
     * 收到客户端消息后调用的方法
     * @param message 客户端发送过来的消息
     * @param session 当前 Session
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        try {
            String sessionId = session.getId();  // 获取当前会话ID

            // 将JSON消息解析为 BaseAlarmQueryReqModel 对象
            BaseAlarmQueryReqModel queryModel = JSONUtil.toBean(message, BaseAlarmQueryReqModel.class);
            // 执行参数校验
            queryModel.validate();

            SUBSCRIPTION_CACHE.put(sessionId, queryModel);

            // 立即发送一次数据作为响应
            sendRealtimeAlarm(session, queryModel);

        } catch (Exception e) {
            log.error("处理WebSocket消息错误: {}", e.getMessage(), e);
            sendErrorMessage(session, "消息处理失败: " + e.getMessage());
        }
    }

    /**
     * 出错后回调
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.error("WebSocket错误: sessionId={}, error={}", session.getId(), error.getMessage(), error);
    }

    /**
     * 向指定会话发送实时数据
     */
    private static void sendRealtimeAlarm(Session session, BaseAlarmQueryReqModel queryModel) {
        try {
            // 调用服务获取历史报警数据
            Page<InspAlarmRecord> page = queryModel.toPage();

            IPage<AlarmListItemModel> realtimeAlarms = inspAlarmRecordService.pageRealtimeAlarms(
                    page,
                    queryModel.getNodeId(),
                    queryModel.getStartTime(),
                    queryModel.getEndTime(),
                    queryModel.getSearchStr());
            String response = JSONUtil.toJsonStr(realtimeAlarms);
            sendMessage(session, response);

        } catch (Exception e) {
            log.error("发送实时数据错误: {}", e.getMessage(), e);
            sendErrorMessage(session, "获取实时数据失败: " + e.getMessage());
        }
    }

    /**
     * 定时向所有订阅的客户端发送数据
     */
    private static void sendRealtimeAlarmToAll() {
        if (SESSION_CACHE.isEmpty()) {
            return;
        }

        SUBSCRIPTION_CACHE.forEach((sessionId, queryModel) -> {
            if (queryModel != null) {
                Session session = SESSION_CACHE.get(sessionId);
                if (session != null && session.isOpen()) {
                    // 重新执行查询并发送数据
                    sendRealtimeAlarm(session, queryModel);
                }
            }
        });
    }

    /**
     * 发送消息到客户端
     */
    private static void sendMessage(Session session, String message) {
        try {
            if (session != null && session.isOpen()) {
                session.getBasicRemote().sendText(message);
            }
        } catch (IOException e) {
            log.error("发送实时报警WebSocket消息失败: {}", e.getMessage(), e);
            try {
                session.close();
            } catch (IOException ex) {
                log.error("关闭session失败: {}", ex.getMessage());
            }
            SESSION_CACHE.remove(session.getId());
            SUBSCRIPTION_CACHE.remove(session.getId());
        }
    }

    /**
     * 发送错误消息
     */
    private static void sendErrorMessage(Session session, String errorMessage) {
        try {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", errorMessage);
            errorResponse.put("timestamp", String.valueOf(System.currentTimeMillis()));

            String response = JSONUtil.toJsonStr(errorResponse);
            sendMessage(session, response);
        } catch (Exception e) {
            log.error("发送错误消息失败: {}", e.getMessage());
        }
    }
}
