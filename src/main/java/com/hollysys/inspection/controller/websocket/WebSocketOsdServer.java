package com.hollysys.inspection.controller.websocket;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.hollysys.inspection.constants.WebsocketEventType;
import com.hollysys.inspection.model.socket.WebsocketMessage;
import com.hollysys.inspection.service.InspCommonConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.util.Collection;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 实时OSD通知
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@ServerEndpoint("/websocket/osd/{channelId}")
public class WebSocketOsdServer {

    /**
     * Session连接缓存
     */
    private static final Map<String, Set<Session>> SESSION_CACHE = MapUtil.newConcurrentHashMap(20);


    private String channelId;

    private static final InspCommonConfigService commonConfigService = SpringUtil.getBean(InspCommonConfigService.class);

    /**
     * 消息缓存（缓存每个通道最后一个OSD绘制时间）
     */
    private static final TimedCache<String, Long> LAST_OSD_TIME_CACHE = CacheUtil.newTimedCache(10000L);

    {
        // 开启定时任务，一定时间后清除OSD
        LAST_OSD_TIME_CACHE.setListener((key, value) -> {
            // 发送消息清除OSD
            log.debug("清除OSD，key = {}", key);
            cleanWebOSD(key);
        });
        // 100毫秒定时清理一次过期数据
        LAST_OSD_TIME_CACHE.schedulePrune(100);
    }

    /**
     * 连接建立成
     * 功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam(value = "channelId") String channelId) {
        // 连接加入缓存
        putCache(channelId, session);
        this.channelId = channelId;

        log.debug("通道-{}-连接成功,当前在线通道数为：{}，当前连接数为：{}", this.channelId, getOnlineChannelCount(), getOnlineCount());
    }

    /**
     * 连接关闭
     * 调用的方法
     */
    @OnClose
    public void onClose(Session session) {
        // 连接缓存移除
        deleteCache(this.channelId, session);
        // TODO 消息缓存移除
        // LAST_MESSAGE_CACHE.remove(this.channelId);
        log.debug("通道-{}-退出,当前在线通道数为：{}，当前连接数为：{}", this.channelId, getOnlineChannelCount(), getOnlineCount());
    }

    /**
     * 收到客户端消
     * 息后调用的方法
     *
     * @param message 客户端发送过来的消息
     **/
    @OnMessage
    public void onMessage(String message) {
        log.debug("用户消息,报文:" + message);
    }


    /**
     * 出错后回调
     *
     * @param error 错误信息
     */
    @OnError
    public void onError(Throwable error) {
        log.error("websocket错误:{}", error.getMessage());
    }

    /**
     * 清除web端OSD
     *
     * @param channelId 通道ID
     */
    public void cleanWebOSD(String channelId) {
        WebsocketMessage websocketMessage = new WebsocketMessage();
        websocketMessage.setEventType(WebsocketEventType.CLEAR_OSD);
        websocketMessage.setChannelId(channelId);
        sendMessageToChannel(websocketMessage);
    }

    /**
     * 向通道下全部连接发送消息
     */
    public void sendMessageToChannel(WebsocketMessage message) {
        String messageJson = JSONUtil.toJsonStr(message);
        String channelId = message.getChannelId();

        Set<Session> sessions = getCache(channelId);
        if (CollectionUtil.isEmpty(sessions)) {
            return;
        }

        String eventType = message.getEventType();
        if (WebsocketEventType.DRAW_OSD.equals(eventType)) {
            // OSD时间缓存
            LAST_OSD_TIME_CACHE.put(channelId, System.currentTimeMillis(),
                    commonConfigService.getCommonConfigCache().getOsdConfig().getWebOsdTimeout() * 1000L);
        }

        log.debug("向[{}]客户端发送OSD消息:{}", channelId, messageJson);
        for (Session session : sessions) {
            doSendMessage(session, messageJson);
        }
    }

    private void doSendMessage(Session session, String messageJson) {
        try {
            if (!session.isOpen()) {
                deleteCache(this.channelId, session);
                return;
            }
            session.getBasicRemote().sendText(messageJson);
        } catch (Exception e) {
            log.error("sendMessage出现IO错误", e);
        }
    }

    /**
     * 获得此时的
     * 在线连接数量
     *
     * @return 在线连接数量
     */
    private synchronized int getOnlineCount() {
        Collection<Set<Session>> values = SESSION_CACHE.values();
        return values.stream().mapToInt(Set::size).sum();
    }

    /**
     * 获得此时的
     * 在线通道数量
     *
     * @return 在线通道数量
     */
    private synchronized int getOnlineChannelCount() {
        return SESSION_CACHE.size();
    }

    private synchronized void putCache(String channelId, Session session) {
        Set<Session> sessions = getCache(channelId);
        sessions.add(session);
        SESSION_CACHE.put(channelId, sessions);
    }

    private synchronized Set<Session> getCache(String channelId) {
        Set<Session> sessions = SESSION_CACHE.get(channelId);
        if (CollectionUtil.isEmpty(sessions)) {
            sessions = new HashSet<>();
        }
        return sessions;
    }

    private synchronized void deleteCache(String channelId, Session session) {
        Set<Session> sessions = getCache(channelId);
        sessions.remove(session);
        if (CollectionUtil.isEmpty(sessions)) {
            SESSION_CACHE.remove(channelId);
        }
    }
}

