package com.hollysys.inspection.controller.websocket;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.hollysys.inspection.constants.ScheduleTaskStatus;
import com.hollysys.inspection.model.schedule.ScheduleTaskNodeModel;
import com.hollysys.inspection.model.socket.TaskSocketMsg;
import com.hollysys.inspection.service.InspScheduleTaskService;
import com.hollysys.inspection.utils.ExecuteUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 任务执行节点状态通知
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@ServerEndpoint("/websocket/task-schedule/{taskId}")
public class WebSocketTaskServer {

    /**
     * Session连接缓存
     */
    private static final Map<String, List<Session>> SESSION_CACHE = MapUtil.newConcurrentHashMap(20);

    private final InspScheduleTaskService inspScheduleTaskService = SpringUtil.getBean(InspScheduleTaskService.class);

    private String taskId;

    /**
     * 连接建立成
     * 功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam(value = "taskId") String taskId) {
        // 连接加入缓存
        putCache(taskId, session);
        this.taskId = taskId;

        try {
            List<ScheduleTaskNodeModel> scheduleTaskNodeModels = inspScheduleTaskService.listTaskNodesWithStatusByTaskId(taskId);
            long runningCount = scheduleTaskNodeModels.stream().map(ScheduleTaskNodeModel::getStatus)
                    .filter(ScheduleTaskStatus.RUNNING.name()::equals)
                    .count();
            if (runningCount > 0) {
                TaskSocketMsg taskSocketMsg = TaskSocketMsg.buildProgress(scheduleTaskNodeModels);
                sendMessageToWeb(ListUtil.of(taskId), taskSocketMsg);
            }
        } catch (Exception exception) {
            log.error("onOpen... sendMessageToWeb error", exception);
        }

        log.debug("任务调度执行节点状态监听通道注册成功，任务ID：{},当前在线通道数为：{}，当前连接数为：{}", taskId, getOnlineTaskCount(), getOnlineCount());
    }

    /**
     * 连接关闭
     * 调用的方法
     */
    @OnClose
    public void onClose(Session session) {
        // 连接缓存移除
        deleteCache(this.taskId, session);
        log.debug("任务调度执行节点状态监听通道退出成功，任务ID：{},当前在线通道数为：{}，当前连接数为：{}", taskId, getOnlineTaskCount(), getOnlineCount());
    }

    /**
     * 收到客户端消
     * 息后调用的方法
     *
     * @param message 客户端发送过来的消息
     **/
    @OnMessage
    public void onMessage(String message) {
        log.debug("用户消息,报文: {}", message);
    }


    /**
     * 出错后回调
     *
     * @param error 错误信息
     */
    @OnError
    public void onError(Throwable error) {
        log.error("websocket错误:{}", error.getMessage());
    }

    /**
     * 向通道下全部连接发送消息
     */
    public void sendMessageToWeb(List<String> taskIds, TaskSocketMsg taskSocketMsg) {
        List<Session> cache;
        if (CollectionUtil.isEmpty(taskIds)) {
            // 任务ID为空，则向全部客户端发送
            Collection<List<Session>> values = SESSION_CACHE.values();
            cache = values.stream().flatMap(Collection::stream).collect(Collectors.toList());
        } else {
            cache = new ArrayList<>();
            for (String id : taskIds) {
                List<Session> cacheItem = getCache(id);
                cache.addAll(cacheItem);
            }
        }

        if (CollectionUtil.isEmpty(cache)) {
            return;
        }

        String messageJson = JSON.toJSONString(taskSocketMsg);
        ExecuteUtil.printExecuteLog(log, "向任务调度状态监听客户端: {} 发送消息：{}", JSONUtil.toJsonStr(taskIds),
                messageJson);
        for (Session session : cache) {
            doSendMessage(session, messageJson);
        }
    }

    public void sendMessageToWeb(String taskId) {
        List<Session> cache = getCache(taskId);
        if (CollectionUtil.isEmpty(cache)) {
            return;
        }
        List<ScheduleTaskNodeModel> messageData = inspScheduleTaskService.listTaskNodesWithStatusByTaskId(taskId);
        TaskSocketMsg taskSocketMsg = TaskSocketMsg.buildProgress(messageData);
        String messageJson = JSON.toJSONString(taskSocketMsg);
        ExecuteUtil.printExecuteLog(log, "向任务调度状态监听客户端: {} 发送消息：{}", taskId, messageJson);
        for (Session session : cache) {
            doSendMessage(session, messageJson);
        }
    }

    private void doSendMessage(Session session, String messageJson) {
        try {
            session.getBasicRemote().sendText(messageJson);
        } catch (Exception e) {
            log.error("sendMessage出现错误", e);
        }
    }

    /**
     * 获得此时的
     * 在线连接数量
     *
     * @return 在线连接数量
     */
    private synchronized int getOnlineCount() {
        Collection<List<Session>> values = SESSION_CACHE.values();
        return values.stream().mapToInt(List::size).sum();
    }

    /**
     * 获得此时的
     * 在线通道数量
     *
     * @return 在线通道数量
     */
    private synchronized int getOnlineTaskCount() {
        return SESSION_CACHE.size();
    }

    private synchronized void putCache(String taskId, Session session) {
        List<Session> sessions = getCache(taskId);
        sessions.add(session);
        SESSION_CACHE.put(taskId, sessions);
    }

    private synchronized List<Session> getCache(String taskId) {
        List<Session> sessions = SESSION_CACHE.get(taskId);
        if (CollectionUtil.isEmpty(sessions)) {
            sessions = new ArrayList<>();
        }
        return sessions;
    }

    private synchronized void deleteCache(String taskId, Session session) {
        List<Session> sessions = getCache(taskId);
        sessions.remove(session);
        if (CollectionUtil.isEmpty(sessions)) {
            SESSION_CACHE.remove(taskId);
        }
    }
}

