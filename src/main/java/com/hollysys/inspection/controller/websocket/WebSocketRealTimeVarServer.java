package com.hollysys.inspection.controller.websocket;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.model.variable.VariableRealtimeValue;
import com.hollysys.inspection.service.InspGlobalVariableService;
import com.hollysys.inspection.service.InspThirdPermissionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 提供给第三方的查询变量实时值的websocket服务
 */
@Slf4j
@Component
@ServerEndpoint("/open-api/websocket/realtime-vars/{token}")
public class WebSocketRealTimeVarServer {
    private static final Map<String, Session> SESSION_CACHE = MapUtil.newConcurrentHashMap(20);

    private static final Map<String, List<String>> SUBSCRIPTION_CACHE = MapUtil.newConcurrentHashMap(20);
    private static final String urlPath = "/open-api/websocket/realtime-vars";
    // 用于定时刷新变量值的定时任务
    private static final int REFRESH_INTERVAL_MS = 5; // 秒

    private static final InspGlobalVariableService globalVariableService = SpringUtil.getBean(InspGlobalVariableService.class);
    private static final InspThirdPermissionService inspThirdPermissionService = SpringUtil.getBean(InspThirdPermissionService.class);
    // 使用单线程调度器定时发送数据
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();

    static {
        // 初始化定时任务
        scheduler.scheduleAtFixedRate(WebSocketRealTimeVarServer::sendRealtimeDataToAll,
                0, REFRESH_INTERVAL_MS, TimeUnit.SECONDS);
    }

    /**
     * 连接建立时调用
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("token") String token) {
        // 进行鉴权
        try {
            inspThirdPermissionService.checkPermission(token, "-", urlPath);
        } catch (InspectionException e) {
            log.error("WebSocket鉴权失败: {}", e.getMessage(), e);
            sendErrorMessage(session, "认证失败: " + e.getMessage());
            throw new InspectionException(e.getMessage());
        }

        String sessionId = session.getId();
        SESSION_CACHE.put(sessionId, session);
    }

    /**
     * 连接关闭调用的方法
     * @param session 当前关闭的 Session
     */
    @OnClose
    public void onClose(Session session) {
        String sessionId = session.getId();
        SESSION_CACHE.remove(sessionId);
        SUBSCRIPTION_CACHE.remove(sessionId);
    }

    /**
     * 收到客户端消息后调用的方法
     * @param message 客户端发送过来的消息
     * @param session 当前 Session
     */
    @OnMessage
    @SuppressWarnings("unchecked")  // 抑制编译器 unchecked 警告，因为涉及到泛型类型转换
    public void onMessage(String message, Session session) {
        try {
            String sessionId = session.getId();  // 获取当前会话ID

            // 解析客户端消息，将JSON字符串转换为Map对象
            Map<String, Object> msgMap = JSONUtil.toBean(message, Map.class);

            // 获取valNames字段
            @SuppressWarnings("unchecked")
            List<String> valNames = (List<String>) msgMap.get("valNames");

            if (valNames != null && !valNames.isEmpty()) {
                SUBSCRIPTION_CACHE.put(sessionId, valNames);
                log.info("Session {} 订阅变量: {}", sessionId, valNames);

                // 立即发送一次数据作为响应
                sendRealtimeData(session, valNames);
            } else {
                log.warn("Session {} 发送的消息缺少valNames字段或为空: {}", sessionId, message);
                sendErrorMessage(session, "发送的消息缺少valNames字段或为空");
            }

        } catch (Exception e) {
            log.error("处理WebSocket消息错误: {}", e.getMessage(), e);
            sendErrorMessage(session, "消息处理失败: " + e.getMessage());
        }
    }

    /**
     * 出错后回调
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.error("WebSocket错误: sessionId={}, error={}", session.getId(), error.getMessage(), error);
    }

    /**
     * 向指定会话发送实时数据
     */
    private static void sendRealtimeData(Session session, List<String> variables) {
        try {
            if (CollectionUtil.isEmpty(variables)) {
                return;
            }

            // 调用服务获取实时数据
            Map<String, VariableRealtimeValue> realtimeData = globalVariableService.listValueWithRefresh(variables);

            String response = JSONUtil.toJsonStr(realtimeData);

            sendMessage(session, response);

        } catch (Exception e) {
            log.error("发送实时变量错误: {}", e.getMessage(), e);
            sendErrorMessage(session, "获取实时变量失败: " + e.getMessage());
        }
    }

    /**
     * 定时向所有订阅的客户端发送数据
     */
    private static void sendRealtimeDataToAll() {
        if (SESSION_CACHE.isEmpty()) {
            return;
        }

        SUBSCRIPTION_CACHE.forEach((sessionId, variables) -> {
            if (variables != null && !variables.isEmpty()) {
                Session session = SESSION_CACHE.get(sessionId);
                if (session != null && session.isOpen()) {
                    sendRealtimeData(session, variables);
                }
            }
        });
    }

    /**
     * 发送消息到客户端
     */
    private static void sendMessage(Session session, String message) {
        try {
            if (session != null && session.isOpen()) {
                session.getBasicRemote().sendText(message);
            }
        } catch (IOException e) {
            log.error("发送实时变量WebSocket消息失败: {}", e.getMessage(), e);
            try {
                session.close();
            } catch (IOException ex) {
                log.error("关闭session失败: {}", ex.getMessage());
            }
            SESSION_CACHE.remove(session.getId());
            SUBSCRIPTION_CACHE.remove(session.getId());
        }
    }

    /**
     * 发送错误消息
     */
    private static void sendErrorMessage(Session session, String errorMessage) {
        try {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", errorMessage);
            errorResponse.put("timestamp", String.valueOf(System.currentTimeMillis()));

            String response = JSONUtil.toJsonStr(errorResponse);
            sendMessage(session, response);
        } catch (Exception e) {
            log.error("发送错误消息失败: {}", e.getMessage());
        }
    }
}
