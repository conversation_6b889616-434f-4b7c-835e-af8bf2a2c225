package com.hollysys.inspection.controller.websocket;

import ch.qos.logback.core.util.CloseUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.hollysys.inspection.constants.MsgLevel;
import com.hollysys.inspection.model.socket.ExecuteMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.websocket.OnClose;
import javax.websocket.OnError;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.util.Map;
import java.util.Objects;

/**
 * 算法实例执行（算法调试）
 * clientKey 为了与调试接口对应
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@ServerEndpoint("/websocket/execute/algorithm/log/{clientKey}")
public class ExecuteAlgorithmServer {
    /**
     * Session连接缓存
     */
    private static final Map<String, Session> SESSION_CACHE = MapUtil.newConcurrentHashMap(20);

    /**
     * 连接建立成
     * 功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("clientKey") String clientKey) {
        // 判断当前客户端和子算法ID是否存在缓存
        if (SESSION_CACHE.containsKey(clientKey)) {
            sendLogToWeb(clientKey, ExecuteMsg.create(MsgLevel.ERROR, "算法正在调试中，请勿重复执行"));
            CloseUtil.closeQuietly(session);
            return;
        }
        // 连接加入缓存
        putCache(clientKey, session);
    }

    /**
     * 连接关闭
     * 调用的方法
     */
    @OnClose
    public void onClose(@PathParam("clientKey") String clientKey) {
        // 连接缓存移除
        deleteCache(clientKey);
    }

    /**
     * 出错后回调
     *
     * @param error 错误信息
     */
    @OnError
    public void onError(Throwable error) {
        log.error("websocket错误:{}", error.getMessage());
    }

    public void sendLogToWeb(String clientKey, ExecuteMsg msg) {
        Session session = SESSION_CACHE.get(clientKey);
        if (Objects.isNull(session)) {
            return;
        }
        try {
            session.getBasicRemote().sendText(JSONUtil.toJsonStr(msg));
        } catch (Exception e) {
            log.error("sendMessage出现错误", e);
        }
    }

    public void close(String clientKey) {
        Session session = SESSION_CACHE.get(clientKey);
        if (Objects.isNull(session)) {
            return;
        }
        CloseUtil.closeQuietly(session);
    }

    private synchronized void putCache(String clientKey, Session session) {
        SESSION_CACHE.put(clientKey, session);
    }

    private synchronized void deleteCache(String clientKey) {
        SESSION_CACHE.remove(clientKey);
    }
}

