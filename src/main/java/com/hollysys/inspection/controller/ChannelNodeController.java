package com.hollysys.inspection.controller;


import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hollysys.inspection.constants.ProjectNodeType;
import com.hollysys.inspection.constants.channel.CameraPtzAction;
import com.hollysys.inspection.controller.base.BaseApiController;
import com.hollysys.inspection.entity.InspChannelInfo;
import com.hollysys.inspection.entity.InspPresetInfo;
import com.hollysys.inspection.model.CropPicModel;
import com.hollysys.inspection.model.channel.CatchCreateChannelReq;
import com.hollysys.inspection.model.channel.ChannelGetRespModel;
import com.hollysys.inspection.model.channel.ScaleModel;
import com.hollysys.inspection.model.channelexcel.ImportReqModel;
import com.hollysys.inspection.service.InspChannelInfoService;
import com.hollysys.inspection.service.InspPresetInfoService;
import com.hollysys.inspection.service.protocol.impl.CameraCtrlProxyService;
import com.hollysys.inspection.utils.LocalFileServerUtil;
import com.hollysys.inspection.utils.MinioUtil;
import com.hollysys.inspection.utils.PicUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.File;

/**
 * (ChannelNode)表控制层
 *
 * <AUTHOR>
 * @since 2023-02-01 15:36:00
 */
@Api(description = "[ChannelNode]表控制层")
@RestController
@RequestMapping("channel-info")
public class ChannelNodeController extends BaseApiController {
    /**
     * 服务对象
     */
    @Resource
    private InspChannelInfoService inspChannelInfoService;

    @Resource
    private InspPresetInfoService inspPresetInfoService;

    @Resource
    private CameraCtrlProxyService cameraCtrlProxyService;

    /**
     * 分页查询所有数据
     *
     * @param page 分页对象
     * @return 所有数据
     */
    @ApiOperation("分页查询所有数据")
    @GetMapping("list-page")
    public Object listPage(Page<InspChannelInfo> page) {
        return success(this.inspChannelInfoService.listPage(page));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation("通过主键查询单条数据")
    @GetMapping("{id}")
    public Object selectOne(@PathVariable String id) {
        InspChannelInfo channelInfo = this.inspChannelInfoService.getOneById(id);
        ChannelGetRespModel channelGetRespModel = new ChannelGetRespModel();
        BeanUtils.copyProperties(channelInfo, channelGetRespModel);
        return success(channelGetRespModel);
    }

    /**
     * 修改数据
     *
     * @param inspChannelInfo 实体对象
     * @return 修改结果
     */
    @ApiOperation("修改数据")
    @PutMapping
    public Object update(@RequestBody InspChannelInfo inspChannelInfo) {
        return success(this.inspChannelInfoService.updateChannelInfo(inspChannelInfo));
    }

    @ApiOperation("解析RTSP流地址")
    @GetMapping("rtsp-by-channel")
    public Object getRtspByChannelInfo(InspChannelInfo channelInfo) {
        return success(inspChannelInfoService.getRtspByChannelInfo(channelInfo));
    }

    @ApiOperation("获取摄像头厂商")
    @GetMapping("camera-factory")
    public Object getCameraFactory(InspChannelInfo channelInfo) {
        return success(inspChannelInfoService.getCameraManufacture(channelInfo));
    }

    /**
     * 获取视频预览链接
     *
     * @param channelId 通道id
     * @return 视频预览链接
     */
    @ApiOperation("获取通道视频预览信息")
    @GetMapping("preview-info")
    public Object getPreviewUrl(String channelId) {
        return success(this.inspChannelInfoService.getPreviewInfo(channelId));
    }

    /**
     * 获取视频预览分辨率
     *
     * @param id   id
     * @param type type
     * @return 视频预览链接
     */
    @ApiOperation("获取视频分辨率")
    @GetMapping("scale")
    public Object getScale(@RequestParam String id, @RequestParam String type) {
        ScaleModel result;
        if (type.equals(ProjectNodeType.CHANNEL)) {
            result = inspChannelInfoService.getScale(id);
        } else {
            InspPresetInfo byId = inspPresetInfoService.getOneById(id);
            result = inspChannelInfoService.getScale(byId.getChannelId());
        }
        return success(result);
    }

    /**
     * 模板导出
     */
    @ApiOperation("模板导出")
    @GetMapping("export-template")
    public Object exportToExcelTemplate() {
        return this.inspChannelInfoService.exportToExcelTemplate();
    }

    /**
     * 通道导入
     */
    @ApiOperation("通道导出")
    @GetMapping("export-excel")
    public Object exportToExcel() {
        return this.inspChannelInfoService.exportToExcel();
    }

    /**
     * 通道导入
     */
    @ApiOperation("通道导入")
    @PostMapping("import-excel")
    public Object importFromExcel(ImportReqModel reqModel) {
        return success(this.inspChannelInfoService.importFromExcel(reqModel));
    }

    /**
     * @param channelId 通道id
     * @param action    操作动作 1 上 2 下 3左 4右 5 放大 6 缩小
     */
    @ApiOperation("开启控制")
    @GetMapping("start-control-operate")
    public Object startControlOperate(@RequestParam String channelId, @RequestParam CameraPtzAction action) {
        InspChannelInfo channelInfo = inspChannelInfoService.getOneById(channelId);
        cameraCtrlProxyService.startCameraPtz(channelInfo, action);
        return success("ok");
    }

    /**
     * @param channelId 通道id
     */
    @ApiOperation("结束控制")
    @GetMapping("stop-control-operate")
    public Object stopControlOperate(@RequestParam String channelId, @RequestParam CameraPtzAction action) {
        InspChannelInfo channelInfo = inspChannelInfoService.getOneById(channelId);
        cameraCtrlProxyService.stopCameraPtz(channelInfo, action);
        return success("ok");
    }

    /**
     * @param cropPicModel 裁剪图片、坐标
     * @return 裁剪后的图片
     */
    @ApiOperation("裁剪图片")
    @PostMapping("crop")
    public Object cropPic(@RequestBody CropPicModel cropPicModel) {
        // 将文件存储到本地
        String filePath = cropPicModel.getFilePath();
        File fileSource = LocalFileServerUtil.downloadToTemp(MinioUtil.urlToAbsolute(filePath));
        File file = PicUtil.cropPic(fileSource, cropPicModel.getSquare());
        String httpUrl = MinioUtil.uploadFile("corp_" + file.getName(), FileUtil.getInputStream(file));
        String urlToRelative = MinioUtil.urlToRelative(httpUrl);
        // 拼接随机数,避免前端缓存
        urlToRelative += "?timestamp=" + System.currentTimeMillis();
        return success(urlToRelative);
    }

    @ApiOperation("获取当前通道的实时图片")
    @GetMapping("channel-pic")
    public Object getChannelPic(String channelId) {
        InspChannelInfo channelInfo = inspChannelInfoService.getOneById(channelId);
        String picUrl = cameraCtrlProxyService.getPic(channelInfo);
        return success(MinioUtil.urlToRelative(picUrl));
    }

    @ApiOperation("批量插入通道，用于单设备多通道机器")
    @PostMapping("batch-create")
    public Object batchCreateChannel(@RequestBody CatchCreateChannelReq paramReqModel) {
        return success(inspChannelInfoService.batchCreateChannel(paramReqModel));
    }
}

