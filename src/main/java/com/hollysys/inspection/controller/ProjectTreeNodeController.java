package com.hollysys.inspection.controller;


import com.hollysys.inspection.controller.base.BaseApiController;
import com.hollysys.inspection.entity.InspProjectTreeNode;
import com.hollysys.inspection.model.BatchAreaModel;
import com.hollysys.inspection.model.tree.UpdateNodeInfoReqModel;
import com.hollysys.inspection.service.ProjectTreeNodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * (ProjectTreeNode)表控制层
 *
 * <AUTHOR>
 * @since 2023-02-01 15:48:34
 */
@Api(description = "[ProjectTreeNode]表控制层")
@RestController
@RequestMapping("project-tree-node")
public class ProjectTreeNodeController extends BaseApiController {
    /**
     * 服务对象
     */
    @Resource
    private ProjectTreeNodeService projectTreeNodeService;

    /**
     * 新增数据
     *
     * @param projectTreeNode 实体对象
     * @return 新增结果
     */
    @ApiOperation("新增数据")
    @PostMapping
    public Object insert(@RequestBody InspProjectTreeNode projectTreeNode) {
        return success(this.projectTreeNodeService.createProjectNode(projectTreeNode));
    }

    /**
     * 批量新增区域数据
     *
     * @param batchAreaModel 实体对象
     * @return 新增结果
     */
    @ApiOperation("新增数据")
    @PostMapping("batch")
    public Object batchInsert(@RequestBody @Valid BatchAreaModel batchAreaModel) {
        return success(this.projectTreeNodeService.createBatchArea(batchAreaModel));
    }

    /**
     * 批量新增数据
     *
     * @param projectTreeNodes 实体对象,每次限制500条
     * @return 新增结果
     */
    @ApiOperation("新增数据")
    @PostMapping("insertList")
    public Object insert(@RequestBody List<InspProjectTreeNode> projectTreeNodes) {
        return success(this.projectTreeNodeService.createProjectNodes(projectTreeNodes));
    }

    /**
     * 修改数据
     *
     * @param projectTreeNode 实体对象
     * @return 修改结果
     */
    @ApiOperation("修改数据")
    @PutMapping
    public Object update(@RequestBody UpdateNodeInfoReqModel projectTreeNode) {
        return success(this.projectTreeNodeService.updateProjectNode(projectTreeNode));
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @ApiOperation("删除数据")
    @DeleteMapping
    public Object delete(@RequestParam("idList") List<String> idList) {
        return success(this.projectTreeNodeService.removeProjectNode(idList));
    }
}

