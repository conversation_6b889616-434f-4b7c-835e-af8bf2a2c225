package com.hollysys.inspection.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hollysys.inspection.controller.base.BaseApiController;
import com.hollysys.inspection.entity.InspAlgorithmModel;
import com.hollysys.inspection.model.algorithm.SaveAlgorithmReqModel;
import com.hollysys.inspection.model.model.ListModelReqModel;
import com.hollysys.inspection.model.model.SaveModelReqModel;
import com.hollysys.inspection.service.InspAlgorithmService;
import com.hollysys.inspection.service.InspModelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 算法信息表(InspAlgorithm)表控制层
 *
 * <AUTHOR>
 * @since 2023-02-03 15:49:14
 */
@Api(description = "[InspAlgorithm]表控制层")
@RestController
@RequestMapping("algorithm")
public class InspAlgorithmController extends BaseApiController {

    /**
     * 服务对象
     */
    @Resource
    private InspAlgorithmService inspAlgorithmService;


    @Resource
    private InspModelService inspModelService;

    /**
     * 算法添加
     *
     * @param reqModel 算法数据
     * @return 算法添加
     */
    @ApiOperation("算法添加")
    @PostMapping
    public Object newAlgorithm(SaveAlgorithmReqModel reqModel) {
        return success(this.inspAlgorithmService.newAlgorithm(reqModel));
    }

    /**
     * 算法更新
     *
     * @param reqModel 实体对象
     * @return 修改结果
     */
    @ApiOperation("算法修改数据")
    @PutMapping
    public Object update(SaveAlgorithmReqModel reqModel) {
        return success(this.inspAlgorithmService.updateAlgorithm(reqModel));
    }

    /**
     * 查询数据列表
     *
     * @param algorithmName 算法名称
     * @return 所有数据
     */
    @ApiOperation("查询算法数据列表")
    @GetMapping("list")
    public Object pageList(String algorithmName) {
        return success(this.inspAlgorithmService.listAlgorithm(algorithmName));
    }

    /**
     * 删除数据
     *
     * @param algorithmId 主键
     * @return 删除结果
     */
    @ApiOperation("删除算法数据")
    @DeleteMapping
    public Object delete(@RequestParam("algorithmId") String algorithmId) {
        return success(this.inspAlgorithmService.removeAlgorithm(algorithmId));
    }

    /**
     * 模型添加
     *
     * @param reqModel 模型数据
     * @return 模型
     */
    @ApiOperation("模型添加")
    @PostMapping("/model")
    public Object newModel(SaveModelReqModel reqModel) {
        return success(this.inspModelService.newModal(reqModel));
    }

    /**
     * 模型更新
     *
     * @param reqModel 模型数据
     * @return 模型
     */
    @ApiOperation("模型更新")
    @PutMapping("/model")
    public Object updateModel(SaveModelReqModel reqModel) {
        return success(this.inspModelService.updateModal(reqModel));
    }

    /**
     * 删除模型
     *
     * @param modelId 主键
     * @return 删除结果
     */
    @ApiOperation("删除算法数据")
    @DeleteMapping("/model")
    public Object deleteModel(@RequestParam("modelId") String modelId) {
        return success(this.inspModelService.removeModel(modelId));
    }

    /**
     * 查询模型数据列表
     *
     * @param reqModel 请求对象
     * @return 所有数据
     */
    @ApiOperation("分页查询模型数据列表")
    @GetMapping("list-model")
    public Object listModel(Page<InspAlgorithmModel> page, ListModelReqModel reqModel) {
        return success(this.inspModelService.listModel(page, reqModel));
    }
}

