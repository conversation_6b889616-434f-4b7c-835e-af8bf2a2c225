package com.hollysys.inspection.controller;

import com.hollysys.inspection.controller.base.BaseApiController;
import com.hollysys.inspection.service.LicenseService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

@RestController
@RequestMapping("license")
public class AuthorizationInfoController extends BaseApiController {

    @Resource
    private LicenseService licenseService;

    @ApiOperation("获取授权证书信息")
    @GetMapping("authorization-info")
    public Object getAuthorizationInfo() {
        return success(this.licenseService.getLicenseExtraParam());
    }

    @ApiOperation("上传授权证书")
    @PostMapping("upload-license-file")
    public Object uploadLicenseFile(MultipartFile licenseFile) {
        return success(this.licenseService.uploadLicenseFile(licenseFile));
    }

    @ApiOperation("校验授权证书")
    @GetMapping("check-license")
    public Object checkLicense() {
        return success(this.licenseService.checkLicense());
    }


}
