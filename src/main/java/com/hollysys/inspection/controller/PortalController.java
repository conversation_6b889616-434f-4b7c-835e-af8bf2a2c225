package com.hollysys.inspection.controller;


import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.config.redis.RedisHelper;
import com.hollysys.inspection.controller.base.BaseApiController;
import com.hollysys.inspection.model.portal.LoginReqModel;
import com.hollysys.inspection.service.PortalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 门户控制层
 *
 * <AUTHOR>
 * @since 2022-11-26 14:38:10
 */
@Slf4j
@Api(description = "门户控制层")
@RestController
@RequestMapping("portal")
public class PortalController extends BaseApiController {

    @Resource
    private RedisHelper redisUtil;

    @Resource
    private PortalService portalService;

    @Value("${captcha.show-of-times}")
    private int captchaShowOfTimes;

    @Value("${db-base.auth-srv.enabled}")
    private boolean hsmAuthEnabled;

    /**
     * 获取验证码
     *
     * @return 验证码
     */
    @ApiOperation("获取验证码")
    @GetMapping("captcha")
    public Object getCaptcha() {
        return success(portalService.getCaptcha());
    }

    /**
     * 登录接口
     *
     * @param reqModel 实体对象
     * @return 新增结果
     */
    @ApiOperation("用户登录")
    @PostMapping("login")
    public Object login(@RequestBody LoginReqModel reqModel, HttpServletRequest request) {
        if (hsmAuthEnabled) {
            // 如果开启单点登录，则此方法禁止执行
            throw new InspectionException("已开启单点登录，请访问单点登录页面进行登录操作");
        }
        String clientIp = ServletUtil.getClientIP(request);
        try {
            SaTokenInfo loginResult = portalService.login(reqModel, clientIp);
            // 登陆成功，清除验证码计数器
            redisUtil.delete(clientIp);
            return success(loginResult);
        } catch (InspectionException exception) {
            // 登陆失败，记录当前客户端IP，开始计数
            Long aLong = redisUtil.incrementCounter(clientIp);
            if (aLong >= captchaShowOfTimes) {
                exception.setInspStatusCode(168);
            }
            throw exception;
        }
    }

    /**
     * 退出登录
     *
     * @return 退出登录结果
     */
    @ApiOperation("退出登录")
    @DeleteMapping("logout")
    public Object logout() {
        return success(portalService.logout());
    }

    /**
     * 判断是否登录
     *
     * @return 判断是否登录
     */
    @ApiOperation("判断是否登录")
    @GetMapping("is-login")
    public Object isLogin() {
        boolean isLogin = StpUtil.isLogin();
        return success(String.valueOf(isLogin));
    }
}

