package com.hollysys.inspection.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hollysys.inspection.config.exceptions.InspectionException;
import com.hollysys.inspection.config.redis.RedisHelper;
import com.hollysys.inspection.constants.InspConstants;
import com.hollysys.inspection.constants.alarm.AlarmStatus;
import com.hollysys.inspection.constants.algorithm.ExecuteResultStatus;
import com.hollysys.inspection.constants.channel.VideoProtocolType;
import com.hollysys.inspection.controller.base.BaseApiController;
import com.hollysys.inspection.entity.*;
import com.hollysys.inspection.model.alarm.AlarmListItemModel;
import com.hollysys.inspection.model.algorithm.execute.AlgorithmExecuteRsp;
import com.hollysys.inspection.model.algorithm.execute.OutputValueObj;
import com.hollysys.inspection.model.algorithm.execute.record.AlgorithmRecordItemModel;
import com.hollysys.inspection.model.mes.ExecuteRecordModel;
import com.hollysys.inspection.model.mes.ListAlarmItem;
import com.hollysys.inspection.model.mes.UpdateAlarmStatusReq;
import com.hollysys.inspection.model.schedule.ScheduleTaskDetailModel;
import com.hollysys.inspection.model.schedule.ScheduleTaskNodeModel;
import com.hollysys.inspection.service.*;
import com.hollysys.inspection.service.protocol.impl.CameraCtrlProxyService;
import com.hollysys.inspection.utils.MesUtil;
import com.hollysys.inspection.utils.MinioUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Api(description = "MES访问接口")
@RestController
@RequestMapping("/os/mes")
public class OpenServerForMesController extends BaseApiController {

    @Value("${server.host}")
    private String serverHost;

    @Resource
    private InspScheduleTaskTreeNodeService scheduleTaskTreeNodeService;

    @Resource
    private InspAlarmRecordService alarmRecordService;

    @Resource
    private InspRecordPresetService recordPresetService;

    @Resource
    private InspRecordAlgorithmService recordAlgorithmService;

    @Resource
    private MediamtxService mediamtxService;

    @Resource
    private InspScheduleTaskService scheduleTaskService;

    @Resource
    private InspScheduleTaskNodeService scheduleTaskNodeService;

    @Resource
    private IndexStatisticalService indexStatisticalService;

    @Resource
    private CameraCtrlProxyService cameraCtrlProxyService;

    @Resource
    private InspChannelInfoService channelInfoService;

    @Resource
    private InspPresetInfoService presetInfoService;

    @Resource
    private InspProjectService projectService;

    @Resource
    private RedisHelper redisHelper;

    @ApiOperation("摄像头预置点树结构查询")
    @GetMapping("/tree-list")
    public Object treeList() {
        return success(projectService.mesTreeList());
    }

    @ApiOperation("查询任务树结构")
    @GetMapping("task/tree")
    public Object getTaskTree() {
        return success(this.scheduleTaskTreeNodeService.getTaskTree());
    }

    /**
     * 通过任务ID查询巡检任务详情
     *
     * @param taskId 任务ID
     * @return 巡检任务详情
     */
    @ApiOperation("通过任务ID查询巡检任务详情")
    @GetMapping("task/detail")
    public Object listByTaskId(String taskId) {
        ScheduleTaskDetailModel taskDetail = scheduleTaskService.getTaskDetail(taskId);
        List<ScheduleTaskNodeModel> taskNodes = taskDetail.getTaskNodes();
        if (CollectionUtil.isNotEmpty(taskNodes)) {
            // 设置通道扩展信息（通道实时截图、推流操作等）
            setChannelExt(taskNodes);
        }

        return success(taskDetail);
    }

    private void setChannelExt(List<ScheduleTaskNodeModel> scheduleTaskNodeModels) {
        if (CollectionUtil.isEmpty(scheduleTaskNodeModels)) {
            return;
        }
        List<String> channelIds = scheduleTaskNodeModels.stream().map(ScheduleTaskNodeModel::getChannelId)
                .distinct()
                .collect(Collectors.toList());
        Map<String, String> picMap = new HashMap<>();
        channelIds.stream().parallel().forEach(channelId -> {
            try {
                InspChannelInfo channelInfo = channelInfoService.getOneById(channelId);
                String picUrl = redisHelper.get(getChannelPicCacheKey(channelId));
                if (StrUtil.isBlank(picUrl)) {
                    picUrl = cameraCtrlProxyService.getPic(channelInfo);
                    picUrl = setChannelPicCache(channelId, picUrl);
                }
                picMap.put(channelId, picUrl);
            } catch (Exception exception) {
                log.error("设置任务点封面图片出现异常，通道ID = {}", channelId, exception);
            }
        });

        ScheduleTaskNodeModel scheduleTaskNodeModel = scheduleTaskNodeModels.get(0);
        String channelIdFirst = scheduleTaskNodeModel.getChannelId();
        try {
            // 推流，默认推送第一个巡检点的视频
            InspChannelInfo channelInfo = channelInfoService.getOneById(channelIdFirst);
            if (VideoProtocolType.RTSP.name().equals(channelInfo.getVideoProtocolType())) {
                mediamtxService.pushStream(channelInfo);
            }
        } catch (Exception exception) {
            log.error("尝试推送第一个巡检点视频流失败，通道ID = {}", channelIdFirst, exception);
        }

        for (ScheduleTaskNodeModel taskNodeModel : scheduleTaskNodeModels) {
            String channelId = taskNodeModel.getChannelId();
            String pic = picMap.get(channelId);
            if (StrUtil.isNotBlank(pic)) {
                // 将图片相对路径设置为全路径
                pic = MesUtil.urlToAbsolute(serverHost, pic);
                taskNodeModel.setChannelPic(pic);
            }
        }
    }

    /**
     * 任务暂停调度（切换为手动）
     */
    @ApiOperation("任务暂停调度（切换为手动）")
    @PostMapping("task/pause")
    public Object pauseTask(@RequestBody InspScheduleTaskNode param) {
        scheduleTaskService.pauseTask(param.getTaskId());
        return success("ok");
    }

    /**
     * 任务恢复调度（切换为自动）
     */
    @ApiOperation("任务恢复调度（切换为自动）")
    @PostMapping("task/resume")
    public Object resumeTask(@RequestBody InspScheduleTaskNode param) {
        scheduleTaskService.resumeTask(param.getTaskId());
        return success("ok");
    }

    /**
     * 分页查询报警信息
     *
     * @param isConfirm 查询的报警信息是否确认筛选条件
     * @param isClose   查询的报警信息是否恢复筛选条件
     * @param nodeId    所属节点信息筛选条件
     * @param startTime 报警的开始时间筛选条件
     * @param endTime   报警的结束时间筛选条件
     */
    @ApiOperation("查询全部报警信息")
    @GetMapping("list-alarm")
    public Object pageAlarmHistory(Page<InspAlarmRecord> page, String nodeId, String startTime, String endTime,
                                   Boolean isConfirm,
                                   Boolean isClose,
                                   String searchStr) {
        IPage<AlarmListItemModel> alarmPage = this.alarmRecordService.pageMesAlarms(page, nodeId, startTime, endTime,
                isConfirm, isClose, searchStr);
        IPage<ListAlarmItem> convert = alarmPage.convert(record -> {
            ListAlarmItem listAlarmItem = new ListAlarmItem();
            BeanUtils.copyProperties(record, listAlarmItem);
            // 将图片相对路径设置为全路径
            List<String> strings = MesUtil.urlsToAbsolute(serverHost, record.getResultImgs());
            listAlarmItem.setImgPath(strings);

            listAlarmItem.setTime(DateUtil.format(record.getUpdateTime(), InspConstants.yyyy_MM_ddHHmmss));

            LocalDateTime restoreTime = record.getRestoreTime();
            if (Objects.nonNull(restoreTime)) {
                listAlarmItem.setRestoreTime(DateUtil.format(restoreTime, InspConstants.yyyy_MM_ddHHmmss));
            }

            listAlarmItem.setType(record.getAlgorithmInstanceName());
            return listAlarmItem;
        });
        return success(convert);
    }

    /**
     * 修改报警状态
     */
    @ApiOperation("修改报警状态")
    @PostMapping("update-alarm-status")
    public Object updateAlarmStatus(@RequestBody UpdateAlarmStatusReq reqModel) {
        String alarmId = reqModel.getAlarmId();
        InspAlarmRecord alarmRecord = alarmRecordService.getById(alarmId);
        if (Objects.isNull(alarmRecord)) {
            throw new InspectionException("报警信息不存在");
        }
        String status = reqModel.getStatus();
        AlarmStatus alarmStatus = EnumUtil.fromStringQuietly(AlarmStatus.class, status);
        if (Objects.isNull(alarmStatus)) {
            throw new InspectionException("报警状态不合法");
        }

        if (AlarmStatus.CLOSED.equals(alarmStatus)) {
            LocalDateTime now = LocalDateTime.now();
            alarmRecord.setRestoreTime(now);
        }

        alarmRecordService.updateById(alarmRecord);
        return success("ok");
    }

    @ApiOperation("分页查询巡检记录数据")
    @GetMapping("execute-record/all")
    public Object pageExecuteRecord(Page<InspRecordAlgorithm> page, String nodeId, String searchStr,
                                    @RequestParam(required = false) String startTime,
                                    @RequestParam(required = false) String endTime,
                                    ExecuteResultStatus executeResultStatus) {
        IPage<AlgorithmRecordItemModel> iPage = recordAlgorithmService.pageAllAlgorithmRecords(page, nodeId, startTime,
                endTime, searchStr, executeResultStatus);
        List<AlgorithmRecordItemModel> records = iPage.getRecords();
        if (CollectionUtil.isNotEmpty(records)) {
            // 将图片相对路径设置为全路径
            for (AlgorithmRecordItemModel record : records) {
                List<String> inputImages = record.getInputImages();
                if (CollectionUtil.isNotEmpty(inputImages)) {
                    inputImages = MesUtil.urlsToAbsolute(serverHost, inputImages);
                    record.setInputImages(inputImages);
                }
                String outputImage = record.getOutputImage();
                if (StrUtil.isNotBlank(outputImage)) {
                    outputImage = MesUtil.urlToAbsolute(serverHost, outputImage);
                    record.setOutputImage(outputImage);
                }
            }
        }

        iPage.convert(item -> {
            ExecuteRecordModel executeRecordModel = new ExecuteRecordModel();
            executeRecordModel.setPresetName(item.getPresetName());
            executeRecordModel.setChannelName(item.getChannelName());
            executeRecordModel.setTime(DateUtil.format(item.getStartTime(), InspConstants.yyyy_MM_ddHHmmss));
            executeRecordModel.setImgPath(ListUtil.of(item.getOutputImage()));

            // 设置预置点执行启动方式（手动或调度）
            InspRecordPreset recordPreset = recordPresetService.getById(item.getRecordPresetId());
            executeRecordModel.setModel(recordPreset.getModel());
            AlgorithmExecuteRsp executeResult = item.getExecuteResult();
            if (Objects.nonNull(executeResult) && BooleanUtil.isTrue(executeResult.getIsSuccess())) {
                // TODO 取第一个结果
                Map<String, OutputValueObj> output = executeResult.getOutput();
                if (CollectionUtil.isNotEmpty(output)) {
                    Collection<OutputValueObj> values = output.values();
                    OutputValueObj first = CollectionUtil.getFirst(values);
                    Object value = first.getValue();
                    if (Objects.nonNull(value)) {
                        executeRecordModel.setResult(value.toString());
                    }
                }
            }
            executeRecordModel.setStatus(item.getResultStatus());
            return executeRecordModel;
        });
        return success(iPage);
    }

    @ApiOperation("查询首页统计数据")
    @GetMapping("statistical-data")
    public Object getStatisticalData() {
        Map<String, Object> result = new HashMap<>();
        // 累计报警数量
        int sumAlarmCount = indexStatisticalService.getSumAlarmCount();
        result.put("sumAlarmCount", sumAlarmCount);
        // 累计巡检记录数量
        int sumExecuteRecords = indexStatisticalService.getSumExeRecordCount();
        result.put("sumExecuteRecords", sumExecuteRecords);
        // 今日告警数
        int todayAlarmCount = indexStatisticalService.getTodayAlarmCount();
        result.put("todayAlarmCount", todayAlarmCount);
        // 今日巡检记录数
        int todayExeRecordCount = indexStatisticalService.getTodayExeRecordCount();
        result.put("todayExeRecordCount", todayExeRecordCount);
        // 总巡航点数
        int allInspPoint = indexStatisticalService.insPointCount();
        result.put("allInspPoint", allInspPoint);
        // 巡检报警排名
        Map<String, Integer> alarmRank = indexStatisticalService.getAlarmRank();
        result.put("alarmRank", alarmRank);
        // 巡检异常分类统计
        Map<String, Integer> alarmRankByAlgorithm = indexStatisticalService.getAlarmRankByAlgorithm();
        result.put("alarmCountByClass", alarmRankByAlgorithm);

        // 今日巡检报警率
        float todayAlarmRate = indexStatisticalService.todayAlarmRate();
        result.put("todayAlarmRate", todayAlarmRate);
        // 总巡检报警率
        float totalAlarmRate = indexStatisticalService.totalAlarmRate();
        result.put("totalAlarmRate", totalAlarmRate);

        // 近七日报警趋势统计
        Map<String, Integer> sumAlarmByDay = indexStatisticalService.sumAlarmByDay();
        result.put("sumAlarmByDay", sumAlarmByDay);

        // 巡检综合评分
        int inspScore = indexStatisticalService.getInspScore();
        result.put("inspScore", inspScore);
        return success(result);
    }

    @ApiOperation("确认报警")
    @PostMapping("confirm-alarm")
    public Object confirmAlarm(@RequestBody UpdateAlarmStatusReq reqModel) {
        String alarmId = reqModel.getAlarmId();
        if (StrUtil.isBlank(alarmId)) {
            throw new InspectionException("报警ID不允许为空");
        }
        InspAlarmRecord byId = alarmRecordService.getById(alarmId);
        if (Objects.isNull(byId)) {
            throw new InspectionException("报警信息不存在");
        }
        Boolean confirm = byId.getConfirm();
        if (!BooleanUtil.isTrue(confirm)) {
            alarmRecordService.lambdaUpdate()
                    .set(InspAlarmRecord::getConfirm, true)
                    .set(InspAlarmRecord::getConfirmTime, LocalDateTime.now())
                    .eq(InspAlarmRecord::getId, alarmId)
                    .update();
        }
        return success("ok");
    }

    /**
     * 为MES提供预置点视频预览功能
     */
    @ApiOperation("跳转到预置点")
    @GetMapping("jump-to-preset")
    public Object jumpToPreset(String presetId) {
        InspPresetInfo presetInfo = presetInfoService.getOneById(presetId);
        InspChannelInfo channelInfo = channelInfoService.getOneById(presetInfo.getChannelId());
        return success(cameraCtrlProxyService.gotoPreset(channelInfo, presetInfo));
    }

    @Scheduled(fixedDelay = 1, timeUnit = TimeUnit.HOURS)
    public void cacheTaskChannelPic() {
        // 查询全部任务点
        List<InspScheduleTaskNode> list = scheduleTaskNodeService.list();
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        List<String> channelIds = list.stream().map(InspScheduleTaskNode::getChannelId).distinct().collect(Collectors.toList());
        for (String channelId : channelIds) {
            try {
                InspChannelInfo channelInfo = channelInfoService.getOneById(channelId);
                String picUrl = cameraCtrlProxyService.getPic(channelInfo);
                setChannelPicCache(channelId, picUrl);
                log.debug("缓存通道{}截图成功", channelId);
            } catch (Exception e) {
                log.error("缓存通道{}截图失败", channelId, e);
            }
        }
    }

    private String setChannelPicCache(String channelId, String picUrl) {
        picUrl = MinioUtil.urlToRelative(picUrl);
        redisHelper.setEx(getChannelPicCacheKey(channelId), picUrl, 1, TimeUnit.DAYS);
        return picUrl;
    }

    private String getChannelPicCacheKey(String channelId) {
        return "CHANNEL-PIC:" + channelId;
    }
}
