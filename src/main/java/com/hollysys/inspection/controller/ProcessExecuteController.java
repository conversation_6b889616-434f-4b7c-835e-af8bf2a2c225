package com.hollysys.inspection.controller;


import com.hollysys.inspection.controller.base.BaseApiController;
import com.hollysys.inspection.model.algorithm.execute.AlgorithmExecuteReq;
import com.hollysys.inspection.model.execute.GetTestInputPicReq;
import com.hollysys.inspection.service.ProcessExecuteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 算法流程执行控制层
 *
 * <AUTHOR>
 * @since 2023-02-01 15:36:00
 */
@Api(description = "算法流程执行控制层")
@RestController
@RequestMapping("process-execute")
public class ProcessExecuteController extends BaseApiController {

    @Resource
    private ProcessExecuteService processExecuteService;

    @ApiOperation("算法调试")
    @PostMapping("test-algorithm")
    public Object testAlgorithm(@RequestBody AlgorithmExecuteReq reqModel) {
        return success(processExecuteService.testAlgorithm(reqModel));
    }

    @ApiOperation("获取算法调试输入截图")
    @PostMapping("test-input-pic")
    public Object getTestInputPic(@RequestBody GetTestInputPicReq reqModel) {
        return success(processExecuteService.getTestInputPic(reqModel));
    }
}

