package com.hollysys.inspection.controller;

import com.hollysys.inspection.controller.base.BaseApiController;
import com.hollysys.inspection.entity.InspNtpManageInfo;
import com.hollysys.inspection.model.ntp.SetSysDateReqModel;
import com.hollysys.inspection.service.NtpManageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * NTP校时相关接口控制层
 */
@Api(description = "NTP校时相关接口控制层")
@RestController
@RequestMapping("npt-manage")
public class NtpManageController extends BaseApiController {

    @Resource
    private NtpManageService ntpManageService;

    @ApiOperation("获取系统当前时间")
    @GetMapping("date-now")
    public Object getSysDateNow() {
        return success(ntpManageService.getSysDateNow());
    }

    @ApiOperation("设置系统当前时间")
    @PutMapping("system-date")
    public Object setSystemDate(@RequestBody SetSysDateReqModel reqModel) {
        return success(ntpManageService.setSystemDate(reqModel.getDateStr()));
    }

    @ApiOperation("获取NTP配置数据")
    @GetMapping("ntp-info")
    public Object getNtpConfigInfo() {
        return success(ntpManageService.getNtpConfigInfo());
    }

    @ApiOperation("保存NTP配置数据")
    @PutMapping("ntp-info")
    public Object saveNtpConfigInfo(@RequestBody InspNtpManageInfo reqModel) {
        return success(ntpManageService.saveNtpDateFromNtp(reqModel));
    }

    // @ApiOperation("从NTP服务同步时间")
    // @PutMapping("sync-from-ntp")
    // public Object syncDateFromNtp(@RequestBody SyncFromNtpReqModel reqModel) {
    //     return success(ntpManageService.syncDateFromNtp(reqModel.getNtpServerHost()));
    // }

    // @ApiOperation("执行脚本命令")
    // @PostMapping("exec-command")
    // public Object execCommand(@RequestBody String[] param) {
    //     // String[] cmds = new String[param.size()];
    //     // for (int i = 0; i < param.size(); i++) {
    //     //     cmds[i] = param.get(i);
    //     // }
    //     return success(RuntimeUtil.execForLines(param));
    // }
}
