package com.hollysys.inspection.controller;


import com.hollysys.inspection.controller.base.BaseApiController;
import com.hollysys.inspection.model.alarm.config.AlarmConfigModel;
import com.hollysys.inspection.service.InspAlarmConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 报警配置控制层
 */
@Api(description = "报警配置控制层")
@RestController
@RequestMapping("alarm-config")
public class InspAlarmConfigController extends BaseApiController {

    @Resource
    private InspAlarmConfigService alarmConfigService;

    @ApiOperation("查询预置点下的报警配置信息")
    @GetMapping("list-by-preset")
    public Object listByPreset(String presetId) {
        return success(alarmConfigService.getConfigListByPreset(presetId));
    }

    @ApiOperation("保存报警配置信息")
    @PostMapping
    public Object saveAlarmConfig(@RequestBody List<AlarmConfigModel> reqModels) {
        return success(alarmConfigService.saveAlarmConfig(reqModels));
    }
}

