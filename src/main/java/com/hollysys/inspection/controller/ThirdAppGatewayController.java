package com.hollysys.inspection.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hollysys.inspection.controller.base.BaseApiController;
import com.hollysys.inspection.entity.InspGlobalVariable;
import com.hollysys.inspection.model.thirdapp.AlgorithmSubscriptionReqModel;
import com.hollysys.inspection.service.InspGlobalVariableService;
import com.hollysys.inspection.service.InspThirdAlgorithmSubscriptionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 第三方应用接口调用网关控制层
 *
 * <AUTHOR>
 * @since 2023-07-05 14:28:35
 */
@Api(description = "第三方应用接口调用网关控制层")
@RestController
@RequestMapping("open-api")
public class ThirdAppGatewayController extends BaseApiController {

    @Resource
    private InspThirdAlgorithmSubscriptionService inspThirdAlgorithmSubscriptionService;

    @Resource
    private InspGlobalVariableService globalVariableService;

    /**
     * 分页查询变量清单
     *
     * @return 分页查询变量清单
     */
    @ApiOperation("分页查询变量清单")
    @GetMapping("global-variable")
    public Object selectAll(@RequestParam(value = "current", required = false) String  currentParam,
                            @RequestParam(value = "size", required = false) String  sizeParam) {
        return success(globalVariableService.pageList(currentParam, sizeParam));
    }

    /**
     * 查询全部变量清单
     *
     * @return 变量清单
     */
    @ApiOperation("查询全部变量清单")
    @GetMapping("global-variable/all")
    public Object listAll() {
        return success(globalVariableService.list());
    }

    /**
     * 查询变量实时值
     *
     * @param valNames 查询实体
     * @return 变量实时值
     */
    @ApiOperation("查询变量实时值")
    @PostMapping("global-variable/realtime-val")
    public Object getRealtimeVal(@RequestBody(required = false) List<String> valNames) {
        return success(globalVariableService.listValueWithRefresh(valNames));
    }

    /**
     * 查询变量历史值
     *
     * @param algorithmSubscriptionReqModel 请求体
     * @return 变量实时值
     */
    @ApiOperation("算法订阅")
    @PostMapping("algorithm-subscription")
    public Object algorithmSubscription(@RequestBody @Valid AlgorithmSubscriptionReqModel algorithmSubscriptionReqModel, @RequestHeader(HttpHeaders.AUTHORIZATION) String appAuthorizationCode) {
        return success(this.inspThirdAlgorithmSubscriptionService.subscriptionAlgorithm(algorithmSubscriptionReqModel, appAuthorizationCode));
    }

    /**
     * 查询系统中所有的预置点信息
     *
     * @return 预置点信息
     */
    @ApiOperation("查询预置点信息")
    @GetMapping("preset-infos")
    public Object presentInfos() {
        return success(this.inspThirdAlgorithmSubscriptionService.getPresentInfos());
    }

    /**
     * 查询系统中所有的预置点信息
     *
     * @return 预置点信息
     */
    @ApiOperation("查询算法信息")
    @GetMapping("algorithm-infos")
    public Object algorithmInfos() {
        return success(this.inspThirdAlgorithmSubscriptionService.getAlgorithmInfos());
    }

    /**
     * 查询工程树节点、状态信息
     *
     * @return 单条数据
     */
    @ApiOperation("查询工程树节点、状态信息")
    @GetMapping("tree-and-state")
    public Object selectOne() {
        return success(this.inspThirdAlgorithmSubscriptionService.getProjectTreeAndState());
    }
}

