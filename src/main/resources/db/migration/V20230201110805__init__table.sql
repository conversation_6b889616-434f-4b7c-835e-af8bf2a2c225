/*
 Navicat Premium Data Transfer

 Source Server         : ***************
 Source Server Type    : PostgreSQL
 Source Server Version : 120015
 Source Host           : ***************:5432
 Source Catalog        : postgres
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 120015
 File Encoding         : 65001

 Date: 22/03/2024 11:52:34
*/


-- ----------------------------
-- Table structure for insp_alarm_record
-- ----------------------------
DROP TABLE IF EXISTS "public"."insp_alarm_record";
CREATE TABLE "public"."insp_alarm_record" (
                                              "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                              "channel_id" varchar(32) COLLATE "pg_catalog"."default",
                                              "model_instance_id" varchar(32) COLLATE "pg_catalog"."default",
                                              "algorithm_id" varchar(32) COLLATE "pg_catalog"."default",
                                              "remake" varchar(255) COLLATE "pg_catalog"."default",
                                              "img_path" varchar(255) COLLATE "pg_catalog"."default",
                                              "start_time" timestamp(6) NOT NULL,
                                              "end_time" timestamp(6) NOT NULL,
                                              "type" varchar(255) COLLATE "pg_catalog"."default",
                                              "confirm" bool NOT NULL DEFAULT false,
                                              "source" varchar(255) COLLATE "pg_catalog"."default",
                                              "code" varchar(255) COLLATE "pg_catalog"."default",
                                              "algorithm_detail_id" varchar(32) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."insp_alarm_record"."channel_id" IS '通道id';
COMMENT ON COLUMN "public"."insp_alarm_record"."model_instance_id" IS '模型实例id';
COMMENT ON COLUMN "public"."insp_alarm_record"."algorithm_id" IS '算法实例id';
COMMENT ON COLUMN "public"."insp_alarm_record"."remake" IS '备注';
COMMENT ON COLUMN "public"."insp_alarm_record"."img_path" IS '原图地址';
COMMENT ON COLUMN "public"."insp_alarm_record"."start_time" IS '开始时间';
COMMENT ON COLUMN "public"."insp_alarm_record"."end_time" IS '结束时间';
COMMENT ON COLUMN "public"."insp_alarm_record"."type" IS '识别类型';
COMMENT ON COLUMN "public"."insp_alarm_record"."confirm" IS '是否确认';
COMMENT ON COLUMN "public"."insp_alarm_record"."source" IS '来源';
COMMENT ON COLUMN "public"."insp_alarm_record"."code" IS '识别code';
COMMENT ON COLUMN "public"."insp_alarm_record"."algorithm_detail_id" IS '算法arrayId';

-- ----------------------------
-- Table structure for insp_algorithm
-- ----------------------------
DROP TABLE IF EXISTS "public"."insp_algorithm";
CREATE TABLE "public"."insp_algorithm" (
                                           "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                           "code" varchar(255) COLLATE "pg_catalog"."default",
                                           "type" varchar(255) COLLATE "pg_catalog"."default",
                                           "name" varchar(255) COLLATE "pg_catalog"."default",
                                           "des" varchar(255) COLLATE "pg_catalog"."default",
                                           "roi_draw_type" varchar(255) COLLATE "pg_catalog"."default",
                                           "image_size" int2 NOT NULL DEFAULT 1,
                                           "interval" int2 NOT NULL DEFAULT 200,
                                           "classification" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
                                           "package_file" varchar(255) COLLATE "pg_catalog"."default",
                                           "is_preset" bool NOT NULL DEFAULT false
)
;
COMMENT ON COLUMN "public"."insp_algorithm"."code" IS '算法编码（业务主键）';
COMMENT ON COLUMN "public"."insp_algorithm"."type" IS '算法类型（CV、视觉）';
COMMENT ON COLUMN "public"."insp_algorithm"."name" IS '算法名称';
COMMENT ON COLUMN "public"."insp_algorithm"."des" IS '算法描述';
COMMENT ON COLUMN "public"."insp_algorithm"."roi_draw_type" IS '算法ROI绘制图像类型';
COMMENT ON COLUMN "public"."insp_algorithm"."image_size" IS '算法采集图片张数';
COMMENT ON COLUMN "public"."insp_algorithm"."interval" IS '算法采集图片间隔时间（单位毫秒）';
COMMENT ON COLUMN "public"."insp_algorithm"."classification" IS '算法分类';
COMMENT ON COLUMN "public"."insp_algorithm"."package_file" IS '算法打包文件';
COMMENT ON COLUMN "public"."insp_algorithm"."is_preset" IS '是否是系统预置算法';
COMMENT ON TABLE "public"."insp_algorithm" IS '算法信息表';

-- ----------------------------
-- Table structure for insp_algorithm_instance
-- ----------------------------
DROP TABLE IF EXISTS "public"."insp_algorithm_instance";
CREATE TABLE "public"."insp_algorithm_instance" (
                                                    "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                    "code" varchar(255) COLLATE "pg_catalog"."default",
                                                    "type" varchar(255) COLLATE "pg_catalog"."default",
                                                    "name" varchar(255) COLLATE "pg_catalog"."default",
                                                    "des" varchar(255) COLLATE "pg_catalog"."default",
                                                    "roi_draw_type" varchar(255) COLLATE "pg_catalog"."default",
                                                    "image_size" int2 NOT NULL DEFAULT 1,
                                                    "interval" int2 NOT NULL DEFAULT 200,
                                                    "roi" varchar(255) COLLATE "pg_catalog"."default",
                                                    "scene_id" varchar(32) COLLATE "pg_catalog"."default",
                                                    "model_instance_id" varchar(32) COLLATE "pg_catalog"."default",
                                                    "algorithm_id" varchar(32) COLLATE "pg_catalog"."default",
                                                    "classification" varchar(255) COLLATE "pg_catalog"."default",
                                                    "package_file" varchar(255) COLLATE "pg_catalog"."default",
                                                    "is_preset" bool,
                                                    "has_save_config" bool NOT NULL DEFAULT false,
                                                    "create_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP
)
;
COMMENT ON COLUMN "public"."insp_algorithm_instance"."code" IS '算法编码（业务主键）';
COMMENT ON COLUMN "public"."insp_algorithm_instance"."type" IS '算法类型（CV、视觉）';
COMMENT ON COLUMN "public"."insp_algorithm_instance"."name" IS '算法名称';
COMMENT ON COLUMN "public"."insp_algorithm_instance"."des" IS '算法描述';
COMMENT ON COLUMN "public"."insp_algorithm_instance"."roi_draw_type" IS '算法ROI绘制图像类型';
COMMENT ON COLUMN "public"."insp_algorithm_instance"."image_size" IS '算法采集图片张数';
COMMENT ON COLUMN "public"."insp_algorithm_instance"."interval" IS '算法采集图片间隔时间（单位毫秒）';
COMMENT ON COLUMN "public"."insp_algorithm_instance"."roi" IS 'ROI坐标';
COMMENT ON COLUMN "public"."insp_algorithm_instance"."scene_id" IS '所属场景id';
COMMENT ON COLUMN "public"."insp_algorithm_instance"."model_instance_id" IS '所属模型实例id';
COMMENT ON COLUMN "public"."insp_algorithm_instance"."algorithm_id" IS '所属算法ID';
COMMENT ON COLUMN "public"."insp_algorithm_instance"."classification" IS '算法分类';
COMMENT ON COLUMN "public"."insp_algorithm_instance"."package_file" IS '算法打包文件';
COMMENT ON COLUMN "public"."insp_algorithm_instance"."is_preset" IS '是否是系统预置算法';
COMMENT ON COLUMN "public"."insp_algorithm_instance"."has_save_config" IS '是否保存过配置';
COMMENT ON COLUMN "public"."insp_algorithm_instance"."create_time" IS '创建时间';
COMMENT ON TABLE "public"."insp_algorithm_instance" IS '算法信息表';

-- ----------------------------
-- Table structure for insp_algorithm_instance_output
-- ----------------------------
DROP TABLE IF EXISTS "public"."insp_algorithm_instance_output";
CREATE TABLE "public"."insp_algorithm_instance_output" (
                                                           "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                           "algorithm_instance_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                           "model_instance_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                           "output_val_type" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
                                                           "is_bind_alarm" bool NOT NULL DEFAULT false,
                                                           "is_write_history" bool NOT NULL DEFAULT false,
                                                           "create_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                           "update_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP
)
;
COMMENT ON COLUMN "public"."insp_algorithm_instance_output"."algorithm_instance_id" IS '所属算法实例ID';
COMMENT ON COLUMN "public"."insp_algorithm_instance_output"."model_instance_id" IS '所属模型实例id';
COMMENT ON COLUMN "public"."insp_algorithm_instance_output"."output_val_type" IS '输出值类型（仅质量位、数值-质量位+数值、报警、数据模板）';
COMMENT ON COLUMN "public"."insp_algorithm_instance_output"."is_bind_alarm" IS '是否绑定报警配置';
COMMENT ON COLUMN "public"."insp_algorithm_instance_output"."is_write_history" IS '结果是否写入历史库';
COMMENT ON COLUMN "public"."insp_algorithm_instance_output"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."insp_algorithm_instance_output"."update_time" IS '更新时间';
COMMENT ON TABLE "public"."insp_algorithm_instance_output" IS '算法实例输出配置表';

-- ----------------------------
-- Table structure for insp_algorithm_param
-- ----------------------------
DROP TABLE IF EXISTS "public"."insp_algorithm_param";
CREATE TABLE "public"."insp_algorithm_param" (
                                                 "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                 "algorithm_id" varchar(32) COLLATE "pg_catalog"."default",
                                                 "name" varchar(255) COLLATE "pg_catalog"."default",
                                                 "type" varchar(255) COLLATE "pg_catalog"."default",
                                                 "key" varchar(255) COLLATE "pg_catalog"."default",
                                                 "des" varchar(255) COLLATE "pg_catalog"."default",
                                                 "val_range" varchar(255) COLLATE "pg_catalog"."default",
                                                 "group_key" varchar(255) COLLATE "pg_catalog"."default",
                                                 "sort_no" int4,
                                                 "is_val_unique" bool
)
;
COMMENT ON COLUMN "public"."insp_algorithm_param"."algorithm_id" IS '所属算法ID';
COMMENT ON COLUMN "public"."insp_algorithm_param"."name" IS '参数名称';
COMMENT ON COLUMN "public"."insp_algorithm_param"."type" IS '参数类型';
COMMENT ON COLUMN "public"."insp_algorithm_param"."key" IS '参数key';
COMMENT ON COLUMN "public"."insp_algorithm_param"."des" IS '参数描述';
COMMENT ON COLUMN "public"."insp_algorithm_param"."val_range" IS '值列表';
COMMENT ON COLUMN "public"."insp_algorithm_param"."group_key" IS '出参或者入参类型';
COMMENT ON COLUMN "public"."insp_algorithm_param"."sort_no" IS '排序参数';
COMMENT ON COLUMN "public"."insp_algorithm_param"."is_val_unique" IS '是否值全局唯一';
COMMENT ON TABLE "public"."insp_algorithm_param" IS '算法参数定义表';

-- ----------------------------
-- Table structure for insp_algorithm_param_instance
-- ----------------------------
DROP TABLE IF EXISTS "public"."insp_algorithm_param_instance";
CREATE TABLE "public"."insp_algorithm_param_instance" (
                                                          "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                          "algorithm_instance_id" varchar(32) COLLATE "pg_catalog"."default",
                                                          "value" text COLLATE "pg_catalog"."default",
                                                          "name" varchar(255) COLLATE "pg_catalog"."default",
                                                          "type" varchar(255) COLLATE "pg_catalog"."default",
                                                          "key" varchar(255) COLLATE "pg_catalog"."default",
                                                          "des" varchar(255) COLLATE "pg_catalog"."default",
                                                          "val_range" text COLLATE "pg_catalog"."default",
                                                          "group_key" varchar(255) COLLATE "pg_catalog"."default",
                                                          "scene_id" varchar(32) COLLATE "pg_catalog"."default",
                                                          "sort_no" int4,
                                                          "is_val_unique" bool
)
;
COMMENT ON COLUMN "public"."insp_algorithm_param_instance"."algorithm_instance_id" IS '所属算法实例ID';
COMMENT ON COLUMN "public"."insp_algorithm_param_instance"."value" IS '参数值';
COMMENT ON COLUMN "public"."insp_algorithm_param_instance"."name" IS '参数名称';
COMMENT ON COLUMN "public"."insp_algorithm_param_instance"."type" IS '参数类型';
COMMENT ON COLUMN "public"."insp_algorithm_param_instance"."key" IS '参数key';
COMMENT ON COLUMN "public"."insp_algorithm_param_instance"."des" IS '参数描述';
COMMENT ON COLUMN "public"."insp_algorithm_param_instance"."val_range" IS '值列表';
COMMENT ON COLUMN "public"."insp_algorithm_param_instance"."group_key" IS '出参或者入参类型';
COMMENT ON COLUMN "public"."insp_algorithm_param_instance"."scene_id" IS '所属场景iD';
COMMENT ON COLUMN "public"."insp_algorithm_param_instance"."sort_no" IS '排序参数';
COMMENT ON COLUMN "public"."insp_algorithm_param_instance"."is_val_unique" IS '是否值全局唯一';
COMMENT ON TABLE "public"."insp_algorithm_param_instance" IS '算法参数定义表';

-- ----------------------------
-- Table structure for insp_algorithm_sub
-- ----------------------------
DROP TABLE IF EXISTS "public"."insp_algorithm_sub";
CREATE TABLE "public"."insp_algorithm_sub" (
                                               "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                               "algorithm_instance_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                               "des" varchar(255) COLLATE "pg_catalog"."default",
                                               "classify" varchar(255) COLLATE "pg_catalog"."default",
                                               "strategy" varchar(255) COLLATE "pg_catalog"."default",
                                               "index" int4,
                                               "model_instance_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                               "scene_id" varchar(32) COLLATE "pg_catalog"."default",
                                               "available" bool NOT NULL DEFAULT false
)
;
COMMENT ON COLUMN "public"."insp_algorithm_sub"."algorithm_instance_id" IS '父级算法实例id';
COMMENT ON COLUMN "public"."insp_algorithm_sub"."des" IS '算法描述';
COMMENT ON COLUMN "public"."insp_algorithm_sub"."classify" IS '算法主次';
COMMENT ON COLUMN "public"."insp_algorithm_sub"."strategy" IS '光照策略（强光、弱光）';
COMMENT ON COLUMN "public"."insp_algorithm_sub"."index" IS '算法编码（业务主键）';
COMMENT ON COLUMN "public"."insp_algorithm_sub"."model_instance_id" IS '所属模型实例Id';
COMMENT ON COLUMN "public"."insp_algorithm_sub"."scene_id" IS '所属场景Id';
COMMENT ON COLUMN "public"."insp_algorithm_sub"."available" IS '是否启用';
COMMENT ON TABLE "public"."insp_algorithm_sub" IS '算法信息表';

-- ----------------------------
-- Table structure for insp_channel_node
-- ----------------------------
DROP TABLE IF EXISTS "public"."insp_channel_node";
CREATE TABLE "public"."insp_channel_node" (
                                              "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                              "project_id" varchar(32) COLLATE "pg_catalog"."default",
                                              "device_type" varchar(255) COLLATE "pg_catalog"."default",
                                              "address" varchar(255) COLLATE "pg_catalog"."default",
                                              "port" int4,
                                              "username" varchar(255) COLLATE "pg_catalog"."default",
                                              "password" varchar(255) COLLATE "pg_catalog"."default",
                                              "protocol" varchar(255) COLLATE "pg_catalog"."default",
                                              "url" varchar(255) COLLATE "pg_catalog"."default",
                                              "is_bind_wind" bool,
                                              "web_port" int4,
                                              "address_type" varchar(255) COLLATE "pg_catalog"."default",
                                              "mode" varchar(50) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."insp_channel_node"."project_id" IS '所属工程id';
COMMENT ON COLUMN "public"."insp_channel_node"."device_type" IS '通道类型';
COMMENT ON COLUMN "public"."insp_channel_node"."address" IS '通道连接地址';
COMMENT ON COLUMN "public"."insp_channel_node"."port" IS '后端连接端口';
COMMENT ON COLUMN "public"."insp_channel_node"."username" IS '用户名';
COMMENT ON COLUMN "public"."insp_channel_node"."password" IS '密码';
COMMENT ON COLUMN "public"."insp_channel_node"."protocol" IS '传输协议';
COMMENT ON COLUMN "public"."insp_channel_node"."is_bind_wind" IS '是否绑定展示宫格';
COMMENT ON COLUMN "public"."insp_channel_node"."web_port" IS 'web段连接端口';
COMMENT ON COLUMN "public"."insp_channel_node"."address_type" IS 'ONVIF/RTSP';
COMMENT ON COLUMN "public"."insp_channel_node"."mode" IS '手动、自动模式';
COMMENT ON TABLE "public"."insp_channel_node" IS '通道类型节点信息表';

-- ----------------------------
-- Table structure for insp_channel_zero_calibration
-- ----------------------------
DROP TABLE IF EXISTS "public"."insp_channel_zero_calibration";
CREATE TABLE "public"."insp_channel_zero_calibration" (
                                                          "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                          "project_id" varchar(32) COLLATE "pg_catalog"."default",
                                                          "channel_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                          "url" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
                                                          "pan" float4 NOT NULL,
                                                          "tilt" float4 NOT NULL,
                                                          "zoom" float4 NOT NULL,
                                                          "square" varchar(255) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "public"."insp_channel_zero_calibration"."project_id" IS '项目id';
COMMENT ON COLUMN "public"."insp_channel_zero_calibration"."channel_id" IS '通道id';
COMMENT ON COLUMN "public"."insp_channel_zero_calibration"."url" IS '校准图url';
COMMENT ON COLUMN "public"."insp_channel_zero_calibration"."pan" IS '水平参数';
COMMENT ON COLUMN "public"."insp_channel_zero_calibration"."tilt" IS '垂直参数';
COMMENT ON COLUMN "public"."insp_channel_zero_calibration"."zoom" IS '焦距倍数';
COMMENT ON COLUMN "public"."insp_channel_zero_calibration"."square" IS '截图边框四点坐标';

-- ----------------------------
-- Table structure for insp_classify_config
-- ----------------------------
DROP TABLE IF EXISTS "public"."insp_classify_config";
CREATE TABLE "public"."insp_classify_config" (
                                                 "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                 "name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
                                                 "create_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                                 "update_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                                 "items" varchar(4096) COLLATE "pg_catalog"."default",
                                                 "label" varchar(255) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "public"."insp_classify_config"."name" IS '分类库名称';
COMMENT ON COLUMN "public"."insp_classify_config"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."insp_classify_config"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."insp_classify_config"."items" IS '下属绑定的图片ID';
COMMENT ON COLUMN "public"."insp_classify_config"."label" IS '分类标签';
COMMENT ON TABLE "public"."insp_classify_config" IS '通道运行周期调度信息表';

-- ----------------------------
-- Table structure for insp_execute_record
-- ----------------------------
DROP TABLE IF EXISTS "public"."insp_execute_record";
CREATE TABLE "public"."insp_execute_record" (
                                                "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                "channel_id" varchar(32) COLLATE "pg_catalog"."default",
                                                "model_instance_id" varchar(32) COLLATE "pg_catalog"."default",
                                                "algorithm_id" varchar(32) COLLATE "pg_catalog"."default",
                                                "img_path" varchar(255) COLLATE "pg_catalog"."default",
                                                "is_alarm" bool NOT NULL DEFAULT false,
                                                "result" varchar(255) COLLATE "pg_catalog"."default",
                                                "status" int4,
                                                "failure_reason" varchar(255) COLLATE "pg_catalog"."default",
                                                "start_time" timestamp(6) NOT NULL,
                                                "end_time" timestamp(6),
                                                "model" varchar(255) COLLATE "pg_catalog"."default",
                                                "preset_name" varchar(255) COLLATE "pg_catalog"."default",
                                                "channel_name" varchar(255) COLLATE "pg_catalog"."default",
                                                "algorithm_name" varchar(255) COLLATE "pg_catalog"."default",
                                                "point_name" varchar(255) COLLATE "pg_catalog"."default",
                                                "algorithm_detail_id" varchar(32) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."insp_execute_record"."channel_id" IS '通道id';
COMMENT ON COLUMN "public"."insp_execute_record"."model_instance_id" IS '模型实例id';
COMMENT ON COLUMN "public"."insp_execute_record"."algorithm_id" IS '算法实例id';
COMMENT ON COLUMN "public"."insp_execute_record"."img_path" IS '图片地址';
COMMENT ON COLUMN "public"."insp_execute_record"."is_alarm" IS '是否报警';
COMMENT ON COLUMN "public"."insp_execute_record"."result" IS '结果';
COMMENT ON COLUMN "public"."insp_execute_record"."status" IS '执行状态';
COMMENT ON COLUMN "public"."insp_execute_record"."failure_reason" IS '失败原因';
COMMENT ON COLUMN "public"."insp_execute_record"."start_time" IS '开始时间';
COMMENT ON COLUMN "public"."insp_execute_record"."end_time" IS '结束时间';
COMMENT ON COLUMN "public"."insp_execute_record"."model" IS '触发模式';
COMMENT ON COLUMN "public"."insp_execute_record"."preset_name" IS '预置点';
COMMENT ON COLUMN "public"."insp_execute_record"."channel_name" IS '通道';
COMMENT ON COLUMN "public"."insp_execute_record"."algorithm_name" IS '算法名称';
COMMENT ON COLUMN "public"."insp_execute_record"."point_name" IS '点名';
COMMENT ON COLUMN "public"."insp_execute_record"."algorithm_detail_id" IS '算法arrayId';

-- ----------------------------
-- Table structure for insp_file_node
-- ----------------------------
DROP TABLE IF EXISTS "public"."insp_file_node";
CREATE TABLE "public"."insp_file_node" (
                                           "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                           "project_id" varchar(64) COLLATE "pg_catalog"."default",
                                           "contact" varchar(255) COLLATE "pg_catalog"."default",
                                           "phone" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."insp_file_node"."project_id" IS '所属工程id';
COMMENT ON COLUMN "public"."insp_file_node"."contact" IS '负责人（联系人）';
COMMENT ON COLUMN "public"."insp_file_node"."phone" IS '联系人电话';
COMMENT ON TABLE "public"."insp_file_node" IS '文件类型节点信息表';

-- ----------------------------
-- Table structure for insp_global_variable
-- ----------------------------
DROP TABLE IF EXISTS "public"."insp_global_variable";
CREATE TABLE "public"."insp_global_variable" (
                                                 "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                 "val_name" varchar(15) COLLATE "pg_catalog"."default",
                                                 "nick_name" varchar(15) COLLATE "pg_catalog"."default",
                                                 "description" varchar(255) COLLATE "pg_catalog"."default",
                                                 "output_val_temp" varchar(255) COLLATE "pg_catalog"."default",
                                                 "algorithm_instance_id" varchar(32) COLLATE "pg_catalog"."default",
                                                 "point_name" varchar(255) COLLATE "pg_catalog"."default",
                                                 "val_type" varchar(255) COLLATE "pg_catalog"."default",
                                                 "is_bind" bool,
                                                 "model_instance_id" varchar(32) COLLATE "pg_catalog"."default",
                                                 "create_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                 "update_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                 "algorithm_detail_id" varchar(32) COLLATE "pg_catalog"."default",
                                                 "report_to_ics" bool,
                                                 "namespace" varchar(255) COLLATE "pg_catalog"."default",
                                                 "tag" varchar(255) COLLATE "pg_catalog"."default",
                                                 "item" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."insp_global_variable"."val_name" IS '变量名';
COMMENT ON COLUMN "public"."insp_global_variable"."nick_name" IS '别名';
COMMENT ON COLUMN "public"."insp_global_variable"."description" IS '描述';
COMMENT ON COLUMN "public"."insp_global_variable"."output_val_temp" IS '输出值模板';
COMMENT ON COLUMN "public"."insp_global_variable"."algorithm_instance_id" IS '所属算法实例ID';
COMMENT ON COLUMN "public"."insp_global_variable"."point_name" IS '点名';
COMMENT ON COLUMN "public"."insp_global_variable"."val_type" IS '数据类型';
COMMENT ON COLUMN "public"."insp_global_variable"."is_bind" IS '是否绑定';
COMMENT ON COLUMN "public"."insp_global_variable"."model_instance_id" IS '模型id';
COMMENT ON COLUMN "public"."insp_global_variable"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."insp_global_variable"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."insp_global_variable"."report_to_ics" IS '是否上报至ICS';
COMMENT ON COLUMN "public"."insp_global_variable"."namespace" IS '点域名称';
COMMENT ON COLUMN "public"."insp_global_variable"."tag" IS '点名';
COMMENT ON COLUMN "public"."insp_global_variable"."item" IS '项名';
COMMENT ON TABLE "public"."insp_global_variable" IS '全局变量配置表';

-- ----------------------------
-- Table structure for insp_history_record
-- ----------------------------
DROP TABLE IF EXISTS "public"."insp_history_record";
CREATE TABLE "public"."insp_history_record" (
                                                "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                "channel_id" varchar(32) COLLATE "pg_catalog"."default",
                                                "model_instance_id" varchar(32) COLLATE "pg_catalog"."default",
                                                "algorithm_id" varchar(32) COLLATE "pg_catalog"."default",
                                                "img_path" varchar(255) COLLATE "pg_catalog"."default",
                                                "is_alarm" bool NOT NULL DEFAULT false,
                                                "result" varchar(255) COLLATE "pg_catalog"."default",
                                                "start_time" timestamp(6) NOT NULL,
                                                "end_time" timestamp(6),
                                                "model" varchar(255) COLLATE "pg_catalog"."default",
                                                "preset_name" varchar(255) COLLATE "pg_catalog"."default",
                                                "channel_name" varchar(255) COLLATE "pg_catalog"."default",
                                                "status" int4,
                                                "algorithm_name" varchar(255) COLLATE "pg_catalog"."default",
                                                "point_name" varchar(255) COLLATE "pg_catalog"."default",
                                                "algorithm_detail_id" varchar(32) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."insp_history_record"."channel_id" IS '通道id';
COMMENT ON COLUMN "public"."insp_history_record"."model_instance_id" IS '模型实例id';
COMMENT ON COLUMN "public"."insp_history_record"."algorithm_id" IS '算法实例id';
COMMENT ON COLUMN "public"."insp_history_record"."img_path" IS '图片地址';
COMMENT ON COLUMN "public"."insp_history_record"."is_alarm" IS '是否报警';
COMMENT ON COLUMN "public"."insp_history_record"."result" IS '结果';
COMMENT ON COLUMN "public"."insp_history_record"."start_time" IS '开始时间';
COMMENT ON COLUMN "public"."insp_history_record"."end_time" IS '结束时间';
COMMENT ON COLUMN "public"."insp_history_record"."model" IS '触发模式';
COMMENT ON COLUMN "public"."insp_history_record"."preset_name" IS '预置点';
COMMENT ON COLUMN "public"."insp_history_record"."channel_name" IS '通道';
COMMENT ON COLUMN "public"."insp_history_record"."status" IS '执行状态';
COMMENT ON COLUMN "public"."insp_history_record"."algorithm_name" IS '算法名称';
COMMENT ON COLUMN "public"."insp_history_record"."point_name" IS '点名';
COMMENT ON COLUMN "public"."insp_history_record"."algorithm_detail_id" IS '算法arrayId';

-- ----------------------------
-- Table structure for insp_log_operate
-- ----------------------------
DROP TABLE IF EXISTS "public"."insp_log_operate";
CREATE TABLE "public"."insp_log_operate" (
                                             "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                             "time" timestamp(6) NOT NULL,
                                             "user_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                             "operate_type" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                             "business_type" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                             "message" varchar(255) COLLATE "pg_catalog"."default",
                                             "operate_ids" varchar(255) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "public"."insp_log_operate"."time" IS '时间';
COMMENT ON COLUMN "public"."insp_log_operate"."user_id" IS '操作人用户ID';
COMMENT ON COLUMN "public"."insp_log_operate"."operate_type" IS '操作类型';
COMMENT ON COLUMN "public"."insp_log_operate"."business_type" IS '业务划分';
COMMENT ON COLUMN "public"."insp_log_operate"."message" IS '信息';
COMMENT ON COLUMN "public"."insp_log_operate"."operate_ids" IS '被操作相关数据的主键';

-- ----------------------------
-- Table structure for insp_model_instance
-- ----------------------------
DROP TABLE IF EXISTS "public"."insp_model_instance";
CREATE TABLE "public"."insp_model_instance" (
                                                "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                "project_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                "available" bool NOT NULL DEFAULT true,
                                                "channel_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                "pan" float8,
                                                "tilt" float8,
                                                "zoom" float8,
                                                "pre_execute_delay" int2 NOT NULL DEFAULT 3,
                                                "post_execute_delay" int2 NOT NULL DEFAULT 3,
                                                "valid_times" int4 DEFAULT 1,
                                                "is_running" bool NOT NULL DEFAULT false,
                                                "correct_param" varchar(255) COLLATE "pg_catalog"."default",
                                                "correct_available" bool NOT NULL DEFAULT false,
                                                "failure_strategy" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
                                                "is_schedule" bool NOT NULL DEFAULT false,
                                                "notice" varchar(500) COLLATE "pg_catalog"."default",
                                                "match_enable" bool NOT NULL DEFAULT false,
                                                "common_info" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."insp_model_instance"."id" IS '主键';
COMMENT ON COLUMN "public"."insp_model_instance"."project_id" IS '项目id';
COMMENT ON COLUMN "public"."insp_model_instance"."available" IS '是否可用';
COMMENT ON COLUMN "public"."insp_model_instance"."channel_id" IS '所属通道id';
COMMENT ON COLUMN "public"."insp_model_instance"."pan" IS '水平参数（角度数*10）';
COMMENT ON COLUMN "public"."insp_model_instance"."tilt" IS '垂直参数（角度数*10）';
COMMENT ON COLUMN "public"."insp_model_instance"."zoom" IS '焦距倍数';
COMMENT ON COLUMN "public"."insp_model_instance"."pre_execute_delay" IS '前置延时（秒）';
COMMENT ON COLUMN "public"."insp_model_instance"."post_execute_delay" IS '后置延时（秒）';
COMMENT ON COLUMN "public"."insp_model_instance"."valid_times" IS '验证次数';
COMMENT ON COLUMN "public"."insp_model_instance"."is_running" IS '是否运行中';
COMMENT ON COLUMN "public"."insp_model_instance"."correct_param" IS '透视变换矫正参数';
COMMENT ON COLUMN "public"."insp_model_instance"."correct_available" IS '是否开启透视变换';
COMMENT ON COLUMN "public"."insp_model_instance"."failure_strategy" IS '失败策略';
COMMENT ON COLUMN "public"."insp_model_instance"."is_schedule" IS '是否PTZ截图';
COMMENT ON COLUMN "public"."insp_model_instance"."notice" IS '0-语音、1-钉钉、2-微信';
COMMENT ON COLUMN "public"."insp_model_instance"."match_enable" IS '是否进行模板匹配';

-- ----------------------------
-- Table structure for insp_notice_config
-- ----------------------------
DROP TABLE IF EXISTS "public"."insp_notice_config";
CREATE TABLE "public"."insp_notice_config" (
                                               "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                               "type" int4,
                                               "address" varchar(255) COLLATE "pg_catalog"."default",
                                               "nick_name" varchar(255) COLLATE "pg_catalog"."default",
                                               "is_enable" bool
)
;

-- ----------------------------
-- Table structure for insp_ntp_manage_info
-- ----------------------------
DROP TABLE IF EXISTS "public"."insp_ntp_manage_info";
CREATE TABLE "public"."insp_ntp_manage_info" (
                                                 "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                 "available" bool DEFAULT false,
                                                 "ntp_server_host" varchar(200) COLLATE "pg_catalog"."default",
                                                 "ntp_server_port" int4 DEFAULT 123,
                                                 "interval" int4 DEFAULT 1
)
;
COMMENT ON COLUMN "public"."insp_ntp_manage_info"."available" IS '是否可用';
COMMENT ON COLUMN "public"."insp_ntp_manage_info"."ntp_server_host" IS 'NTP服务主机名';
COMMENT ON COLUMN "public"."insp_ntp_manage_info"."ntp_server_port" IS 'NTP服务端口';
COMMENT ON COLUMN "public"."insp_ntp_manage_info"."interval" IS '校准间隔';

-- ----------------------------
-- Table structure for insp_output_alarm_conf
-- ----------------------------
DROP TABLE IF EXISTS "public"."insp_output_alarm_conf";
CREATE TABLE "public"."insp_output_alarm_conf" (
                                                   "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                   "algorithm_instance_id" varchar(32) COLLATE "pg_catalog"."default",
                                                   "alarm_info" varchar(500) COLLATE "pg_catalog"."default",
                                                   "alarm_type" varchar(255) COLLATE "pg_catalog"."default",
                                                   "is_override_alarm" bool,
                                                   "algorithm_detail_id" varchar(32) COLLATE "pg_catalog"."default",
                                                   "create_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                                   "update_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP
)
;
COMMENT ON COLUMN "public"."insp_output_alarm_conf"."algorithm_instance_id" IS '所属算法实例ID';
COMMENT ON COLUMN "public"."insp_output_alarm_conf"."alarm_info" IS '报警信息';
COMMENT ON COLUMN "public"."insp_output_alarm_conf"."alarm_type" IS '报警类型';
COMMENT ON COLUMN "public"."insp_output_alarm_conf"."is_override_alarm" IS '是否覆盖原有报警';
COMMENT ON COLUMN "public"."insp_output_alarm_conf"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."insp_output_alarm_conf"."update_time" IS '更新时间';
COMMENT ON TABLE "public"."insp_output_alarm_conf" IS '算法实例输出报警配置';

-- ----------------------------
-- Table structure for insp_project
-- ----------------------------
DROP TABLE IF EXISTS "public"."insp_project";
CREATE TABLE "public"."insp_project" (
                                         "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                         "name" varchar(255) COLLATE "pg_catalog"."default",
                                         "des" varchar(255) COLLATE "pg_catalog"."default",
                                         "deploy_state" varchar(20) COLLATE "pg_catalog"."default",
                                         "online_state" varchar(20) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 1
)
;
COMMENT ON COLUMN "public"."insp_project"."name" IS '工程名称';
COMMENT ON COLUMN "public"."insp_project"."des" IS '工程描述';
COMMENT ON COLUMN "public"."insp_project"."deploy_state" IS '部署状态（部署、停止）';
COMMENT ON COLUMN "public"."insp_project"."online_state" IS '在线状态（在线、离线）';
COMMENT ON TABLE "public"."insp_project" IS '算法信息表';

-- ----------------------------
-- Table structure for insp_project_tree_node
-- ----------------------------
DROP TABLE IF EXISTS "public"."insp_project_tree_node";
CREATE TABLE "public"."insp_project_tree_node" (
                                                   "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                   "label" varchar(255) COLLATE "pg_catalog"."default",
                                                   "type" varchar(255) COLLATE "pg_catalog"."default",
                                                   "parent_id" varchar(32) COLLATE "pg_catalog"."default",
                                                   "des" varchar(255) COLLATE "pg_catalog"."default",
                                                   "sort_no" int4,
                                                   "project_id" varchar(32) COLLATE "pg_catalog"."default",
                                                   "status" varchar(50) COLLATE "pg_catalog"."default",
                                                   "url" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."insp_project_tree_node"."label" IS '节点名称';
COMMENT ON COLUMN "public"."insp_project_tree_node"."type" IS '通道类型';
COMMENT ON COLUMN "public"."insp_project_tree_node"."parent_id" IS '父级id';
COMMENT ON COLUMN "public"."insp_project_tree_node"."des" IS '节点描述';
COMMENT ON COLUMN "public"."insp_project_tree_node"."sort_no" IS '排序字段';
COMMENT ON COLUMN "public"."insp_project_tree_node"."project_id" IS '所属工程id';
COMMENT ON COLUMN "public"."insp_project_tree_node"."status" IS '节点状态';
COMMENT ON COLUMN "public"."insp_project_tree_node"."url" IS '跳转地址';
COMMENT ON TABLE "public"."insp_project_tree_node" IS '工程树节点信息表';

-- ----------------------------
-- Table structure for insp_record_scene_time
-- ----------------------------
DROP TABLE IF EXISTS "public"."insp_record_scene_time";
CREATE TABLE "public"."insp_record_scene_time" (
                                                   "id" varchar COLLATE "pg_catalog"."default" NOT NULL,
                                                   "start_time" timestamp(6) NOT NULL,
                                                   "channel_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                   "model_instance_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                   "day" int4 NOT NULL DEFAULT 3
)
;
COMMENT ON COLUMN "public"."insp_record_scene_time"."start_time" IS '记录截止时间';
COMMENT ON COLUMN "public"."insp_record_scene_time"."channel_id" IS '通道id';
COMMENT ON COLUMN "public"."insp_record_scene_time"."model_instance_id" IS '模型实例id';
COMMENT ON COLUMN "public"."insp_record_scene_time"."day" IS '截至几天前';

-- ----------------------------
-- Table structure for insp_scene_custom_pic
-- ----------------------------
DROP TABLE IF EXISTS "public"."insp_scene_custom_pic";
CREATE TABLE "public"."insp_scene_custom_pic" (
                                                  "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                  "model_instance_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                  "pics" varchar(2000) COLLATE "pg_catalog"."default" NOT NULL,
                                                  "type" varchar(255) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "public"."insp_scene_custom_pic"."model_instance_id" IS '模型实例id';
COMMENT ON COLUMN "public"."insp_scene_custom_pic"."pics" IS '上传自定义场景路径';
COMMENT ON COLUMN "public"."insp_scene_custom_pic"."type" IS '上传来源(手动上传、自动截取)';

-- ----------------------------
-- Table structure for insp_scene_definition
-- ----------------------------
DROP TABLE IF EXISTS "public"."insp_scene_definition";
CREATE TABLE "public"."insp_scene_definition" (
                                                  "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                  "project_id" varchar(32) COLLATE "pg_catalog"."default",
                                                  "model_instance_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                  "tmp_pic" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
                                                  "bench_pic" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
                                                  "name" varchar(255) COLLATE "pg_catalog"."default",
                                                  "tmp_square" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."insp_scene_definition"."project_id" IS '项目id';
COMMENT ON COLUMN "public"."insp_scene_definition"."model_instance_id" IS '模型实例id';
COMMENT ON COLUMN "public"."insp_scene_definition"."tmp_pic" IS '模板图';
COMMENT ON COLUMN "public"."insp_scene_definition"."bench_pic" IS '基准图';
COMMENT ON COLUMN "public"."insp_scene_definition"."name" IS '场景名';
COMMENT ON COLUMN "public"."insp_scene_definition"."tmp_square" IS '模板小图坐标';

-- ----------------------------
-- Table structure for insp_schedule_period
-- ----------------------------
DROP TABLE IF EXISTS "public"."insp_schedule_period";
CREATE TABLE "public"."insp_schedule_period" (
                                                 "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                 "task_id" varchar(32) COLLATE "pg_catalog"."default",
                                                 "week" int4,
                                                 "time_slot" varchar(200) COLLATE "pg_catalog"."default",
                                                 "from_task_id" varchar(32) COLLATE "pg_catalog"."default",
                                                 "interval" int4,
                                                 "interval_unit" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."insp_schedule_period"."task_id" IS '巡检任务id';
COMMENT ON COLUMN "public"."insp_schedule_period"."week" IS '所属星期几（1-7:SUN-SAT）';
COMMENT ON COLUMN "public"."insp_schedule_period"."time_slot" IS '时间段集合';
COMMENT ON COLUMN "public"."insp_schedule_period"."from_task_id" IS '复制来源任务id';
COMMENT ON COLUMN "public"."insp_schedule_period"."interval" IS '调度间隔';
COMMENT ON COLUMN "public"."insp_schedule_period"."interval_unit" IS '调度间隔时间单位';
COMMENT ON TABLE "public"."insp_schedule_period" IS '通道运行周期调度信息表';

-- ----------------------------
-- Table structure for insp_schedule_task
-- ----------------------------
DROP TABLE IF EXISTS "public"."insp_schedule_task";
CREATE TABLE "public"."insp_schedule_task" (
                                               "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                               "name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
                                               "sort_no" int4 NOT NULL,
                                               "type" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                               "trigger_enable" bool NOT NULL DEFAULT false,
                                               "namespace" varchar(255) COLLATE "pg_catalog"."default",
                                               "tag" varchar(255) COLLATE "pg_catalog"."default",
                                               "item" varchar(255) COLLATE "pg_catalog"."default",
                                               "trigger_value" bool
)
;
COMMENT ON COLUMN "public"."insp_schedule_task"."name" IS '任务名称';
COMMENT ON COLUMN "public"."insp_schedule_task"."sort_no" IS '排序字段';
COMMENT ON COLUMN "public"."insp_schedule_task"."type" IS '单、双通道';
COMMENT ON COLUMN "public"."insp_schedule_task"."trigger_enable" IS '条件触发开关';
COMMENT ON COLUMN "public"."insp_schedule_task"."namespace" IS '点域名称';
COMMENT ON COLUMN "public"."insp_schedule_task"."tag" IS '点名';
COMMENT ON COLUMN "public"."insp_schedule_task"."item" IS '项名';
COMMENT ON COLUMN "public"."insp_schedule_task"."trigger_value" IS '触发点项值';
COMMENT ON TABLE "public"."insp_schedule_task" IS '通道运行周期调度信息表';

-- ----------------------------
-- Table structure for insp_schedule_task_node
-- ----------------------------
DROP TABLE IF EXISTS "public"."insp_schedule_task_node";
CREATE TABLE "public"."insp_schedule_task_node" (
                                                    "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                    "task_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                    "model_instance_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                    "sort_no" int4 NOT NULL,
                                                    "channel_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                    "status" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."insp_schedule_task_node"."task_id" IS '所属任务ID';
COMMENT ON COLUMN "public"."insp_schedule_task_node"."model_instance_id" IS '关联模型实例ID';
COMMENT ON COLUMN "public"."insp_schedule_task_node"."sort_no" IS '排序字段';
COMMENT ON COLUMN "public"."insp_schedule_task_node"."channel_id" IS '通道ID';
COMMENT ON COLUMN "public"."insp_schedule_task_node"."status" IS '运行状态';
COMMENT ON TABLE "public"."insp_schedule_task_node" IS '通道运行周期调度信息表';

-- ----------------------------
-- Table structure for insp_sub_algorithm_param
-- ----------------------------
DROP TABLE IF EXISTS "public"."insp_sub_algorithm_param";
CREATE TABLE "public"."insp_sub_algorithm_param" (
                                                     "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                     "sub_algorithm_id" varchar(32) COLLATE "pg_catalog"."default",
                                                     "value" varchar(32) COLLATE "pg_catalog"."default",
                                                     "name" varchar(255) COLLATE "pg_catalog"."default",
                                                     "type" varchar(255) COLLATE "pg_catalog"."default",
                                                     "key" varchar(255) COLLATE "pg_catalog"."default",
                                                     "des" varchar(255) COLLATE "pg_catalog"."default",
                                                     "val_range" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."insp_sub_algorithm_param"."sub_algorithm_id" IS '所属子算法ID';
COMMENT ON COLUMN "public"."insp_sub_algorithm_param"."value" IS '参数值';
COMMENT ON COLUMN "public"."insp_sub_algorithm_param"."name" IS '参数名称';
COMMENT ON COLUMN "public"."insp_sub_algorithm_param"."type" IS '参数类型';
COMMENT ON COLUMN "public"."insp_sub_algorithm_param"."key" IS '参数key';
COMMENT ON COLUMN "public"."insp_sub_algorithm_param"."des" IS '参数描述';
COMMENT ON COLUMN "public"."insp_sub_algorithm_param"."val_range" IS 'select下拉值列表';
COMMENT ON TABLE "public"."insp_sub_algorithm_param" IS '算法参数定义表';

-- ----------------------------
-- Table structure for insp_system_user
-- ----------------------------
DROP TABLE IF EXISTS "public"."insp_system_user";
CREATE TABLE "public"."insp_system_user" (
                                             "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                             "name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
                                             "username" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
                                             "password" varchar(255) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "public"."insp_system_user"."name" IS '用户名称';
COMMENT ON COLUMN "public"."insp_system_user"."username" IS '账号名';
COMMENT ON COLUMN "public"."insp_system_user"."password" IS '密码';
COMMENT ON TABLE "public"."insp_system_user" IS '算法信息表';

-- ----------------------------
-- Table structure for insp_third_app
-- ----------------------------
DROP TABLE IF EXISTS "public"."insp_third_app";
CREATE TABLE "public"."insp_third_app" (
                                           "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                           "name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
                                           "app_code" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                           "license_code" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
                                           "available" bool NOT NULL DEFAULT true,
                                           "update_time" timestamp(6) NOT NULL,
                                           "create_time" timestamp(6) NOT NULL
)
;
COMMENT ON COLUMN "public"."insp_third_app"."name" IS '应用名称';
COMMENT ON COLUMN "public"."insp_third_app"."app_code" IS '应用码（业务主键）';
COMMENT ON COLUMN "public"."insp_third_app"."license_code" IS '授权码';
COMMENT ON COLUMN "public"."insp_third_app"."available" IS '是否可用';
COMMENT ON COLUMN "public"."insp_third_app"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."insp_third_app"."create_time" IS '创建时间';

-- ----------------------------
-- Table structure for insp_third_interface
-- ----------------------------
DROP TABLE IF EXISTS "public"."insp_third_interface";
CREATE TABLE "public"."insp_third_interface" (
                                                 "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                 "name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
                                                 "url" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
                                                 "http_method" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                 "sort_no" int4 NOT NULL,
                                                 "available" bool NOT NULL DEFAULT true,
                                                 "classification" varchar(255) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "public"."insp_third_interface"."name" IS '接口名称';
COMMENT ON COLUMN "public"."insp_third_interface"."url" IS '接口地址';
COMMENT ON COLUMN "public"."insp_third_interface"."http_method" IS '接口请求方式（GET、POST...）';
COMMENT ON COLUMN "public"."insp_third_interface"."sort_no" IS '排序字段';
COMMENT ON COLUMN "public"."insp_third_interface"."available" IS '是否可用';
COMMENT ON COLUMN "public"."insp_third_interface"."classification" IS '接口分类';
COMMENT ON TABLE "public"."insp_third_interface" IS '第三方接口';

-- ----------------------------
-- Table structure for insp_third_permission
-- ----------------------------
DROP TABLE IF EXISTS "public"."insp_third_permission";
CREATE TABLE "public"."insp_third_permission" (
                                                  "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                  "app_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                  "interface_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "public"."insp_third_permission"."app_id" IS '应用ID';
COMMENT ON COLUMN "public"."insp_third_permission"."interface_id" IS '接口ID';
COMMENT ON TABLE "public"."insp_third_permission" IS '第三方应用接口权限';

-- ----------------------------
-- Table structure for insp_variable_relation
-- ----------------------------
DROP TABLE IF EXISTS "public"."insp_variable_relation";
CREATE TABLE "public"."insp_variable_relation" (
                                                   "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                   "algorithm_instance_id" varchar(32) COLLATE "pg_catalog"."default",
                                                   "point_name" varchar(255) COLLATE "pg_catalog"."default",
                                                   "val_type" varchar(255) COLLATE "pg_catalog"."default",
                                                   "is_bind" bool,
                                                   "model_instance_id" varchar(32) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."insp_variable_relation"."algorithm_instance_id" IS '所属算法实例ID';
COMMENT ON COLUMN "public"."insp_variable_relation"."point_name" IS '点名';
COMMENT ON COLUMN "public"."insp_variable_relation"."val_type" IS '数据类型';
COMMENT ON COLUMN "public"."insp_variable_relation"."is_bind" IS '是否绑定';
COMMENT ON COLUMN "public"."insp_variable_relation"."model_instance_id" IS '模型id';

-- ----------------------------
-- Table structure for power_execute_config
-- ----------------------------
DROP TABLE IF EXISTS "public"."power_execute_config";
CREATE TABLE "public"."power_execute_config" (
                                                 "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                 "model_instance_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
                                                 "roi" varchar(255) COLLATE "pg_catalog"."default",
                                                 "thickness_topic" varchar(255) COLLATE "pg_catalog"."default",
                                                 "range" varchar(255) COLLATE "pg_catalog"."default",
                                                 "temperature_topic" varchar(255) COLLATE "pg_catalog"."default",
                                                 "state_topic" varchar(255) COLLATE "pg_catalog"."default",
                                                 "all_topic" varchar(255) COLLATE "pg_catalog"."default",
                                                 "thickness_threshold" varchar(255) COLLATE "pg_catalog"."default",
                                                 "sort_no" int4
)
;
COMMENT ON COLUMN "public"."power_execute_config"."model_instance_id" IS '模型实例id';
COMMENT ON COLUMN "public"."power_execute_config"."roi" IS 'ROI';
COMMENT ON COLUMN "public"."power_execute_config"."thickness_topic" IS 'thicknessTopic';
COMMENT ON COLUMN "public"."power_execute_config"."range" IS 'range';
COMMENT ON COLUMN "public"."power_execute_config"."temperature_topic" IS 'temperatureTopic';
COMMENT ON COLUMN "public"."power_execute_config"."state_topic" IS 'stateTopic';
COMMENT ON COLUMN "public"."power_execute_config"."all_topic" IS 'allTopic';
COMMENT ON COLUMN "public"."power_execute_config"."thickness_threshold" IS '业务判断上下限';
COMMENT ON COLUMN "public"."power_execute_config"."sort_no" IS '排序字段';

-- ----------------------------
-- Primary Key structure for table insp_alarm_record
-- ----------------------------
ALTER TABLE "public"."insp_alarm_record" ADD CONSTRAINT "insp_execute_record_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table insp_algorithm
-- ----------------------------
ALTER TABLE "public"."insp_algorithm" ADD CONSTRAINT "insp_algorithm_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table insp_algorithm_instance
-- ----------------------------
ALTER TABLE "public"."insp_algorithm_instance" ADD CONSTRAINT "insp_algorithm_copy1_pkey1" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table insp_algorithm_instance_output
-- ----------------------------
ALTER TABLE "public"."insp_algorithm_instance_output" ADD CONSTRAINT "insp_global_variable_copy1_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table insp_algorithm_param
-- ----------------------------
ALTER TABLE "public"."insp_algorithm_param" ADD CONSTRAINT "insp_algorithm_param_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table insp_algorithm_param_instance
-- ----------------------------
ALTER TABLE "public"."insp_algorithm_param_instance" ADD CONSTRAINT "insp_algorithm_param_instance_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table insp_algorithm_sub
-- ----------------------------
ALTER TABLE "public"."insp_algorithm_sub" ADD CONSTRAINT "insp_algorithm_copy1_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table insp_channel_node
-- ----------------------------
ALTER TABLE "public"."insp_channel_node" ADD CONSTRAINT "insp_channel_node_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table insp_channel_zero_calibration
-- ----------------------------
ALTER TABLE "public"."insp_channel_zero_calibration" ADD CONSTRAINT "insp_channel_zero_calibration_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Uniques structure for table insp_classify_config
-- ----------------------------
ALTER TABLE "public"."insp_classify_config" ADD CONSTRAINT "insp_classify_config_name_key" UNIQUE ("name");
ALTER TABLE "public"."insp_classify_config" ADD CONSTRAINT "insp_classify_config_label_key" UNIQUE ("label");

-- ----------------------------
-- Primary Key structure for table insp_classify_config
-- ----------------------------
ALTER TABLE "public"."insp_classify_config" ADD CONSTRAINT "insp_schedule_task_copy1_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table insp_execute_record
-- ----------------------------
ALTER TABLE "public"."insp_execute_record" ADD CONSTRAINT "insp_execute_record_pkey1" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table insp_file_node
-- ----------------------------
ALTER TABLE "public"."insp_file_node" ADD CONSTRAINT "insp_file_node_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table insp_global_variable
-- ----------------------------
ALTER TABLE "public"."insp_global_variable" ADD CONSTRAINT "insp_algorithm_instance_copy1_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table insp_history_record
-- ----------------------------
ALTER TABLE "public"."insp_history_record" ADD CONSTRAINT "insp_history_record_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table insp_log_operate
-- ----------------------------
ALTER TABLE "public"."insp_log_operate" ADD CONSTRAINT "insp_alarm_record_copy1_pkey1" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table insp_model_instance
-- ----------------------------
ALTER TABLE "public"."insp_model_instance" ADD CONSTRAINT "insp_model_instance_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table insp_notice_config
-- ----------------------------
ALTER TABLE "public"."insp_notice_config" ADD CONSTRAINT "insp_notice_config_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table insp_ntp_manage_info
-- ----------------------------
ALTER TABLE "public"."insp_ntp_manage_info" ADD CONSTRAINT "insp_alarm_record_copy1_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table insp_output_alarm_conf
-- ----------------------------
ALTER TABLE "public"."insp_output_alarm_conf" ADD CONSTRAINT "insp_global_variable_copy1_pkey1" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table insp_project
-- ----------------------------
ALTER TABLE "public"."insp_project" ADD CONSTRAINT "insp_algorithm_copy1_pkey2" PRIMARY KEY ("id");

-- ----------------------------
-- Uniques structure for table insp_project_tree_node
-- ----------------------------
ALTER TABLE "public"."insp_project_tree_node" ADD CONSTRAINT "label" UNIQUE ("label");

-- ----------------------------
-- Primary Key structure for table insp_project_tree_node
-- ----------------------------
ALTER TABLE "public"."insp_project_tree_node" ADD CONSTRAINT "insp_project_tree_node_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table insp_record_scene_time
-- ----------------------------
ALTER TABLE "public"."insp_record_scene_time" ADD CONSTRAINT "insp_record_scene_time_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table insp_scene_custom_pic
-- ----------------------------
ALTER TABLE "public"."insp_scene_custom_pic" ADD CONSTRAINT "insp_scene_custom_pic_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table insp_scene_definition
-- ----------------------------
ALTER TABLE "public"."insp_scene_definition" ADD CONSTRAINT "insp_scene definition_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table insp_schedule_period
-- ----------------------------
ALTER TABLE "public"."insp_schedule_period" ADD CONSTRAINT "insp_schedule_period_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table insp_schedule_task
-- ----------------------------
ALTER TABLE "public"."insp_schedule_task" ADD CONSTRAINT "insp_schedule_task_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table insp_schedule_task_node
-- ----------------------------
ALTER TABLE "public"."insp_schedule_task_node" ADD CONSTRAINT "insp_schedule_task_rela_copy1_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table insp_sub_algorithm_param
-- ----------------------------
ALTER TABLE "public"."insp_sub_algorithm_param" ADD CONSTRAINT "insp_algorithm_param_instance_copy1_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table insp_system_user
-- ----------------------------
ALTER TABLE "public"."insp_system_user" ADD CONSTRAINT "insp_algorithm_copy1_pkey3" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table insp_third_app
-- ----------------------------
ALTER TABLE "public"."insp_third_app" ADD CONSTRAINT "insp_third_app_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table insp_variable_relation
-- ----------------------------
ALTER TABLE "public"."insp_variable_relation" ADD CONSTRAINT "insp_variable_relation_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table power_execute_config
-- ----------------------------
ALTER TABLE "public"."power_execute_config" ADD CONSTRAINT "insp_alarm_record_copy1_pkey2" PRIMARY KEY ("id");


-- ----------------------------
-- 数据初始化
-- ----------------------------
INSERT INTO "public"."insp_algorithm" ("id", "code", "type", "name", "des", "roi_draw_type", "image_size", "interval", "classification", "package_file", "is_preset") VALUES ('5af6d94d946c4a32a3e2a7fdf9c30ffe', 'fire_temp_thick', 'openCV', '垃圾燃烧过程状态识别', '垃圾燃烧过程状态识别算法.V1', 'square', 1, 200, 'OTHERS', 'fire_temp_thick.zip', 't');

INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('cad327e0527a8f982082478712cb3b65', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '主火线面积', 'string', 'area_of_fireline_main_stage_5', '主火线在5阶段中的面积', NULL, 'firelineinStage5Info', 3, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('e01a49f80624edca22d38f09de66f334', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '主副火线间面积与ROI1的面积占比', 'string', 'ratio_between_fireline_main2aux_stages_roi1', '阶段主副火线间面积与ROI1的面积占比', NULL, 'firelinebetweenStageInfo', 2, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('c91cfda4a00d19d7da89b2457b7ccd9a', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '主火线最低位', 'string', 'lowest_fireline_main_stage_4', '4阶段主火线最低位', NULL, 'firelineinStage4Info', 1, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('4b2ea4d95511c9def7e1fb00b963cf18', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '主火线与ROI1区域内的平均温度', 'string', 'mean_temp_of_fireline_main_stage_4', '4阶段主火线与ROI1区域内的平均温度', NULL, 'firelineinStage4Info', 5, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('672797d12a61e4003473598b8c691ba0', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '主副火线间的最高温度', 'string', 'area_between_fireline_main2aux_stages_temp_max', '阶段主副火线间的最高温度', NULL, 'firelinebetweenStageInfo', 5, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('61dcbe000f4b3a2db0b7dcc482f630b3', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '主副火线间面积与ROI2的面积占比', 'string', 'ratio_between_fireline_main2aux_stages_roi2', '阶段主副火线间面积与ROI2的面积占比', NULL, 'firelinebetweenStageInfo', 3, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('71d572271136a9f93b27511919123bb4', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '主副火线间面积', 'string', 'area_between_fireline_main2aux_stages', '阶段主副火线间面积', NULL, 'firelinebetweenStageInfo', 1, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('2e82a785f2f9d18ac75a103b2ef58d22', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '蒙灰', 'string', 'isAsh', '蒙灰', NULL, 'instanceInfo', 3, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('106f5ad9af061c0c498dd46be2edb4c1', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '主火线面积', 'string', 'area_of_fireline_main_stage_4', '主火线在4阶段中的面积', NULL, 'firelineinStage4Info', 3, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('32e6dd5e2ee61f65db29eaa9b5a61078', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '主火线最低位', 'string', 'lowest_fireline_main_stage_5', '5阶段主火线最低位', NULL, 'firelineinStage5Info', 1, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('bcdc236c159cc9e60920c9118b9290ad', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '主火线区域面积与ROI1的面积占比', 'string', 'ratio_of_fireline_main_stage_4', '4阶段主火线区域面积与ROI1的面积占比', NULL, 'firelineinStage4Info', 4, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('23185cf1065b232233ca90dd82a51a76', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '辅火线最低位', 'string', 'lowest_fireline_aux_stage_4', '4阶段辅火线最低位', NULL, 'firelineinStage4Info', 2, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('074dcf1802bee48c4ac22ea52f371891', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '辅火线最低位', 'string', 'lowest_fireline_aux_stage_5', '5阶段辅火线最低位', NULL, 'firelineinStage5Info', 2, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('c720c2828d60196f4af358fed4d7bdb2', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '主火线最低位', 'string', 'lowest_fireline_main', '主火线最低位', NULL, 'firelineInfo', 3, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('9953158a9678540da73f9af38e486500', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '最低火线位置', 'string', 'lowest_fireline_stage_4', '4阶段最低火线位置', NULL, 'firelineinStage4Info', 7, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('cb715175c2bf290d12f4b863a5aee186', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '主火线与ROI1区域内的最大温度', 'string', 'max_temp_of_fireline_main_stage_4', '4阶段主火线与ROI1区域内的最大温度', NULL, 'firelineinStage4Info', 6, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('935093e53de28f3c2879e58e7a3d2639', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '主火线与ROI2区域内的平均温度', 'string', 'mean_temp_of_fireline_main_stage_5', '5阶段主火线与ROI2区域内的平均温度', NULL, 'firelineinStage5Info', 5, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('12ec8563c21fcb9213b88c1a8c2f03c7', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '启停开关topic', 'string', 'switchEnableTopic', '启停开关topic', NULL, 'instanceInfo', 2, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('aa04618a2d74901f1acedcbb6b4e4162', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '最高温设定', 'number', 'max_temp', '最高温设定', '[0,1500,1,1]', 'fireInput', 3, 'f');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('a20b00bac4bbeef948dd3b1556765d75', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '辅火线最低位', 'string', 'lowest_fireline_aux', '辅火线最低位', NULL, 'firelineInfo', 4, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('2dac67bb54890e28b4b191b1f4323816', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '主副火线间的平均温度', 'string', 'area_between_fireline_main2aux_stages_temp_mean', '阶段主副火线间的平均温度', NULL, 'firelinebetweenStageInfo', 4, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('14744f0aff1af1dbb670ddbc8f5c6ea3', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '辅火线平均', 'string', 'mean_fireline_aux', '辅火线平均', NULL, 'firelineInfo', 2, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('d49733e08c286bf6a9aa062ce5dfd72e', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '最低温度', 'string', 'sub_min_temperature_stage_4', '最低温度', NULL, 'roiInfo1', 5, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('a166101b94708d45a5b45750eeadbcb6', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '最低火线位置', 'string', 'lowest_fireline_stage_5', '5阶段最低火线位置', NULL, 'firelineinStage5Info', 7, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('14744f0aff1af1dbb670ddbc8f5c6ea2', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '主火线平均', 'string', 'mean_fireline_main', '主火线平均', NULL, 'firelineInfo', 1, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('77cfad1903c44e223fae275de47a2a74', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '主火线区域面积与ROI2的面积占比', 'string', 'ratio_of_fireline_main_stage_5', '5阶段主火线区域面积与ROI2的面积占比', NULL, 'firelineinStage5Info', 4, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('3fa257908cca95a5ddd5d96a5dfb6ccb', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '主火线温度值', 'number', 'main_fireline_temp', '主火线温度值', '[0,1500,1,1]', 'fireInput', 1, 'f');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('bcf2d6edfbd5bc39de34b19ea329cbb0', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '主火线与ROI2区域内的最大温度', 'string', 'max_temp_of_fireline_main_stage_5', '5阶段主火线与ROI2区域内的最大温度', NULL, 'firelineinStage5Info', 6, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('ae98411dc3878b2675f7144f86683e42', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '算法服务地址', 'string', 'serverHost', '算法服务地址', NULL, 'instanceInfo', 1, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('a2fa4ce48206900855641dfbf6b9525a', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '平均温度', 'string', 'sub_mean_temperature_stage_4', '平均温度', NULL, 'roiInfo1', 3, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('37a9dbb9f821fa5e8be91371e9619133', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '温度最低点', 'string', 'sub_T_min_point_stage_4', '温度最低点', NULL, 'roiInfo1', 7, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('261ea9cfbbe4008888b2860eb8277e1f', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '最高温度', 'string', 'sub_max_temperature_stage_4', '最高温度', NULL, 'roiInfo1', 4, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('1d2b70c178d75e81a9579a0e6acd9b9b', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '最高温度', 'string', 'sub_max_temperature_stage_5', '最高温度', NULL, 'roiInfo2', 4, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('014d34d94cad46483a89cb2b3ea729ed', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '温度最低点', 'string', 'sub_T_min_point_stage_5', '温度最低点', NULL, 'roiInfo2', 7, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('ab2b1fdf1024595831f9912c1a433a46', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '辅火线温度值', 'number', 'aux_fireline_temp', '辅火线温度值', '[0,1500,1,1]', 'fireInput', 2, 'f');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('78crad1903c44e223fae275de47a3a97', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '去噪程度', 'number', 'remove_noise_kernel_size', '值越大，去噪区域越大', '[3,10,1,1]', 'fireInput', 5, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('77cfad1903c44e223fae275de47a2a88', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '蒙灰阈值', 'number', 'ash_threshold', '值越大，蒙灰判定界限越宽松', '[10,1000,1,1]', 'fireInput', 4, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('14744f0aff1af1dbb670ddbc8f5c6ea1', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '绘制区域', 'square', 'roi1', '绘制区域', NULL, 'roiInfo1', 1, 'f');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('7c3d8906042b0973e8df53f0529f6fd8', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '量程', 'range', 'range1', '量程', '[-100,200,1]', 'roiInfo1', 2, 'f');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('37a198f1b76b6e2a7fa283e38220d73e', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '绘制区域2', 'square', 'roi2', '绘制区域2', NULL, 'roiInfo2', 1, 'f');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('14744f0aff1af1dbb670ddbc8f5c6ea0', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '量程', 'range', 'range2', '量程', '[-100,200,1]', 'roiInfo2', 2, 'f');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('fc888471af9327b20ca1fa259af90000', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '温度最高点', 'string', 'sub_T_max_point_stage_5', '温度最高点', NULL, 'roiInfo2', 6, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('1400896688f657e07d8a87adb851140c', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '最低温度', 'string', 'sub_min_temperature_stage_5', '最低温度', NULL, 'roiInfo2', 5, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('bd76273844d22912520e9ae2a14db89d', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '平均温度', 'string', 'sub_mean_temperature_stage_5', '平均温度', NULL, 'roiInfo2', 3, 't');
INSERT INTO "public"."insp_algorithm_param" ("id", "algorithm_id", "name", "type", "key", "des", "val_range", "group_key", "sort_no", "is_val_unique") VALUES ('e4d9ad2fa58e4420f4513c33e1ca53d2', '5af6d94d946c4a32a3e2a7fdf9c30ffe', '温度最高点', 'string', 'sub_T_max_point_stage_4', '温度最高点', NULL, 'roiInfo1', 6, 't');


INSERT INTO "public"."insp_system_user" ("id", "name", "username", "password") VALUES ('0f7396ef1a70f5e8b672617a8d01991e', '管理员', 'admin', 'e10adc3949ba59abbe56e057f20f883e');
INSERT INTO "public"."insp_system_user" ("id", "name", "username", "password") VALUES ('2500050680240cbd9760609f474f03b5', '操作员', 'ops', 'e10adc3949ba59abbe56e057f20f883e');

