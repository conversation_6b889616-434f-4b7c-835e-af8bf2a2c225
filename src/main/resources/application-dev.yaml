server:
  port: 9090
  host: ************
  tomcat:
    max-swallow-size: -1
  ssl:
    enabled: false
    key-store: classpath:cert/server.p12
    key-store-password: hollysys
    key-store-type: PKCS12
inspection:
  config:
    sceneCount: 20
# 日志配置
logging:
  # 大华SDK日志是否开启
  dh-log-open: false
  # 日志配置（xml扩展配置）
  logPath: ${project-file-dir}/logs
  logName: inspection-service
  logRootLevel: info
  itemMaxSize: 10MB
  maxHistory: 15
   # 数据库SQL的默认日志级别
  db-log-level: INFO
#  level:
#    com.hollysys.inspection: info

spring:
  datasource:
    url: jdbc:postgresql://${server.host:postgres}:5432/postgres
    username: postgres
    password: postgres
    driver-class-name: org.postgresql.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 20
      min-idle: 20
      maxActive: 1000
      maxWait: 60000
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20
      connectionErrorRetryAttempts: 3
      breakAfterAcquireFailure: true
      timeBetweenConnectErrorMillis: 300000
      asyncInit: true

  servlet:
    multipart:
      #单个文件最大上传大小
      max-file-size: 2048MB
      #每次请求上传文件大小最大值
      max-request-size: 2048MB
      #If you want to handle MaxUploadSizeExceededException with ExceptionHandler directly in your controller class, you should configure following property:
      #resolve-lazily: true
  redis:
    host: ${server.host}
    port: 6379
    password: 654321
    database: 0
    # 连接超时时间（毫秒）
    timeout: 60000
    # Lettuce
    lettuce:
      # 关闭超时时间
      shutdown-timeout: 100
      pool:
        # 连接池最大连接数（使用负值表示没有限制
        max-active: 1500
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: 10000
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池中的最小空闲连接
        min-idle: 0
        #“空闲链接”检测线程，检测的周期，毫秒数。如果为负值，表示不运行“检测线程”。默认为-1.
        time-between-eviction-runs: 60000
py-server:
  url:
    cv:
      base: http://${server.host}:6200
      # 算法执行
      algorithm-execute: ${py-server.url.cv.base}/execute
      # 算法上传至python服务
      algorithm-upload: ${py-server.url.cv.base}/block-upload
      # 算法删除
      algorithm-remove: ${py-server.url.cv.base}/block-remove
    depl:
      base: http://${server.host}:6300
      # 算法执行
      algorithm-execute: ${py-server.url.depl.base}/execute
      # 算法上传至python服务
      algorithm-upload: ${py-server.url.depl.base}/block-upload
      # 算法删除
      algorithm-remove: ${py-server.url.depl.base}/block-remove
      # 算法分类库更新
      gallery-cls-update: ${py-server.url.depl.base}/update-cls-gallery
    opt:
      base: http://${server.host}:6400
      # 模板匹配
      matching: ${py-server.url.opt.base}/position
      # 透视变换图像（梯形校正图像）
      transform-pic: ${py-server.url.opt.base}/transform-pic
      # 透视变换点列表（梯形校正点列表）
      transform-points: ${py-server.url.opt.base}/transform-points
      # 透视变换圆形到椭圆
      transform-circle: ${py-server.url.opt.base}/transform-circle
      # 绘制图形到图片中
      draw-shape: ${py-server.url.opt.base}/draw-shape
      # 获取截图URL（这不是截图URL）
      channel-pic: ${py-server.url.opt.base}/channel/pic?ip=%s&user=%s&pwd=%s&point=%s
      # 获得推流地址
      channel-stream: ${py-server.url.opt.base}/channel/streams?ip=%s&user=%s&pwd=%s
      # 控制云台
      control-relative-operate: ${py-server.url.opt.base}/channel/relative-move?ip=%s&user=%s&pwd=%s&action=%s
      # 持续转动
      start-control-continuous: ${py-server.url.opt.base}/channel/start-continuous-move?ip=%s&user=%s&pwd=%s&action=%s
      stop-control-continuous: ${py-server.url.opt.base}/channel/stop-continuous-move?ip=%s&user=%s&pwd=%s
      # 获取分辨率
      channel-scale-ip: ${py-server.url.opt.base}/channel/scale/ip?ip=%s&user=%s&pwd=%s
      # 获取分辨率
      channel-scale-rtsp: ${py-server.url.opt.base}/channel/scale/rtsp?rtsp=%s
      # 设置预置点
      point-set: ${py-server.url.opt.base}/point/set?ip=%s&user=%s&pwd=%s&name=%s&number=%s
      # 跳转预置点
      point-goto: ${py-server.url.opt.base}/point/goto?ip=%s&user=%s&pwd=%s&number=%s
      # 跳转预置点（异步）
      point-goto-async: ${py-server.url.opt.base}/point/goto-async?ip=%s&user=%s&pwd=%s&number=%s
      # 移除预置点
      point-remove: ${py-server.url.opt.base}/point/remove?ip=%s&user=%s&pwd=%s&number=%s
      # 摄像头厂商
      manufacturer-info: ${py-server.url.opt.base}/channel/manufacturer-info?ip=%s&user=%s&pwd=%s

project-file-dir: E:/intelligent_inspection/java
file-server:
  minio:
    endpoint: http://${server.host}:18010
    access-key: admin
    secret-key: Qqwe@1234
    bucket: inspection
    # 临时文件超时时间，超时后自动删除
    temp-file-expiration-days: 1
  files-mapping-dir: file-server
  root-dir: ${project-file-dir}/file-server/
#srs-server:
#  host: ${server.host:srs}
#  sip-port: 8025
#  # SIP邀请接口
#  invite-api: http://${srs-server.host}:${srs-server.sip-port}/srs-sip/v1/invite
#  ffmpeg-push-type: cpu
#  # SRS服务IP:http_server.listen
#  rtsp-play-url: /srs-server/%s.flv
#  gb28181-play-url: /srs-server/live/%s.flv
#  publish-url: rtmp://${srs-server.host}/%s
#  api-base: http://${srs-server.host}:1985/api/v1
#  t-stream-api: ${srs-server.api-base}/clients/%s?callback=angular.callbacks._j0&method=DELETE
#  all-stream-api: ${srs-server.api-base}/streams?count=500
#  all-client-api: ${srs-server.api-base}/clients?count=500
#  cron: 0 0/5 * * * ?
#  isT: true

schedule:
  execute-task-interval: 2  # 定时循环调度任务间隔（秒）
  execute-task-enable: true # 定时循环调度任务执行开关
  refresh-node-status-interval: 10  # 定时刷新节点状态 间隔（秒）
  refresh-node-status-enable: true # 定时刷新节点状态任务执行开关
  remove-temp-dir:
    expire: -2 # 文件过期时间（小时），只保留2小时的最新文件
    cron: 0 0 */2 * * ?   # 定时循环，每两小时执行一次

ntp:
  sync-enable: true
  sync-command: ntpdate %s %s # 从NTP服务同步系统时间命令模板
  # set-date-command: date -s %s # 设置系统时间命令模板
  date-format-temp: yyyy-MM-dd HH:mm:ss # NTP配置页面时间显示格式

# Sa-Token配置
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: Authorization
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效  86400 = 1 Day
  # timeout: -1
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结 1800 = 30 min
  activity-timeout: 1800
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # token风格
  token-style: uuid
  # 是否输出操作日志
  is-log: false
  # 是否从cookie中读取token
  is-read-cookie: false
  # 是否从head中读取token
  is-read-header: true
  include-path: /**
  exclude-path: /video-play-back/**, /test-network/**,/common-config/**,/os/mes/**,/api/hsm/auth/**,/license/**,/project/tree-and-state,/global-variable/page-all,/test/**,/portal/**,/swagger-ui.html,/index.html,/static/**,/swagger-resources/**,/error,/uri/behavior,/csrf,/webjars/**,/,/ping,/api/v1/streams/un-publish,/api/v1/streams/**,/api-for-dcs/**,/api/v1/inspection/alarm/**,/license/getAppCode,/files/**,/operate-statistical/**,/channel-info/**,/project/tree-and-state,/alarm-record/all,/execute-record/all,/schedule-task/all,/schedule-task/task-node/by-task-id

swagger:
  enable: false

cross-origin:
  enable: true

# 验证码配置
captcha:
  # 登录失败三次后，需要验证码
  show-of-times: 3
  # 验证码图片宽度，单位：像素
  img-width: 130
  # 验证码图片高度，单位：像素
  img-height: 60
  # 验证码字符个数
  code-len: 4
  # 验证码有效期，单位：分钟
  timeout: 10

custom-schedule-pic:
  enable: true
  cron: 0 0 0/1 * * ?

tree-node:
  directory-recursive:
    depth: 5
  channel:
    max-algorithm-instance-count: 100 # 同一通道各预置点下合计可添加算法总数不超过100个
  preset:
    max-algorithm-instance-count: 20 # 同一预置点下可添加算法个数不超过20个

license:
  system-version: 正式版
  product-version: v1.0.0
  verify:
    subject: 软件许可证书 # 证书名称, 默认：软件许可证书
    storePwd: 123456abc # 秘钥库加载密码
    public-pwd: 12345678910abc # 公钥库访问密码
    publicAlias: publicCert # 公钥别名，默认：publicCert
    public-keys-store-path: ${project-file-dir}/license/publicCerts.keystore  # 公钥库所在的位置，默认：classpath:/publicCerts.keystore
    exclude-path-patterns: /license/**, /api/hsm/auth/tokenCallback, /api/hsm/auth/logout,/common-config/loginMode, /api-for-dcs/**, /video-play-back/**  # 需要跳过验证授权的接口
    license-path: ${project-file-dir}/license/license.lic # 证书位置， 默认：classpath:license.lic

system-backup:
  enable: true
  backup-check-interval: 1 # 备份文件夹大小检查执行周期间隔
  backup-check-interval-unit: HOURS # 备份文件大小检查间隔单位，支持HOURS、DAYS
  execute-record:
    interval: 1 # 备份执行周期间隔
    interval-unit: HOURS # 备份执行周期间隔单位，支持HOURS、DAYS
    backup-file-expiration-days: 90 # 执行记录备份文件保存时间 超时则删除
  operation-log-record:
    interval: 1 # 备份执行周期间隔
    interval-unit: HOURS # 备份执行周期间隔单位，支持HOURS、DAYS
    backup-file-expiration-days: 90 # 操作记录备份文件保存时间 超时则删除
  alarm-record:
    interval: 1 # 备份执行周期间隔
    interval-unit: HOURS # 备份执行周期间隔单位，支持HOURS、DAYS
    backup-file-expiration-days: 90 # 报警记录备份文件保存时间 超时则删除

model:
  base: ${project-file-dir}/algorithm-model/  # 模型文件存放路径

recording-file:
  base: ${project-file-dir}/recording-file/  # 录像文件存放路径
  folder-clean:
    cron: 0 0,10 * * * ?
# 平台单点登录
db-base:
  real-srv:
    api-base: 6543
    api:
      # 获取token
      get-token: /api/gateway/appSignIn
      # 写点4
      write-points: /api/hsm-db-rtserver/v1/rtdata/node/write
      # 读点
      find-points: /api/hsm-db-rtserver/v1/rtdata/node/read
  auth-srv:
  # 当关闭单点登录时，走so-token登录框架
    enabled: false

mediamtx:
  container-name: "mediamtx"
  server-ip: ${server.host}
  port: 9997
  base-play-url: /mtx-server/
  yml-file-path: ${project-file-dir}/mediamtx/mediamtx.yml
  ip-file-path: ${project-file-dir}/host-ips.txt
  api:
    add-stream: http://${mediamtx.server-ip}:${mediamtx.port}/v3/config/paths/add/
    del-stream: http://${mediamtx.server-ip}:${mediamtx.port}/v3/config/paths/delete/
    get-stream: http://${mediamtx.server-ip}:${mediamtx.port}/v3/paths/get/
    list-stream: http://${mediamtx.server-ip}:${mediamtx.port}/v3/paths/list
#    list-stream: http://${mediamtx.server-ip}:${mediamtx.port}/v3/paths/list

rtsp-pic-srv:
  base-url: http://${server.host}:9097
  get-pic-url: ${rtsp-pic-srv.base-url}/rtsp-pic

alarm-restore:
  max-times: 3 # 实时报警恢复次数，满足后才置为恢复状态
#  timeout: 1 # 单位HOURS 最新实时报警时间与上次时间差，超过此设置时间时，直接可设置为恢复

portainer:
  auth-url: http://${server.host}:19000/api/auth
  service-list-url: http://${server.host}:19000/api/endpoints/1/docker/containers/json?all=true
  service-restart-url: http://${server.host}:19000/api/endpoints/1/docker/containers/%s/restart
  username: "admin"
  password: "3edc#EDC3edc"
