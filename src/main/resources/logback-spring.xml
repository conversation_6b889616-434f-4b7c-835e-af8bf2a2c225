<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <contextName>logback</contextName>

    <!--在yml文件中找到对应的配置项-->
    <!--日志路径-->
    <springProperty scope="context" name="LOG_HOME" source="logging.logPath"/>
    <!--日志文件名-->
    <springProperty scope="context" name="LOG_NAME" source="logging.logName"/>
    <!--日志级别-->
    <springProperty scope="context" name="LOG_ROOT_LEVEL" source="logging.logRootLevel"/>
    <!--单个日志文件大小-->
    <springProperty scope="context" name="LOG_FILE_ITEM_SIZE" source="logging.itemMaxSize"/>
    <!--日志文件最大保留时间-->
    <springProperty scope="context" name="LOG_FILE_MAX_HISTORY" source="logging.maxHistory"/>
    <!--数据库SQL的日志级别-->
    <springProperty scope="context" name="DB_LOG_LEVEL_FROM_YML" source="logging.db-log-level"/>

    <!--彩色日志依赖的渲染类-->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex"
                    converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx"
                    converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>
    <!--彩色日志格式(console控制台输出)-->
    <property name="CONSOLE_LOG_PATTERN"
              value="${CONSOLE_LOG_PATTERN:-%clr(%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>

    <!--日志文件输出格式-->
    <property name="FILE_LOG_PATTERN"
              value="%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}} [%thread] %-5level %logger{50} - %msg%n"/>

    <!--输出到控制台-->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!--按天生成日志,即一天只生成一个文件夹和一个日志文件-->
    <appender name="logFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--正在记录的日志文档的路径及文档名-->
        <!--日志文档输出格式-->
        <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度，%msg：日志消息，%n：换行符-->
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!--日志记录器的滚动策略，按日期,按大小记录-->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--日志文件路径：这里的%d{yyyy-MM-dd}表示按天分类日志-->
            <fileNamePattern>${LOG_HOME}/%d{yyyy-MM-dd}/${LOG_NAME}-%d{yyyy-MM-dd}-%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <!--设置最大日志大小-->
                <maxFileSize>${LOG_FILE_ITEM_SIZE}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <!--日志文档保留天数-->
            <maxHistory>${LOG_FILE_MAX_HISTORY}</maxHistory>
        </rollingPolicy>
    </appender>

    <!--MyBatis 框架的日志级别-->
    <logger name="org.apache.ibatis" level="${DB_LOG_LEVEL_FROM_YML}">
        <appender-ref ref="console"/>
        <appender-ref ref="logFile"/>
    </logger>

    <!--应用程序中 Mapper 接口所在的包的日志记录级别-->
    <logger name="com.hollysys.inspection.mapper" level="${DB_LOG_LEVEL_FROM_YML}">
        <appender-ref ref="console"/>
        <appender-ref ref="logFile"/>
    </logger>

    <!--Hibernate 框架生成 SQL 语句的日志级别-->
    <logger name="org.hibernate.SQL" level="${DB_LOG_LEVEL_FROM_YML}">
        <appender-ref ref="console"/>
        <appender-ref ref="logFile"/>
    </logger>

    <!--Hibernate 框架打印 SQL 参数的日志级别-->
    <logger name="org.hibernate.type.descriptor.sql" level="${DB_LOG_LEVEL_FROM_YML}">
        <appender-ref ref="console"/>
        <appender-ref ref="logFile"/>
    </logger>
    <!--项目的整体的日志打印级别-->
    <root level="${LOG_ROOT_LEVEL}">
        <appender-ref ref="console"/>
        <appender-ref ref="logFile"/>
    </root>
</configuration>