# 服务部署

## 1. 本地打包

### python 服务

```shell
cd py
docker build . -t inspection-backend:v1.0.0
docker save inspection-backend:v1.0.0 > inspection-backend.tar
```

打包后将 镜像 save 到服务器侧 然后 通过 load 加载

### java 服务

```shell
cd .
mvn clean package -DskipTests=true  # 得到 target/*.jar

```

### 前端web 服务
* TODO 
```shell
cd .
docker build  -t inspection-web:1.0.0 .
docker run --name inspection-web -p 8000:80  -d inspection-web:1.0.0
```

## 2. 服务器部署

环境：windows10企业版 *************** SmartFactory/hollysys
显卡: Nvidia GeForce RTX 3090 * 2

### python 服务

```shell
docker load -i inspection-backend.tar
docker load -i inspection-web.tar
```

### java 服务

### 前端web 服务

## 3. 运行
```shell
docker-compose up -d
```

端口矩阵

| 分类  | 	服务名                | 	端口号  | 	备注 |
|-----|---------------------|-------|-----|
| 应用  | 	inspection-backend | 	6001 |     |
|     | inspection-svr 	    | 9090  |     |
|     | inspection-web	     | 8000  |     |
| 中间件 | 	postgresql         | 	5432 |     |
|     | ~~kafka~~           | 	9092 |
|     | ~~rabbitmq~~        | 	5672 |
|     | redis               | 	6379 |
|     | file-server         | 	9527 |
|     | srs                 | 8080  |

java 服务

```shell
  ./script/start.bat
```
