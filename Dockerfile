ARG ARCHTECTURE

# 基于java镜像创建新镜像
FROM ************:5000/java-base:${ARCHTECTURE}ffmpeg7.1.1-yn
# 作者
MAINTAINER hollysys
# 版本
ARG APP_VERSION=1.0.0
# 执行linux 的cmd命令
#系统编码
ENV LANG=C.UTF-8 LC_ALL=C.UTF-8
ENV APP_VERSION=${APP_VERSION}
LABEL version=${APP_VERSION}
LABEL description="service"

RUN rm -f /etc/localtime \
    && ln -sv /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone

# 新建部署文件夹
RUN mkdir -p /opt/deploy

# 添加SDK依赖
COPY libs/dh_linux64 /libs/dh_linux64
COPY libs/hik_linux64 /libs/hik_linux64
COPY libs/ys_linux64 /libs/ys_linux64
# 添加库路径到动态链接器
RUN echo "/libs/ys_linux64" > /etc/ld.so.conf.d/ys_sdk.conf && ldconfig
# 拷贝本地所有tar.gz文件到镜像的指定目录下
COPY target/intelligent_inspection-1.0.0-SNAPSHOT.jar /opt/deploy/service.jar

# 添加项目中使用的环境变量
ENV PROJECT_DIR=/project
ENV FFMPEG_PUSH_TYPE=cpu
ENV MQTT_ENABLE=true
ENV RABBITMQ_ENABLE=true
ENV SPRING_PROFILES_ACTIVE=prod
ENTRYPOINT ["java","-jar","service.jar","--spring.profiles.active=${SPRING_PROFILES_ACTIVE}","--project-file-dir=${PROJECT_DIR}","--srs-server.ffmpeg-push-type=${FFMPEG_PUSH_TYPE}","--spring.mqtt.enable=${MQTT_ENABLE}","--spring.rabbitmq.enable=${RABBITMQ_ENABLE}"]

# docker build  -t ***************:5000/hia-vision/vision_svr:V1.0.0_LTSP2_2025.6.18_V2 .
# docker build  -t ***************:5000/inspection-service:arm64-inspection_alarm  -f Dockerfile-Arm .
# docker build  -t ***************:5000/inspection-service:inspection_alarm-test .
# GPU启动命令
# docker run --privileged=true --restart=always --runtime=nvidia --name inspection-service -e SERVER_HOST=***************  -e PROJECT_DIR=/project -e FFMPEG_PUSH_TYPE=gpu -p 9090:9090 -p 9527:9527  -v /home/<USER>/intelligent_inspection:/project -v /etc/localtime:/etc/localtime:ro -v /etc/timezone:/etc/timezone:ro -d inspection-service:1.0.0
# CPU启动命令
# docker run --privileged=true --restart=always  --name inspection-service -e SERVER_HOST=***************  -e PROJECT_DIR=/project -e FFMPEG_PUSH_TYPE=cpu -p 9090:9090 -p 9527:9527  -v /home/<USER>/intelligent_inspection:/project -v /etc/localtime:/etc/localtime:ro -v /etc/timezone:/etc/timezone:ro -d inspection-service:1.0.0

