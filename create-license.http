###第一步: 获取 appCode 申请码 by license-core-springboot-starter  详请参考 https://github.com/a852203465/license-spring-boot-starter

GET http://localhost:9090/license/getAppCode


###第二步: 下载私钥文件  by license-creator-springboot-starter
GET http://localhost:8088/license/privateKeys?validity=1&storePwd=123456abc&keyPwd=123456a123456


###第三步: 下载公钥文件  by license-creator-springboot-starter
GET http://***************:8088/license/publicCerts?validity=1&storePwd=123456abc&publicPwd=12345678910abc


###第四步: 生成证书文件, 此处用到了 私钥文件  by license-creator-springboot-starter
POST http://***************:8088/license/generate
Content-Type: application/json

{
    "subject": "软件许可证书",
    "keyPwd": "123456a123456",
    "storePwd": "123456abc",
    "privateAlias": "privateKeys",
    "privateKeysStorePath": "/privateKey/privateKeys.keystore",
    "consumerAmount": 1,
    "expiryTime": "2034-12-31 23:59:59",
    "description": "系统软件许可证书",
    "checkIpAddress": false,
    "checkMacAddress":true,
    "appCode": "5b2d533e14522c70d9580384a2b7e1305f4b61e644683cfe3e40a31b9b50bbf03f632cefac25e8ada226446d3e0920e6555d37b4884bccf22220d9abd259e3b49f0a67416938d4395b273fdf8439db197dabcc2ec4802bc99022d65a1f2e4de4067ca8cd1770c99f9653f864bd4480982378fca0049291e9dbf0837ad45e2cde23acc796376e2020ad040fb9c2794c5ebd5881f88c6a1c74ea58d20527523349"
}

###第五步: 下载证书文件  by license-creator-springboot-starter

GET http://localhost:8088/license/download?path=/opt/deploy/lic/1f66fff1f4d755aad18800346ca5a432/license.lic


###第六步: 下载下来的证书文件和 公钥文件 配置进 application.yml 中 配合 license-verfify-springboot-starter 使用即可

GET http://localhost:8088/some-url-to-verify-license
