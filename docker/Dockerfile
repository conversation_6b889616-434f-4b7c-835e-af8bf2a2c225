# 基于java镜像创建新镜像
FROM ***************:5000/base-java:1.0.0
# 作者
MAINTAINER hollysys
# 版本
ARG APP_VERSION=1.0.0
# 执行linux 的cmd命令
#系统编码
ENV LANG=C.UTF-8 LC_ALL=C.UTF-8
ENV APP_VERSION=${APP_VERSION}
LABEL version=${APP_VERSION}
LABEL description="service"


# 新建部署文件夹
RUN mkdir -p /opt/deploy

# 拷贝本地所有tar.gz文件到镜像的指定目录下
COPY intelligent_inspection-1.0.0-SNAPSHOT.jar /opt/deploy/service.jar

# 添加项目中使用的环境变量
ENV PROJECT_DIR=/project
ENV SERVER_HOST=localhost
ENV FFMPEG_PUSH_TYPE=cpu
ENV MQTT_ENABLE=true
ENV RABBITMQ_ENABLE=true
ENTRYPOINT ["java","-jar","service.jar","--project-file-dir=${PROJECT_DIR}","--server.host=${SERVER_HOST}","--srs-server.ffmpeg-push-type=${FFMPEG_PUSH_TYPE}","--spring.mqtt.enable=${MQTT_ENABLE}","--spring.rabbitmq.enable=${RABBITMQ_ENABLE}"]

# docker build  -t ***************:5000/inspection-service:inspection_power  .
# GPU启动命令
# docker run --privileged=true --restart=always --runtime=nvidia --name inspection-service -e SERVER_HOST=***************  -e PROJECT_DIR=/project -e FFMPEG_PUSH_TYPE=gpu -p 9090:9090 -p 9527:9527  -v /home/<USER>/intelligent_inspection:/project -v /etc/localtime:/etc/localtime:ro -v /etc/timezone:/etc/timezone:ro -d inspection-service:1.0.0
# CPU启动命令
# docker run --privileged=true --restart=always  --name inspection-service -e SERVER_HOST=***************  -e PROJECT_DIR=/project -e FFMPEG_PUSH_TYPE=cpu -p 9090:9090 -p 9527:9527  -v /home/<USER>/intelligent_inspection:/project -v /etc/localtime:/etc/localtime:ro -v /etc/timezone:/etc/timezone:ro -d inspection-service:1.0.0

