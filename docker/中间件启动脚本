# postgres
docker run --restart=always --name postgres -v /home/<USER>/intelligent_inspection/postgre/data:/var/lib/postgresql/data -p 5432:5432 -e POSTGRES_PASSWORD=postgres -d postgres:12
docker run --restart=always --name postgres -v E:/project/postgre/data:/var/lib/postgresql/data -p 5432:5432 -e POSTGRES_PASSWORD=postgres -d postgres:12
# redis
docker run --restart=always --name redis -p 6379:6379 -d redis:6
# rabbitmq
docker run --restart=always --name rabbit  -p 5672:5672 -p 15672:15672 -d rabbitmq:3.11-management
# zookeeper
docker run -d --name zookeeper-server \
    --restart=always \
    -p 2181:2181 -v /etc/localtime:/etc/localtime \
    -e ALLOW_ANONYMOUS_LOGIN=yes \
    bitnami/zookeeper:3.8.0
# kafka
docker run -d --name kafka-server \
    --restart=always \
    -p 9092:9092 -v /etc/localtime:/etc/localtime \
    -e ALLOW_PLAINTEXT_LISTENER=yes \
    -e KAFKA_CFG_ZOOKEEPER_CONNECT=zookeeper-server:2181 \
    bitnami/kafka:3.4.0
# py
docker run --name inspection-backend-cv1 --restart=always -v /home/<USER>/intelligent_inspection/file-server/images:/project/file-server/images/ -p 6201:6200 -d  inspection-backend-cv:1.0.0
docker run --name inspection-backend-cv2 --restart=always -v /home/<USER>/intelligent_inspection/file-server/images:/project/file-server/images/ -p 6202:6200 -d  inspection-backend-cv:1.0.0
docker run --name inspection-backend-opt1 --restart=always -v /home/<USER>/intelligent_inspection/file-server/images:/project/file-server/images/ -p 6401:6400 -d  inspection-backend-opt:1.0.0
docker run --name inspection-backend-opt2 --restart=always -v /home/<USER>/intelligent_inspection/file-server/images:/project/file-server/images/ -p 6402:6400 -d  inspection-backend-opt:1.0.0

docker run --name inspection-backend-depl1 --restart=always \
-v /home/<USER>/intelligent_inspection/file-server/images:/project/file-server/images/ \
-v /home/<USER>/intelligent_inspection/file-server/voice/:/voice/ \
--gpus all --shm-size=16g --ulimit memlock=-1 -p 6301:6300 -d  inspection-backend-depl:1.0.0

docker run --name inspection-backend-depl2 --restart=always \
-v /home/<USER>/intelligent_inspection/file-server/images:/project/file-server/images/ \
-v /home/<USER>/intelligent_inspection/file-server/voice/:/voice/ \
--gpus all --shm-size=16g --ulimit memlock=-1 -p 6302:6300 -d  inspection-backend-depl:1.0.0

docker run -it --name inspection-backend-tensorrt --restart=always \
-v /home/<USER>/intelligent_inspection/file-server/images:/project/file-server/images/ \
--gpus all --shm-size=16g --ulimit memlock=-1 -p 6500:6500 -d tensorrt:2.0.0

# srs
export CANDIDATE="***************"
docker run --name srs --env CANDIDATE=$CANDIDATE -v /home/<USER>/intelligent_inspection/srs/rtmp2rtc.conf:/usr/local/srs/conf/rtmp2rtc.conf -p 1935:1935 -p 8080:8080 -p 1985:1985 -p 8000:8000/udp -d ossrs/srs:6 objs/srs -c conf/rtmp2rtc.conf
docker run --name srs --env CANDIDATE=$CANDIDATE -v /home/<USER>/intelligent_inspection/srs/rtmp2rtc.conf:/usr/local/srs/conf/rtmp2rtc.conf -p 1935:1935 -p 8080:8080 -p 1985:1985 -p 8000:8000/udp -d ossrs/srs:6 objs/srs -c conf/rtmp2rtc.conf
export CANDIDATE="***************"
docker run --rm --env CANDIDATE=$CANDIDATE \
  -p 1935:1935 -p 8080:8080 -p 1985:1985 -p 8000:8000/udp \
  registry.cn-hangzhou.aliyuncs.com/ossrs/srs:v5 \
  objs/srs -c conf/rtc.conf

docker run --restart=always --name srs \
  -p 1935:1935 -p 8080:8080 -p 1985:1985 -p 8000:8000/udp \
  -d registry.cn-hangzhou.aliyuncs.com/ossrs/srs:v5 \
  objs/srs -c conf/docker.conf


# GPU启动命令
# docker run  --restart=always --runtime=nvidia --name inspection-service -e SERVER_HOST=***************  -e PROJECT_DIR=/project -e FFMPEG_PUSH_TYPE=gpu -p 9090:9090 -p 9527:9527  -v /home/<USER>/intelligent_inspection:/project -d inspection-service:1.0.0
# CPU启动命令
# docker run  --restart=always  --name inspection-service -e SERVER_HOST=***************  -e PROJECT_DIR=/project -e FFMPEG_PUSH_TYPE=cpu -p 9090:9090 -p 9527:9527  -v /home/<USER>/intelligent_inspection:/project -d inspection-service:1.0.0

 # mqtt
 docker run  --restart=always --name mosquitto -p 18083:18083 -p 1883:1883 -p 8084:8084 -p 8883:8883 -p 8083:8083 -d registry.cn-hangzhou.aliyuncs.com/synbop/emqttd:2.3.6
 docker run --restart=always  --name mosquitto -p 1883:1883 -p 9001:9001 -v /mosquitto/config/mosquitto.conf:/mosquitto/config/mosquitto.conf  -d eclipse-mosquitto:1.6.15
 # nodered/node-red
 docker run --restart=always --user=root -it -p 1880:1880 -v  /home/<USER>/intelligent_inspection/nodered:/data -d  --name mynodered nodered/node-red:2.2.3
