# Network services, Internet style
#
# Note that it is presently the policy of IANA to assign a single well-known
# port number for both TCP and UDP; hence, officially ports have two entries
# even if the protocol doesn't support UDP operations.
#
# Updated from https://www.iana.org/assignments/service-names-port-numbers/service-names-port-numbers.xhtml .
#
# New ports will be added on request if they have been officially assigned
# by IANA and used in the real-world or are needed by a debian package.
# If you need a huge list of used numbers please install the nmap package.

tcpmux		1/tcp				# TCP port service multiplexer
echo		7/tcp
echo		7/udp
discard		9/tcp		sink null
discard		9/udp		sink null
systat		11/tcp		users
daytime		13/tcp
daytime		13/udp
netstat		15/tcp
qotd		17/tcp		quote
chargen		19/tcp		ttytst source
chargen		19/udp		ttytst source
ftp-data	20/tcp
ftp		21/tcp
fsp		21/udp		fspd
ssh		22/tcp				# SSH Remote Login Protocol
telnet		23/tcp
smtp		25/tcp		mail
time		37/tcp		timserver
time		37/udp		timserver
whois		43/tcp		nicname
tacacs		49/tcp				# Login Host Protocol (TACACS)
tacacs		49/udp
domain		53/tcp				# Domain Name Server
domain		53/udp
bootps		67/udp
bootpc		68/udp
tftp		69/udp
gopher		70/tcp				# Internet Gopher
finger		79/tcp
http		80/tcp		www		# WorldWideWeb HTTP
kerberos	88/tcp		kerberos5 krb5 kerberos-sec	# Kerberos v5
kerberos	88/udp		kerberos5 krb5 kerberos-sec	# Kerberos v5
iso-tsap	102/tcp		tsap		# part of ISODE
acr-nema	104/tcp		dicom		# Digital Imag. & Comm. 300
pop3		110/tcp		pop-3		# POP version 3
sunrpc		111/tcp		portmapper	# RPC 4.0 portmapper
sunrpc		111/udp		portmapper
auth		113/tcp		authentication tap ident
nntp		119/tcp		readnews untp	# USENET News Transfer Protocol
ntp		123/udp				# Network Time Protocol
epmap		135/tcp		loc-srv		# DCE endpoint resolution
netbios-ns	137/tcp				# NETBIOS Name Service
netbios-ns	137/udp
netbios-dgm	138/tcp				# NETBIOS Datagram Service
netbios-dgm	138/udp
netbios-ssn	139/tcp				# NETBIOS session service
netbios-ssn	139/udp
imap2		143/tcp		imap		# Interim Mail Access P 2 and 4
snmp		161/tcp				# Simple Net Mgmt Protocol
snmp		161/udp
snmp-trap	162/tcp		snmptrap	# Traps for SNMP
snmp-trap	162/udp		snmptrap
cmip-man	163/tcp				# ISO mgmt over IP (CMOT)
cmip-man	163/udp
cmip-agent	164/tcp
cmip-agent	164/udp
mailq		174/tcp			# Mailer transport queue for Zmailer
xdmcp		177/udp			# X Display Manager Control Protocol
bgp		179/tcp				# Border Gateway Protocol
smux		199/tcp				# SNMP Unix Multiplexer
qmtp		209/tcp				# Quick Mail Transfer Protocol
z3950		210/tcp		wais		# NISO Z39.50 database
ipx		213/udp				# IPX [RFC1234]
ptp-event	319/udp
ptp-general	320/udp
pawserv		345/tcp				# Perf Analysis Workbench
zserv		346/tcp				# Zebra server
rpc2portmap	369/tcp
rpc2portmap	369/udp				# Coda portmapper
codaauth2	370/tcp
codaauth2	370/udp				# Coda authentication server
clearcase	371/udp		Clearcase
ldap		389/tcp			# Lightweight Directory Access Protocol
ldap		389/udp
svrloc		427/tcp				# Server Location
svrloc		427/udp
https		443/tcp				# http protocol over TLS/SSL
snpp		444/tcp				# Simple Network Paging Protocol
microsoft-ds	445/tcp				# Microsoft Naked CIFS
microsoft-ds	445/udp
kpasswd		464/tcp
kpasswd		464/udp
submissions	465/tcp		ssmtp smtps urd # Submission over TLS [RFC8314]
saft		487/tcp			# Simple Asynchronous File Transfer
isakmp		500/udp				# IPSEC key management
rtsp		554/tcp			# Real Time Stream Control Protocol
rtsp		554/udp
nqs		607/tcp				# Network Queuing system
asf-rmcp	623/udp		# ASF Remote Management and Control Protocol
qmqp		628/tcp
ipp		631/tcp				# Internet Printing Protocol
#
# UNIX specific services
#
exec		512/tcp
biff		512/udp		comsat
login		513/tcp
who		513/udp		whod
shell		514/tcp		cmd syslog	# no passwords used
syslog		514/udp
printer		515/tcp		spooler		# line printer spooler
talk		517/udp
ntalk		518/udp
route		520/udp		router routed	# RIP
gdomap		538/tcp				# GNUstep distributed objects
gdomap		538/udp
uucp		540/tcp		uucpd		# uucp daemon
klogin		543/tcp				# Kerberized `rlogin' (v5)
kshell		544/tcp		krcmd		# Kerberized `rsh' (v5)
dhcpv6-client	546/udp
dhcpv6-server	547/udp
afpovertcp	548/tcp				# AFP over TCP
nntps		563/tcp		snntp		# NNTP over SSL
submission	587/tcp				# Submission [RFC4409]
ldaps		636/tcp				# LDAP over SSL
ldaps		636/udp
tinc		655/tcp				# tinc control port
tinc		655/udp
silc		706/tcp
kerberos-adm	749/tcp				# Kerberos `kadmin' (v5)
#
domain-s	853/tcp				# DNS over TLS [RFC7858]
domain-s	853/udp				# DNS over DTLS [RFC8094]
rsync		873/tcp
ftps-data	989/tcp				# FTP over SSL (data)
ftps		990/tcp
telnets		992/tcp				# Telnet over SSL
imaps		993/tcp				# IMAP over SSL
pop3s		995/tcp				# POP-3 over SSL
#
# From ``Assigned Numbers'':
#
#> The Registered Ports are not controlled by the IANA and on most systems
#> can be used by ordinary user processes or programs executed by ordinary
#> users.
#
#> Ports are used in the TCP [45,106] to name the ends of logical
#> connections which carry long term conversations.  For the purpose of
#> providing services to unknown callers, a service contact port is
#> defined.  This list specifies the port used by the server process as its
#> contact port.  While the IANA can not control uses of these ports it
#> does register or list uses of these ports as a convienence to the
#> community.
#
socks		1080/tcp			# socks proxy server
proofd		1093/tcp
rootd		1094/tcp
openvpn		1194/tcp
openvpn		1194/udp
rmiregistry	1099/tcp			# Java RMI Registry
lotusnote	1352/tcp	lotusnotes	# Lotus Note
ms-sql-s	1433/tcp			# Microsoft SQL Server
ms-sql-s	1433/udp
ms-sql-m	1434/tcp			# Microsoft SQL Monitor
ms-sql-m	1434/udp
ingreslock	1524/tcp
datametrics	1645/tcp	old-radius
datametrics	1645/udp	old-radius
sa-msg-port	1646/tcp	old-radacct
sa-msg-port	1646/udp	old-radacct
kermit		1649/tcp
groupwise	1677/tcp
l2f		1701/udp	l2tp
radius		1812/tcp
radius		1812/udp
radius-acct	1813/tcp	radacct		# Radius Accounting
radius-acct	1813/udp	radacct
cisco-sccp	2000/tcp			# Cisco SCCP
nfs		2049/tcp			# Network File System
nfs		2049/udp			# Network File System
gnunet		2086/tcp
gnunet		2086/udp
rtcm-sc104	2101/tcp			# RTCM SC-104 IANA 1/29/99
rtcm-sc104	2101/udp
gsigatekeeper	2119/tcp
gris		2135/tcp		# Grid Resource Information Server
cvspserver	2401/tcp			# CVS client/server operations
venus		2430/tcp			# codacon port
venus		2430/udp			# Venus callback/wbc interface
venus-se	2431/tcp			# tcp side effects
venus-se	2431/udp			# udp sftp side effect
codasrv		2432/tcp			# not used
codasrv		2432/udp			# server port
codasrv-se	2433/tcp			# tcp side effects
codasrv-se	2433/udp			# udp sftp side effect
mon		2583/tcp			# MON traps
mon		2583/udp
dict		2628/tcp			# Dictionary server
f5-globalsite	2792/tcp
gsiftp		2811/tcp
gpsd		2947/tcp
gds-db		3050/tcp	gds_db		# InterBase server
icpv2		3130/tcp	icp		# Internet Cache Protocol
icpv2		3130/udp	icp
isns		3205/tcp			# iSNS Server Port
isns		3205/udp			# iSNS Server Port
iscsi-target	3260/tcp
mysql		3306/tcp
ms-wbt-server	3389/tcp
nut		3493/tcp			# Network UPS Tools
nut		3493/udp
distcc		3632/tcp			# distributed compiler
distcc		3632/udp
daap		3689/tcp			# Digital Audio Access Protocol
daap		3689/udp
svn		3690/tcp	subversion	# Subversion protocol
svn		3690/udp	subversion
suucp		4031/tcp			# UUCP over SSL
suucp		4031/udp
sysrqd		4094/tcp			# sysrq daemon
sysrqd		4094/udp
sieve		4190/tcp			# ManageSieve Protocol
epmd		4369/tcp			# Erlang Port Mapper Daemon
epmd		4369/udp
remctl		4373/tcp		# Remote Authenticated Command Service
remctl		4373/udp
f5-iquery	4353/tcp			# F5 iQuery
f5-iquery	4353/udp
ipsec-nat-t	4500/udp			# IPsec NAT-Traversal [RFC3947]
iax		4569/tcp			# Inter-Asterisk eXchange
iax		4569/udp
mtn		4691/tcp			# monotone Netsync Protocol
mtn		4691/udp
radmin-port	4899/tcp			# RAdmin Port
radmin-port	4899/udp
sip		5060/tcp			# Session Initiation Protocol
sip		5060/udp
sip-tls		5061/tcp
sip-tls		5061/udp
xmpp-client	5222/tcp	jabber-client	# Jabber Client Connection
xmpp-server	5269/tcp	jabber-server	# Jabber Server Connection
cfengine	5308/tcp
mdns		5353/udp			# Multicast DNS
postgresql	5432/tcp	postgres	# PostgreSQL Database
freeciv		5556/tcp	rptp		# Freeciv gameplay
amqps		5671/tcp			# AMQP protocol over TLS/SSL
amqp		5672/tcp
amqp		5672/udp
amqp		5672/sctp
x11		6000/tcp	x11-0		# X Window System
x11-1		6001/tcp
x11-2		6002/tcp
x11-3		6003/tcp
x11-4		6004/tcp
x11-5		6005/tcp
x11-6		6006/tcp
x11-7		6007/tcp
gnutella-svc	6346/tcp			# gnutella
gnutella-svc	6346/udp
gnutella-rtr	6347/tcp			# gnutella
gnutella-rtr	6347/udp
sge-qmaster	6444/tcp	sge_qmaster	# Grid Engine Qmaster Service
sge-execd	6445/tcp	sge_execd	# Grid Engine Execution Service
mysql-proxy	6446/tcp			# MySQL Proxy
babel		6696/udp			# Babel Routing Protocol
ircs-u		6697/tcp		# Internet Relay Chat via TLS/SSL
afs3-fileserver 7000/tcp	bbs		# file server itself
afs3-fileserver 7000/udp	bbs
afs3-callback	7001/tcp			# callbacks to cache managers
afs3-callback	7001/udp
afs3-prserver	7002/tcp			# users & groups database
afs3-prserver	7002/udp
afs3-vlserver	7003/tcp			# volume location database
afs3-vlserver	7003/udp
afs3-kaserver	7004/tcp			# AFS/Kerberos authentication
afs3-kaserver	7004/udp
afs3-volser	7005/tcp			# volume managment server
afs3-volser	7005/udp
afs3-errors	7006/tcp			# error interpretation service
afs3-errors	7006/udp
afs3-bos	7007/tcp			# basic overseer process
afs3-bos	7007/udp
afs3-update	7008/tcp			# server-to-server updater
afs3-update	7008/udp
afs3-rmtsys	7009/tcp			# remote cache manager service
afs3-rmtsys	7009/udp
font-service	7100/tcp	xfs		# X Font Service
http-alt	8080/tcp	webcache	# WWW caching service
puppet		8140/tcp			# The Puppet master service
bacula-dir	9101/tcp			# Bacula Director
bacula-fd	9102/tcp			# Bacula File Daemon
bacula-sd	9103/tcp			# Bacula Storage Daemon
xmms2		9667/tcp	# Cross-platform Music Multiplexing System
nbd		10809/tcp			# Linux Network Block Device
zabbix-agent	10050/tcp			# Zabbix Agent
zabbix-trapper	10051/tcp			# Zabbix Trapper
amanda		10080/tcp			# amanda backup services
dicom		11112/tcp
hkp		11371/tcp			# OpenPGP HTTP Keyserver
db-lsp		17500/tcp			# Dropbox LanSync Protocol
dcap		22125/tcp			# dCache Access Protocol
gsidcap		22128/tcp			# GSI dCache Access Protocol
wnn6		22273/tcp			# wnn6

#
# Datagram Delivery Protocol services
#
rtmp		1/ddp			# Routing Table Maintenance Protocol
nbp		2/ddp			# Name Binding Protocol
echo		4/ddp			# AppleTalk Echo Protocol
zip		6/ddp			# Zone Information Protocol

#=========================================================================
# The remaining port numbers are not as allocated by IANA.
#=========================================================================

# Kerberos (Project Athena/MIT) services
kerberos4	750/udp		kerberos-iv kdc	# Kerberos (server)
kerberos4	750/tcp		kerberos-iv kdc
kerberos-master	751/udp		kerberos_master	# Kerberos authentication
kerberos-master	751/tcp
passwd-server	752/udp		passwd_server	# Kerberos passwd server
krb-prop	754/tcp		krb_prop krb5_prop hprop # Kerberos slave propagation
zephyr-srv	2102/udp			# Zephyr server
zephyr-clt	2103/udp			# Zephyr serv-hm connection
zephyr-hm	2104/udp			# Zephyr hostmanager
iprop		2121/tcp			# incremental propagation
supfilesrv	871/tcp			# Software Upgrade Protocol server
supfiledbg	1127/tcp		# Software Upgrade Protocol debugging

#
# Services added for the Debian GNU/Linux distribution
#
poppassd	106/tcp				# Eudora
poppassd	106/udp
moira-db	775/tcp		moira_db	# Moira database
moira-update	777/tcp		moira_update	# Moira update protocol
moira-ureg	779/udp		moira_ureg	# Moira user registration
spamd		783/tcp				# spamassassin daemon
skkserv		1178/tcp			# skk jisho server port
predict		1210/udp			# predict -- satellite tracking
rmtcfg		1236/tcp			# Gracilis Packeten remote config server
xtel		1313/tcp			# french minitel
xtelw		1314/tcp			# french minitel
support		1529/tcp			# GNATS
cfinger		2003/tcp			# GNU Finger
frox		2121/tcp			# frox: caching ftp proxy
zebrasrv	2600/tcp			# zebra service
zebra		2601/tcp			# zebra vty
ripd		2602/tcp			# ripd vty (zebra)
ripngd		2603/tcp			# ripngd vty (zebra)
ospfd		2604/tcp			# ospfd vty (zebra)
bgpd		2605/tcp			# bgpd vty (zebra)
ospf6d		2606/tcp			# ospf6d vty (zebra)
ospfapi		2607/tcp			# OSPF-API
isisd		2608/tcp			# ISISd vty (zebra)
afbackup	2988/tcp			# Afbackup system
afbackup	2988/udp
afmbackup	2989/tcp			# Afmbackup system
afmbackup	2989/udp
fax		4557/tcp			# FAX transmission service (old)
hylafax		4559/tcp			# HylaFAX client-server protocol (new)
distmp3		4600/tcp			# distmp3host daemon
munin		4949/tcp	lrrd		# Munin
enbd-cstatd	5051/tcp			# ENBD client statd
enbd-sstatd	5052/tcp			# ENBD server statd
pcrd		5151/tcp			# PCR-1000 Daemon
noclog		5354/tcp			# noclogd with TCP (nocol)
noclog		5354/udp			# noclogd with UDP (nocol)
hostmon		5355/tcp			# hostmon uses TCP (nocol)
hostmon		5355/udp			# hostmon uses UDP (nocol)
rplay		5555/udp			# RPlay audio service
nrpe		5666/tcp			# Nagios Remote Plugin Executor
nsca		5667/tcp			# Nagios Agent - NSCA
mrtd		5674/tcp			# MRT Routing Daemon
bgpsim		5675/tcp			# MRT Routing Simulator
canna		5680/tcp			# cannaserver
syslog-tls	6514/tcp			# Syslog over TLS [RFC5425]
sane-port	6566/tcp	sane saned	# SANE network scanner daemon
ircd		6667/tcp			# Internet Relay Chat
zope-ftp	8021/tcp			# zope management by ftp
tproxy		8081/tcp			# Transparent Proxy
omniorb		8088/tcp			# OmniORB
omniorb		8088/udp
clc-build-daemon 8990/tcp			# Common lisp build daemon
xinetd		9098/tcp
mandelspawn	9359/udp	mandelbrot	# network mandelbrot
git		9418/tcp			# Git Version Control System
zope		9673/tcp			# zope server
webmin		10000/tcp
kamanda		10081/tcp			# amanda backup services (Kerberos)
amandaidx	10082/tcp			# amanda backup services
amidxtape	10083/tcp			# amanda backup services
smsqp		11201/tcp			# Alamin SMS gateway
smsqp		11201/udp
xpilot		15345/tcp			# XPilot Contact Port
xpilot		15345/udp
sgi-cmsd	17001/udp		# Cluster membership services daemon
sgi-crsd	17002/udp
sgi-gcd		17003/udp			# SGI Group membership daemon
sgi-cad		17004/tcp			# Cluster Admin daemon
isdnlog		20011/tcp			# isdn logging system
isdnlog		20011/udp
vboxd		20012/tcp			# voice box system
vboxd		20012/udp
binkp		24554/tcp			# binkp fidonet protocol
asp		27374/tcp			# Address Search Protocol
asp		27374/udp
csync2		30865/tcp			# cluster synchronization tool
dircproxy	57000/tcp			# Detachable IRC Proxy
tfido		60177/tcp			# fidonet EMSI over telnet
fido		60179/tcp			# fidonet EMSI over TCP

# Local services
