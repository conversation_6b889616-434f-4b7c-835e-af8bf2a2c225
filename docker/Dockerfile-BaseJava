# 基于java镜像创建新镜像
FROM linuxserver/ffmpeg:5.1.2
# 作者
MAINTAINER hollysys
# 版本
ARG APP_VERSION=1.0.0

# 拷贝services，解决ntpdate命令无法解析主机名
COPY services /etc/services


# 拷贝ntpdate，调用ntp服务命令
COPY ntpdate /opt/deploy/ntpdate
RUN  chmod +x /opt/deploy/ntpdate
# 添加ntpdate环境变量
ENV PATH=/opt/deploy:$PATH

# ntp校时服务依赖安装
COPY libssl1.1_1.1.1-1ubuntu2.1~18.04.22_amd64.deb /opt/deploy/libssl1.1_1.1.1-1ubuntu2.1~18.04.22_amd64.deb
RUN dpkg -i /opt/deploy/libssl1.1_1.1.1-1ubuntu2.1~18.04.22_amd64.deb

# 拷贝字体
COPY fonts /usr/share/fonts

# 拷贝jdk
COPY OpenJDK8U-jdk_x64_linux.tar.gz /opt/deploy/jdk8.tar.gz

# 切换工作目录（docker）
WORKDIR /opt/deploy
RUN tar -xzvf jdk8.tar.gz

# 添加jdk环境变量
ENV PATH=/opt/deploy/jdk8u362-b09/bin:$PATH
# docker build  -t inspection-service:1.0.0  .
