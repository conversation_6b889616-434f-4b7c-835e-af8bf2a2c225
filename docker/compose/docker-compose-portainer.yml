# docker-compose -f docker-compose-portainer.yml up -d
version: "3.6"
services:
  portainer:
    container_name: "portainer"
    hostname: "portainer"
    image: "portainer/portainer-ce:2.18.2"
    ports:
      - "8000:8000/tcp"
      - "9000:9000/tcp"
      - "9443:9443/tcp"
    restart: "always"
    environment:
      - "TZ=Asia/Shanghai"
    volumes:
      - "/etc/localtime:/etc/localtime:ro"
      - "/etc/timezone:/etc/timezone:ro"
      - "${OUT_PROJECT_HOME}/intelligent_inspection/portainer/data:/data"
      - "/var/run/docker.sock:/var/run/docker.sock"
    logging:
      driver: "json-file"
      options:
        max-size: "1G"
    deploy:
      resources:
        limits:
          cpus: "1.00"
          memory: 256M
        reservations:
          cpus: "0.25"
          memory: 128M
