version: '3.6'

services:
  jenkins:
    container_name: "jenkins"
    image: bitnami/jenkins:latest
    restart: "unless-stopped"
    ports:
      - '8082:8080'
    environment:
      - JENKINS_PASSWORD=bitnami
      - LANG=C.UTF-8
      - TZ=Asia/Shanghai
    volumes:
      - ${OUT_PROJECT_HOME}/jenkins/workspace:/bitnami/jenkins/home/<USER>
      - '/etc/localtime:/etc/localtime:ro'
    logging:
      driver: "json-file"
      options:
        max-size: "1G"

  #服务名称
  sonarqube:
    container_name: "sonarqube"
    image: "sonarqube:latest"
    restart: "unless-stopped"
    networks:
      - sonarnet
    privileged: true
    ports:
      - "9666:9000/tcp"
      - "9661:9001/tcp"
    # depends_on:
    #   - postgres
    environment:
      - sonar.jdbc.username=sonar
      - sonar.jdbc.password=sonar
      - sonar.jdbc.url=jdbc:postgresql://${LOCAL_HOST}:5432/sonarqube
      - SONARQUBE_JDBC_USERNAME=sonar
      - SONARQUBE_JDBC_PASSWORD=sonar
      - SONARQUBE_JDBC_URL=jdbc:postgresql://${LOCAL_HOST}:5432/sonarqube
      - TZ=Asia/Shanghai
    volumes:
      - ${OUT_PROJECT_HOME}/intelligent_inspection/sonarqube/extensions:/opt/sonarqube/extensions
      - ${OUT_PROJECT_HOME}/intelligent_inspection/sonarqube/logs:/opt/sonarqube/logs
      - ${OUT_PROJECT_HOME}/intelligent_inspection/sonarqube/data:/opt/sonarqube/data
      - ${OUT_PROJECT_HOME}/intelligent_inspection/sonarqube/conf:/opt/sonarqube/conf
      - '/etc/localtime:/etc/localtime:ro'
    logging:
      driver: "json-file"
      options:
        max-size: "1G"
volumes:
  jenkins_data:
    driver: local

networks:
  sonarnet:
    driver: bridge
