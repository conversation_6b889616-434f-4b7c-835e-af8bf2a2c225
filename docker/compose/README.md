# 部署前提：
- 相关服务docker镜像已经上传到私库

# 部署步骤：
## 1.启动portainer服务（使用docker run或docker-compose-portainer.yml都可以）
```
docker run -p 9000:9000 --name portainer \
--restart=always \
-v /var/run/docker.sock:/var/run/docker.sock \
-v /root/dev/portainer/data:/data \
-d portainer/portainer-ce:2.18.2
```
## 2.登录portainer管理页面，创建Stacks
## 3.进入新建Stacks，复制docker-compose.yml内容，并且导入export.env(变量配置)
![img.png](img.png)
## 4.修改变量值为实际环境的值，参数含义如下
|变量名|变量含义|值示例|
|-|-|-|
|OUT_PROJECT_HOME|宿主机工程根目录，用来映射容器内部路径|E:|
|REGISTRY_URL|Docker私库地址|***************:5000|
|IMAGE_VERSION|私库镜像统一版本|1.0.0|
|LOCAL_HOST|需要部署的服务器IP|***************|
|POSTGRES_PASSWORD|数据库密码|postgres|
|REDIS_PASSWD|Redis密码|暂时没有使用|
## 5.创建inspection-web和py-server-gateway服务的宿主机映射目录
```
# 目录一
mkdir -p ${OUT_PROJECT_HOME}/intelligent_inspection/web/config
# 目录二
mkdir -p ${OUT_PROJECT_HOME}/intelligent_inspection/py-server-gateway/conf
```
## 6.修改py-server-gateway-conf/vhost下文件里的IP地址为```${LOCAL_HOST}```后，将文件全部放入目录一
## 7.修改config.js里的服务IP为```${LOCAL_HOST}```后，将文件放入目录二
## 8.点击"Deploy the stack"按钮启动stack，开始部署服务

