# docker-compose up -d
version: "3.6"
services:
  # 数据库
  postgres:
    container_name: "postgres"
    hostname: "postgres"
    image: "${REGISTRY_URL}/postgres:12"
    environment:
      - "POSTGRES_PASSWORD=${POSTGRES_PASSWORD}"
      - "TZ=Asia/Shanghai"
    ports:
      - "5432:5432/tcp"
    restart: "unless-stopped"
    volumes:
      - "${OUT_PROJECT_HOME}/intelligent_inspection/postgre/data:/var/lib/postgresql/data"
      - "/etc/localtime:/etc/localtime:ro"
      - "/etc/timezone:/etc/timezone:ro"
    logging:
      driver: "json-file"
      options:
        max-size: "1G"
    deploy:
      resources:
        limits:
          cpus: "1.00"
          memory: 512M
        reservations:
          cpus: "0.25"
          memory: 128M
  # # 守护进程
  inspection-service-deamon:
    container_name: "inspection-service-deamon"
    hostname: "inspection-service-deamon"
    image: "${REGISTRY_URL}/inspection-service-deamon:1.0.0"
    environment:
      - "ping-pong.python.opt-urls=http://${LOCAL_HOST}:6401,http://${LOCAL_HOST}:6402,http://${LOCAL_HOST}:6403"
      - "ping-pong.python.opt-names=/inspection-backend-opt1,/inspection-backend-opt2,/inspection-backend-opt3"
      - "ping-pong.python.cv-urls=http://${LOCAL_HOST}:6201,http://${LOCAL_HOST}:6202"
      - "ping-pong.python.cv-names=/inspection-backend-cv1,/inspection-backend-cv2"
      - "ping-pong.java.system-urls=http://${LOCAL_HOST}:9090"
      - "ping-pong.java.system-names=/inspection-service"
      - "ping-pong.dependent-service.ips=${LOCAL_HOST},${LOCAL_HOST},${LOCAL_HOST}"
      - "ping-pong.dependent-service.ports=5432,1883,1935"
      - "ping-pong.dependent-service.names=/postgres,/emqx,/srs"
      - "portainer.auth-url=http://${LOCAL_HOST}:9000/api/auth"
      - "portainer.service-list-url=http://${LOCAL_HOST}:9000/api/endpoints/2/docker/containers/json?all=true"
      - "portainer.service-restart-url=http://${LOCAL_HOST}:9000/api/endpoints/2/docker/containers/%s/restart"
      - "portainer.username=admin"
      - "portainer.password=3edc#EDC3edc"
      - "TZ=Asia/Shanghai"
    restart: "unless-stopped"
    privileged: true
    working_dir: "/opt/deploy"
    logging:
      driver: "json-file"
      options:
        max-size: "1G"
    deploy:
      resources:
        limits:
          cpus: "0.5"
          memory: 256M
        reservations:
          cpus: "0.25"
          memory: 128M
  #  后端服务
  inspection-service:
    container_name: "inspection-service"
    hostname: "inspection-service"
    image: "${REGISTRY_URL}/inspection-service:inspection_power"
    mac_address: 02-42-AC-99-00-FF
    # runtime: nvidia
    environment:
      - "FFMPEG_PUSH_TYPE=cpu"
      - "SERVER_HOST=${LOCAL_HOST}"
      - "PROJECT_DIR=/project"
      - "TZ=Asia/Shanghai"
      - "SWAGGER_ENABLE=false"
      - "CROSS_ORIGIN_ENABLE=true"
      - "RABBITMQ_ENABLE=false"
      - "ALARM_INTERVAL_TIME=5"
      # - "NVIDIA_VISIBLE_DEVICES=all"
      - "SRS-SERVER_IST=FALSE"
      - "file-save-interval.time=60000000"
    ports:
      - "9090:9090/tcp"
      - "9527:9527/tcp"
    restart: "unless-stopped"
    privileged: true
    volumes:
      - "/etc/localtime:/etc/localtime:ro"
      - "/etc/timezone:/etc/timezone:ro"
      - "${OUT_PROJECT_HOME}/intelligent_inspection/java:/project"
      - "${OUT_PROJECT_HOME}/intelligent_inspection/java/alarm/inspection_alarm.json:/project/alarm/inspection_alarm.json"
    working_dir: "/opt/deploy"
    depends_on:
      - postgres
      - redis
      - emqx
    logging:
      driver: "json-file"
      options:
        max-size: "1G"
    deploy:
      resources:
        limits:
          cpus: "1.00"
          memory: 4096M
        reservations:
          cpus: "0.25"
          memory: 1024M
  # redis
  redis:
    command:
      - "redis-server"
      #      - "/usr/local/etc/redis/redis.conf"
      #      - "--requirepass"
      #      - "${REDIS_PASSWD}"
      - "--notify-keyspace-events"
      - "Ex"
    container_name: "redis"
    hostname: "redis"
    image: "${REGISTRY_URL}/redis:6"
    environment:
      - "TZ=Asia/Shanghai"
    ports:
      - "6379:6379/tcp"
    volumes:
      - "/etc/localtime:/etc/localtime:ro"
      - "/etc/timezone:/etc/timezone:ro"
      - "${OUT_PROJECT_HOME}/intelligent_inspection/redis/data:/data"
    restart: "unless-stopped"
    logging:
      driver: "json-file"
      options:
        max-size: "1G"
    deploy:
      resources:
        limits:
          cpus: "1.00"
          memory: 512M
        reservations:
          cpus: "0.25"
          memory: 128M
  # nodered
  nodered:
    container_name: "nodered"
    hostname: "nodered"
    image: "${REGISTRY_URL}/nodered/node-red:3.1.0-18"
    user: root
    environment:
      - "TZ=Asia/Shanghai"
    ports:
      - "1880:1880/tcp"
    volumes:
      - "/etc/localtime:/etc/localtime:ro"
      - "/etc/timezone:/etc/timezone:ro"
      - "${OUT_PROJECT_HOME}/intelligent_inspection/nodered/data:/data"
    restart: "unless-stopped"
    logging:
      driver: "json-file"
      options:
        max-size: "1G"
    deploy:
      resources:
        limits:
          cpus: "1.00"
          memory: 512M
        reservations:
          cpus: "0.25"
          memory: 128M

  # emqx
  emqx:
    container_name: "emqx"
    hostname: "emqx"
    image: "${REGISTRY_URL}/emqttd:2.3.6"
    environment:
      - "TZ=Asia/Shanghai"
    volumes:
      - "/etc/localtime:/etc/localtime:ro"
      - "/etc/timezone:/etc/timezone:ro"
    ports:
      - "18083:18083/tcp"
      - "1883:1883/tcp"
      - "8083:8083/tcp"
      - "8084:8084/tcp"
      - "8883:8883/tcp"
    restart: "unless-stopped"
    logging:
      driver: "json-file"
      options:
        max-size: "1G"
    deploy:
      resources:
        limits:
          cpus: "1.00"
          memory: 512M
        reservations:
          cpus: "0.25"
          memory: 128M
  # srs
  srs:
    command:
      - "objs/srs"
      - "-c"
      - "conf/docker.conf"
    container_name: "srs"
    hostname: "srs"
    image: "${REGISTRY_URL}/ossrs/srs:v5.0.176"
    environment:
      - "TZ=Asia/Shanghai"
      - "LOCAL_HOST=$LOCAL_HOST"
    volumes:
      - "/etc/localtime:/etc/localtime:ro"
      - "/etc/timezone:/etc/timezone:ro"
      - "${OUT_PROJECT_HOME}/intelligent_inspection/srs/docker.conf:/usr/local/srs/conf/docker.conf"
    ports:
      - "1935:1935/tcp"
      - "1985:1985/tcp"
      - "8000:8000/udp"
      - "8080:8080/tcp"
      - "5060:5060/tcp"
    restart: "unless-stopped"
    logging:
      driver: "json-file"
      options:
        max-size: "1G"
    deploy:
      resources:
        limits:
          cpus: "1.00"
          memory: 1024M
        reservations:
          cpus: "0.25"
          memory: 256M
  # 前端服务
  inspection-web:
    container_name: "inspection-web"
    hostname: "inspection-web"
    image: "${REGISTRY_URL}/inspection-web:inspection_power"
    environment:
      - "TZ=Asia/Shanghai"
    volumes:
      - "${OUT_PROJECT_HOME}/intelligent_inspection/web/config:/usr/share/nginx/html/config"
      - "/etc/localtime:/etc/localtime:ro"
      - "/etc/timezone:/etc/timezone:ro"
    ports:
      - "80:80/tcp"
    restart: "unless-stopped"
    logging:
      driver: "json-file"
      options:
        max-size: "1G"
    deploy:
      resources:
        limits:
          cpus: "0.5"
          memory: 128M
        reservations:
          cpus: "0.25"
          memory: 64M
  # Python服务网关
  py-server-gateway:
    container_name: "py-server-gateway"
    hostname: "py-server-gateway"
    image: "${REGISTRY_URL}/nginx:1.20"
    environment:
      - "TZ=Asia/Shanghai"
    ports:
      - "6200:6200/tcp"
      - "6300:6300/tcp"
      - "6400:6400/tcp"
    restart: "unless-stopped"
    volumes:
      - "/etc/localtime:/etc/localtime:ro"
      - "/etc/timezone:/etc/timezone:ro"
      - "${OUT_PROJECT_HOME}/intelligent_inspection/py-server-gateway/conf:/etc/nginx"
      - "${OUT_PROJECT_HOME}/intelligent_inspection/py-server-gateway/html:/usr/share/nginx/html"
      - "${OUT_PROJECT_HOME}/intelligent_inspection/py-server-gateway/logs:/var/log/nginx"
    logging:
      driver: "json-file"
      options:
        max-size: "1G"
    deploy:
      resources:
        limits:
          cpus: "1.00"
          memory: 64M
        reservations:
          cpus: "0.25"
          memory: 32M

  # cv服务1
  inspection-backend-cv1:
    container_name: "inspection-backend-cv1"
    hostname: "inspection-backend-cv1"
    image: "${REGISTRY_URL}/inspection-backend-cv:inspection_power"
    environment:
      - "TZ=Asia/Shanghai"
    ports:
      - "6201:6200/tcp"
    restart: "unless-stopped"
    volumes:
      - "/etc/localtime:/etc/localtime:ro"
      - "/etc/timezone:/etc/timezone:ro"
      - "${OUT_PROJECT_HOME}/intelligent_inspection/java/file-server/images:/project/file-server/images"
      - "${OUT_PROJECT_HOME}/intelligent_inspection/backend-cv1/logs:/logs"
      - "${OUT_PROJECT_HOME}/intelligent_inspection/algorithm-cv:/home/<USER>/cv_maths/algorithm/user"
    # logging:
    #   driver: "json-file"
    #   options:
    #     max-size: "1G"
    # deploy:
    #   resources:
    #     limits:
    #       cpus: "6.00"
    #       memory: 1024M
    #     reservations:
    #       cpus: "0.75"
    #       memory: 256M

  # cv服务2
  inspection-backend-cv2:
    container_name: "inspection-backend-cv2"
    hostname: "inspection-backend-cv2"
    image: "${REGISTRY_URL}/inspection-backend-cv:inspection_power"
    environment:
      - "TZ=Asia/Shanghai"
    ports:
      - "6202:6200/tcp"
    restart: "unless-stopped"
    volumes:
      - "/etc/localtime:/etc/localtime:ro"
      - "/etc/timezone:/etc/timezone:ro"
      - "${OUT_PROJECT_HOME}/intelligent_inspection/java/file-server/images:/project/file-server/images"
      - "${OUT_PROJECT_HOME}/intelligent_inspection/backend-cv2/logs:/logs"
      - "${OUT_PROJECT_HOME}/intelligent_inspection/algorithm-cv:/home/<USER>/cv_maths/algorithm/user"
  #   logging:
  #     driver: "json-file"
  #     options:
  #       max-size: "1G"
  #   deploy:
  #     resources:
  #       limits:
  #         cpus: "2.00"
  #         memory: 1024M
  #       reservations:
  #         cpus: "0.25"
  #         memory: 256M
  # cv服务3
  inspection-backend-cv3:
    container_name: "inspection-backend-cv3"
    hostname: "inspection-backend-cv3"
    image: "${REGISTRY_URL}/inspection-backend-cv:inspection_power"
    environment:
      - "TZ=Asia/Shanghai"
    ports:
      - "6203:6200/tcp"
    restart: "unless-stopped"
    volumes:
      - "/etc/localtime:/etc/localtime:ro"
      - "/etc/timezone:/etc/timezone:ro"
      - "${OUT_PROJECT_HOME}/intelligent_inspection/java/file-server/images:/project/file-server/images"
      - "${OUT_PROJECT_HOME}/intelligent_inspection/backend-cv3/logs:/logs"
      - "${OUT_PROJECT_HOME}/intelligent_inspection/algorithm-cv:/home/<USER>/cv_maths/algorithm/user"
  #   logging:
  #     driver: "json-file"
  #     options:
  #       max-size: "1G"
  #   deploy:
  #     resources:
  #       limits:
  #         cpus: "1.00"
  #         memory: 1024M
  #       reservations:
  #         cpus: "0.25"
  #         memory: 256M
  # cv服务4
  inspection-backend-cv4:
    container_name: "inspection-backend-cv4"
    hostname: "inspection-backend-cv4"
    image: "${REGISTRY_URL}/inspection-backend-cv:inspection_power"
    environment:
      - "TZ=Asia/Shanghai"
    ports:
      - "6204:6200/tcp"
    restart: "unless-stopped"
    volumes:
      - "/etc/localtime:/etc/localtime:ro"
      - "/etc/timezone:/etc/timezone:ro"
      - "${OUT_PROJECT_HOME}/intelligent_inspection/java/file-server/images:/project/file-server/images"
      - "${OUT_PROJECT_HOME}/intelligent_inspection/backend-cv4/logs:/logs"
      - "${OUT_PROJECT_HOME}/intelligent_inspection/algorithm-cv:/home/<USER>/cv_maths/algorithm/user"
    # logging:
    #   driver: "json-file"
    #   options:
    #     max-size: "1G"
    # deploy:
    #   resources:
    #     limits:
    #       cpus: "1.00"
    #       memory: 1024M
    #     reservations:
    #       cpus: "0.25"
    #       memory: 256M

  # 算法预测服务1
  inspection-backend-predict1:
    container_name: "inspection-backend-predict1"
    hostname: "inspection-backend-predict1"
    image: "${REGISTRY_URL}/inspection-backend-predict:inspection_power"
    environment:
      - "TZ=Asia/Shanghai"
      - "DATA_SIZE=25"
    ports:
      - "6500:6500/tcp"
    restart: "unless-stopped"
    volumes:
      - "/etc/localtime:/etc/localtime:ro"
      - "/etc/timezone:/etc/timezone:ro"
      - "${OUT_PROJECT_HOME}/intelligent_inspection/backend-predict1/logs:/logs"
    logging:
      driver: "json-file"
      options:
        max-size: "1G"
    deploy:
      resources:
        limits:
          cpus: "1.00"
          memory: 2048M
        reservations:
          cpus: "0.25"
          memory: 512M

  # 操作服务1
  inspection-backend-opt1:
    container_name: "inspection-backend-opt1"
    hostname: "inspection-backend-opt1"
    image: "${REGISTRY_URL}/inspection-backend-opt:1.0.0"
    environment:
      - "TZ=Asia/Shanghai"
    ports:
      - "6401:6400/tcp"
    restart: "unless-stopped"
    volumes:
      - "/etc/localtime:/etc/localtime:ro"
      - "/etc/timezone:/etc/timezone:ro"
      - "${OUT_PROJECT_HOME}/intelligent_inspection/java/file-server/images:/project/file-server/images"
      - "${OUT_PROJECT_HOME}/intelligent_inspection/backend-opt1/logs:/logs"
    logging:
      driver: "json-file"
      options:
        max-size: "1G"
    deploy:
      resources:
        limits:
          cpus: "1.00"
          memory: 2048M
        reservations:
          cpus: "0.25"
          memory: 512M

  # 操作服务2
  inspection-backend-opt2:
    container_name: "inspection-backend-opt2"
    hostname: "inspection-backend-opt2"
    image: "${REGISTRY_URL}/inspection-backend-opt:1.0.0"
    environment:
      - "TZ=Asia/Shanghai"
    ports:
      - "6402:6400/tcp"
    restart: "unless-stopped"
    volumes:
      - "/etc/localtime:/etc/localtime:ro"
      - "/etc/timezone:/etc/timezone:ro"
      - "${OUT_PROJECT_HOME}/intelligent_inspection/java/file-server/images:/project/file-server/images"
      - "${OUT_PROJECT_HOME}/intelligent_inspection/backend-opt2/logs:/logs"
    logging:
      driver: "json-file"
      options:
        max-size: "1G"
    deploy:
      resources:
        limits:
          cpus: "1.00"
          memory: 2048M
        reservations:
          cpus: "0.25"
          memory: 512M
  # 操作服务3
  inspection-backend-opt3:
    container_name: "inspection-backend-opt3"
    hostname: "inspection-backend-opt3"
    image: "${REGISTRY_URL}/inspection-backend-opt:1.0.0"
    environment:
      - "TZ=Asia/Shanghai"
    ports:
      - "6403:6400/tcp"
    restart: "unless-stopped"
    volumes:
      - "/etc/localtime:/etc/localtime:ro"
      - "/etc/timezone:/etc/timezone:ro"
      - "${OUT_PROJECT_HOME}/intelligent_inspection/java/file-server/images:/project/file-server/images"
      - "${OUT_PROJECT_HOME}/intelligent_inspection/backend-opt3/logs:/logs"
    logging:
      driver: "json-file"
      options:
        max-size: "1G"
    deploy:
      resources:
        limits:
          cpus: "1.00"
          memory: 2048M
        reservations:
          cpus: "0.25"
          memory: 512M
  # fuxa
  fuxa:
    container_name: "fuxa"
    hostname: "fuxa"
    image: "***************:5000/fuxa:power"
    environment:
      - "TZ=Asia/Shanghai"
    ports:
      - "1881:1881/tcp"
    volumes:
      - "/etc/localtime:/etc/localtime:ro"
      - "/etc/timezone:/etc/timezone:ro"
      - "${OUT_PROJECT_HOME}/intelligent_inspection/fuxa/data:/usr/src/app/FUXA/server/_appdata"
      - "${OUT_PROJECT_HOME}/intelligent_inspection/fuxa/db:/usr/src/app/FUXA/server/_db"
      - "${OUT_PROJECT_HOME}/intelligent_inspection/fuxa/logs:/usr/src/app/FUXA/server/_logs"
      - "${OUT_PROJECT_HOME}/intelligent_inspection/fuxa/images:/usr/src/app/FUXA/server/_images"
    restart: "unless-stopped"
    logging:
      driver: "json-file"
      options:
        max-size: "1G"
    deploy:
      resources:
        limits:
          cpus: "1.00"
          memory: 512M
        reservations:
          cpus: "0.25"
          memory: 128M



